import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/sonner";
import { ThemeProvider } from "@/components/theme-provider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Ashish Kamat - Full Stack Developer & UI/UX Designer",
  description: "Passionate full-stack developer and UI/UX designer creating innovative digital experiences. Specializing in React, Next.js, TypeScript, and modern web technologies.",
  keywords: ["Ashish Kamat", "Full Stack Developer", "UI/UX Designer", "React", "Next.js", "TypeScript", "Web Development", "Portfolio"],
  authors: [{ name: "<PERSON><PERSON>" }],
  creator: "<PERSON><PERSON>",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://ashishkamat.com.np",
    title: "Ashish Kamat - Full Stack Developer & UI/UX Designer",
    description: "Passionate full-stack developer and UI/UX designer creating innovative digital experiences.",
    siteName: "Ashish Kamat Portfolio",
  },
  twitter: {
    card: "summary_large_image",
    title: "Ashish Kamat - Full Stack Developer & UI/UX Designer",
    description: "Passionate full-stack developer and UI/UX designer creating innovative digital experiences.",
    creator: "@ashishkamat",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          {children}
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}
