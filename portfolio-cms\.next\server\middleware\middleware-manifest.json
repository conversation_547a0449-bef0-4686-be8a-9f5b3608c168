{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/:path*{(\\\\.json)}?", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GmR490q78K+bBIm3nmrLlRQO8rHHbnjcdhaBgWmTT64=", "__NEXT_PREVIEW_MODE_ID": "fc8c39bd91233004387a4adf433bdecb", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "23d1cdf862919ab8024b86a1245babc7df868f88bea35138bc6c2ca5d4a38d06", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "47e5a01fbfeff1242bbe0133f97be3734012be22121ae1e944309b4da83d3df7"}}}, "instrumentation": null, "functions": {}}