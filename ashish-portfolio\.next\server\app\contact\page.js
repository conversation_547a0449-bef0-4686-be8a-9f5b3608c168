(()=>{var e={};e.id=977,e.ids=[977],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16381:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>h,tree:()=>d});var s=r(65239),o=r(48088),n=r(88170),i=r.n(n),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,43839)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\contact\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,p=["C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\contact\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37022:(e,t,r)=>{Promise.resolve().then(r.bind(r,12302)),Promise.resolve().then(r.bind(r,94101)),Promise.resolve().then(r.bind(r,89650))},41750:(e,t,r)=>{Promise.resolve().then(r.bind(r,83802)),Promise.resolve().then(r.bind(r,64947)),Promise.resolve().then(r.bind(r,64544))},43839:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>a});var s=r(37413),o=r(64544),n=r(64947),i=r(83802);let a={title:"Contact - Ashish Kamat",description:"Get in touch with Ashish Kamat for web development projects, collaborations, or just to say hello. Available for freelance work and consulting.",openGraph:{title:"Contact - Ashish Kamat",description:"Get in touch with Ashish Kamat for web development projects, collaborations, or just to say hello. Available for freelance work and consulting."}};function l(){return(0,s.jsxs)("div",{className:"min-h-screen",children:[(0,s.jsx)(o.Navigation,{}),(0,s.jsx)("main",{className:"pt-16",children:(0,s.jsx)(i.ContactSection,{})}),(0,s.jsx)(n.Footer,{})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,760,658,373,150,468,153],()=>r(16381));module.exports=s})();