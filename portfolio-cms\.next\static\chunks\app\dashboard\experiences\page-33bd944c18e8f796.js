(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8487],{4516:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},13717:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},26126:(e,s,r)=>{"use strict";r.d(s,{E:()=>d});var a=r(95155);r(12115);var i=r(99708),t=r(74466),c=r(59434);let n=(0,t.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:s,variant:r,asChild:t=!1,...d}=e,l=t?i.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,c.cn)(n({variant:r}),s),...d})}},33786:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},53957:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>f});var a=r(95155),i=r(12115),t=r(35695),c=r(83930),n=r(30285),d=r(66695),l=r(26126),o=r(84616),h=r(48136),x=r(4516),p=r(69074),m=r(13717),u=r(62525),v=r(33786),y=r(56671);function f(){let e=(0,t.useRouter)(),[s,r]=(0,i.useState)([]),[f,j]=(0,i.useState)(!0);(0,i.useEffect)(()=>{b()},[]);let b=async()=>{try{let e=await fetch("/api/experiences");if(!e.ok)throw Error("Failed to fetch experiences");let s=await e.json();r(s)}catch(e){console.error("Error fetching experiences:",e),y.oR.error("Failed to fetch experiences")}finally{j(!1)}},g=async e=>{if(confirm("Are you sure you want to delete this experience?"))try{if(!(await fetch("/api/experiences/".concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete experience");r(s.filter(s=>s.id!==e)),y.oR.success("Experience deleted successfully")}catch(e){console.error("Error deleting experience:",e),y.oR.error("Failed to delete experience")}},N=async(e,a)=>{try{if(!(await fetch("/api/experiences/".concat(e),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({published:!a})})).ok)throw Error("Failed to update experience");r(s.map(s=>s.id===e?{...s,published:!a}:s)),y.oR.success("Experience ".concat(a?"unpublished":"published"))}catch(e){console.error("Error updating experience:",e),y.oR.error("Failed to update experience")}};return f?(0,a.jsx)(c.DashboardLayout,{children:(0,a.jsx)("div",{className:"container mx-auto py-8",children:(0,a.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-300 rounded w-64"}),(0,a.jsx)("div",{className:"grid gap-4",children:[1,2,3].map(e=>(0,a.jsx)("div",{className:"h-48 bg-gray-300 rounded"},e))})]})})}):(0,a.jsx)(c.DashboardLayout,{children:(0,a.jsxs)("div",{className:"container mx-auto py-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Work Experience"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage your professional experience"})]}),(0,a.jsxs)(n.$,{onClick:()=>e.push("/dashboard/experiences/add"),children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Add Experience"]})]}),(0,a.jsx)("div",{className:"grid gap-6",children:s.map(s=>(0,a.jsxs)(d.Zp,{className:"overflow-hidden",children:[(0,a.jsx)(d.aR,{className:"pb-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[s.companyLogo&&(0,a.jsx)("img",{src:s.companyLogo,alt:"".concat(s.company," logo"),className:"w-12 h-12 rounded-lg object-cover"}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d.ZB,{className:"text-xl",children:s.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-muted-foreground mt-1",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-1"}),s.company]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-1"}),s.location]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-1"}),s.period]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(l.E,{variant:s.published?"default":"secondary",children:s.published?"Published":"Draft"}),(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>N(s.id,s.published),children:s.published?"Unpublish":"Publish"}),(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>e.push("/dashboard/experiences/".concat(s.id,"/edit")),children:(0,a.jsx)(m.A,{className:"h-4 w-4"})}),(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>g(s.id),children:(0,a.jsx)(u.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsxs)(d.Wu,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:s.description}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-2",children:"Key Achievements"}),(0,a.jsx)("ul",{className:"list-disc list-inside text-sm space-y-1",children:s.achievements.map((e,s)=>(0,a.jsx)("li",{children:e},s))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-2",children:"Technologies"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:s.technologies.map((e,s)=>(0,a.jsx)(l.E,{variant:"outline",children:e},s))})]}),s.website&&(0,a.jsx)("div",{children:(0,a.jsxs)("a",{href:s.website,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-sm text-primary hover:underline",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-1"}),"Company Website"]})})]})]})]},s.id))}),0===s.length&&(0,a.jsx)(d.Zp,{children:(0,a.jsx)(d.Wu,{className:"py-8 text-center",children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"No experiences found. Add your first experience to get started."})})})]})})}},62525:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},69074:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},84616:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},93649:(e,s,r)=>{Promise.resolve().then(r.bind(r,53957))}},e=>{var s=s=>e(e.s=s);e.O(0,[4854,2108,8858,9368,6671,9008,8441,1684,7358],()=>s(93649)),_N_E=e.O()}]);