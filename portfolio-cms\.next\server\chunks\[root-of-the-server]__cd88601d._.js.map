{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma) as any,\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          role: user.role,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;gBACjB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;YACxB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/lib/cors.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nconst ALLOWED_ORIGINS = [\n  'http://localhost:3000',\n  'http://localhost:3001',\n  'http://localhost:3002',\n  'http://localhost:3003',\n  'http://127.0.0.1:3000',\n  'http://127.0.0.1:3001',\n  'http://127.0.0.1:3002',\n  'http://127.0.0.1:3003',\n]\n\nexport function corsHeaders(origin?: string) {\n  const allowedOrigin = origin && ALLOWED_ORIGINS.includes(origin) ? origin : '*'\n\n  return {\n    'Access-Control-Allow-Origin': allowedOrigin,\n    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',\n    'Access-Control-Allow-Credentials': 'true',\n    'Access-Control-Max-Age': '86400',\n  }\n}\n\nexport function withCors(response: NextResponse, origin?: string) {\n  const headers = corsHeaders(origin)\n  Object.entries(headers).forEach(([key, value]) => {\n    response.headers.set(key, value)\n  })\n  return response\n}\n\nexport function handleOptions(origin?: string) {\n  return new NextResponse(null, {\n    status: 200,\n    headers: corsHeaders(origin),\n  })\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAAS,YAAY,MAAe;IACzC,MAAM,gBAAgB,UAAU,gBAAgB,QAAQ,CAAC,UAAU,SAAS;IAE5E,OAAO;QACL,+BAA+B;QAC/B,gCAAgC;QAChC,gCAAgC;QAChC,oCAAoC;QACpC,0BAA0B;IAC5B;AACF;AAEO,SAAS,SAAS,QAAsB,EAAE,MAAe;IAC9D,MAAM,UAAU,YAAY;IAC5B,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC3C,SAAS,OAAO,CAAC,GAAG,CAAC,KAAK;IAC5B;IACA,OAAO;AACT;AAEO,SAAS,cAAc,MAAe;IAC3C,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS,YAAY;IACvB;AACF", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/portfolio-cms/src/app/api/certifications/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\nimport { withCors } from '@/lib/cors'\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { id } = await params\n    const certification = await prisma.certification.findUnique({\n      where: { id },\n    })\n\n    if (!certification) {\n      return withCors(NextResponse.json(\n        { error: 'Certification not found' },\n        { status: 404 }\n      ))\n    }\n\n    return withCors(NextResponse.json(certification))\n  } catch (error) {\n    console.error('Error fetching certification:', error)\n    return withCors(NextResponse.json(\n      { error: 'Failed to fetch certification' },\n      { status: 500 }\n    ))\n  }\n}\n\nexport async function PATCH(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session?.user) {\n      return NextResponse.json(\n        { error: 'Unauthorized' },\n        { status: 401 }\n      )\n    }\n\n    const { id } = await params\n    const body = await request.json()\n\n    const certification = await prisma.certification.update({\n      where: { id },\n      data: body,\n    })\n\n    return withCors(NextResponse.json(certification))\n  } catch (error) {\n    console.error('Error updating certification:', error)\n    return withCors(NextResponse.json(\n      { error: 'Failed to update certification' },\n      { status: 500 }\n    ))\n  }\n}\n\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session?.user) {\n      return NextResponse.json(\n        { error: 'Unauthorized' },\n        { status: 401 }\n      )\n    }\n\n    const { id } = await params\n    \n    await prisma.certification.delete({\n      where: { id },\n    })\n\n    return withCors(NextResponse.json({ success: true }))\n  } catch (error) {\n    console.error('Error deleting certification:', error)\n    return withCors(NextResponse.json(\n      { error: 'Failed to delete certification' },\n      { status: 500 }\n    ))\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,UAAU,CAAC;YAC1D,OAAO;gBAAE;YAAG;QACd;QAEA,IAAI,CAAC,eAAe;YAClB,OAAO,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,EAAE,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC/B;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,EAAE,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IACpC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,EAAE,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC/B;YAAE,OAAO;QAAgC,GACzC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,MACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAe,GACxB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YACtD,OAAO;gBAAE;YAAG;YACZ,MAAM;QACR;QAEA,OAAO,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,EAAE,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IACpC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,EAAE,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC/B;YAAE,OAAO;QAAiC,GAC1C;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAe,GACxB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QAErB,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YAChC,OAAO;gBAAE;YAAG;QACd;QAEA,OAAO,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,EAAE,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAK;IACpD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,CAAA,GAAA,oHAAA,CAAA,WAAQ,AAAD,EAAE,gIAAA,CAAA,eAAY,CAAC,IAAI,CAC/B;YAAE,OAAO;QAAiC,GAC1C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}