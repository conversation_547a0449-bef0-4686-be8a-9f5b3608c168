'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { DashboardLayout } from '@/components/dashboard/layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Plus, Edit, Trash2, Building, MapPin, Calendar, ExternalLink } from 'lucide-react'
import { toast } from 'sonner'

interface Experience {
  id: string
  title: string
  company: string
  companyLogo?: string
  location: string
  period: string
  type: string
  description: string
  achievements: string[]
  technologies: string[]
  website?: string
  order: number
  published: boolean
  createdAt: string
  updatedAt: string
}

export default function ExperiencesPage() {
  const router = useRouter()
  const [experiences, setExperiences] = useState<Experience[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchExperiences()
  }, [])

  const fetchExperiences = async () => {
    try {
      const response = await fetch('/api/experiences')
      if (!response.ok) throw new Error('Failed to fetch experiences')
      const data = await response.json()
      setExperiences(data)
    } catch (error) {
      console.error('Error fetching experiences:', error)
      toast.error('Failed to fetch experiences')
    } finally {
      setLoading(false)
    }
  }

  const deleteExperience = async (id: string) => {
    if (!confirm('Are you sure you want to delete this experience?')) return

    try {
      const response = await fetch(`/api/experiences/${id}`, {
        method: 'DELETE',
      })
      
      if (!response.ok) throw new Error('Failed to delete experience')
      
      setExperiences(experiences.filter(exp => exp.id !== id))
      toast.success('Experience deleted successfully')
    } catch (error) {
      console.error('Error deleting experience:', error)
      toast.error('Failed to delete experience')
    }
  }

  const togglePublished = async (id: string, published: boolean) => {
    try {
      const response = await fetch(`/api/experiences/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ published: !published }),
      })
      
      if (!response.ok) throw new Error('Failed to update experience')
      
      setExperiences(experiences.map(exp => 
        exp.id === id ? { ...exp, published: !published } : exp
      ))
      toast.success(`Experience ${!published ? 'published' : 'unpublished'}`)
    } catch (error) {
      console.error('Error updating experience:', error)
      toast.error('Failed to update experience')
    }
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="container mx-auto py-8">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-300 rounded w-64"></div>
            <div className="grid gap-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-48 bg-gray-300 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Work Experience</h1>
          <p className="text-muted-foreground">Manage your professional experience</p>
        </div>
        <Button onClick={() => router.push('/dashboard/experiences/add')}>
          <Plus className="h-4 w-4 mr-2" />
          Add Experience
        </Button>
      </div>

      <div className="grid gap-6">
        {experiences.map((experience) => (
          <Card key={experience.id} className="overflow-hidden">
            <CardHeader className="pb-4">
              <div className="flex justify-between items-start">
                <div className="flex items-start space-x-4">
                  {experience.companyLogo && (
                    <img
                      src={experience.companyLogo}
                      alt={`${experience.company} logo`}
                      className="w-12 h-12 rounded-lg object-cover"
                    />
                  )}
                  <div>
                    <CardTitle className="text-xl">{experience.title}</CardTitle>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                      <div className="flex items-center">
                        <Building className="h-4 w-4 mr-1" />
                        {experience.company}
                      </div>
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 mr-1" />
                        {experience.location}
                      </div>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {experience.period}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant={experience.published ? "default" : "secondary"}>
                    {experience.published ? "Published" : "Draft"}
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => togglePublished(experience.id, experience.published)}
                  >
                    {experience.published ? "Unpublish" : "Publish"}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.push(`/dashboard/experiences/${experience.id}/edit`)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => deleteExperience(experience.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">{experience.description}</p>
              
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Key Achievements</h4>
                  <ul className="list-disc list-inside text-sm space-y-1">
                    {experience.achievements.map((achievement, index) => (
                      <li key={index}>{achievement}</li>
                    ))}
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">Technologies</h4>
                  <div className="flex flex-wrap gap-2">
                    {experience.technologies.map((tech, index) => (
                      <Badge key={index} variant="outline">{tech}</Badge>
                    ))}
                  </div>
                </div>
                
                {experience.website && (
                  <div>
                    <a
                      href={experience.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-sm text-primary hover:underline"
                    >
                      <ExternalLink className="h-4 w-4 mr-1" />
                      Company Website
                    </a>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {experiences.length === 0 && (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">No experiences found. Add your first experience to get started.</p>
          </CardContent>
        </Card>
      )}
      </div>
    </DashboardLayout>
  )
}
