(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3551],{72149:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(95155),a=r(83930),l=r(71502);function n(){return(0,t.jsx)(a.DashboardLayout,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Create New Blog Post"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Write and publish a new blog post"})]}),(0,t.jsx)(l<PERSON><PERSON><PERSON>,{})]})})}},95824:(e,s,r)=>{Promise.resolve().then(r.bind(r,72149))}},e=>{var s=s=>e(e.s=s);e.O(0,[5004,277,4854,2108,8858,9368,6671,6766,7481,6411,9008,1502,8441,1684,7358],()=>s(95824)),_N_E=e.O()}]);