(()=>{var e={};e.id=9210,e.ids=[9210],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var s=r(13581),i=r(16467),o=r(94747),a=r(85663);let n={adapter:(0,i.y)(o.z),providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=await o.z.user.findUnique({where:{email:e.email}});return t&&await a.Ay.compare(e.password,t.password)?{id:t.id,email:t.email,name:t.name,role:t.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},27746:(e,t,r)=>{"use strict";r.d(t,{JB:()=>n,gx:()=>a});var s=r(32190);let i=["http://localhost:3000","http://localhost:3001","http://localhost:3002","http://localhost:3003","http://127.0.0.1:3000","http://127.0.0.1:3001","http://127.0.0.1:3002","http://127.0.0.1:3003"];function o(e){return{"Access-Control-Allow-Origin":e&&i.includes(e)?e:"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-Requested-With","Access-Control-Allow-Credentials":"true","Access-Control-Max-Age":"86400"}}function a(e,t){return Object.entries(o(t)).forEach(([t,r])=>{e.headers.set(t,r)}),e}function n(e){return new s.NextResponse(null,{status:200,headers:o(e)})}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},94747:(e,t,r)=>{"use strict";r.d(t,{z:()=>i});let s=require("@prisma/client"),i=globalThis.prisma??new s.PrismaClient},96465:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>x,serverHooks:()=>f,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>d,OPTIONS:()=>m,POST:()=>h});var i=r(96559),o=r(48088),a=r(37719),n=r(32190),u=r(19854),l=r(12909),c=r(94747),p=r(27746);async function d(){try{let e=await c.z.testimonial.findMany({include:{author:{select:{id:!0,name:!0,email:!0}}},orderBy:{order:"asc"}});return(0,p.gx)(n.NextResponse.json(e))}catch(e){return console.error("Error fetching testimonials:",e),(0,p.gx)(n.NextResponse.json({error:"Failed to fetch testimonials"},{status:500}))}}async function h(e){try{let t=await (0,u.getServerSession)(l.N);if(!t?.user)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{name:r,role:s,company:i,content:o,avatar:a,rating:d,featured:h,published:m,order:x}=await e.json(),w=await c.z.testimonial.create({data:{name:r,role:s,company:i,content:o,avatar:a,rating:d||5,featured:h||!1,published:void 0===m||m,order:x||0,authorId:t.user.id},include:{author:{select:{id:!0,name:!0,email:!0}}}});return(0,p.gx)(n.NextResponse.json(w,{status:201}))}catch(e){return console.error("Error creating testimonial:",e),(0,p.gx)(n.NextResponse.json({error:"Failed to create testimonial"},{status:500}))}}async function m(){return(0,p.JB)()}let x=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/testimonials/route",pathname:"/api/testimonials",filename:"route",bundlePath:"app/api/testimonials/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\testimonials\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:g,serverHooks:f}=x;function y(){return(0,a.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:g})}},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,3542,2190],()=>r(96465));module.exports=s})();