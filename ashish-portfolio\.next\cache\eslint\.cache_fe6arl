[{"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\blog\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\blog\\[slug]\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\contact\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\layout.tsx": "5", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\manifest.ts": "6", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\projects\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\robots.ts": "9", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\sitemap.ts": "10", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\test-api\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\about\\about-hero.tsx": "12", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\about\\education.tsx": "13", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\about\\experience.tsx": "14", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\about\\skills.tsx": "15", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\blog\\blog-grid.tsx": "16", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\blog\\blog-hero.tsx": "17", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\blog\\blog-newsletter.tsx": "18", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\blog\\blog-post-content.tsx": "19", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\blog\\blog-post-header.tsx": "20", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\blog\\blog-post-navigation.tsx": "21", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\blog\\blog-post-sidebar.tsx": "22", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\blog\\related-posts.tsx": "23", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\command-palette.tsx": "24", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\contact-section.tsx": "25", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\experience-section.tsx": "26", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\footer.tsx": "27", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\hero-section.tsx": "28", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\navigation.tsx": "29", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\projects\\projects-grid.tsx": "30", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\projects\\projects-hero.tsx": "31", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\projects\\projects-stats.tsx": "32", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\projects-showcase.tsx": "33", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\providers\\query-provider.tsx": "34", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\services.tsx": "35", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\tech-stack.tsx": "36", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\testimonials.tsx": "37", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\theme-provider.tsx": "38", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\avatar.tsx": "39", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\background-gradient-animation.tsx": "40", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\badge.tsx": "41", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\button.tsx": "42", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\card.tsx": "43", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\dialog.tsx": "44", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\dropdown-menu.tsx": "45", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\input.tsx": "46", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\label.tsx": "47", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\navigation-menu.tsx": "48", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\progress.tsx": "49", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\separator.tsx": "50", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\sonner.tsx": "51", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\textarea.tsx": "52", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\tooltip.tsx": "53", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\lib\\api.ts": "54", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\lib\\queries.ts": "55", "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\lib\\utils.ts": "56"}, {"size": 1054, "mtime": 1751352779867, "results": "57", "hashOfConfig": "58"}, {"size": 1061, "mtime": 1751353031504, "results": "59", "hashOfConfig": "58"}, {"size": 2413, "mtime": 1751596746744, "results": "60", "hashOfConfig": "58"}, {"size": 869, "mtime": 1751353132213, "results": "61", "hashOfConfig": "58"}, {"size": 2210, "mtime": 1751596170451, "results": "62", "hashOfConfig": "58"}, {"size": 631, "mtime": 1751352264167, "results": "63", "hashOfConfig": "58"}, {"size": 858, "mtime": 1751596099456, "results": "64", "hashOfConfig": "58"}, {"size": 1052, "mtime": 1751352934948, "results": "65", "hashOfConfig": "58"}, {"size": 269, "mtime": 1751420254168, "results": "66", "hashOfConfig": "58"}, {"size": 941, "mtime": 1751420263528, "results": "67", "hashOfConfig": "58"}, {"size": 1386, "mtime": 1751359314661, "results": "68", "hashOfConfig": "58"}, {"size": 5569, "mtime": 1751354911249, "results": "69", "hashOfConfig": "58"}, {"size": 14724, "mtime": 1751528303319, "results": "70", "hashOfConfig": "58"}, {"size": 10480, "mtime": 1751528921544, "results": "71", "hashOfConfig": "58"}, {"size": 6625, "mtime": 1751352864763, "results": "72", "hashOfConfig": "58"}, {"size": 8599, "mtime": 1751361223977, "results": "73", "hashOfConfig": "58"}, {"size": 4140, "mtime": 1751353050050, "results": "74", "hashOfConfig": "58"}, {"size": 5889, "mtime": 1751353122166, "results": "75", "hashOfConfig": "58"}, {"size": 8071, "mtime": 1751596914783, "results": "76", "hashOfConfig": "58"}, {"size": 5735, "mtime": 1751597100932, "results": "77", "hashOfConfig": "58"}, {"size": 4104, "mtime": 1751354062993, "results": "78", "hashOfConfig": "58"}, {"size": 8768, "mtime": 1751597171867, "results": "79", "hashOfConfig": "58"}, {"size": 6449, "mtime": 1751360956050, "results": "80", "hashOfConfig": "58"}, {"size": 9683, "mtime": 1751352314548, "results": "81", "hashOfConfig": "58"}, {"size": 12141, "mtime": 1751596519469, "results": "82", "hashOfConfig": "58"}, {"size": 12116, "mtime": 1751533195609, "results": "83", "hashOfConfig": "58"}, {"size": 8539, "mtime": 1751596316808, "results": "84", "hashOfConfig": "58"}, {"size": 7731, "mtime": 1751595234145, "results": "85", "hashOfConfig": "58"}, {"size": 6496, "mtime": 1751352370134, "results": "86", "hashOfConfig": "58"}, {"size": 7979, "mtime": 1751361067617, "results": "87", "hashOfConfig": "58"}, {"size": 3457, "mtime": 1751352952202, "results": "88", "hashOfConfig": "58"}, {"size": 1872, "mtime": 1751352968446, "results": "89", "hashOfConfig": "58"}, {"size": 10902, "mtime": 1751358709643, "results": "90", "hashOfConfig": "58"}, {"size": 1080, "mtime": 1751357703474, "results": "91", "hashOfConfig": "58"}, {"size": 6588, "mtime": 1751358734133, "results": "92", "hashOfConfig": "58"}, {"size": 8316, "mtime": 1751595779267, "results": "93", "hashOfConfig": "58"}, {"size": 10279, "mtime": 1751597308345, "results": "94", "hashOfConfig": "58"}, {"size": 316, "mtime": 1751597322399, "results": "95", "hashOfConfig": "58"}, {"size": 1097, "mtime": 1751351592317, "results": "96", "hashOfConfig": "58"}, {"size": 6480, "mtime": 1751594422279, "results": "97", "hashOfConfig": "58"}, {"size": 1631, "mtime": 1751351592310, "results": "98", "hashOfConfig": "58"}, {"size": 2123, "mtime": 1751351592262, "results": "99", "hashOfConfig": "58"}, {"size": 1989, "mtime": 1751351592282, "results": "100", "hashOfConfig": "58"}, {"size": 3982, "mtime": 1751351592336, "results": "101", "hashOfConfig": "58"}, {"size": 8284, "mtime": 1751351592369, "results": "102", "hashOfConfig": "58"}, {"size": 967, "mtime": 1751351592286, "results": "103", "hashOfConfig": "58"}, {"size": 611, "mtime": 1751351592298, "results": "104", "hashOfConfig": "58"}, {"size": 6664, "mtime": 1751351592388, "results": "105", "hashOfConfig": "58"}, {"size": 740, "mtime": 1751352881838, "results": "106", "hashOfConfig": "58"}, {"size": 699, "mtime": 1751353994316, "results": "107", "hashOfConfig": "58"}, {"size": 564, "mtime": 1751351592394, "results": "108", "hashOfConfig": "58"}, {"size": 759, "mtime": 1751351592291, "results": "109", "hashOfConfig": "58"}, {"size": 1891, "mtime": 1751351592408, "results": "110", "hashOfConfig": "58"}, {"size": 6624, "mtime": 1751531744715, "results": "111", "hashOfConfig": "58"}, {"size": 2005, "mtime": 1751357693205, "results": "112", "hashOfConfig": "58"}, {"size": 166, "mtime": 1751351570251, "results": "113", "hashOfConfig": "58"}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7jgdo5", {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\blog\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\blog\\[slug]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\manifest.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\projects\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\robots.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\sitemap.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\test-api\\page.tsx", ["282"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\about\\about-hero.tsx", ["283", "284", "285", "286"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\about\\education.tsx", ["287"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\about\\experience.tsx", ["288"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\about\\skills.tsx", ["289", "290"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\blog\\blog-grid.tsx", ["291", "292"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\blog\\blog-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\blog\\blog-newsletter.tsx", ["293", "294"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\blog\\blog-post-content.tsx", ["295", "296"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\blog\\blog-post-header.tsx", ["297"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\blog\\blog-post-navigation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\blog\\blog-post-sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\blog\\related-posts.tsx", ["298"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\command-palette.tsx", ["299", "300", "301", "302", "303"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\contact-section.tsx", ["304", "305", "306", "307", "308", "309"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\experience-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\hero-section.tsx", ["310"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\navigation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\projects\\projects-grid.tsx", ["311"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\projects\\projects-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\projects\\projects-stats.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\projects-showcase.tsx", ["312"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\providers\\query-provider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\services.tsx", ["313", "314"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\tech-stack.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\testimonials.tsx", ["315", "316", "317", "318", "319", "320"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\theme-provider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\background-gradient-animation.tsx", ["321", "322"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\navigation-menu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\sonner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\lib\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\lib\\queries.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\lib\\utils.ts", [], [], {"ruleId": "323", "severity": 2, "message": "324", "line": 6, "column": 36, "nodeType": "325", "messageId": "326", "endLine": 6, "endColumn": 39, "suggestions": "327"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 37, "column": 18, "nodeType": "330", "messageId": "331", "suggestions": "332"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 59, "column": 23, "nodeType": "330", "messageId": "331", "suggestions": "333"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 59, "column": 41, "nodeType": "330", "messageId": "331", "suggestions": "334"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 76, "column": 20, "nodeType": "330", "messageId": "331", "suggestions": "335"}, {"ruleId": "336", "severity": 2, "message": "337", "line": 5, "column": 42, "nodeType": null, "messageId": "338", "endLine": 5, "endColumn": 50}, {"ruleId": "336", "severity": 2, "message": "337", "line": 5, "column": 10, "nodeType": null, "messageId": "338", "endLine": 5, "endColumn": 18}, {"ruleId": "336", "severity": 2, "message": "339", "line": 6, "column": 10, "nodeType": null, "messageId": "338", "endLine": 6, "endColumn": 18}, {"ruleId": "328", "severity": 2, "message": "329", "line": 140, "column": 16, "nodeType": "330", "messageId": "331", "suggestions": "340"}, {"ruleId": "336", "severity": 2, "message": "341", "line": 7, "column": 39, "nodeType": null, "messageId": "338", "endLine": 7, "endColumn": 42}, {"ruleId": "323", "severity": 2, "message": "324", "line": 47, "column": 67, "nodeType": "325", "messageId": "326", "endLine": 47, "endColumn": 70, "suggestions": "342"}, {"ruleId": "336", "severity": 2, "message": "343", "line": 34, "column": 14, "nodeType": null, "messageId": "338", "endLine": 34, "endColumn": 19}, {"ruleId": "328", "severity": 2, "message": "329", "line": 118, "column": 55, "nodeType": "330", "messageId": "331", "suggestions": "344"}, {"ruleId": "323", "severity": 2, "message": "324", "line": 110, "column": 63, "nodeType": "325", "messageId": "326", "endLine": 110, "endColumn": 66, "suggestions": "345"}, {"ruleId": "346", "severity": 1, "message": "347", "line": 154, "column": 17, "nodeType": "348", "endLine": 158, "endColumn": 19}, {"ruleId": "336", "severity": 2, "message": "349", "line": 3, "column": 8, "nodeType": null, "messageId": "338", "endLine": 3, "endColumn": 13}, {"ruleId": "336", "severity": 2, "message": "350", "line": 39, "column": 10, "nodeType": null, "messageId": "338", "endLine": 39, "endColumn": 19}, {"ruleId": "336", "severity": 2, "message": "351", "line": 5, "column": 18, "nodeType": null, "messageId": "338", "endLine": 5, "endColumn": 33}, {"ruleId": "323", "severity": 2, "message": "324", "line": 31, "column": 29, "nodeType": "325", "messageId": "326", "endLine": 31, "endColumn": 32, "suggestions": "352"}, {"ruleId": "328", "severity": 2, "message": "353", "line": 218, "column": 37, "nodeType": "330", "messageId": "331", "suggestions": "354"}, {"ruleId": "328", "severity": 2, "message": "353", "line": 218, "column": 46, "nodeType": "330", "messageId": "331", "suggestions": "355"}, {"ruleId": "336", "severity": 2, "message": "356", "line": 228, "column": 45, "nodeType": null, "messageId": "338", "endLine": 228, "endColumn": 50}, {"ruleId": "336", "severity": 2, "message": "343", "line": 116, "column": 14, "nodeType": null, "messageId": "338", "endLine": 116, "endColumn": 19}, {"ruleId": "328", "severity": 2, "message": "329", "line": 133, "column": 48, "nodeType": "330", "messageId": "331", "suggestions": "357"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 136, "column": 38, "nodeType": "330", "messageId": "331", "suggestions": "358"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 137, "column": 16, "nodeType": "330", "messageId": "331", "suggestions": "359"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 152, "column": 18, "nodeType": "330", "messageId": "331", "suggestions": "360"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 238, "column": 18, "nodeType": "330", "messageId": "331", "suggestions": "361"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 80, "column": 25, "nodeType": "330", "messageId": "331", "suggestions": "362"}, {"ruleId": "323", "severity": 2, "message": "324", "line": 44, "column": 66, "nodeType": "325", "messageId": "326", "endLine": 44, "endColumn": 69, "suggestions": "363"}, {"ruleId": "323", "severity": 2, "message": "324", "line": 46, "column": 66, "nodeType": "325", "messageId": "326", "endLine": 46, "endColumn": 69, "suggestions": "364"}, {"ruleId": "323", "severity": 2, "message": "324", "line": 21, "column": 31, "nodeType": "325", "messageId": "326", "endLine": 21, "endColumn": 34, "suggestions": "365"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 165, "column": 18, "nodeType": "330", "messageId": "331", "suggestions": "366"}, {"ruleId": "336", "severity": 2, "message": "349", "line": 4, "column": 8, "nodeType": null, "messageId": "338", "endLine": 4, "endColumn": 13}, {"ruleId": "367", "severity": 1, "message": "368", "line": 54, "column": 6, "nodeType": "369", "endLine": 54, "endColumn": 27, "suggestions": "370"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 128, "column": 16, "nodeType": "330", "messageId": "331", "suggestions": "371"}, {"ruleId": "328", "severity": 2, "message": "329", "line": 128, "column": 49, "nodeType": "330", "messageId": "331", "suggestions": "372"}, {"ruleId": "328", "severity": 2, "message": "353", "line": 164, "column": 23, "nodeType": "330", "messageId": "331", "suggestions": "373"}, {"ruleId": "328", "severity": 2, "message": "353", "line": 164, "column": 52, "nodeType": "330", "messageId": "331", "suggestions": "374"}, {"ruleId": "367", "severity": 1, "message": "375", "line": 59, "column": 6, "nodeType": "369", "endLine": 59, "endColumn": 8, "suggestions": "376"}, {"ruleId": "367", "severity": 1, "message": "377", "line": 74, "column": 6, "nodeType": "369", "endLine": 74, "endColumn": 16, "suggestions": "378"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["379", "380"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["381", "382", "383", "384"], ["385", "386", "387", "388"], ["389", "390", "391", "392"], ["393", "394", "395", "396"], "@typescript-eslint/no-unused-vars", "'Calendar' is defined but never used.", "unusedVar", "'Progress' is defined but never used.", ["397", "398", "399", "400"], "'Tag' is defined but never used.", ["401", "402"], "'error' is defined but never used.", ["403", "404", "405", "406"], ["407", "408"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "'Image' is defined but never used.", "'isLoading' is assigned a value but never used.", "'AnimatePresence' is defined but never used.", ["409", "410"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["411", "412", "413", "414"], ["415", "416", "417", "418"], "'index' is defined but never used.", ["419", "420", "421", "422"], ["423", "424", "425", "426"], ["427", "428", "429", "430"], ["431", "432", "433", "434"], ["435", "436", "437", "438"], ["439", "440", "441", "442"], ["443", "444"], ["445", "446"], ["447", "448"], ["449", "450", "451", "452"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'nextTestimonial'. Either include it or remove the dependency array.", "ArrayExpression", ["453"], ["454", "455", "456", "457"], ["458", "459", "460", "461"], ["462", "463", "464", "465"], ["466", "467", "468", "469"], "React Hook useEffect has missing dependencies: 'blendingValue', 'fifthColor', 'firstColor', 'fourthColor', 'gradientBackgroundEnd', 'gradientBackgroundStart', 'pointerColor', 'secondColor', 'size', and 'thirdColor'. Either include them or remove the dependency array.", ["470"], "React Hook useEffect has missing dependencies: 'curX' and 'curY'. Either include them or remove the dependency array. You can also do a functional update 'setCurX(c => ...)' if you only need 'curX' in the 'setCurX' call.", ["471"], {"messageId": "472", "fix": "473", "desc": "474"}, {"messageId": "475", "fix": "476", "desc": "477"}, {"messageId": "478", "data": "479", "fix": "480", "desc": "481"}, {"messageId": "478", "data": "482", "fix": "483", "desc": "484"}, {"messageId": "478", "data": "485", "fix": "486", "desc": "487"}, {"messageId": "478", "data": "488", "fix": "489", "desc": "490"}, {"messageId": "478", "data": "491", "fix": "492", "desc": "481"}, {"messageId": "478", "data": "493", "fix": "494", "desc": "484"}, {"messageId": "478", "data": "495", "fix": "496", "desc": "487"}, {"messageId": "478", "data": "497", "fix": "498", "desc": "490"}, {"messageId": "478", "data": "499", "fix": "500", "desc": "481"}, {"messageId": "478", "data": "501", "fix": "502", "desc": "484"}, {"messageId": "478", "data": "503", "fix": "504", "desc": "487"}, {"messageId": "478", "data": "505", "fix": "506", "desc": "490"}, {"messageId": "478", "data": "507", "fix": "508", "desc": "481"}, {"messageId": "478", "data": "509", "fix": "510", "desc": "484"}, {"messageId": "478", "data": "511", "fix": "512", "desc": "487"}, {"messageId": "478", "data": "513", "fix": "514", "desc": "490"}, {"messageId": "478", "data": "515", "fix": "516", "desc": "481"}, {"messageId": "478", "data": "517", "fix": "518", "desc": "484"}, {"messageId": "478", "data": "519", "fix": "520", "desc": "487"}, {"messageId": "478", "data": "521", "fix": "522", "desc": "490"}, {"messageId": "472", "fix": "523", "desc": "474"}, {"messageId": "475", "fix": "524", "desc": "477"}, {"messageId": "478", "data": "525", "fix": "526", "desc": "481"}, {"messageId": "478", "data": "527", "fix": "528", "desc": "484"}, {"messageId": "478", "data": "529", "fix": "530", "desc": "487"}, {"messageId": "478", "data": "531", "fix": "532", "desc": "490"}, {"messageId": "472", "fix": "533", "desc": "474"}, {"messageId": "475", "fix": "534", "desc": "477"}, {"messageId": "472", "fix": "535", "desc": "474"}, {"messageId": "475", "fix": "536", "desc": "477"}, {"messageId": "478", "data": "537", "fix": "538", "desc": "539"}, {"messageId": "478", "data": "540", "fix": "541", "desc": "542"}, {"messageId": "478", "data": "543", "fix": "544", "desc": "545"}, {"messageId": "478", "data": "546", "fix": "547", "desc": "548"}, {"messageId": "478", "data": "549", "fix": "550", "desc": "539"}, {"messageId": "478", "data": "551", "fix": "552", "desc": "542"}, {"messageId": "478", "data": "553", "fix": "554", "desc": "545"}, {"messageId": "478", "data": "555", "fix": "556", "desc": "548"}, {"messageId": "478", "data": "557", "fix": "558", "desc": "481"}, {"messageId": "478", "data": "559", "fix": "560", "desc": "484"}, {"messageId": "478", "data": "561", "fix": "562", "desc": "487"}, {"messageId": "478", "data": "563", "fix": "564", "desc": "490"}, {"messageId": "478", "data": "565", "fix": "566", "desc": "481"}, {"messageId": "478", "data": "567", "fix": "568", "desc": "484"}, {"messageId": "478", "data": "569", "fix": "570", "desc": "487"}, {"messageId": "478", "data": "571", "fix": "572", "desc": "490"}, {"messageId": "478", "data": "573", "fix": "574", "desc": "481"}, {"messageId": "478", "data": "575", "fix": "576", "desc": "484"}, {"messageId": "478", "data": "577", "fix": "578", "desc": "487"}, {"messageId": "478", "data": "579", "fix": "580", "desc": "490"}, {"messageId": "478", "data": "581", "fix": "582", "desc": "481"}, {"messageId": "478", "data": "583", "fix": "584", "desc": "484"}, {"messageId": "478", "data": "585", "fix": "586", "desc": "487"}, {"messageId": "478", "data": "587", "fix": "588", "desc": "490"}, {"messageId": "478", "data": "589", "fix": "590", "desc": "481"}, {"messageId": "478", "data": "591", "fix": "592", "desc": "484"}, {"messageId": "478", "data": "593", "fix": "594", "desc": "487"}, {"messageId": "478", "data": "595", "fix": "596", "desc": "490"}, {"messageId": "478", "data": "597", "fix": "598", "desc": "481"}, {"messageId": "478", "data": "599", "fix": "600", "desc": "484"}, {"messageId": "478", "data": "601", "fix": "602", "desc": "487"}, {"messageId": "478", "data": "603", "fix": "604", "desc": "490"}, {"messageId": "472", "fix": "605", "desc": "474"}, {"messageId": "475", "fix": "606", "desc": "477"}, {"messageId": "472", "fix": "607", "desc": "474"}, {"messageId": "475", "fix": "608", "desc": "477"}, {"messageId": "472", "fix": "609", "desc": "474"}, {"messageId": "475", "fix": "610", "desc": "477"}, {"messageId": "478", "data": "611", "fix": "612", "desc": "481"}, {"messageId": "478", "data": "613", "fix": "614", "desc": "484"}, {"messageId": "478", "data": "615", "fix": "616", "desc": "487"}, {"messageId": "478", "data": "617", "fix": "618", "desc": "490"}, {"desc": "619", "fix": "620"}, {"messageId": "478", "data": "621", "fix": "622", "desc": "481"}, {"messageId": "478", "data": "623", "fix": "624", "desc": "484"}, {"messageId": "478", "data": "625", "fix": "626", "desc": "487"}, {"messageId": "478", "data": "627", "fix": "628", "desc": "490"}, {"messageId": "478", "data": "629", "fix": "630", "desc": "481"}, {"messageId": "478", "data": "631", "fix": "632", "desc": "484"}, {"messageId": "478", "data": "633", "fix": "634", "desc": "487"}, {"messageId": "478", "data": "635", "fix": "636", "desc": "490"}, {"messageId": "478", "data": "637", "fix": "638", "desc": "539"}, {"messageId": "478", "data": "639", "fix": "640", "desc": "542"}, {"messageId": "478", "data": "641", "fix": "642", "desc": "545"}, {"messageId": "478", "data": "643", "fix": "644", "desc": "548"}, {"messageId": "478", "data": "645", "fix": "646", "desc": "539"}, {"messageId": "478", "data": "647", "fix": "648", "desc": "542"}, {"messageId": "478", "data": "649", "fix": "650", "desc": "545"}, {"messageId": "478", "data": "651", "fix": "652", "desc": "548"}, {"desc": "653", "fix": "654"}, {"desc": "655", "fix": "656"}, "suggestUnknown", {"range": "657", "text": "658"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "659", "text": "660"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "replaceWithAlt", {"alt": "661"}, {"range": "662", "text": "663"}, "Replace with `&apos;`.", {"alt": "664"}, {"range": "665", "text": "666"}, "Replace with `&lsquo;`.", {"alt": "667"}, {"range": "668", "text": "669"}, "Replace with `&#39;`.", {"alt": "670"}, {"range": "671", "text": "672"}, "Replace with `&rsquo;`.", {"alt": "661"}, {"range": "673", "text": "674"}, {"alt": "664"}, {"range": "675", "text": "676"}, {"alt": "667"}, {"range": "677", "text": "678"}, {"alt": "670"}, {"range": "679", "text": "680"}, {"alt": "661"}, {"range": "681", "text": "682"}, {"alt": "664"}, {"range": "683", "text": "684"}, {"alt": "667"}, {"range": "685", "text": "686"}, {"alt": "670"}, {"range": "687", "text": "688"}, {"alt": "661"}, {"range": "689", "text": "690"}, {"alt": "664"}, {"range": "691", "text": "692"}, {"alt": "667"}, {"range": "693", "text": "694"}, {"alt": "670"}, {"range": "695", "text": "696"}, {"alt": "661"}, {"range": "697", "text": "698"}, {"alt": "664"}, {"range": "699", "text": "700"}, {"alt": "667"}, {"range": "701", "text": "702"}, {"alt": "670"}, {"range": "703", "text": "704"}, {"range": "705", "text": "658"}, {"range": "706", "text": "660"}, {"alt": "661"}, {"range": "707", "text": "708"}, {"alt": "664"}, {"range": "709", "text": "710"}, {"alt": "667"}, {"range": "711", "text": "712"}, {"alt": "670"}, {"range": "713", "text": "714"}, {"range": "715", "text": "658"}, {"range": "716", "text": "660"}, {"range": "717", "text": "658"}, {"range": "718", "text": "660"}, {"alt": "719"}, {"range": "720", "text": "721"}, "Replace with `&quot;`.", {"alt": "722"}, {"range": "723", "text": "724"}, "Replace with `&ldquo;`.", {"alt": "725"}, {"range": "726", "text": "727"}, "Replace with `&#34;`.", {"alt": "728"}, {"range": "729", "text": "730"}, "Replace with `&rdquo;`.", {"alt": "719"}, {"range": "731", "text": "732"}, {"alt": "722"}, {"range": "733", "text": "734"}, {"alt": "725"}, {"range": "735", "text": "736"}, {"alt": "728"}, {"range": "737", "text": "738"}, {"alt": "661"}, {"range": "739", "text": "740"}, {"alt": "664"}, {"range": "741", "text": "742"}, {"alt": "667"}, {"range": "743", "text": "744"}, {"alt": "670"}, {"range": "745", "text": "746"}, {"alt": "661"}, {"range": "747", "text": "748"}, {"alt": "664"}, {"range": "749", "text": "750"}, {"alt": "667"}, {"range": "751", "text": "752"}, {"alt": "670"}, {"range": "753", "text": "754"}, {"alt": "661"}, {"range": "755", "text": "756"}, {"alt": "664"}, {"range": "757", "text": "758"}, {"alt": "667"}, {"range": "759", "text": "760"}, {"alt": "670"}, {"range": "761", "text": "762"}, {"alt": "661"}, {"range": "763", "text": "764"}, {"alt": "664"}, {"range": "765", "text": "766"}, {"alt": "667"}, {"range": "767", "text": "768"}, {"alt": "670"}, {"range": "769", "text": "770"}, {"alt": "661"}, {"range": "771", "text": "772"}, {"alt": "664"}, {"range": "773", "text": "774"}, {"alt": "667"}, {"range": "775", "text": "776"}, {"alt": "670"}, {"range": "777", "text": "778"}, {"alt": "661"}, {"range": "779", "text": "780"}, {"alt": "664"}, {"range": "781", "text": "782"}, {"alt": "667"}, {"range": "783", "text": "784"}, {"alt": "670"}, {"range": "785", "text": "786"}, {"range": "787", "text": "658"}, {"range": "788", "text": "660"}, {"range": "789", "text": "658"}, {"range": "790", "text": "660"}, {"range": "791", "text": "658"}, {"range": "792", "text": "660"}, {"alt": "661"}, {"range": "793", "text": "794"}, {"alt": "664"}, {"range": "795", "text": "796"}, {"alt": "667"}, {"range": "797", "text": "798"}, {"alt": "670"}, {"range": "799", "text": "800"}, "Update the dependencies array to be: [nextTestimonial, testimonials.length]", {"range": "801", "text": "802"}, {"alt": "661"}, {"range": "803", "text": "804"}, {"alt": "664"}, {"range": "805", "text": "806"}, {"alt": "667"}, {"range": "807", "text": "808"}, {"alt": "670"}, {"range": "809", "text": "810"}, {"alt": "661"}, {"range": "811", "text": "812"}, {"alt": "664"}, {"range": "813", "text": "814"}, {"alt": "667"}, {"range": "815", "text": "816"}, {"alt": "670"}, {"range": "817", "text": "818"}, {"alt": "719"}, {"range": "819", "text": "820"}, {"alt": "722"}, {"range": "821", "text": "822"}, {"alt": "725"}, {"range": "823", "text": "824"}, {"alt": "728"}, {"range": "825", "text": "826"}, {"alt": "719"}, {"range": "827", "text": "828"}, {"alt": "722"}, {"range": "829", "text": "830"}, {"alt": "725"}, {"range": "831", "text": "832"}, {"alt": "728"}, {"range": "833", "text": "834"}, "Update the dependencies array to be: [blendingValue, fifthColor, firstColor, fourthColor, gradientBackgroundEnd, gradientBackgroundStart, pointerColor, secondColor, size, thirdColor]", {"range": "835", "text": "836"}, "Update the dependencies array to be: [curX, curY, tgX, tgY]", {"range": "837", "text": "838"}, [134, 137], "unknown", [134, 137], "never", "&apos;", [1355, 1555], "\n                I&apos;m a passionate full-stack developer and UI/UX designer with over 3 years of experience \n                creating innovative digital solutions that make a real impact.\n              ", "&lsquo;", [1355, 1555], "\n                I&l<PERSON><PERSON><PERSON>;m a passionate full-stack developer and UI/UX designer with over 3 years of experience \n                creating innovative digital solutions that make a real impact.\n              ", "&#39;", [1355, 1555], "\n                I&#39;m a passionate full-stack developer and UI/UX designer with over 3 years of experience \n                creating innovative digital solutions that make a real impact.\n              ", "&rsquo;", [1355, 1555], "\n                I&rsq<PERSON>;m a passionate full-stack developer and UI/UX designer with over 3 years of experience \n                creating innovative digital solutions that make a real impact.\n              ", [2496, 2730], "\n                When I&apos;m not coding, you'll find me exploring new technologies, contributing to open-source \n                projects, or sharing knowledge with the developer community through blog posts and mentoring.\n              ", [2496, 2730], "\n                When I&lsquo;m not coding, you'll find me exploring new technologies, contributing to open-source \n                projects, or sharing knowledge with the developer community through blog posts and mentoring.\n              ", [2496, 2730], "\n                When I&#39;m not coding, you'll find me exploring new technologies, contributing to open-source \n                projects, or sharing knowledge with the developer community through blog posts and mentoring.\n              ", [2496, 2730], "\n                When I&rsquo;m not coding, you'll find me exploring new technologies, contributing to open-source \n                projects, or sharing knowledge with the developer community through blog posts and mentoring.\n              ", [2496, 2730], "\n                When I'm not coding, you&apos;ll find me exploring new technologies, contributing to open-source \n                projects, or sharing knowledge with the developer community through blog posts and mentoring.\n              ", [2496, 2730], "\n                When I'm not coding, you&lsquo;ll find me exploring new technologies, contributing to open-source \n                projects, or sharing knowledge with the developer community through blog posts and mentoring.\n              ", [2496, 2730], "\n                When I'm not coding, you&#39;ll find me exploring new technologies, contributing to open-source \n                projects, or sharing knowledge with the developer community through blog posts and mentoring.\n              ", [2496, 2730], "\n                When I'm not coding, you&rsquo;ll find me exploring new technologies, contributing to open-source \n                projects, or sharing knowledge with the developer community through blog posts and mentoring.\n              ", [3281, 3323], "\n                Let&apos;s Chat\n              ", [3281, 3323], "\n                Let&lsquo;s <PERSON>t\n              ", [3281, 3323], "\n                Let&#39;s <PERSON>t\n              ", [3281, 3323], "\n                Let&rsquo;s <PERSON>t\n              ", [5886, 6104], "\n              I&apos;m passionate about staying up-to-date with the latest technologies and best practices. \n              Currently exploring AI/ML integration in web applications and advanced React patterns.\n            ", [5886, 6104], "\n              I&lsquo;m passionate about staying up-to-date with the latest technologies and best practices. \n              Currently exploring AI/ML integration in web applications and advanced React patterns.\n            ", [5886, 6104], "\n              I&#39;m passionate about staying up-to-date with the latest technologies and best practices. \n              Currently exploring AI/ML integration in web applications and advanced React patterns.\n            ", [5886, 6104], "\n              I&rsquo;m passionate about staying up-to-date with the latest technologies and best practices. \n              Currently exploring AI/ML integration in web applications and advanced React patterns.\n            ", [1618, 1621], [1618, 1621], [4737, 4856], "\n                        Thank you for subscribing! You&apos;ll receive a confirmation email shortly.\n                      ", [4737, 4856], "\n                        Thank you for subscribing! You&lsquo;ll receive a confirmation email shortly.\n                      ", [4737, 4856], "\n                        Thank you for subscribing! You&#39;ll receive a confirmation email shortly.\n                      ", [4737, 4856], "\n                        Thank you for subscribing! You&rsquo;ll receive a confirmation email shortly.\n                      ", [4696, 4699], [4696, 4699], [604, 607], [604, 607], "&quot;", [6305, 6343], "\n              No commands found for &quot;", "&ldquo;", [6305, 6343], "\n              No commands found for &ldquo;", "&#34;", [6305, 6343], "\n              No commands found for &#34;", "&rdquo;", [6305, 6343], "\n              No commands found for &rdquo;", [6351, 6365], "&quot;\n            ", [6351, 6365], "&ldquo;\n            ", [6351, 6365], "&#34;\n            ", [6351, 6365], "&rdquo;\n            ", [3437, 3456], "Let&apos;s Work Together", [3437, 3456], "Let&lsquo;s Work Together", [3437, 3456], "Let&#39;s Work Together", [3437, 3456], "Let&rsquo;s Work Together", [3553, 3691], "\n            Have a project in mind? I&apos;d love to hear about it. \n            Let's discuss how we can bring your ideas to life.\n          ", [3553, 3691], "\n            Have a project in mind? I&lsquo;d love to hear about it. \n            Let's discuss how we can bring your ideas to life.\n          ", [3553, 3691], "\n            Have a project in mind? I&#39;d love to hear about it. \n            Let's discuss how we can bring your ideas to life.\n          ", [3553, 3691], "\n            Have a project in mind? I&rsquo;d love to hear about it. \n            Let's discuss how we can bring your ideas to life.\n          ", [3553, 3691], "\n            Have a project in mind? I'd love to hear about it. \n            Let&apos;s discuss how we can bring your ideas to life.\n          ", [3553, 3691], "\n            Have a project in mind? I'd love to hear about it. \n            Let&lsquo;s discuss how we can bring your ideas to life.\n          ", [3553, 3691], "\n            Have a project in mind? I'd love to hear about it. \n            Let&#39;s discuss how we can bring your ideas to life.\n          ", [3553, 3691], "\n            Have a project in mind? I'd love to hear about it. \n            Let&rsquo;s discuss how we can bring your ideas to life.\n          ", [4199, 4368], "\n                I&apos;m always open to discussing new opportunities, creative projects, \n                or potential collaborations. Feel free to reach out!\n              ", [4199, 4368], "\n                I&lsquo;m always open to discussing new opportunities, creative projects, \n                or potential collaborations. Feel free to reach out!\n              ", [4199, 4368], "\n                I&#39;m always open to discussing new opportunities, creative projects, \n                or potential collaborations. Feel free to reach out!\n              ", [4199, 4368], "\n                I&rsquo;m always open to discussing new opportunities, creative projects, \n                or potential collaborations. Feel free to reach out!\n              ", [8066, 8177], "\n                I&apos;m currently accepting new client work and interesting project collaborations.\n              ", [8066, 8177], "\n                I&lsquo;m currently accepting new client work and interesting project collaborations.\n              ", [8066, 8177], "\n                I&#39;m currently accepting new client work and interesting project collaborations.\n              ", [8066, 8177], "\n                I&rsquo;m currently accepting new client work and interesting project collaborations.\n              ", [2792, 2834], "\n                Hello, I&apos;m\n              ", [2792, 2834], "\n                Hello, I&lsquo;m\n              ", [2792, 2834], "\n                Hello, I&#39;m\n              ", [2792, 2834], "\n                Hello, I&rsquo;m\n              ", [1477, 1480], [1477, 1480], [1549, 1552], [1549, 1552], [550, 553], [550, 553], [5940, 6088], "\n              Let&apos;s discuss how I can help bring your ideas to life with modern, \n              scalable, and user-friendly solutions.\n            ", [5940, 6088], "\n              Let&lsquo;s discuss how I can help bring your ideas to life with modern, \n              scalable, and user-friendly solutions.\n            ", [5940, 6088], "\n              Let&#39;s discuss how I can help bring your ideas to life with modern, \n              scalable, and user-friendly solutions.\n            ", [5940, 6088], "\n              Let&rsquo;s discuss how I can help bring your ideas to life with modern, \n              scalable, and user-friendly solutions.\n            ", [1811, 1832], "[nextTestimonial, testimonials.length]", [4337, 4471], "\n            Don&apos;t just take my word for it. Here's what some of my clients have to say \n            about working with me.\n          ", [4337, 4471], "\n            Don&lsquo;t just take my word for it. Here's what some of my clients have to say \n            about working with me.\n          ", [4337, 4471], "\n            Don&#39;t just take my word for it. Here's what some of my clients have to say \n            about working with me.\n          ", [4337, 4471], "\n            Don&rsquo;t just take my word for it. Here's what some of my clients have to say \n            about working with me.\n          ", [4337, 4471], "\n            Don't just take my word for it. Here&apos;s what some of my clients have to say \n            about working with me.\n          ", [4337, 4471], "\n            Don't just take my word for it. Here&lsquo;s what some of my clients have to say \n            about working with me.\n          ", [4337, 4471], "\n            Don't just take my word for it. Here&#39;s what some of my clients have to say \n            about working with me.\n          ", [4337, 4471], "\n            Don't just take my word for it. Here&rsquo;s what some of my clients have to say \n            about working with me.\n          ", [5969, 5993], "\n                      &quot;", [5969, 5993], "\n                      &ldquo;", [5969, 5993], "\n                      &#34;", [5969, 5993], "\n                      &rdquo;", [6021, 6043], "&quot;\n                    ", [6021, 6043], "&ldquo;\n                    ", [6021, 6043], "&#34;\n                    ", [6021, 6043], "&rdquo;\n                    ", [1904, 1906], "[blending<PERSON><PERSON>ue, fifthColor, firstColor, fourthColor, gradientBackgroundEnd, gradientBackgroundStart, pointerColor, secondColor, size, thirdColor]", [2241, 2251], "[curX, curY, tgX, tgY]"]