"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9730],{26126:(e,t,r)=>{r.d(t,{E:()=>o});var a=r(95155);r(12115);var s=r(99708),i=r(74466),n=r(59434);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:r,asChild:i=!1,...o}=e,d=i?s.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(l({variant:r}),t),...o})}},47262:(e,t,r)=>{r.d(t,{S:()=>l});var a=r(95155);r(12115);var s=r(76981),i=r(5196),n=r(59434);function l(e){let{className:t,...r}=e;return(0,a.jsx)(s.bL,{"data-slot":"checkbox",className:(0,n.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...r,children:(0,a.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,a.jsx)(i.A,{className:"size-3.5"})})})}},59409:(e,t,r)=>{r.d(t,{bq:()=>u,eb:()=>h,gC:()=>p,l6:()=>d,yv:()=>c});var a=r(95155);r(12115);var s=r(38715),i=r(66474),n=r(5196),l=r(47863),o=r(59434);function d(e){let{...t}=e;return(0,a.jsx)(s.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,a.jsx)(s.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:r="default",children:n,...l}=e;return(0,a.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":r,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l,children:[n,(0,a.jsx)(s.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function p(e){let{className:t,children:r,position:i="popper",...n}=e;return(0,a.jsx)(s.ZL,{children:(0,a.jsxs)(s.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...n,children:[(0,a.jsx)(g,{}),(0,a.jsx)(s.LM,{className:(0,o.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:r}),(0,a.jsx)(x,{})]})})}function h(e){let{className:t,children:r,...i}=e;return(0,a.jsxs)(s.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...i,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(s.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),(0,a.jsx)(s.p4,{children:r})]})}function g(e){let{className:t,...r}=e;return(0,a.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(l.A,{className:"size-4"})})}function x(e){let{className:t,...r}=e;return(0,a.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(i.A,{className:"size-4"})})}},62154:(e,t,r)=>{r.d(t,{B:()=>h});var a=r(95155),s=r(12115),i=r(30285),n=r(62523),l=r(85057),o=r(54416),d=r(27213),c=r(29869),u=r(66766),p=r(56671);function h(e){let{value:t,onChange:r,onRemove:h,disabled:g,label:x="Upload Image",className:v=""}=e,[m,f]=(0,s.useState)(!1),b=(0,s.useRef)(null),j=async e=>{var t;let a=null==(t=e.target.files)?void 0:t[0];if(a){f(!0);try{let e=new FormData;e.append("file",a);let t=await fetch("/api/upload",{method:"POST",body:e});if(!t.ok)throw Error("Upload failed");let s=await t.json();r(s.url)}catch(e){console.error("Error uploading image:",e),p.oR.error("Failed to upload image. Please try again.")}finally{f(!1)}}};return(0,a.jsxs)("div",{className:"space-y-2 ".concat(v),children:[(0,a.jsx)(l.J,{children:x}),t?(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"relative w-full h-48 rounded-lg overflow-hidden border",children:(0,a.jsx)(u.default,{src:t,alt:"Uploaded image",fill:!0,className:"object-cover"})}),(0,a.jsx)(i.$,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2",onClick:h,disabled:g,children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})]}):(0,a.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(d.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>{var e;null==(e=b.current)||e.click()},disabled:g||m,children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4"}),m?"Uploading...":"Choose Image"]})}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"PNG, JPG, GIF up to 10MB"})]})}),(0,a.jsx)(n.p,{ref:b,type:"file",accept:"image/*",onChange:j,className:"hidden",disabled:g||m})]})}},62523:(e,t,r)=>{r.d(t,{p:()=>i});var a=r(95155);r(12115);var s=r(59434);function i(e){let{className:t,type:r,...i}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},85057:(e,t,r)=>{r.d(t,{J:()=>n});var a=r(95155);r(12115);var s=r(40968),i=r(59434);function n(e){let{className:t,...r}=e;return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},88539:(e,t,r)=>{r.d(t,{T:()=>i});var a=r(95155);r(12115);var s=r(59434);function i(e){let{className:t,...r}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...r})}},89730:(e,t,r)=>{r.d(t,{ProjectForm:()=>f});var a=r(95155),s=r(12115),i=r(35695),n=r(30285),l=r(62523),o=r(85057),d=r(88539),c=r(47262),u=r(59409),p=r(66695),h=r(62154),g=r(26126),x=r(54416),v=r(56671);let m=["Full Stack","Frontend","Backend","Mobile","AI/ML","DevOps","Design"];function f(e){let{projectId:t,onSuccess:r}=e,f=(0,i.useRouter)(),[b,j]=(0,s.useState)(!1),[y,w]=(0,s.useState)(""),[N,k]=(0,s.useState)({title:"",description:"",longDescription:"",image:"",category:"",technologies:[],liveUrl:"",githubUrl:"",featured:!1,published:!0,order:0});(0,s.useEffect)(()=>{t&&C()},[t]);let C=async()=>{try{let e=await fetch("/api/projects/".concat(t));if(e.ok){let t=await e.json();k({title:t.title||"",description:t.description||"",longDescription:t.longDescription||"",image:t.image||"",category:t.category||"",technologies:t.technologies||[],liveUrl:t.liveUrl||"",githubUrl:t.githubUrl||"",featured:t.featured||!1,published:t.published||!0,order:t.order||0})}}catch(e){console.error("Error fetching project:",e)}},U=async e=>{e.preventDefault(),j(!0);try{if((await fetch(t?"/api/projects/".concat(t):"/api/projects",{method:t?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(N)})).ok)null==r||r(),t||f.push("/dashboard/projects");else throw Error("Failed to save project")}catch(e){console.error("Error saving project:",e),v.oR.error("Failed to save project. Please try again.")}finally{j(!1)}},z=()=>{y.trim()&&!N.technologies.includes(y.trim())&&(k(e=>({...e,technologies:[...e.technologies,y.trim()]})),w(""))},F=e=>{k(t=>({...t,technologies:t.technologies.filter(t=>t!==e)}))};return(0,a.jsxs)("form",{onSubmit:U,className:"space-y-6",children:[(0,a.jsxs)(p.Zp,{children:[(0,a.jsxs)(p.aR,{children:[(0,a.jsx)(p.ZB,{children:"Basic Information"}),(0,a.jsx)(p.BT,{children:"Enter the basic details about your project"})]}),(0,a.jsxs)(p.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{htmlFor:"title",children:"Project Title *"}),(0,a.jsx)(l.p,{id:"title",value:N.title,onChange:e=>k(t=>({...t,title:e.target.value})),required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{htmlFor:"description",children:"Short Description *"}),(0,a.jsx)(d.T,{id:"description",value:N.description,onChange:e=>k(t=>({...t,description:e.target.value})),rows:3,required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{htmlFor:"longDescription",children:"Detailed Description"}),(0,a.jsx)(d.T,{id:"longDescription",value:N.longDescription,onChange:e=>k(t=>({...t,longDescription:e.target.value})),rows:5})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{htmlFor:"category",children:"Category *"}),(0,a.jsxs)(u.l6,{value:N.category,onValueChange:e=>k(t=>({...t,category:e})),children:[(0,a.jsx)(u.bq,{children:(0,a.jsx)(u.yv,{placeholder:"Select a category"})}),(0,a.jsx)(u.gC,{children:m.map(e=>(0,a.jsx)(u.eb,{value:e,children:e},e))})]})]})]})]}),(0,a.jsxs)(p.Zp,{children:[(0,a.jsxs)(p.aR,{children:[(0,a.jsx)(p.ZB,{children:"Project Image"}),(0,a.jsx)(p.BT,{children:"Upload a screenshot or preview image of your project"})]}),(0,a.jsx)(p.Wu,{children:(0,a.jsx)(h.B,{value:N.image,onChange:e=>k(t=>({...t,image:e})),onRemove:()=>k(e=>({...e,image:""})),disabled:b})})]}),(0,a.jsxs)(p.Zp,{children:[(0,a.jsxs)(p.aR,{children:[(0,a.jsx)(p.ZB,{children:"Technologies"}),(0,a.jsx)(p.BT,{children:"Add the technologies and tools used in this project"})]}),(0,a.jsxs)(p.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(l.p,{placeholder:"Enter technology (e.g., React, Node.js)",value:y,onChange:e=>w(e.target.value),onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),z())}}),(0,a.jsx)(n.$,{type:"button",onClick:z,children:"Add"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:N.technologies.map(e=>(0,a.jsxs)(g.E,{variant:"secondary",className:"flex items-center gap-1",children:[e,(0,a.jsx)(x.A,{className:"h-3 w-3 cursor-pointer",onClick:()=>F(e)})]},e))})]})]}),(0,a.jsxs)(p.Zp,{children:[(0,a.jsxs)(p.aR,{children:[(0,a.jsx)(p.ZB,{children:"Links"}),(0,a.jsx)(p.BT,{children:"Add links to the live project and source code"})]}),(0,a.jsxs)(p.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{htmlFor:"liveUrl",children:"Live URL"}),(0,a.jsx)(l.p,{id:"liveUrl",type:"url",placeholder:"https://example.com",value:N.liveUrl,onChange:e=>k(t=>({...t,liveUrl:e.target.value}))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{htmlFor:"githubUrl",children:"GitHub URL"}),(0,a.jsx)(l.p,{id:"githubUrl",type:"url",placeholder:"https://github.com/username/repo",value:N.githubUrl,onChange:e=>k(t=>({...t,githubUrl:e.target.value}))})]})]})]}),(0,a.jsxs)(p.Zp,{children:[(0,a.jsxs)(p.aR,{children:[(0,a.jsx)(p.ZB,{children:"Settings"}),(0,a.jsx)(p.BT,{children:"Configure project visibility and display options"})]}),(0,a.jsxs)(p.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c.S,{id:"featured",checked:N.featured,onCheckedChange:e=>k(t=>({...t,featured:e}))}),(0,a.jsx)(o.J,{htmlFor:"featured",children:"Featured Project"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c.S,{id:"published",checked:N.published,onCheckedChange:e=>k(t=>({...t,published:e}))}),(0,a.jsx)(o.J,{htmlFor:"published",children:"Published"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{htmlFor:"order",children:"Display Order"}),(0,a.jsx)(l.p,{id:"order",type:"number",min:"0",value:N.order,onChange:e=>k(t=>({...t,order:parseInt(e.target.value)||0}))})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(n.$,{type:"submit",disabled:b,children:b?"Saving...":t?"Update Project":"Create Project"}),(0,a.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>f.back(),children:"Cancel"})]})]})}}}]);