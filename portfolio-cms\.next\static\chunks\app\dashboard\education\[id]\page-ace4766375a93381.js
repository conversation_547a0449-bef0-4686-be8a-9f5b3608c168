(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9034],{23457:(e,t,i)=>{Promise.resolve().then(i.bind(i,99956))},31949:(e,t,i)=>{"use strict";i.d(t,{d:()=>w});var r=i(95155),a=i(12115),s=i(85185),n=i(6101),d=i(46081),l=i(5845),o=i(45503),c=i(11275),u=i(63655),h="Switch",[p,g]=(0,d.A)(h),[x,f]=p(h),v=a.forwardRef((e,t)=>{let{__scopeSwitch:i,name:d,checked:o,defaultChecked:c,required:p,disabled:g,value:f="on",onCheckedChange:v,form:b,...m}=e,[k,w]=a.useState(null),N=(0,n.s)(t,e=>w(e)),C=a.useRef(!1),E=!k||b||!!k.closest("form"),[A,F]=(0,l.i)({prop:o,defaultProp:null!=c&&c,onChange:v,caller:h});return(0,r.jsxs)(x,{scope:i,checked:A,disabled:g,children:[(0,r.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":A,"aria-required":p,"data-state":y(A),"data-disabled":g?"":void 0,disabled:g,value:f,...m,ref:N,onClick:(0,s.m)(e.onClick,e=>{F(e=>!e),E&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),E&&(0,r.jsx)(j,{control:k,bubbles:!C.current,name:d,value:f,checked:A,required:p,disabled:g,form:b,style:{transform:"translateX(-100%)"}})]})});v.displayName=h;var b="SwitchThumb",m=a.forwardRef((e,t)=>{let{__scopeSwitch:i,...a}=e,s=f(b,i);return(0,r.jsx)(u.sG.span,{"data-state":y(s.checked),"data-disabled":s.disabled?"":void 0,...a,ref:t})});m.displayName=b;var j=a.forwardRef((e,t)=>{let{__scopeSwitch:i,control:s,checked:d,bubbles:l=!0,...u}=e,h=a.useRef(null),p=(0,n.s)(h,t),g=(0,o.Z)(d),x=(0,c.X)(s);return a.useEffect(()=>{let e=h.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(g!==d&&t){let i=new Event("click",{bubbles:l});t.call(e,d),e.dispatchEvent(i)}},[g,d,l]),(0,r.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:d,...u,tabIndex:-1,ref:p,style:{...u.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function y(e){return e?"checked":"unchecked"}j.displayName="SwitchBubbleInput";var k=i(59434);let w=a.forwardRef((e,t)=>{let{className:i,...a}=e;return(0,r.jsx)(v,{className:(0,k.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",i),...a,ref:t,children:(0,r.jsx)(m,{className:(0,k.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});w.displayName=v.displayName},35169:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40968:(e,t,i)=>{"use strict";i.d(t,{b:()=>d});var r=i(12115),a=i(63655),s=i(95155),n=r.forwardRef((e,t)=>(0,s.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{var i;t.target.closest("button, input, select, textarea")||(null==(i=e.onMouseDown)||i.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var d=n},45503:(e,t,i)=>{"use strict";i.d(t,{Z:()=>a});var r=i(12115);function a(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},62523:(e,t,i)=>{"use strict";i.d(t,{p:()=>s});var r=i(95155);i(12115);var a=i(59434);function s(e){let{className:t,type:i,...s}=e;return(0,r.jsx)("input",{type:i,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},62525:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},84616:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85057:(e,t,i)=>{"use strict";i.d(t,{J:()=>n});var r=i(95155);i(12115);var a=i(40968),s=i(59434);function n(e){let{className:t,...i}=e;return(0,r.jsx)(a.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...i})}},88539:(e,t,i)=>{"use strict";i.d(t,{T:()=>s});var r=i(95155);i(12115);var a=i(59434);function s(e){let{className:t,...i}=e;return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...i})}},99956:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>v});var r=i(95155),a=i(12115),s=i(35695),n=i(83930),d=i(30285),l=i(66695),o=i(62523),c=i(85057),u=i(88539),h=i(31949),p=i(35169),g=i(62525),x=i(84616),f=i(56671);function v(){let e=(0,s.useRouter)(),t=(0,s.useParams)(),[i,v]=(0,a.useState)(!1),[b,m]=(0,a.useState)(!0),[j,y]=(0,a.useState)({degree:"",institution:"",location:"",period:"",grade:"",description:"",highlights:[""],order:0,published:!0});(0,a.useEffect)(()=>{t.id&&k()},[t.id]);let k=async()=>{try{let e=await fetch("/api/education/".concat(t.id));if(!e.ok)throw Error("Failed to fetch education");let i=await e.json();y({degree:i.degree,institution:i.institution,location:i.location,period:i.period,grade:i.grade||"",description:i.description,highlights:i.highlights.length>0?i.highlights:[""],order:i.order,published:i.published})}catch(t){console.error("Error fetching education:",t),f.oR.error("Failed to fetch education"),e.push("/dashboard/education")}finally{m(!1)}},w=(e,t)=>{y(i=>({...i,[e]:t}))},N=(e,t)=>{y(i=>({...i,highlights:i.highlights.map((i,r)=>r===e?t:i)}))},C=e=>{y(t=>({...t,highlights:t.highlights.filter((t,i)=>i!==e)}))},E=async i=>{i.preventDefault(),v(!0);try{let i={...j,highlights:j.highlights.filter(e=>""!==e.trim())};if(!(await fetch("/api/education/".concat(t.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)})).ok)throw Error("Failed to update education");f.oR.success("Education updated successfully"),e.push("/dashboard/education")}catch(e){console.error("Error updating education:",e),f.oR.error("Failed to update education")}finally{v(!1)}};return b?(0,r.jsx)(n.DashboardLayout,{children:(0,r.jsx)("div",{className:"container mx-auto py-8",children:(0,r.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-300 rounded w-64"}),(0,r.jsx)("div",{className:"h-96 bg-gray-300 rounded"})]})})}):(0,r.jsx)(n.DashboardLayout,{children:(0,r.jsxs)("div",{className:"container mx-auto py-8",children:[(0,r.jsxs)("div",{className:"flex items-center mb-8",children:[(0,r.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>e.back(),className:"mr-4",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Edit Education"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Update education entry"})]})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{children:"Education Details"})}),(0,r.jsx)(l.Wu,{children:(0,r.jsxs)("form",{onSubmit:E,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"degree",children:"Degree *"}),(0,r.jsx)(o.p,{id:"degree",value:j.degree,onChange:e=>w("degree",e.target.value),required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"institution",children:"Institution *"}),(0,r.jsx)(o.p,{id:"institution",value:j.institution,onChange:e=>w("institution",e.target.value),required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"location",children:"Location *"}),(0,r.jsx)(o.p,{id:"location",value:j.location,onChange:e=>w("location",e.target.value),required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"period",children:"Period *"}),(0,r.jsx)(o.p,{id:"period",value:j.period,onChange:e=>w("period",e.target.value),placeholder:"e.g., 2020 - 2024",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"grade",children:"Grade/GPA"}),(0,r.jsx)(o.p,{id:"grade",value:j.grade,onChange:e=>w("grade",e.target.value),placeholder:"e.g., First Class with Distinction (8.5/10 CGPA)"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"order",children:"Display Order"}),(0,r.jsx)(o.p,{id:"order",type:"number",value:j.order,onChange:e=>w("order",parseInt(e.target.value)||0)})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"description",children:"Description *"}),(0,r.jsx)(u.T,{id:"description",value:j.description,onChange:e=>w("description",e.target.value),rows:4,required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{children:"Key Highlights"}),j.highlights.map((e,t)=>(0,r.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,r.jsx)(o.p,{value:e,onChange:e=>N(t,e.target.value),placeholder:"Enter a highlight"}),(0,r.jsx)(d.$,{type:"button",variant:"outline",size:"sm",onClick:()=>C(t),children:(0,r.jsx)(g.A,{className:"h-4 w-4"})})]},t)),(0,r.jsxs)(d.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{y(e=>({...e,highlights:[...e.highlights,""]}))},className:"mt-2",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Add Highlight"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(h.d,{id:"published",checked:j.published,onCheckedChange:e=>w("published",e)}),(0,r.jsx)(c.J,{htmlFor:"published",children:"Published"})]}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(d.$,{type:"submit",disabled:i,children:i?"Updating...":"Update Education"}),(0,r.jsx)(d.$,{type:"button",variant:"outline",onClick:()=>e.back(),children:"Cancel"})]})]})})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4854,2108,8858,9368,6671,9008,8441,1684,7358],()=>t(23457)),_N_E=e.O()}]);