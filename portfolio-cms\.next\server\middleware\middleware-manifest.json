{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/:path*{(\\\\.json)}?", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GmR490q78K+bBIm3nmrLlRQO8rHHbnjcdhaBgWmTT64=", "__NEXT_PREVIEW_MODE_ID": "c0c92e1aadaa5afefcc404171dd548e6", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "10e0059bbb077ccb025c8ea48b9c148bbc3c664e13e18a007ba6d50cac3e0e2f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e6f328473747fc39363a0ca8298f1864f928147731eac55db931bb4a48aac66f"}}}, "instrumentation": null, "functions": {}}