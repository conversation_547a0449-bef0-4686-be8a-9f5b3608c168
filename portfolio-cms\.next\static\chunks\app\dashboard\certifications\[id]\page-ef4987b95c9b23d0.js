(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4861],{31949:(e,t,r)=>{"use strict";r.d(t,{d:()=>k});var i=r(95155),a=r(12115),s=r(85185),n=r(6101),d=r(46081),l=r(5845),o=r(45503),c=r(11275),u=r(63655),p="Switch",[h,f]=(0,d.A)(p),[b,x]=h(p),v=a.forwardRef((e,t)=>{let{__scopeSwitch:r,name:d,checked:o,defaultChecked:c,required:h,disabled:f,value:x="on",onCheckedChange:v,form:m,...g}=e,[w,k]=a.useState(null),C=(0,n.s)(t,e=>k(e)),N=a.useRef(!1),D=!w||m||!!w.closest("form"),[E,F]=(0,l.i)({prop:o,defaultProp:null!=c&&c,onChange:v,caller:p});return(0,i.jsxs)(b,{scope:r,checked:E,disabled:f,children:[(0,i.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":E,"aria-required":h,"data-state":y(E),"data-disabled":f?"":void 0,disabled:f,value:x,...g,ref:C,onClick:(0,s.m)(e.onClick,e=>{F(e=>!e),D&&(N.current=e.isPropagationStopped(),N.current||e.stopPropagation())})}),D&&(0,i.jsx)(j,{control:w,bubbles:!N.current,name:d,value:x,checked:E,required:h,disabled:f,form:m,style:{transform:"translateX(-100%)"}})]})});v.displayName=p;var m="SwitchThumb",g=a.forwardRef((e,t)=>{let{__scopeSwitch:r,...a}=e,s=x(m,r);return(0,i.jsx)(u.sG.span,{"data-state":y(s.checked),"data-disabled":s.disabled?"":void 0,...a,ref:t})});g.displayName=m;var j=a.forwardRef((e,t)=>{let{__scopeSwitch:r,control:s,checked:d,bubbles:l=!0,...u}=e,p=a.useRef(null),h=(0,n.s)(p,t),f=(0,o.Z)(d),b=(0,c.X)(s);return a.useEffect(()=>{let e=p.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(f!==d&&t){let r=new Event("click",{bubbles:l});t.call(e,d),e.dispatchEvent(r)}},[f,d,l]),(0,i.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:d,...u,tabIndex:-1,ref:h,style:{...u.style,...b,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function y(e){return e?"checked":"unchecked"}j.displayName="SwitchBubbleInput";var w=r(59434);let k=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,i.jsx)(v,{className:(0,w.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",r),...a,ref:t,children:(0,i.jsx)(g,{className:(0,w.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});k.displayName=v.displayName},35169:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40968:(e,t,r)=>{"use strict";r.d(t,{b:()=>d});var i=r(12115),a=r(63655),s=r(95155),n=i.forwardRef((e,t)=>(0,s.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var d=n},45503:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var i=r(12115);function a(e){let t=i.useRef({value:e,previous:e});return i.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},52907:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var i=r(95155),a=r(12115),s=r(35695),n=r(83930),d=r(30285),l=r(66695),o=r(62523),c=r(85057),u=r(88539),p=r(31949),h=r(35169),f=r(56671);function b(){let e=(0,s.useRouter)(),t=(0,s.useParams)(),[r,b]=(0,a.useState)(!1),[x,v]=(0,a.useState)(!0),[m,g]=(0,a.useState)({title:"",issuer:"",date:"",credentialId:"",emoji:"",description:"",order:0,published:!0});(0,a.useEffect)(()=>{t.id&&j()},[t.id]);let j=async()=>{try{let e=await fetch("/api/certifications/".concat(t.id));if(!e.ok)throw Error("Failed to fetch certification");let r=await e.json();g({title:r.title,issuer:r.issuer,date:r.date,credentialId:r.credentialId||"",emoji:r.emoji||"",description:r.description,order:r.order,published:r.published})}catch(t){console.error("Error fetching certification:",t),f.oR.error("Failed to fetch certification"),e.push("/dashboard/certifications")}finally{v(!1)}},y=(e,t)=>{g(r=>({...r,[e]:t}))},w=async r=>{r.preventDefault(),b(!0);try{if(!(await fetch("/api/certifications/".concat(t.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(m)})).ok)throw Error("Failed to update certification");f.oR.success("Certification updated successfully"),e.push("/dashboard/certifications")}catch(e){console.error("Error updating certification:",e),f.oR.error("Failed to update certification")}finally{b(!1)}};return x?(0,i.jsx)(n.DashboardLayout,{children:(0,i.jsx)("div",{className:"container mx-auto py-8",children:(0,i.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,i.jsx)("div",{className:"h-8 bg-gray-300 rounded w-64"}),(0,i.jsx)("div",{className:"h-96 bg-gray-300 rounded"})]})})}):(0,i.jsx)(n.DashboardLayout,{children:(0,i.jsxs)("div",{className:"container mx-auto py-8",children:[(0,i.jsxs)("div",{className:"flex items-center mb-8",children:[(0,i.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>e.back(),className:"mr-4",children:[(0,i.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold",children:"Edit Certification"}),(0,i.jsx)("p",{className:"text-muted-foreground",children:"Update certification entry"})]})]}),(0,i.jsxs)(l.Zp,{children:[(0,i.jsx)(l.aR,{children:(0,i.jsx)(l.ZB,{children:"Certification Details"})}),(0,i.jsx)(l.Wu,{children:(0,i.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{htmlFor:"title",children:"Title *"}),(0,i.jsx)(o.p,{id:"title",value:m.title,onChange:e=>y("title",e.target.value),placeholder:"e.g., AWS Certified Solutions Architect",required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{htmlFor:"issuer",children:"Issuer *"}),(0,i.jsx)(o.p,{id:"issuer",value:m.issuer,onChange:e=>y("issuer",e.target.value),placeholder:"e.g., Amazon Web Services",required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{htmlFor:"date",children:"Date *"}),(0,i.jsx)(o.p,{id:"date",value:m.date,onChange:e=>y("date",e.target.value),placeholder:"e.g., March 2024",required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{htmlFor:"credentialId",children:"Credential ID"}),(0,i.jsx)(o.p,{id:"credentialId",value:m.credentialId,onChange:e=>y("credentialId",e.target.value),placeholder:"e.g., ABC123DEF456"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{htmlFor:"emoji",children:"Emoji"}),(0,i.jsx)(o.p,{id:"emoji",value:m.emoji,onChange:e=>y("emoji",e.target.value),placeholder:"e.g., \uD83C\uDFC6",maxLength:2})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{htmlFor:"order",children:"Display Order"}),(0,i.jsx)(o.p,{id:"order",type:"number",value:m.order,onChange:e=>y("order",parseInt(e.target.value)||0)})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{htmlFor:"description",children:"Description *"}),(0,i.jsx)(u.T,{id:"description",value:m.description,onChange:e=>y("description",e.target.value),rows:4,placeholder:"Describe what this certification covers and its significance...",required:!0})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(p.d,{id:"published",checked:m.published,onCheckedChange:e=>y("published",e)}),(0,i.jsx)(c.J,{htmlFor:"published",children:"Published"})]}),(0,i.jsxs)("div",{className:"flex gap-4",children:[(0,i.jsx)(d.$,{type:"submit",disabled:r,children:r?"Updating...":"Update Certification"}),(0,i.jsx)(d.$,{type:"button",variant:"outline",onClick:()=>e.back(),children:"Cancel"})]})]})})]})]})})}},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>s});var i=r(95155);r(12115);var a=r(59434);function s(e){let{className:t,type:r,...s}=e;return(0,i.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},85057:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var i=r(95155);r(12115);var a=r(40968),s=r(59434);function n(e){let{className:t,...r}=e;return(0,i.jsx)(a.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},86822:(e,t,r)=>{Promise.resolve().then(r.bind(r,52907))},88539:(e,t,r)=>{"use strict";r.d(t,{T:()=>s});var i=r(95155);r(12115);var a=r(59434);function s(e){let{className:t,...r}=e;return(0,i.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...r})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4854,2108,8858,9368,6671,9008,8441,1684,7358],()=>t(86822)),_N_E=e.O()}]);