(()=>{var e={};e.id=8778,e.ids=[8778],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var s=r(13581),o=r(16467),i=r(94747),a=r(85663);let n={adapter:(0,o.y)(i.z),providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=await i.z.user.findUnique({where:{email:e.email}});return t&&await a.Ay.compare(e.password,t.password)?{id:t.id,email:t.email,name:t.name,role:t.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},27746:(e,t,r)=>{"use strict";r.d(t,{JB:()=>n,gx:()=>a});var s=r(32190);let o=["http://localhost:3000","http://localhost:3001","http://localhost:3002","http://localhost:3003","http://127.0.0.1:3000","http://127.0.0.1:3001","http://127.0.0.1:3002","http://127.0.0.1:3003"];function i(e){return{"Access-Control-Allow-Origin":e&&o.includes(e)?e:"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-Requested-With","Access-Control-Allow-Credentials":"true","Access-Control-Max-Age":"86400"}}function a(e,t){return Object.entries(i(t)).forEach(([t,r])=>{e.headers.set(t,r)}),e}function n(e){return new s.NextResponse(null,{status:200,headers:i(e)})}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},78949:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>b,routeModule:()=>x,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>w});var s={};r.r(s),r.d(s,{GET:()=>d,OPTIONS:()=>g,POST:()=>h});var o=r(96559),i=r(48088),a=r(37719),n=r(32190),l=r(19854),u=r(12909),c=r(94747),p=r(27746);async function d(e){try{let t=e.headers.get("origin"),r=await c.z.blogPost.findMany({include:{author:{select:{id:!0,name:!0,email:!0}}},orderBy:{createdAt:"desc"}});return(0,p.gx)(n.NextResponse.json(r),t)}catch(r){console.error("Error fetching blog posts:",r);let t=e.headers.get("origin");return(0,p.gx)(n.NextResponse.json({error:"Failed to fetch blog posts"},{status:500}),t)}}async function h(e){try{let t=await (0,l.getServerSession)(u.N);if(!t?.user)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{title:r,slug:s,excerpt:o,content:i,image:a,category:d,tags:h,published:g,featured:x,readTime:m}=await e.json(),w=s||r.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/(^-|-$)/g,"");await c.z.blogPost.findUnique({where:{slug:w}})&&(w=`${w}-${Date.now()}`);let f=m;if(!f||f<=0){let e=i.split(/\s+/).length;f=Math.ceil(e/250)}let b=await c.z.blogPost.create({data:{title:r,slug:w,excerpt:o,content:i,image:a,category:d,tags:h,published:g||!1,featured:x||!1,readTime:f,publishedAt:g?new Date:null,authorId:t.user.id},include:{author:{select:{id:!0,name:!0,email:!0}}}});return(0,p.gx)(n.NextResponse.json(b,{status:201}))}catch(e){return console.error("Error creating blog post:",e),(0,p.gx)(n.NextResponse.json({error:"Failed to create blog post"},{status:500}))}}async function g(e){let t=e.headers.get("origin");return(0,p.JB)(t)}let x=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/blog/route",pathname:"/api/blog",filename:"route",bundlePath:"app/api/blog/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\blog\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:w,serverHooks:f}=x;function b(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:w})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},94747:(e,t,r)=>{"use strict";r.d(t,{z:()=>o});let s=require("@prisma/client"),o=globalThis.prisma??new s.PrismaClient},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,3542,2190],()=>r(78949));module.exports=s})();