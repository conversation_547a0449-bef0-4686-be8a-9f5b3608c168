(()=>{var e={};e.id=5105,e.ids=[5105],e.modules={16:(e,s,r)=>{Promise.resolve().then(r.bind(r,58061))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14278:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>l});var t=r(65239),i=r(48088),a=r(88170),o=r.n(a),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(s,d);let l={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,80559)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41872:(e,s,r)=>{Promise.resolve().then(r.bind(r,80559))},58061:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>u});var t=r(60687),i=r(43210),a=r(44493),o=r(96834),n=r(62280),d=r(18179),l=r(10022),c=r(57800),p=r(58887);function u(){let[e,s]=(0,i.useState)({projects:0,blogPosts:0,services:0,testimonials:0}),[r,u]=(0,i.useState)(!0),x=[{title:"Projects",value:e.projects,description:"Total portfolio projects",icon:d.A,color:"text-blue-600"},{title:"Blog Posts",value:e.blogPosts,description:"Published articles",icon:l.A,color:"text-green-600"},{title:"Services",value:e.services,description:"Available services",icon:c.A,color:"text-purple-600"},{title:"Testimonials",value:e.testimonials,description:"Client reviews",icon:p.A,color:"text-orange-600"}];return(0,t.jsx)(n.DashboardLayout,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Dashboard"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Welcome to your portfolio content management system"})]}),(0,t.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:x.map(e=>(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(a.ZB,{className:"text-sm font-medium",children:e.title}),(0,t.jsx)(e.icon,{className:`h-4 w-4 ${e.color}`})]}),(0,t.jsxs)(a.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:r?"...":e.value}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]})]},e.title))}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsx)(a.ZB,{children:"Quick Actions"}),(0,t.jsx)(a.BT,{children:"Common tasks to manage your portfolio content"})]}),(0,t.jsxs)(a.Wu,{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:"Add New Project"}),(0,t.jsx)(o.E,{variant:"secondary",children:"Create"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:"Write Blog Post"}),(0,t.jsx)(o.E,{variant:"secondary",children:"Write"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:"Update Services"}),(0,t.jsx)(o.E,{variant:"secondary",children:"Edit"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:"Manage Testimonials"}),(0,t.jsx)(o.E,{variant:"secondary",children:"Review"})]})]})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsx)(a.ZB,{children:"Recent Activity"}),(0,t.jsx)(a.BT,{children:"Latest changes to your portfolio content"})]}),(0,t.jsx)(a.Wu,{children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center text-sm",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mr-2"}),"Database initialized successfully"]}),(0,t.jsxs)("div",{className:"flex items-center text-sm",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full mr-2"}),"Sample data seeded"]}),(0,t.jsxs)("div",{className:"flex items-center text-sm",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full mr-2"}),"CMS dashboard ready"]})]})})]})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},80559:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\portfolio\\\\portfolio-cms\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\page.tsx","default")},96834:(e,s,r)=>{"use strict";r.d(s,{E:()=>d});var t=r(60687);r(43210);var i=r(8730),a=r(24224),o=r(4780);let n=(0,a.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:s,asChild:r=!1,...a}){let d=r?i.DX:"span";return(0,t.jsx)(d,{"data-slot":"badge",className:(0,o.cn)(n({variant:s}),e),...a})}}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4243,3310,1658,8580,4258,3868,6929],()=>r(14278));module.exports=t})();