(()=>{var e={};e.id=64,e.ids=[64],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>n});var s=t(13581),o=t(16467),i=t(94747),a=t(85663);let n={adapter:(0,o.y)(i.z),providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await i.z.user.findUnique({where:{email:e.email}});return r&&await a.Ay.compare(e.password,r.password)?{id:r.id,email:r.email,name:r.name,role:r.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},25279:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>m,serverHooks:()=>j,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>d,OPTIONS:()=>x,POST:()=>h});var o=t(96559),i=t(48088),a=t(37719),n=t(32190),u=t(19854),c=t(12909),p=t(94747),l=t(27746);async function d(){try{let e=await p.z.project.findMany({include:{author:{select:{id:!0,name:!0,email:!0}}},orderBy:{order:"asc"}});return(0,l.gx)(n.NextResponse.json(e))}catch(e){return console.error("Error fetching projects:",e),(0,l.gx)(n.NextResponse.json({error:"Failed to fetch projects"},{status:500}))}}async function h(e){try{let r=await (0,u.getServerSession)(c.N);if(!r?.user)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{title:t,description:s,longDescription:o,image:i,category:a,technologies:d,liveUrl:h,githubUrl:x,featured:m,published:w,order:g}=await e.json(),j=await p.z.project.create({data:{title:t,description:s,longDescription:o,image:i,category:a,technologies:d,liveUrl:h,githubUrl:x,featured:m||!1,published:void 0===w||w,order:g||0,authorId:r.user.id},include:{author:{select:{id:!0,name:!0,email:!0}}}});return(0,l.gx)(n.NextResponse.json(j,{status:201}))}catch(e){return console.error("Error creating project:",e),(0,l.gx)(n.NextResponse.json({error:"Failed to create project"},{status:500}))}}async function x(){return(0,l.JB)()}let m=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/projects/route",pathname:"/api/projects",filename:"route",bundlePath:"app/api/projects/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\projects\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:g,serverHooks:j}=m;function f(){return(0,a.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:g})}},27746:(e,r,t)=>{"use strict";t.d(r,{JB:()=>n,gx:()=>a});var s=t(32190);let o=["http://localhost:3000","http://localhost:3001","http://localhost:3002","http://localhost:3003","http://127.0.0.1:3000","http://127.0.0.1:3001","http://127.0.0.1:3002","http://127.0.0.1:3003"];function i(e){return{"Access-Control-Allow-Origin":e&&o.includes(e)?e:"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-Requested-With","Access-Control-Allow-Credentials":"true","Access-Control-Max-Age":"86400"}}function a(e,r){return Object.entries(i(r)).forEach(([r,t])=>{e.headers.set(r,t)}),e}function n(e){return new s.NextResponse(null,{status:200,headers:i(e)})}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},94747:(e,r,t)=>{"use strict";t.d(r,{z:()=>o});let s=require("@prisma/client"),o=globalThis.prisma??new s.PrismaClient},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,3542,2190],()=>t(25279));module.exports=s})();