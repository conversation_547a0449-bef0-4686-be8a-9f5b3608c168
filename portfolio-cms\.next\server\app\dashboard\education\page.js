(()=>{var e={};e.id=5840,e.ids=[5840],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5226:(e,t,s)=>{Promise.resolve().then(s.bind(s,84670))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41756:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var r=s(60687),a=s(43210),i=s(16189),d=s(62280),o=s(29523),n=s(44493),c=s(96834),l=s(96474),u=s(27351),h=s(97992),p=s(40228),m=s(63143),x=s(88233),v=s(52581);function f(){let e=(0,i.useRouter)(),[t,s]=(0,a.useState)([]),[f,b]=(0,a.useState)(!0),y=async e=>{(0,v.oR)("Are you sure you want to delete this education record?",{action:{label:"Delete",onClick:async()=>{try{if(!(await fetch(`/api/education/${e}`,{method:"DELETE"})).ok)throw Error("Failed to delete education");s(t.filter(t=>t.id!==e)),v.oR.success("Education record deleted successfully")}catch(e){console.error("Error deleting education:",e),v.oR.error("Failed to delete education")}}},cancel:{label:"Cancel",onClick:()=>{}}})},g=async(e,r)=>{try{if(!(await fetch(`/api/education/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({published:!r})})).ok)throw Error("Failed to update education");s(t.map(t=>t.id===e?{...t,published:!r}:t)),v.oR.success(`Education ${!r?"published":"unpublished"}`)}catch(e){console.error("Error updating education:",e),v.oR.error("Failed to update education")}};return f?(0,r.jsx)(d.DashboardLayout,{children:(0,r.jsx)("div",{className:"container mx-auto py-8",children:(0,r.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-300 rounded w-64"}),(0,r.jsx)("div",{className:"grid gap-4",children:[1,2].map(e=>(0,r.jsx)("div",{className:"h-48 bg-gray-300 rounded"},e))})]})})}):(0,r.jsx)(d.DashboardLayout,{children:(0,r.jsxs)("div",{className:"container mx-auto py-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Education"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage your educational background"})]}),(0,r.jsxs)(o.$,{onClick:()=>e.push("/dashboard/education/add"),children:[(0,r.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Add Education"]})]}),(0,r.jsx)("div",{className:"grid gap-6",children:t.map(t=>(0,r.jsxs)(n.Zp,{className:"overflow-hidden",children:[(0,r.jsx)(n.aR,{className:"pb-4",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center",children:(0,r.jsx)(u.A,{className:"h-6 w-6 text-primary"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)(n.ZB,{className:"text-xl",children:t.degree}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-muted-foreground mt-1",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-1"}),t.institution]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-1"}),t.location]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-1"}),t.period]})]}),t.grade&&(0,r.jsxs)("div",{className:"text-sm text-primary font-medium mt-1",children:["Grade: ",t.grade]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.E,{variant:t.published?"default":"secondary",children:t.published?"Published":"Draft"}),(0,r.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>g(t.id,t.published),children:t.published?"Unpublish":"Publish"}),(0,r.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>e.push(`/dashboard/education/${t.id}`),children:(0,r.jsx)(m.A,{className:"h-4 w-4"})}),(0,r.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>y(t.id),children:(0,r.jsx)(x.A,{className:"h-4 w-4"})})]})]})}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:t.description}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Key Highlights"}),(0,r.jsx)("ul",{className:"list-disc list-inside text-sm space-y-1",children:t.highlights.map((e,t)=>(0,r.jsx)("li",{children:e},t))})]})]})]},t.id))}),0===t.length&&(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"py-8 text-center",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"No education records found. Add your first education record to get started."})})})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},68274:(e,t,s)=>{Promise.resolve().then(s.bind(s,41756))},74750:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>d.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>h,tree:()=>c});var r=s(65239),a=s(48088),i=s(88170),d=s.n(i),o=s(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);s.d(t,n);let c={children:["",{children:["dashboard",{children:["education",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,84670)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\education\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\education\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/education/page",pathname:"/dashboard/education",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},79551:e=>{"use strict";e.exports=require("url")},84670:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\portfolio\\\\portfolio-cms\\\\src\\\\app\\\\dashboard\\\\education\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\education\\page.tsx","default")},88233:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},96474:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>n});var r=s(60687);s(43210);var a=s(8730),i=s(24224),d=s(4780);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function n({className:e,variant:t,asChild:s=!1,...i}){let n=s?a.DX:"span";return(0,r.jsx)(n,{"data-slot":"badge",className:(0,d.cn)(o({variant:t}),e),...i})}},97992:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,3310,1658,8580,4258,3868,2581,6929],()=>s(74750));module.exports=r})();