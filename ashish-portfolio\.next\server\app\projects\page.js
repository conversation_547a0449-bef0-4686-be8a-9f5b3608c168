(()=>{var e={};e.id=893,e.ids=[893],e.modules={3039:(e,t,s)=>{"use strict";s.d(t,{ProjectsStats:()=>l});var r=s(60687),i=s(26001),o=s(37472),a=s(44493);let n=[{number:"50+",label:"Projects Completed",description:"From small websites to complex web applications"},{number:"20+",label:"Happy Clients",description:"Satisfied clients across various industries"},{number:"15+",label:"Technologies",description:"Modern tools and frameworks mastered"},{number:"3+",label:"Years Experience",description:"Professional development experience"}];function l(){let[e,t]=(0,o.Wx)({triggerOnce:!0,threshold:.1});return(0,r.jsx)("section",{ref:e,className:"py-20",children:(0,r.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:n.map((e,s)=>(0,r.jsx)(i.P.div,{initial:{opacity:0,scale:.5},animate:t?{opacity:1,scale:1}:{},transition:{duration:.5,delay:.1*s},children:(0,r.jsx)(a.Zp,{className:"text-center hover-lift border-border/50 hover:border-border transition-all duration-300",children:(0,r.jsxs)(a.Wu,{className:"p-6",children:[(0,r.jsx)("div",{className:"text-3xl sm:text-4xl font-bold gradient-text-blue mb-2",children:e.number}),(0,r.jsx)("div",{className:"font-semibold mb-1",children:e.label}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})})},e.label))})})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20797:(e,t,s)=>{"use strict";s.d(t,{ProjectsStats:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call ProjectsStats() from the server but ProjectsStats is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\projects\\projects-stats.tsx","ProjectsStats")},23586:(e,t,s)=>{Promise.resolve().then(s.bind(s,94101)),Promise.resolve().then(s.bind(s,89650)),Promise.resolve().then(s.bind(s,55116)),Promise.resolve().then(s.bind(s,31249)),Promise.resolve().then(s.bind(s,3039))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31249:(e,t,s)=>{"use strict";s.d(t,{ProjectsHero:()=>l});var r=s(60687),i=s(26001),o=s(80375);let a=(0,s(62688).A)("rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]]);var n=s(42017);function l(){return(0,r.jsx)("section",{className:"py-20 bg-muted/30",children:(0,r.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"text-center max-w-4xl mx-auto",children:(0,r.jsxs)(i.P.div,{className:"space-y-8",initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)(i.P.h1,{className:"text-4xl sm:text-5xl lg:text-6xl font-bold",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:["My ",(0,r.jsx)("span",{className:"gradient-text-blue",children:"Projects"})]}),(0,r.jsx)(i.P.p,{className:"text-xl text-muted-foreground leading-relaxed max-w-2xl mx-auto",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:"A collection of projects that showcase my skills in full-stack development, UI/UX design, and modern web technologies. Each project represents a unique challenge and learning experience."})]}),(0,r.jsxs)(i.P.div,{className:"grid md:grid-cols-3 gap-8 mt-12",initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.4},children:[(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-3",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center",children:(0,r.jsx)(o.A,{className:"h-6 w-6 text-blue-500"})}),(0,r.jsx)("h3",{className:"font-semibold",children:"Clean Code"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground text-center",children:"Well-structured, maintainable code following best practices and industry standards."})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-3",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center",children:(0,r.jsx)(a,{className:"h-6 w-6 text-purple-500"})}),(0,r.jsx)("h3",{className:"font-semibold",children:"Modern Tech"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground text-center",children:"Built with cutting-edge technologies like React, Next.js, TypeScript, and more."})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-3",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center",children:(0,r.jsx)(n.A,{className:"h-6 w-6 text-green-500"})}),(0,r.jsx)("h3",{className:"font-semibold",children:"User-Focused"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground text-center",children:"Designed with user experience in mind, ensuring accessibility and performance."})]})]})]})})})})}},32417:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>c});var r=s(37413),i=s(64544),o=s(64947),a=s(49198),n=s(79238),l=s(20797);let c={title:"Projects - Ashish Kamat",description:"Explore my portfolio of web development projects, featuring modern applications built with React, Next.js, and cutting-edge technologies.",openGraph:{title:"Projects - Ashish Kamat",description:"Explore my portfolio of web development projects, featuring modern applications built with React, Next.js, and cutting-edge technologies."}};function d(){return(0,r.jsxs)("div",{className:"min-h-screen",children:[(0,r.jsx)(i.Navigation,{}),(0,r.jsxs)("main",{className:"pt-16",children:[(0,r.jsx)(a.ProjectsHero,{}),(0,r.jsx)(l.ProjectsStats,{}),(0,r.jsx)(n.ProjectsGrid,{})]}),(0,r.jsx)(o.Footer,{})]})}},33873:e=>{"use strict";e.exports=require("path")},34091:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("code-xml",[["path",{d:"m18 16 4-4-4-4",key:"1inbqp"}],["path",{d:"m6 8-4 4 4 4",key:"15zrgr"}],["path",{d:"m14.5 4-5 16",key:"e7oirm"}]])},34813:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(65239),i=s(48088),o=s(88170),a=s.n(o),n=s(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let c={children:["",{children:["projects",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,32417)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\projects\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\projects\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/projects/page",pathname:"/projects",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},41862:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},42017:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},49198:(e,t,s)=>{"use strict";s.d(t,{ProjectsHero:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call ProjectsHero() from the server but ProjectsHero is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\projects\\projects-hero.tsx","ProjectsHero")},55116:(e,t,s)=>{"use strict";s.d(t,{ProjectsGrid:()=>f});var r=s(60687),i=s(43210),o=s.n(i),a=s(26001),n=s(37472),l=s(41862),c=s(25334),d=s(34091),p=s(44493),m=s(29523),x=s(96834),h=s(30474),u=s(62185);function f(){let[e,t]=(0,i.useState)("All"),[s,f]=(0,i.useState)([]),[j,g]=(0,i.useState)(!0),[b,v]=(0,i.useState)(null),[y,N]=(0,n.Wx)({triggerOnce:!0,threshold:.1}),w=o().useMemo(()=>s.length?["All",...Array.from(new Set(s.map(e=>e.category))).sort()]:["All"],[s]),P=o().useMemo(()=>(0,u.OA)(s,e),[s,e]);return j?(0,r.jsx)("section",{className:"py-20 bg-muted/30",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6",children:(0,r.jsx)("span",{className:"gradient-text",children:"All Projects"})}),(0,r.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"Loading projects..."})]}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)(l.A,{className:"h-8 w-8 animate-spin"})})]})}):b?(0,r.jsx)("section",{className:"py-20 bg-muted/30",children:(0,r.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6",children:(0,r.jsx)("span",{className:"gradient-text",children:"All Projects"})}),(0,r.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"Unable to load projects. Please try again later."})]})})}):(0,r.jsx)("section",{ref:y,className:"py-20 bg-muted/30",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)(a.P.div,{className:"text-center mb-16",initial:{opacity:0,y:50},animate:N?{opacity:1,y:0}:{},transition:{duration:.8},children:[(0,r.jsx)("h2",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6",children:(0,r.jsx)("span",{className:"gradient-text",children:"All Projects"})}),(0,r.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"Explore my complete portfolio of projects, each demonstrating different aspects of modern web development and innovative problem-solving."})]}),(0,r.jsx)(a.P.div,{className:"flex flex-wrap justify-center gap-2 mb-12",initial:{opacity:0,y:30},animate:N?{opacity:1,y:0}:{},transition:{duration:.8,delay:.2},children:w.map(s=>(0,r.jsx)(m.$,{variant:e===s?"default":"outline",size:"sm",onClick:()=>t(s),className:"transition-all duration-300",children:s},s))}),(0,r.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:P.map((e,t)=>(0,r.jsx)(a.P.div,{initial:{opacity:0,y:50},animate:N?{opacity:1,y:0}:{},transition:{duration:.8,delay:.1*t},children:(0,r.jsxs)(p.Zp,{className:"h-full hover-lift group cursor-pointer border-border/50 hover:border-border transition-all duration-300",children:[(0,r.jsxs)("div",{className:"relative h-48 overflow-hidden rounded-t-lg",children:[e.image?(0,r.jsx)(h.default,{src:e.image,alt:e.title,fill:!0,className:"object-cover group-hover:scale-105 transition-transform duration-300"}):(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 flex items-center justify-center",children:(0,r.jsx)("div",{className:"text-4xl opacity-20",children:"\uD83D\uDCBB"})}),(0,r.jsx)("div",{className:"absolute top-3 right-3",children:(0,r.jsx)(x.E,{variant:"secondary",className:"text-xs bg-background/80 backdrop-blur-sm",children:e.category})})]}),(0,r.jsx)(p.aR,{className:"pb-3",children:(0,r.jsx)(p.ZB,{className:"text-xl font-bold group-hover:text-primary transition-colors duration-300",children:e.title})}),(0,r.jsxs)(p.Wu,{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-muted-foreground text-sm line-clamp-3",children:e.description}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.technologies.slice(0,3).map((e,t)=>(0,r.jsx)(x.E,{variant:"outline",className:"text-xs",children:e},t)),e.technologies.length>3&&(0,r.jsxs)(x.E,{variant:"outline",className:"text-xs",children:["+",e.technologies.length-3]})]}),(0,r.jsxs)("div",{className:"flex space-x-2 pt-2",children:[e.liveUrl&&(0,r.jsx)(m.$,{size:"sm",variant:"outline",className:"flex-1 text-xs",asChild:!0,children:(0,r.jsxs)("a",{href:e.liveUrl,target:"_blank",rel:"noopener noreferrer",children:[(0,r.jsx)(c.A,{className:"mr-1 h-3 w-3"}),"Demo"]})}),e.githubUrl&&(0,r.jsx)(m.$,{variant:"outline",size:"sm",asChild:!0,children:(0,r.jsx)("a",{href:e.githubUrl,target:"_blank",rel:"noopener noreferrer",children:(0,r.jsx)(d.A,{className:"h-3 w-3"})})})]})]})]})},e.id))})]})})}},62185:(e,t,s)=>{"use strict";s.d(t,{$_:()=>l,FH:()=>i,LE:()=>d,O6:()=>p,OA:()=>n,Wq:()=>c,a0:()=>x,h1:()=>m,tx:()=>a});let r="http://localhost:3001",i={getProjects:async()=>{let e=await fetch(`${r}/api/projects`);if(!e.ok)throw Error("Failed to fetch projects");return e.json()},getProject:async e=>{let t=await fetch(`${r}/api/projects/${e}`);if(!t.ok)throw Error("Failed to fetch project");return t.json()},getBlogPosts:async()=>{let e=await fetch(`${r}/api/blog`);if(!e.ok)throw Error("Failed to fetch blog posts");return e.json()},getBlogPost:async e=>{let t=await fetch(`${r}/api/blog/${e}`);if(!t.ok)throw Error("Failed to fetch blog post");return t.json()},getServices:async()=>{let e=await fetch(`${r}/api/services`);if(!e.ok)throw Error("Failed to fetch services");return e.json()},getTechStack:async()=>{let e=await fetch(`${r}/api/tech-stack`);if(!e.ok)throw Error("Failed to fetch tech stack");return e.json()},getTestimonials:async()=>{let e=await fetch(`${r}/api/testimonials`);if(!e.ok)throw Error("Failed to fetch testimonials");return e.json()},getExperiences:async()=>{let e=await fetch(`${r}/api/experiences`);if(!e.ok)throw Error("Failed to fetch experiences");return e.json()},getEducation:async()=>{let e=await fetch(`${r}/api/education`);if(!e.ok)throw Error("Failed to fetch education");return e.json()},getCertifications:async()=>{let e=await fetch(`${r}/api/certifications`);if(!e.ok)throw Error("Failed to fetch certifications");return e.json()}},o=e=>e.filter(e=>e.published).sort((e,t)=>e.order-t.order),a=e=>e.filter(e=>e.published&&e.featured).sort((e,t)=>e.order-t.order),n=(e,t)=>"All"===t?o(e):e.filter(e=>e.published&&e.category===t).sort((e,t)=>e.order-t.order),l=e=>e.filter(e=>e.published).sort((e,t)=>e.order-t.order),c=e=>e.filter(e=>e.published).reduce((e,t)=>(e[t.category]||(e[t.category]=[]),e[t.category].push(t),e),{}),d=e=>e.filter(e=>e.published).sort((e,t)=>e.order-t.order),p=e=>e.filter(e=>e.published).sort((e,t)=>e.order-t.order),m=e=>e.filter(e=>e.published).sort((e,t)=>e.order-t.order),x=e=>e.filter(e=>e.published).sort((e,t)=>e.order-t.order)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79238:(e,t,s)=>{"use strict";s.d(t,{ProjectsGrid:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call ProjectsGrid() from the server but ProjectsGrid is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\projects\\projects-grid.tsx","ProjectsGrid")},79551:e=>{"use strict";e.exports=require("url")},83418:(e,t,s)=>{Promise.resolve().then(s.bind(s,64947)),Promise.resolve().then(s.bind(s,64544)),Promise.resolve().then(s.bind(s,79238)),Promise.resolve().then(s.bind(s,49198)),Promise.resolve().then(s.bind(s,20797))}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,760,658,373,474,468],()=>s(34813));module.exports=r})();