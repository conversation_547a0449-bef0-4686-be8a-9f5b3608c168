{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/:path*{(\\\\.json)}?", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GmR490q78K+bBIm3nmrLlRQO8rHHbnjcdhaBgWmTT64=", "__NEXT_PREVIEW_MODE_ID": "80d6daedfa7af97a3bc6529ada971434", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "56fa1fa41ab9bede4482379debf856367b3385266c426aa357bbc951116124a7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e24821d15e6f244ecc44e8a8974bafa4f2c65b9eedca1bdc875ee08ab970b28b"}}}, "instrumentation": null, "functions": {}}