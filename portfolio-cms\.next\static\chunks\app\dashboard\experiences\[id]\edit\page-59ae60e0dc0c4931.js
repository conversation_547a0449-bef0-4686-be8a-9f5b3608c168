(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8388],{23508:(e,t,a)=>{Promise.resolve().then(a.bind(a,75638))},27213:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},29869:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},31949:(e,t,a)=>{"use strict";a.d(t,{d:()=>w});var r=a(95155),i=a(12115),s=a(85185),n=a(6101),l=a(46081),o=a(5845),d=a(45503),c=a(11275),u=a(63655),p="Switch",[h,m]=(0,l.A)(p),[x,v]=h(p),g=i.forwardRef((e,t)=>{let{__scopeSwitch:a,name:l,checked:d,defaultChecked:c,required:h,disabled:m,value:v="on",onCheckedChange:g,form:b,...f}=e,[k,w]=i.useState(null),N=(0,n.s)(t,e=>w(e)),C=i.useRef(!1),A=!k||b||!!k.closest("form"),[E,F]=(0,o.i)({prop:d,defaultProp:null!=c&&c,onChange:g,caller:p});return(0,r.jsxs)(x,{scope:a,checked:E,disabled:m,children:[(0,r.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":E,"aria-required":h,"data-state":j(E),"data-disabled":m?"":void 0,disabled:m,value:v,...f,ref:N,onClick:(0,s.m)(e.onClick,e=>{F(e=>!e),A&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),A&&(0,r.jsx)(y,{control:k,bubbles:!C.current,name:l,value:v,checked:E,required:h,disabled:m,form:b,style:{transform:"translateX(-100%)"}})]})});g.displayName=p;var b="SwitchThumb",f=i.forwardRef((e,t)=>{let{__scopeSwitch:a,...i}=e,s=v(b,a);return(0,r.jsx)(u.sG.span,{"data-state":j(s.checked),"data-disabled":s.disabled?"":void 0,...i,ref:t})});f.displayName=b;var y=i.forwardRef((e,t)=>{let{__scopeSwitch:a,control:s,checked:l,bubbles:o=!0,...u}=e,p=i.useRef(null),h=(0,n.s)(p,t),m=(0,d.Z)(l),x=(0,c.X)(s);return i.useEffect(()=>{let e=p.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==l&&t){let a=new Event("click",{bubbles:o});t.call(e,l),e.dispatchEvent(a)}},[m,l,o]),(0,r.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:l,...u,tabIndex:-1,ref:h,style:{...u.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function j(e){return e?"checked":"unchecked"}y.displayName="SwitchBubbleInput";var k=a(59434);let w=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)(g,{className:(0,k.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...i,ref:t,children:(0,r.jsx)(f,{className:(0,k.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});w.displayName=g.displayName},35169:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40968:(e,t,a)=>{"use strict";a.d(t,{b:()=>l});var r=a(12115),i=a(63655),s=a(95155),n=r.forwardRef((e,t)=>(0,s.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=n},45503:(e,t,a)=>{"use strict";a.d(t,{Z:()=>i});var r=a(12115);function i(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},54416:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},62154:(e,t,a)=>{"use strict";a.d(t,{B:()=>h});var r=a(95155),i=a(12115),s=a(30285),n=a(62523),l=a(85057),o=a(54416),d=a(27213),c=a(29869),u=a(66766),p=a(56671);function h(e){let{value:t,onChange:a,onRemove:h,disabled:m,label:x="Upload Image",className:v=""}=e,[g,b]=(0,i.useState)(!1),f=(0,i.useRef)(null),y=async e=>{var t;let r=null==(t=e.target.files)?void 0:t[0];if(r){b(!0);try{let e=new FormData;e.append("file",r);let t=await fetch("/api/upload",{method:"POST",body:e});if(!t.ok)throw Error("Upload failed");let i=await t.json();a(i.url)}catch(e){console.error("Error uploading image:",e),p.oR.error("Failed to upload image. Please try again.")}finally{b(!1)}}};return(0,r.jsxs)("div",{className:"space-y-2 ".concat(v),children:[(0,r.jsx)(l.J,{children:x}),t?(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"relative w-full h-48 rounded-lg overflow-hidden border",children:(0,r.jsx)(u.default,{src:t,alt:"Uploaded image",fill:!0,className:"object-cover"})}),(0,r.jsx)(s.$,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2",onClick:h,disabled:m,children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})]}):(0,r.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(d.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsxs)(s.$,{type:"button",variant:"outline",onClick:()=>{var e;null==(e=f.current)||e.click()},disabled:m||g,children:[(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4"}),g?"Uploading...":"Choose Image"]})}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"PNG, JPG, GIF up to 10MB"})]})}),(0,r.jsx)(n.p,{ref:f,type:"file",accept:"image/*",onChange:y,className:"hidden",disabled:m||g})]})}},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>s});var r=a(95155);a(12115);var i=a(59434);function s(e){let{className:t,type:a,...s}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},62525:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},75638:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>b});var r=a(95155),i=a(12115),s=a(35695),n=a(83930),l=a(30285),o=a(66695),d=a(62523),c=a(85057),u=a(88539),p=a(31949),h=a(62154),m=a(35169),x=a(62525),v=a(84616),g=a(56671);function b(){let e=(0,s.useRouter)(),t=(0,s.useParams)(),[a,b]=(0,i.useState)(!1),[f,y]=(0,i.useState)(!0),[j,k]=(0,i.useState)({title:"",company:"",companyLogo:"",location:"",period:"",type:"Full-time",description:"",achievements:[""],technologies:[""],website:"",order:0,published:!0});(0,i.useEffect)(()=>{w()},[]);let w=async()=>{try{var a,r,i;let e=await fetch("/api/experiences/".concat(t.id));if(!e.ok)throw Error("Failed to fetch experience");let s=await e.json();k({title:s.title||"",company:s.company||"",companyLogo:s.companyLogo||"",location:s.location||"",period:s.period||"",type:s.type||"Full-time",description:s.description||"",achievements:(null==(a=s.achievements)?void 0:a.length)?s.achievements:[""],technologies:(null==(r=s.technologies)?void 0:r.length)?s.technologies:[""],website:s.website||"",order:s.order||0,published:null==(i=s.published)||i})}catch(t){console.error("Error fetching experience:",t),g.oR.error("Failed to fetch experience"),e.push("/dashboard/experiences")}finally{y(!1)}},N=(e,t)=>{k(a=>({...a,[e]:t}))},C=(e,t,a)=>{k(r=>({...r,[e]:r[e].map((e,r)=>r===t?a:e)}))},A=e=>{k(t=>({...t,[e]:[...t[e],""]}))},E=(e,t)=>{k(a=>({...a,[e]:a[e].filter((e,a)=>a!==t)}))},F=async a=>{a.preventDefault(),b(!0);try{let a={...j,achievements:j.achievements.filter(e=>""!==e.trim()),technologies:j.technologies.filter(e=>""!==e.trim())};if(!(await fetch("/api/experiences/".concat(t.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})).ok)throw Error("Failed to update experience");g.oR.success("Experience updated successfully"),e.push("/dashboard/experiences")}catch(e){console.error("Error updating experience:",e),g.oR.error("Failed to update experience")}finally{b(!1)}};return f?(0,r.jsx)(n.DashboardLayout,{children:(0,r.jsx)("div",{className:"container mx-auto py-8",children:(0,r.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-300 rounded w-64"}),(0,r.jsx)("div",{className:"h-96 bg-gray-300 rounded"})]})})}):(0,r.jsx)(n.DashboardLayout,{children:(0,r.jsxs)("div",{className:"container mx-auto py-8",children:[(0,r.jsxs)("div",{className:"flex items-center mb-8",children:[(0,r.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>e.back(),className:"mr-4",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Edit Experience"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Update work experience details"})]})]}),(0,r.jsxs)(o.Zp,{children:[(0,r.jsx)(o.aR,{children:(0,r.jsx)(o.ZB,{children:"Experience Details"})}),(0,r.jsx)(o.Wu,{children:(0,r.jsxs)("form",{onSubmit:F,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"title",children:"Job Title *"}),(0,r.jsx)(d.p,{id:"title",value:j.title,onChange:e=>N("title",e.target.value),required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"company",children:"Company *"}),(0,r.jsx)(d.p,{id:"company",value:j.company,onChange:e=>N("company",e.target.value),required:!0})]}),(0,r.jsx)("div",{className:"md:col-span-2",children:(0,r.jsx)(h.B,{label:"Company Logo",value:j.companyLogo,onChange:e=>N("companyLogo",e),onRemove:()=>N("companyLogo","")})}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"location",children:"Location *"}),(0,r.jsx)(d.p,{id:"location",value:j.location,onChange:e=>N("location",e.target.value),required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"period",children:"Period *"}),(0,r.jsx)(d.p,{id:"period",value:j.period,onChange:e=>N("period",e.target.value),placeholder:"e.g., 2022 - Present",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"type",children:"Employment Type"}),(0,r.jsx)(d.p,{id:"type",value:j.type,onChange:e=>N("type",e.target.value),placeholder:"e.g., Full-time, Part-time, Contract"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"website",children:"Company Website"}),(0,r.jsx)(d.p,{id:"website",value:j.website,onChange:e=>N("website",e.target.value),placeholder:"https://company.com"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"order",children:"Display Order"}),(0,r.jsx)(d.p,{id:"order",type:"number",value:j.order,onChange:e=>N("order",parseInt(e.target.value)||0)})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"description",children:"Description *"}),(0,r.jsx)(u.T,{id:"description",value:j.description,onChange:e=>N("description",e.target.value),rows:4,required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{children:"Key Achievements"}),j.achievements.map((e,t)=>(0,r.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,r.jsx)(d.p,{value:e,onChange:e=>C("achievements",t,e.target.value),placeholder:"Enter an achievement"}),(0,r.jsx)(l.$,{type:"button",variant:"outline",size:"sm",onClick:()=>E("achievements",t),children:(0,r.jsx)(x.A,{className:"h-4 w-4"})})]},t)),(0,r.jsxs)(l.$,{type:"button",variant:"outline",size:"sm",onClick:()=>A("achievements"),className:"mt-2",children:[(0,r.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Add Achievement"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{children:"Technologies"}),j.technologies.map((e,t)=>(0,r.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,r.jsx)(d.p,{value:e,onChange:e=>C("technologies",t,e.target.value),placeholder:"Enter a technology"}),(0,r.jsx)(l.$,{type:"button",variant:"outline",size:"sm",onClick:()=>E("technologies",t),children:(0,r.jsx)(x.A,{className:"h-4 w-4"})})]},t)),(0,r.jsxs)(l.$,{type:"button",variant:"outline",size:"sm",onClick:()=>A("technologies"),className:"mt-2",children:[(0,r.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Add Technology"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(p.d,{id:"published",checked:j.published,onCheckedChange:e=>N("published",e)}),(0,r.jsx)(c.J,{htmlFor:"published",children:"Published"})]}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(l.$,{type:"submit",disabled:a,children:a?"Updating...":"Update Experience"}),(0,r.jsx)(l.$,{type:"button",variant:"outline",onClick:()=>e.back(),children:"Cancel"})]})]})})]})]})})}},84616:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85057:(e,t,a)=>{"use strict";a.d(t,{J:()=>n});var r=a(95155);a(12115);var i=a(40968),s=a(59434);function n(e){let{className:t,...a}=e;return(0,r.jsx)(i.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},88539:(e,t,a)=>{"use strict";a.d(t,{T:()=>s});var r=a(95155);a(12115);var i=a(59434);function s(e){let{className:t,...a}=e;return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,i.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4854,2108,8858,9368,6671,6766,9008,8441,1684,7358],()=>t(23508)),_N_E=e.O()}]);