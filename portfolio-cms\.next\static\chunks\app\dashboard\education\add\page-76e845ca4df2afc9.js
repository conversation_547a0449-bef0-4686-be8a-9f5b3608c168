(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4168],{31949:(e,t,r)=>{"use strict";r.d(t,{d:()=>w});var i=r(95155),s=r(12115),a=r(85185),n=r(6101),l=r(46081),d=r(5845),o=r(45503),c=r(11275),u=r(63655),h="Switch",[p,g]=(0,l.A)(h),[x,v]=p(h),b=s.forwardRef((e,t)=>{let{__scopeSwitch:r,name:l,checked:o,defaultChecked:c,required:p,disabled:g,value:v="on",onCheckedChange:b,form:f,...m}=e,[k,w]=s.useState(null),N=(0,n.s)(t,e=>w(e)),C=s.useRef(!1),E=!k||f||!!k.closest("form"),[A,R]=(0,d.i)({prop:o,defaultProp:null!=c&&c,onChange:b,caller:h});return(0,i.jsxs)(x,{scope:r,checked:A,disabled:g,children:[(0,i.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":A,"aria-required":p,"data-state":y(A),"data-disabled":g?"":void 0,disabled:g,value:v,...m,ref:N,onClick:(0,a.m)(e.onClick,e=>{R(e=>!e),E&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),E&&(0,i.jsx)(j,{control:k,bubbles:!C.current,name:l,value:v,checked:A,required:p,disabled:g,form:f,style:{transform:"translateX(-100%)"}})]})});b.displayName=h;var f="SwitchThumb",m=s.forwardRef((e,t)=>{let{__scopeSwitch:r,...s}=e,a=v(f,r);return(0,i.jsx)(u.sG.span,{"data-state":y(a.checked),"data-disabled":a.disabled?"":void 0,...s,ref:t})});m.displayName=f;var j=s.forwardRef((e,t)=>{let{__scopeSwitch:r,control:a,checked:l,bubbles:d=!0,...u}=e,h=s.useRef(null),p=(0,n.s)(h,t),g=(0,o.Z)(l),x=(0,c.X)(a);return s.useEffect(()=>{let e=h.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(g!==l&&t){let r=new Event("click",{bubbles:d});t.call(e,l),e.dispatchEvent(r)}},[g,l,d]),(0,i.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:l,...u,tabIndex:-1,ref:p,style:{...u.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function y(e){return e?"checked":"unchecked"}j.displayName="SwitchBubbleInput";var k=r(59434);let w=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,i.jsx)(b,{className:(0,k.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",r),...s,ref:t,children:(0,i.jsx)(m,{className:(0,k.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});w.displayName=b.displayName},35169:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40968:(e,t,r)=>{"use strict";r.d(t,{b:()=>l});var i=r(12115),s=r(63655),a=r(95155),n=i.forwardRef((e,t)=>(0,a.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=n},45503:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var i=r(12115);function s(e){let t=i.useRef({value:e,previous:e});return i.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var i=r(95155);r(12115);var s=r(59434);function a(e){let{className:t,type:r,...a}=e;return(0,i.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...a})}},62525:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},66290:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var i=r(95155),s=r(12115),a=r(35695),n=r(83930),l=r(30285),d=r(66695),o=r(62523),c=r(85057),u=r(88539),h=r(31949),p=r(35169),g=r(62525),x=r(84616),v=r(56671);function b(){let e=(0,a.useRouter)(),[t,r]=(0,s.useState)(!1),[b,f]=(0,s.useState)({degree:"",institution:"",location:"",period:"",grade:"",description:"",highlights:[""],order:0,published:!0}),m=(e,t)=>{f(r=>({...r,[e]:t}))},j=(e,t)=>{f(r=>({...r,highlights:r.highlights.map((r,i)=>i===e?t:r)}))},y=e=>{f(t=>({...t,highlights:t.highlights.filter((t,r)=>r!==e)}))},k=async t=>{t.preventDefault(),r(!0);try{let t={...b,highlights:b.highlights.filter(e=>""!==e.trim())};if(!(await fetch("/api/education",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)})).ok)throw Error("Failed to create education");v.oR.success("Education created successfully"),e.push("/dashboard/education")}catch(e){console.error("Error creating education:",e),v.oR.error("Failed to create education")}finally{r(!1)}};return(0,i.jsx)(n.DashboardLayout,{children:(0,i.jsxs)("div",{className:"container mx-auto py-8",children:[(0,i.jsxs)("div",{className:"flex items-center mb-8",children:[(0,i.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>e.back(),className:"mr-4",children:[(0,i.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold",children:"Add Education"}),(0,i.jsx)("p",{className:"text-muted-foreground",children:"Create a new education entry"})]})]}),(0,i.jsxs)(d.Zp,{children:[(0,i.jsx)(d.aR,{children:(0,i.jsx)(d.ZB,{children:"Education Details"})}),(0,i.jsx)(d.Wu,{children:(0,i.jsxs)("form",{onSubmit:k,className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{htmlFor:"degree",children:"Degree *"}),(0,i.jsx)(o.p,{id:"degree",value:b.degree,onChange:e=>m("degree",e.target.value),required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{htmlFor:"institution",children:"Institution *"}),(0,i.jsx)(o.p,{id:"institution",value:b.institution,onChange:e=>m("institution",e.target.value),required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{htmlFor:"location",children:"Location *"}),(0,i.jsx)(o.p,{id:"location",value:b.location,onChange:e=>m("location",e.target.value),required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{htmlFor:"period",children:"Period *"}),(0,i.jsx)(o.p,{id:"period",value:b.period,onChange:e=>m("period",e.target.value),placeholder:"e.g., 2020 - 2024",required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{htmlFor:"grade",children:"Grade/GPA"}),(0,i.jsx)(o.p,{id:"grade",value:b.grade,onChange:e=>m("grade",e.target.value),placeholder:"e.g., First Class with Distinction (8.5/10 CGPA)"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{htmlFor:"order",children:"Display Order"}),(0,i.jsx)(o.p,{id:"order",type:"number",value:b.order,onChange:e=>m("order",parseInt(e.target.value)||0)})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{htmlFor:"description",children:"Description *"}),(0,i.jsx)(u.T,{id:"description",value:b.description,onChange:e=>m("description",e.target.value),rows:4,required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{children:"Key Highlights"}),b.highlights.map((e,t)=>(0,i.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,i.jsx)(o.p,{value:e,onChange:e=>j(t,e.target.value),placeholder:"Enter a highlight"}),(0,i.jsx)(l.$,{type:"button",variant:"outline",size:"sm",onClick:()=>y(t),children:(0,i.jsx)(g.A,{className:"h-4 w-4"})})]},t)),(0,i.jsxs)(l.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{f(e=>({...e,highlights:[...e.highlights,""]}))},className:"mt-2",children:[(0,i.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Add Highlight"]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(h.d,{id:"published",checked:b.published,onCheckedChange:e=>m("published",e)}),(0,i.jsx)(c.J,{htmlFor:"published",children:"Published"})]}),(0,i.jsxs)("div",{className:"flex gap-4",children:[(0,i.jsx)(l.$,{type:"submit",disabled:t,children:t?"Creating...":"Create Education"}),(0,i.jsx)(l.$,{type:"button",variant:"outline",onClick:()=>e.back(),children:"Cancel"})]})]})})]})]})})}},84616:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85057:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var i=r(95155);r(12115);var s=r(40968),a=r(59434);function n(e){let{className:t,...r}=e;return(0,i.jsx)(s.b,{"data-slot":"label",className:(0,a.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},86225:(e,t,r)=>{Promise.resolve().then(r.bind(r,66290))},88539:(e,t,r)=>{"use strict";r.d(t,{T:()=>a});var i=r(95155);r(12115);var s=r(59434);function a(e){let{className:t,...r}=e;return(0,i.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...r})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4854,2108,8858,9368,6671,9008,8441,1684,7358],()=>t(86225)),_N_E=e.O()}]);