"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1502],{26126:(e,t,s)=>{s.d(t,{E:()=>o});var r=s(95155);s(12115);var a=s(99708),i=s(74466),n=s(59434);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:s,asChild:i=!1,...o}=e,d=i?a.DX:"span";return(0,r.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(l({variant:s}),t),...o})}},47262:(e,t,s)=>{s.d(t,{S:()=>l});var r=s(95155);s(12115);var a=s(76981),i=s(5196),n=s(59434);function l(e){let{className:t,...s}=e;return(0,r.jsx)(a.bL,{"data-slot":"checkbox",className:(0,n.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...s,children:(0,r.jsx)(a.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,r.jsx)(i.A,{className:"size-3.5"})})})}},59409:(e,t,s)=>{s.d(t,{bq:()=>u,eb:()=>x,gC:()=>h,l6:()=>d,yv:()=>c});var r=s(95155);s(12115);var a=s(38715),i=s(66474),n=s(5196),l=s(47863),o=s(59434);function d(e){let{...t}=e;return(0,r.jsx)(a.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,r.jsx)(a.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:s="default",children:n,...l}=e;return(0,r.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":s,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l,children:[n,(0,r.jsx)(a.In,{asChild:!0,children:(0,r.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function h(e){let{className:t,children:s,position:i="popper",...n}=e;return(0,r.jsx)(a.ZL,{children:(0,r.jsxs)(a.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...n,children:[(0,r.jsx)(g,{}),(0,r.jsx)(a.LM,{className:(0,o.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:s}),(0,r.jsx)(p,{})]})})}function x(e){let{className:t,children:s,...i}=e;return(0,r.jsxs)(a.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...i,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(n.A,{className:"size-4"})})}),(0,r.jsx)(a.p4,{children:s})]})}function g(e){let{className:t,...s}=e;return(0,r.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,r.jsx)(l.A,{className:"size-4"})})}function p(e){let{className:t,...s}=e;return(0,r.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,r.jsx)(i.A,{className:"size-4"})})}},62154:(e,t,s)=>{s.d(t,{B:()=>x});var r=s(95155),a=s(12115),i=s(30285),n=s(62523),l=s(85057),o=s(54416),d=s(27213),c=s(29869),u=s(66766),h=s(56671);function x(e){let{value:t,onChange:s,onRemove:x,disabled:g,label:p="Upload Image",className:m=""}=e,[f,v]=(0,a.useState)(!1),b=(0,a.useRef)(null),j=async e=>{var t;let r=null==(t=e.target.files)?void 0:t[0];if(r){v(!0);try{let e=new FormData;e.append("file",r);let t=await fetch("/api/upload",{method:"POST",body:e});if(!t.ok)throw Error("Upload failed");let a=await t.json();s(a.url)}catch(e){console.error("Error uploading image:",e),h.oR.error("Failed to upload image. Please try again.")}finally{v(!1)}}};return(0,r.jsxs)("div",{className:"space-y-2 ".concat(m),children:[(0,r.jsx)(l.J,{children:p}),t?(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"relative w-full h-48 rounded-lg overflow-hidden border",children:(0,r.jsx)(u.default,{src:t,alt:"Uploaded image",fill:!0,className:"object-cover"})}),(0,r.jsx)(i.$,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2",onClick:x,disabled:g,children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})]}):(0,r.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(d.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>{var e;null==(e=b.current)||e.click()},disabled:g||f,children:[(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4"}),f?"Uploading...":"Choose Image"]})}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"PNG, JPG, GIF up to 10MB"})]})}),(0,r.jsx)(n.p,{ref:b,type:"file",accept:"image/*",onChange:j,className:"hidden",disabled:g||f})]})}},62523:(e,t,s)=>{s.d(t,{p:()=>i});var r=s(95155);s(12115);var a=s(59434);function i(e){let{className:t,type:s,...i}=e;return(0,r.jsx)("input",{type:s,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},71502:(e,t,s)=>{s.d(t,{BlogForm:()=>Y});var r=s(95155),a=s(12115),i=s(35695),n=s(30285),l=s(62523),o=s(85057),d=s(88539),c=s(47262),u=s(59409),h=s(66695),x=s(62154),g=s(26126),p=s(75109),m=s(80353),f=s(81891),v=s(36761),b=s(66377),j=s(4589),y=s(4652),w=s(45254),N=s(90192),k=s(423),C=s(43557),A=s(79030),z=s(10034),T=s(87489),S=s(59434);function $(e){let{className:t,orientation:s="horizontal",decorative:a=!0,...i}=e;return(0,r.jsx)(T.b,{"data-slot":"separator",decorative:a,orientation:s,className:(0,S.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...i})}var E=s(9727),F=s(19144),R=s(32643),B=s(29621),L=s(28440),P=s(22705),J=s(92406),U=s(15968),_=s(89140),q=s(40224),Z=s(38164),H=s(27213),I=s(65112),M=s(74347),W=s(93654),D=s(48932);function O(e){let{content:t,onChange:s,className:i}=e,l=(0,z.$)(),o=(0,p.hG)({extensions:[m.A.configure({codeBlock:!1}),f.Ay.configure({HTMLAttributes:{class:"max-w-full h-auto rounded-lg"}}),v.Ay.configure({openOnClick:!1,HTMLAttributes:{class:"text-primary underline hover:text-primary/80"}}),b.A,j.A,y.Ay.configure({multicolor:!0}),w.A.configure({lowlight:l,HTMLAttributes:{class:"bg-muted p-4 rounded-lg font-mono text-sm"}}),N.Ay.configure({resizable:!0}),k.A,A.h,C.A],content:t||"",onUpdate:e=>{let{editor:t}=e;s(t.getHTML())},editorProps:{attributes:{class:(0,S.cn)("prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[300px] p-4","prose-headings:font-bold prose-headings:text-foreground","prose-p:text-muted-foreground prose-p:leading-relaxed","prose-a:text-primary prose-a:no-underline hover:prose-a:underline","prose-strong:text-foreground prose-strong:font-semibold","prose-code:bg-muted prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:text-sm","prose-pre:bg-muted prose-pre:border prose-pre:border-border","prose-blockquote:border-l-4 prose-blockquote:border-primary prose-blockquote:pl-4 prose-blockquote:italic","prose-ul:list-disc prose-ol:list-decimal","prose-li:text-muted-foreground","prose-table:border-collapse prose-table:border prose-table:border-border","prose-th:border prose-th:border-border prose-th:bg-muted prose-th:p-2 prose-th:font-semibold","prose-td:border prose-td:border-border prose-td:p-2",i)}}}),d=(0,a.useCallback)(()=>{let e=window.prompt("Enter image URL:");e&&o&&o.chain().focus().setImage({src:e}).run()},[o]),c=(0,a.useCallback)(()=>{if(!o)return;let e=o.getAttributes("link").href,t=window.prompt("Enter URL:",e);if(null!==t){if(""===t)return void o.chain().focus().extendMarkRange("link").unsetLink().run();o.chain().focus().extendMarkRange("link").setLink({href:t}).run()}},[o]),u=(0,a.useCallback)(()=>{o&&o.chain().focus().insertTable({rows:3,cols:3,withHeaderRow:!0}).run()},[o]);return o?(0,r.jsxs)("div",{className:"border border-border rounded-lg overflow-hidden",children:[(0,r.jsx)("div",{className:"border-b border-border bg-muted/50 p-2",children:(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-1",children:[(0,r.jsx)(n.$,{variant:o.isActive("bold")?"default":"ghost",size:"sm",onClick:()=>o.chain().focus().toggleBold().run(),children:(0,r.jsx)(E.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:o.isActive("italic")?"default":"ghost",size:"sm",onClick:()=>o.chain().focus().toggleItalic().run(),children:(0,r.jsx)(F.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:o.isActive("strike")?"default":"ghost",size:"sm",onClick:()=>o.chain().focus().toggleStrike().run(),children:(0,r.jsx)(R.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:o.isActive("code")?"default":"ghost",size:"sm",onClick:()=>o.chain().focus().toggleCode().run(),children:(0,r.jsx)(B.A,{className:"h-4 w-4"})}),(0,r.jsx)($,{orientation:"vertical",className:"h-6"}),(0,r.jsx)(n.$,{variant:o.isActive("heading",{level:1})?"default":"ghost",size:"sm",onClick:()=>o.chain().focus().toggleHeading({level:1}).run(),children:(0,r.jsx)(L.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:o.isActive("heading",{level:2})?"default":"ghost",size:"sm",onClick:()=>o.chain().focus().toggleHeading({level:2}).run(),children:(0,r.jsx)(P.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:o.isActive("heading",{level:3})?"default":"ghost",size:"sm",onClick:()=>o.chain().focus().toggleHeading({level:3}).run(),children:(0,r.jsx)(J.A,{className:"h-4 w-4"})}),(0,r.jsx)($,{orientation:"vertical",className:"h-6"}),(0,r.jsx)(n.$,{variant:o.isActive("bulletList")?"default":"ghost",size:"sm",onClick:()=>o.chain().focus().toggleBulletList().run(),children:(0,r.jsx)(U.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:o.isActive("orderedList")?"default":"ghost",size:"sm",onClick:()=>o.chain().focus().toggleOrderedList().run(),children:(0,r.jsx)(_.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:o.isActive("blockquote")?"default":"ghost",size:"sm",onClick:()=>o.chain().focus().toggleBlockquote().run(),children:(0,r.jsx)(q.A,{className:"h-4 w-4"})}),(0,r.jsx)($,{orientation:"vertical",className:"h-6"}),(0,r.jsx)(n.$,{variant:"ghost",size:"sm",onClick:c,children:(0,r.jsx)(Z.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"ghost",size:"sm",onClick:d,children:(0,r.jsx)(H.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"ghost",size:"sm",onClick:u,children:(0,r.jsx)(I.A,{className:"h-4 w-4"})}),(0,r.jsx)($,{orientation:"vertical",className:"h-6"}),(0,r.jsx)(n.$,{variant:o.isActive("highlight")?"default":"ghost",size:"sm",onClick:()=>o.chain().focus().toggleHighlight().run(),children:(0,r.jsx)(M.A,{className:"h-4 w-4"})}),(0,r.jsx)($,{orientation:"vertical",className:"h-6"}),(0,r.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>o.chain().focus().undo().run(),disabled:!o.can().undo(),children:(0,r.jsx)(W.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>o.chain().focus().redo().run(),disabled:!o.can().redo(),children:(0,r.jsx)(D.A,{className:"h-4 w-4"})})]})}),(0,r.jsx)(p.$Z,{editor:o,className:"min-h-[300px] max-h-[600px] overflow-y-auto"})]}):null}var G=s(15448),V=s(14186),K=s(54416),X=s(56671);let Q=["Web Development","React","Next.js","TypeScript","JavaScript","CSS","Node.js","Database","DevOps","Tutorial","Tips & Tricks","Career"];function Y(e){let{postId:t,onSuccess:s}=e,p=(0,i.useRouter)(),[m,f]=(0,a.useState)(!1),[v,b]=(0,a.useState)(""),[j,y]=(0,a.useState)(!0),[w,N]=(0,a.useState)(!0),[k,C]=(0,a.useState)({title:"",slug:"",excerpt:"",content:"",image:"",category:"",tags:[],published:!1,featured:!1,readTime:5});(0,a.useEffect)(()=>{t&&A()},[t]),(0,a.useEffect)(()=>{if(k.title&&j&&(!t||""===k.slug)){let e=(0,S.z9)(k.title);C(t=>({...t,slug:e}))}},[k.title,j,t]),(0,a.useEffect)(()=>{if(k.content&&w){let e=(0,S.KE)(k.content),t=(0,S._C)(e);C(e=>({...e,readTime:t}))}},[k.content,w]);let A=async()=>{try{let e=await fetch("/api/blog/".concat(t));if(e.ok){let t=await e.json();C({title:t.title||"",slug:t.slug||"",excerpt:t.excerpt||"",content:t.content||"",image:t.image||"",category:t.category||"",tags:t.tags||[],published:t.published||!1,featured:t.featured||!1,readTime:t.readTime||5}),y(!1),N(!1)}}catch(e){console.error("Error fetching post:",e)}},z=async e=>{e.preventDefault(),f(!0);try{let e=t?"/api/blog/".concat(t):"/api/blog",r=t?"PUT":"POST";if((await fetch(e,{method:r,headers:{"Content-Type":"application/json"},body:JSON.stringify(k)})).ok)null==s||s(),t||p.push("/dashboard/blog");else throw Error("Failed to save post")}catch(e){console.error("Error saving post:",e),X.oR.error("Failed to save post. Please try again.")}finally{f(!1)}},T=()=>{v.trim()&&!k.tags.includes(v.trim())&&(C(e=>({...e,tags:[...e.tags,v.trim()]})),b(""))},$=e=>{C(t=>({...t,tags:t.tags.filter(t=>t!==e)}))};return(0,r.jsxs)("form",{onSubmit:z,className:"space-y-6",children:[(0,r.jsxs)(h.Zp,{children:[(0,r.jsxs)(h.aR,{children:[(0,r.jsx)(h.ZB,{children:"Basic Information"}),(0,r.jsx)(h.BT,{children:"Enter the basic details about your blog post"})]}),(0,r.jsxs)(h.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(o.J,{htmlFor:"title",children:"Post Title *"}),(0,r.jsx)(l.p,{id:"title",value:k.title,onChange:e=>C(t=>({...t,title:e.target.value})),required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)(o.J,{htmlFor:"slug",children:"URL Slug *"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.S,{id:"auto-slug",checked:j,onCheckedChange:e=>y(e)}),(0,r.jsxs)(o.J,{htmlFor:"auto-slug",className:"text-sm text-muted-foreground",children:[(0,r.jsx)(G.A,{className:"inline h-3 w-3 mr-1"}),"Auto-generate"]})]})]}),(0,r.jsx)(l.p,{id:"slug",value:k.slug,onChange:e=>{C(t=>({...t,slug:e.target.value})),y(!1)},placeholder:"url-friendly-slug",required:!0}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"The URL slug will be used in the blog post URL"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(o.J,{htmlFor:"excerpt",children:"Excerpt *"}),(0,r.jsx)(d.T,{id:"excerpt",value:k.excerpt,onChange:e=>C(t=>({...t,excerpt:e.target.value})),rows:3,placeholder:"A brief summary of your post...",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(o.J,{htmlFor:"category",children:"Category *"}),(0,r.jsxs)(u.l6,{value:k.category,onValueChange:e=>C(t=>({...t,category:e})),children:[(0,r.jsx)(u.bq,{children:(0,r.jsx)(u.yv,{placeholder:"Select a category"})}),(0,r.jsx)(u.gC,{children:Q.map(e=>(0,r.jsx)(u.eb,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)(o.J,{htmlFor:"readTime",children:"Reading Time (minutes)"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.S,{id:"auto-read-time",checked:w,onCheckedChange:e=>N(e)}),(0,r.jsxs)(o.J,{htmlFor:"auto-read-time",className:"text-sm text-muted-foreground",children:[(0,r.jsx)(V.A,{className:"inline h-3 w-3 mr-1"}),"Auto-calculate"]})]})]}),(0,r.jsx)(l.p,{id:"readTime",type:"number",min:"1",max:"60",value:k.readTime,onChange:e=>{C(t=>({...t,readTime:parseInt(e.target.value)||5})),N(!1)},placeholder:"5"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Estimated reading time based on content length"})]})]})]}),(0,r.jsxs)(h.Zp,{children:[(0,r.jsxs)(h.aR,{children:[(0,r.jsx)(h.ZB,{children:"Featured Image"}),(0,r.jsx)(h.BT,{children:"Upload a featured image for your blog post"})]}),(0,r.jsx)(h.Wu,{children:(0,r.jsx)(x.B,{value:k.image,onChange:e=>C(t=>({...t,image:e})),onRemove:()=>C(e=>({...e,image:""})),disabled:m})})]}),(0,r.jsxs)(h.Zp,{children:[(0,r.jsxs)(h.aR,{children:[(0,r.jsx)(h.ZB,{children:"Content"}),(0,r.jsx)(h.BT,{children:"Write your blog post content using the rich text editor"})]}),(0,r.jsx)(h.Wu,{children:(0,r.jsxs)("div",{children:[(0,r.jsx)(o.J,{htmlFor:"content",children:"Post Content *"}),(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsx)(O,{content:k.content,onChange:e=>C(t=>({...t,content:e})),placeholder:"Start writing your blog post..."})})]})})]}),(0,r.jsxs)(h.Zp,{children:[(0,r.jsxs)(h.aR,{children:[(0,r.jsx)(h.ZB,{children:"Tags"}),(0,r.jsx)(h.BT,{children:"Add tags to help categorize your post"})]}),(0,r.jsxs)(h.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(l.p,{placeholder:"Enter tag (e.g., react, tutorial)",value:v,onChange:e=>b(e.target.value),onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),T())}}),(0,r.jsx)(n.$,{type:"button",onClick:T,children:"Add"})]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:k.tags.map(e=>(0,r.jsxs)(g.E,{variant:"secondary",className:"flex items-center gap-1",children:[e,(0,r.jsx)(K.A,{className:"h-3 w-3 cursor-pointer",onClick:()=>$(e)})]},e))})]})]}),(0,r.jsxs)(h.Zp,{children:[(0,r.jsxs)(h.aR,{children:[(0,r.jsx)(h.ZB,{children:"Settings"}),(0,r.jsx)(h.BT,{children:"Configure post visibility and display options"})]}),(0,r.jsxs)(h.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.S,{id:"featured",checked:k.featured,onCheckedChange:e=>C(t=>({...t,featured:e}))}),(0,r.jsx)(o.J,{htmlFor:"featured",children:"Featured Post"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.S,{id:"published",checked:k.published,onCheckedChange:e=>C(t=>({...t,published:e}))}),(0,r.jsx)(o.J,{htmlFor:"published",children:"Published"})]})]})]}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(n.$,{type:"submit",disabled:m,children:m?"Saving...":t?"Update Post":"Create Post"}),(0,r.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>p.back(),children:"Cancel"})]})]})}},85057:(e,t,s)=>{s.d(t,{J:()=>n});var r=s(95155);s(12115);var a=s(40968),i=s(59434);function n(e){let{className:t,...s}=e;return(0,r.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s})}},88539:(e,t,s)=>{s.d(t,{T:()=>i});var r=s(95155);s(12115);var a=s(59434);function i(e){let{className:t,...s}=e;return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...s})}}}]);