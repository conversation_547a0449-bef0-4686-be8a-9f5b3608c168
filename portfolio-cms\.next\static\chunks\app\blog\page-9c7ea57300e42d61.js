(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3831],{14186:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},24269:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(95155),a=r(12115),i=r(6874),n=r.n(i),d=r(66695),l=r(26126),c=r(30285),o=r(35169),u=r(69074),x=r(14186),h=r(92657),m=r(66766);function g(){let[e,t]=(0,a.useState)([]),[r,i]=(0,a.useState)(!0),[g,v]=(0,a.useState)("All");(0,a.useEffect)(()=>{f()},[]);let f=async()=>{try{let e=await fetch("/api/blog"),r=await e.json();t(r.filter(e=>e.published))}catch(e){console.error("Error fetching blog posts:",e)}finally{i(!1)}},p=["All",...Array.from(new Set(e.map(e=>e.category)))],b="All"===g?e:e.filter(e=>e.category===g);return r?(0,s.jsx)("div",{className:"min-h-screen bg-background p-8",children:(0,s.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,s.jsx)("div",{className:"text-center",children:"Loading blog posts..."})})}):(0,s.jsx)("div",{className:"min-h-screen bg-background p-8",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[(0,s.jsx)(n(),{href:"/dashboard",children:(0,s.jsxs)(c.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Blog Preview"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Preview your published blog posts"})]})]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2 mb-8",children:p.map(e=>(0,s.jsx)(c.$,{variant:g===e?"default":"outline",size:"sm",onClick:()=>v(e),children:e},e))}),0===b.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("p",{className:"text-muted-foreground",children:"No published blog posts found."}),(0,s.jsx)(n(),{href:"/dashboard/blog/new",children:(0,s.jsx)(c.$,{className:"mt-4",children:"Create your first blog post"})})]}):(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:b.map(e=>(0,s.jsx)(d.Zp,{className:"h-full hover:shadow-lg transition-shadow",children:(0,s.jsxs)(n(),{href:"/blog/".concat(e.slug),children:[(0,s.jsxs)("div",{className:"relative h-48 overflow-hidden rounded-t-lg",children:[e.image?(0,s.jsx)(m.default,{src:e.image,alt:e.title,fill:!0,className:"object-cover hover:scale-105 transition-transform duration-300"}):(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 flex items-center justify-center",children:(0,s.jsx)("div",{className:"text-4xl opacity-20",children:"\uD83D\uDCDD"})}),(0,s.jsx)("div",{className:"absolute top-3 right-3",children:(0,s.jsx)(l.E,{variant:"secondary",className:"text-xs bg-background/80 backdrop-blur-sm",children:e.category})}),e.featured&&(0,s.jsx)("div",{className:"absolute top-3 left-3",children:(0,s.jsx)(l.E,{variant:"default",className:"text-xs",children:"Featured"})})]}),(0,s.jsx)(d.aR,{className:"pb-3",children:(0,s.jsx)(d.ZB,{className:"text-lg line-clamp-2 hover:text-primary transition-colors",children:e.title})}),(0,s.jsxs)(d.Wu,{className:"space-y-4",children:[(0,s.jsx)("p",{className:"text-muted-foreground text-sm line-clamp-3",children:e.excerpt}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.tags.slice(0,3).map((e,t)=>(0,s.jsx)(l.E,{variant:"outline",className:"text-xs",children:e},t)),e.tags.length>3&&(0,s.jsxs)(l.E,{variant:"outline",className:"text-xs",children:["+",e.tags.length-3]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-xs text-muted-foreground pt-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(u.A,{className:"mr-1 h-3 w-3"}),new Date(e.publishedAt||e.createdAt).toLocaleDateString()]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(x.A,{className:"mr-1 h-3 w-3"}),e.readTime||5," min"]})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(h.A,{className:"mr-1 h-3 w-3"}),e.views]})]}),(0,s.jsxs)("div",{className:"text-xs text-muted-foreground",children:["By ",e.author.name]})]})]})},e.id))})]})})}},26126:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(95155);r(12115);var a=r(99708),i=r(74466),n=r(59434);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,asChild:i=!1,...l}=e,c=i?a.DX:"span";return(0,s.jsx)(c,{"data-slot":"badge",className:(0,n.cn)(d({variant:r}),t),...l})}},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var s=r(95155);r(12115);var a=r(99708),i=r(74466),n=r(59434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:i,asChild:l=!1,...c}=e,o=l?a.DX:"button";return(0,s.jsx)(o,{"data-slot":"button",className:(0,n.cn)(d({variant:r,size:i,className:t})),...c})}},35169:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},59434:(e,t,r)=>{"use strict";r.d(t,{KE:()=>x,_C:()=>u,cn:()=>c,z9:()=>o});var s=r(52596),a=r(39688),i=r(96262),n=r.n(i),d=r(60430),l=r.n(d);function c(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}function o(e){return n()(e,{lower:!0,strict:!0,remove:/[*+~.()'"!:@]/g})}function u(e){return Math.ceil(l()(e).minutes)}function x(e){try{let t=JSON.parse(e);if("doc"===t.type&&t.content)return function e(t){let r="";for(let s of t)"text"===s.type?r+=s.text||"":s.content&&(r+=e(s.content)),["paragraph","heading","listItem"].includes(s.type)&&(r+=" ");return r.trim()}(t.content)}catch(e){}return e.replace(/<[^>]*>/g," ").replace(/\s+/g," ").trim()}},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>d,Zp:()=>i,aR:()=>n});var s=r(95155);r(12115);var a=r(59434);function i(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function n(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...r})}function l(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...r})}function c(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...r})}},69074:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},84886:(e,t,r)=>{Promise.resolve().then(r.bind(r,24269))}},e=>{var t=t=>e(e.s=t);e.O(0,[4854,8858,6766,8441,1684,7358],()=>t(84886)),_N_E=e.O()}]);