"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9951],{5196:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},13717:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},15452:(e,t,r)=>{r.d(t,{UC:()=>er,VY:()=>ea,ZL:()=>ee,bL:()=>Q,bm:()=>eo,hE:()=>en,hJ:()=>et,l9:()=>$});var n=r(12115),a=r(85185),o=r(6101),l=r(46081),s=r(61285),i=r(5845),d=r(19178),u=r(25519),c=r(34378),p=r(28905),f=r(63655),h=r(92293),v=r(93795),y=r(38168),g=r(99708),m=r(95155),x="Dialog",[k,b]=(0,l.A)(x),[j,D]=k(x),w=e=>{let{__scopeDialog:t,children:r,open:a,defaultOpen:o,onOpenChange:l,modal:d=!0}=e,u=n.useRef(null),c=n.useRef(null),[p,f]=(0,i.i)({prop:a,defaultProp:null!=o&&o,onChange:l,caller:x});return(0,m.jsx)(j,{scope:t,triggerRef:u,contentRef:c,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};w.displayName=x;var C="DialogTrigger",R=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=D(C,r),s=(0,o.s)(t,l.triggerRef);return(0,m.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":z(l.open),...n,ref:s,onClick:(0,a.m)(e.onClick,l.onOpenToggle)})});R.displayName=C;var A="DialogPortal",[E,I]=k(A,{forceMount:void 0}),M=e=>{let{__scopeDialog:t,forceMount:r,children:a,container:o}=e,l=D(A,t);return(0,m.jsx)(E,{scope:t,forceMount:r,children:n.Children.map(a,e=>(0,m.jsx)(p.C,{present:r||l.open,children:(0,m.jsx)(c.Z,{asChild:!0,container:o,children:e})}))})};M.displayName=A;var N="DialogOverlay",_=n.forwardRef((e,t)=>{let r=I(N,e.__scopeDialog),{forceMount:n=r.forceMount,...a}=e,o=D(N,e.__scopeDialog);return o.modal?(0,m.jsx)(p.C,{present:n||o.open,children:(0,m.jsx)(F,{...a,ref:t})}):null});_.displayName=N;var O=(0,g.TL)("DialogOverlay.RemoveScroll"),F=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=D(N,r);return(0,m.jsx)(v.A,{as:O,allowPinchZoom:!0,shards:[a.contentRef],children:(0,m.jsx)(f.sG.div,{"data-state":z(a.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),P="DialogContent",L=n.forwardRef((e,t)=>{let r=I(P,e.__scopeDialog),{forceMount:n=r.forceMount,...a}=e,o=D(P,e.__scopeDialog);return(0,m.jsx)(p.C,{present:n||o.open,children:o.modal?(0,m.jsx)(G,{...a,ref:t}):(0,m.jsx)(T,{...a,ref:t})})});L.displayName=P;var G=n.forwardRef((e,t)=>{let r=D(P,e.__scopeDialog),l=n.useRef(null),s=(0,o.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,y.Eq)(e)},[]),(0,m.jsx)(q,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,a.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=n.forwardRef((e,t)=>{let r=D(P,e.__scopeDialog),a=n.useRef(!1),o=n.useRef(!1);return(0,m.jsx)(q,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,l;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(a.current||null==(l=r.triggerRef.current)||l.focus(),t.preventDefault()),a.current=!1,o.current=!1},onInteractOutside:t=>{var n,l;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(a.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let s=t.target;(null==(l=r.triggerRef.current)?void 0:l.contains(s))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),q=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:a,onOpenAutoFocus:l,onCloseAutoFocus:s,...i}=e,c=D(P,r),p=n.useRef(null),f=(0,o.s)(t,p);return(0,h.Oh)(),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(u.n,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:l,onUnmountAutoFocus:s,children:(0,m.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":z(c.open),...i,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(J,{titleId:c.titleId}),(0,m.jsx)(Y,{contentRef:p,descriptionId:c.descriptionId})]})]})}),B="DialogTitle",S=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=D(B,r);return(0,m.jsx)(f.sG.h2,{id:a.titleId,...n,ref:t})});S.displayName=B;var H="DialogDescription",Z=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=D(H,r);return(0,m.jsx)(f.sG.p,{id:a.descriptionId,...n,ref:t})});Z.displayName=H;var V="DialogClose",W=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(V,r);return(0,m.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,a.m)(e.onClick,()=>o.onOpenChange(!1))})});function z(e){return e?"open":"closed"}W.displayName=V;var K="DialogTitleWarning",[U,X]=(0,l.q)(K,{contentName:P,titleName:B,docsSlug:"dialog"}),J=e=>{let{titleId:t}=e,r=X(K),a="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(a))},[a,t]),null},Y=e=>{let{contentRef:t,descriptionId:r}=e,a=X("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(a.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(o))},[o,t,r]),null},Q=w,$=R,ee=M,et=_,er=L,en=S,ea=Z,eo=W},27213:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},29869:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},38564:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},40968:(e,t,r)=>{r.d(t,{b:()=>s});var n=r(12115),a=r(63655),o=r(95155),l=n.forwardRef((e,t)=>(0,o.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var s=l},45503:(e,t,r)=>{r.d(t,{Z:()=>a});var n=r(12115);function a(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},54416:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},62525:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},76981:(e,t,r)=>{r.d(t,{C1:()=>D,bL:()=>b});var n=r(12115),a=r(6101),o=r(46081),l=r(85185),s=r(5845),i=r(45503),d=r(11275),u=r(28905),c=r(63655),p=r(95155),f="Checkbox",[h,v]=(0,o.A)(f),[y,g]=h(f);function m(e){let{__scopeCheckbox:t,checked:r,children:a,defaultChecked:o,disabled:l,form:i,name:d,onCheckedChange:u,required:c,value:h="on",internal_do_not_use_render:v}=e,[g,m]=(0,s.i)({prop:r,defaultProp:null!=o&&o,onChange:u,caller:f}),[x,k]=n.useState(null),[b,j]=n.useState(null),D=n.useRef(!1),w=!x||!!i||!!x.closest("form"),C={checked:g,disabled:l,setChecked:m,control:x,setControl:k,name:d,form:i,value:h,hasConsumerStoppedPropagationRef:D,required:c,defaultChecked:!R(o)&&o,isFormControl:w,bubbleInput:b,setBubbleInput:j};return(0,p.jsx)(y,{scope:t,...C,children:"function"==typeof v?v(C):a})}var x="CheckboxTrigger",k=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,onKeyDown:o,onClick:s,...i}=e,{control:d,value:u,disabled:f,checked:h,required:v,setControl:y,setChecked:m,hasConsumerStoppedPropagationRef:k,isFormControl:b,bubbleInput:j}=g(x,r),D=(0,a.s)(t,y),w=n.useRef(h);return n.useEffect(()=>{let e=null==d?void 0:d.form;if(e){let t=()=>m(w.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[d,m]),(0,p.jsx)(c.sG.button,{type:"button",role:"checkbox","aria-checked":R(h)?"mixed":h,"aria-required":v,"data-state":A(h),"data-disabled":f?"":void 0,disabled:f,value:u,...i,ref:D,onKeyDown:(0,l.m)(o,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(s,e=>{m(e=>!!R(e)||!e),j&&b&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})})});k.displayName=x;var b=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:a,defaultChecked:o,required:l,disabled:s,value:i,onCheckedChange:d,form:u,...c}=e;return(0,p.jsx)(m,{__scopeCheckbox:r,checked:a,defaultChecked:o,disabled:s,required:l,onCheckedChange:d,name:n,form:u,value:i,internal_do_not_use_render:e=>{let{isFormControl:n}=e;return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(k,{...c,ref:t,__scopeCheckbox:r}),n&&(0,p.jsx)(C,{__scopeCheckbox:r})]})}})});b.displayName=f;var j="CheckboxIndicator",D=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...a}=e,o=g(j,r);return(0,p.jsx)(u.C,{present:n||R(o.checked)||!0===o.checked,children:(0,p.jsx)(c.sG.span,{"data-state":A(o.checked),"data-disabled":o.disabled?"":void 0,...a,ref:t,style:{pointerEvents:"none",...e.style}})})});D.displayName=j;var w="CheckboxBubbleInput",C=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,...o}=e,{control:l,hasConsumerStoppedPropagationRef:s,checked:u,defaultChecked:f,required:h,disabled:v,name:y,value:m,form:x,bubbleInput:k,setBubbleInput:b}=g(w,r),j=(0,a.s)(t,b),D=(0,i.Z)(u),C=(0,d.X)(l);n.useEffect(()=>{if(!k)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!s.current;if(D!==u&&e){let r=new Event("click",{bubbles:t});k.indeterminate=R(u),e.call(k,!R(u)&&u),k.dispatchEvent(r)}},[k,D,u,s]);let A=n.useRef(!R(u)&&u);return(0,p.jsx)(c.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=f?f:A.current,required:h,disabled:v,name:y,value:m,form:x,...o,tabIndex:-1,ref:j,style:{...o.style,...C,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function R(e){return"indeterminate"===e}function A(e){return R(e)?"indeterminate":e?"checked":"unchecked"}C.displayName=w},84616:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}}]);