"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Calendar, MapPin, ExternalLink, Briefcase, ArrowRight } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { api, getPublishedExperiences, type Experience } from "@/lib/api";
import Image from "next/image";
import Link from "next/link";

// Fallback experiences data
const fallbackExperiences = [
  {
    title: "Senior Full Stack Developer",
    company: "TechCorp Solutions",
    location: "Mumbai, India",
    period: "2022 - Present",
    type: "Full-time",
    description: "Leading development of scalable web applications using React, Next.js, and Node.js. Mentoring junior developers and implementing best practices.",
    achievements: [
      "Increased application performance by 40%",
      "Led a team of 5 developers",
      "Implemented CI/CD pipelines"
    ],
    technologies: ["React", "Next.js", "TypeScript", "Node.js", "PostgreSQL", "AWS"],
  },
  {
    title: "Full Stack Developer",
    company: "StartupXYZ",
    location: "Remote",
    period: "2021 - 2022",
    type: "Full-time",
    description: "Developed and maintained multiple client projects using modern web technologies. Collaborated with design teams to create pixel-perfect interfaces.",
    achievements: [
      "Built 10+ responsive web applications",
      "Reduced page load times by 50%",
      "Implemented real-time features"
    ],
    technologies: ["React", "Vue.js", "Express.js", "MongoDB", "Firebase"],
  },
  {
    title: "Frontend Developer",
    company: "Digital Agency Pro",
    location: "Mumbai, India",
    period: "2020 - 2021",
    type: "Full-time",
    description: "Focused on creating beautiful and functional user interfaces for various client projects. Worked closely with designers to bring mockups to life.",
    achievements: [
      "Delivered 20+ client projects on time",
      "Improved client satisfaction by 25%",
      "Created reusable component library"
    ],
    technologies: ["HTML", "CSS", "JavaScript", "React", "Sass", "Figma"],
  }
];

export function ExperienceSection() {
  const [experiences, setExperiences] = useState<Experience[]>([]);
  const [loading, setLoading] = useState(true);
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  useEffect(() => {
    const fetchExperiences = async () => {
      try {
        const data = await api.getExperiences();
        const publishedExperiences = getPublishedExperiences(data);
        // Show only the first 3 experiences for the home page
        setExperiences(publishedExperiences.slice(0, 3));
      } catch (error) {
        console.error('Failed to fetch experiences:', error);
        // Use fallback data if API fails
        setExperiences(fallbackExperiences.map((exp, index) => ({
          id: `fallback-${index}`,
          title: exp.title,
          company: exp.company,
          companyLogo: undefined,
          location: exp.location,
          period: exp.period,
          type: exp.type,
          description: exp.description,
          achievements: exp.achievements,
          technologies: exp.technologies,
          website: undefined,
          order: index,
          published: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })));
      } finally {
        setLoading(false);
      }
    };

    fetchExperiences();
  }, []);

  if (loading) {
    return (
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="animate-pulse space-y-8">
            <div className="text-center">
              <div className="h-12 bg-gray-300 rounded w-64 mx-auto mb-4"></div>
              <div className="h-6 bg-gray-300 rounded w-96 mx-auto"></div>
            </div>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-80 bg-gray-300 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section ref={ref} className="py-20 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
            <span className="gradient-text">Work Experience</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            My professional journey in web development, building scalable applications and leading development teams.
          </p>
        </motion.div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {experiences.map((experience, index) => (
            <motion.div
              key={experience.id}
              initial={{ opacity: 0, y: 50 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: index * 0.2 }}
            >
              <Card className="h-full hover:shadow-lg transition-shadow duration-300 border-border/50">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4 mb-4">
                    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                      {experience.companyLogo ? (
                        <Image
                          src={experience.companyLogo}
                          alt={`${experience.company} logo`}
                          width={32}
                          height={32}
                          className="rounded-lg object-contain"
                        />
                      ) : (
                        <Briefcase className="h-6 w-6 text-primary" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-lg leading-tight mb-1">
                        {experience.title}
                      </h3>
                      <p className="text-primary font-medium mb-2">
                        {experience.company}
                      </p>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-1" />
                          {experience.location}
                        </div>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          {experience.period}
                        </div>
                      </div>
                    </div>
                  </div>

                  <p className="text-muted-foreground text-sm mb-4 line-clamp-3">
                    {experience.description}
                  </p>

                  <div className="mb-4">
                    <h4 className="font-medium text-sm mb-2">Key Achievements:</h4>
                    <ul className="space-y-1">
                      {experience.achievements.slice(0, 2).map((achievement, achievementIndex) => (
                        <li key={achievementIndex} className="flex items-start space-x-2 text-xs text-muted-foreground">
                          <div className="w-1 h-1 rounded-full bg-primary mt-2 flex-shrink-0" />
                          <span className="line-clamp-1">{achievement}</span>
                        </li>
                      ))}
                      {experience.achievements.length > 2 && (
                        <li className="text-xs text-muted-foreground">
                          +{experience.achievements.length - 2} more achievements
                        </li>
                      )}
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-medium text-sm mb-2">Technologies:</h4>
                    <div className="flex flex-wrap gap-1">
                      {experience.technologies.slice(0, 4).map((tech) => (
                        <Badge key={tech} variant="secondary" className="text-xs px-2 py-1">
                          {tech}
                        </Badge>
                      ))}
                      {experience.technologies.length > 4 && (
                        <Badge variant="outline" className="text-xs px-2 py-1">
                          +{experience.technologies.length - 4}
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <Link href="/about#experience">
            <Button variant="outline" size="lg" className="group">
              View Full Experience
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </motion.div>
      </div>
    </section>
  );
}
