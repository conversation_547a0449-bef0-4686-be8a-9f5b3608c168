(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{26126:(e,s,t)=>{"use strict";t.d(s,{E:()=>c});var r=t(95155);t(12115);var i=t(99708),a=t(74466),n=t(59434);let l=(0,a.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:s,variant:t,asChild:a=!1,...c}=e,o=a?i.DX:"span";return(0,r.jsx)(o,{"data-slot":"badge",className:(0,n.cn)(l({variant:t}),s),...c})}},44879:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var r=t(95155),i=t(12115),a=t(66695),n=t(26126),l=t(83930),c=t(14395),o=t(57434),d=t(17576),x=t(81497);function h(){let[e,s]=(0,i.useState)({projects:0,blogPosts:0,services:0,testimonials:0}),[t,h]=(0,i.useState)(!0);(0,i.useEffect)(()=>{(async()=>{try{let[e,t,r,i]=await Promise.all([fetch("/api/projects"),fetch("/api/blog"),fetch("/api/services"),fetch("/api/testimonials")]),[a,n,l,c]=await Promise.all([e.json(),t.json(),r.json(),i.json()]);s({projects:a.length||0,blogPosts:n.length||0,services:l.length||0,testimonials:c.length||0})}catch(e){console.error("Error fetching stats:",e)}finally{h(!1)}})()},[]);let u=[{title:"Projects",value:e.projects,description:"Total portfolio projects",icon:c.A,color:"text-blue-600"},{title:"Blog Posts",value:e.blogPosts,description:"Published articles",icon:o.A,color:"text-green-600"},{title:"Services",value:e.services,description:"Available services",icon:d.A,color:"text-purple-600"},{title:"Testimonials",value:e.testimonials,description:"Client reviews",icon:x.A,color:"text-orange-600"}];return(0,r.jsx)(l.DashboardLayout,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Dashboard"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Welcome to your portfolio content management system"})]}),(0,r.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:u.map(e=>(0,r.jsxs)(a.Zp,{children:[(0,r.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ZB,{className:"text-sm font-medium",children:e.title}),(0,r.jsx)(e.icon,{className:"h-4 w-4 ".concat(e.color)})]}),(0,r.jsxs)(a.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:t?"...":e.value}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]})]},e.title))}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,r.jsxs)(a.Zp,{children:[(0,r.jsxs)(a.aR,{children:[(0,r.jsx)(a.ZB,{children:"Quick Actions"}),(0,r.jsx)(a.BT,{children:"Common tasks to manage your portfolio content"})]}),(0,r.jsxs)(a.Wu,{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Add New Project"}),(0,r.jsx)(n.E,{variant:"secondary",children:"Create"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Write Blog Post"}),(0,r.jsx)(n.E,{variant:"secondary",children:"Write"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Update Services"}),(0,r.jsx)(n.E,{variant:"secondary",children:"Edit"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Manage Testimonials"}),(0,r.jsx)(n.E,{variant:"secondary",children:"Review"})]})]})]}),(0,r.jsxs)(a.Zp,{children:[(0,r.jsxs)(a.aR,{children:[(0,r.jsx)(a.ZB,{children:"Recent Activity"}),(0,r.jsx)(a.BT,{children:"Latest changes to your portfolio content"})]}),(0,r.jsx)(a.Wu,{children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center text-sm",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mr-2"}),"Database initialized successfully"]}),(0,r.jsxs)("div",{className:"flex items-center text-sm",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full mr-2"}),"Sample data seeded"]}),(0,r.jsxs)("div",{className:"flex items-center text-sm",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full mr-2"}),"CMS dashboard ready"]})]})})]})]})]})})}},54376:(e,s,t)=>{Promise.resolve().then(t.bind(t,44879))}},e=>{var s=s=>e(e.s=s);e.O(0,[4854,2108,8858,9368,9008,8441,1684,7358],()=>s(54376)),_N_E=e.O()}]);