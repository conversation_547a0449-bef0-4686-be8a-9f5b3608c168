"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[27],{518:(e,t,n)=>{n.d(t,{bm:()=>ts,UC:()=>ti,hJ:()=>tr,ZL:()=>tn,bL:()=>tt,hE:()=>to});var r,i,o,s=n(2115),a=n.t(s,2);function l(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var u=n(6101),c=n(6081),d=n(2712),h=a[" useId ".trim().toString()]||(()=>void 0),p=0;function m(e){let[t,n]=s.useState(h());return(0,d.N)(()=>{e||n(e=>e??String(p++))},[e]),e||(t?`radix-${t}`:"")}var f=a[" useInsertionEffect ".trim().toString()]||d.N,g=(Symbol("RADIX:SYNC_STATE"),n(3655)),v=n(9033),y=n(5155),b="dismissableLayer.update",x=s.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),w=s.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:a,onPointerDownOutside:c,onFocusOutside:d,onInteractOutside:h,onDismiss:p,...m}=e,f=s.useContext(x),[w,T]=s.useState(null),P=null!=(r=null==w?void 0:w.ownerDocument)?r:null==(n=globalThis)?void 0:n.document,[,S]=s.useState({}),A=(0,u.s)(t,e=>T(e)),M=Array.from(f.layers),[C]=[...f.layersWithOutsidePointerEventsDisabled].slice(-1),R=M.indexOf(C),D=w?M.indexOf(w):-1,j=f.layersWithOutsidePointerEventsDisabled.size>0,V=D>=R,L=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,v.c)(e),i=s.useRef(!1),o=s.useRef(()=>{});return s.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){E("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",o.current),o.current=t,n.addEventListener("click",o.current,{once:!0})):t()}else n.removeEventListener("click",o.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",o.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...f.branches].some(e=>e.contains(t));V&&!n&&(null==c||c(e),null==h||h(e),e.defaultPrevented||null==p||p())},P),O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,v.c)(e),i=s.useRef(!1);return s.useEffect(()=>{let e=e=>{e.target&&!i.current&&E("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...f.branches].some(e=>e.contains(t))&&(null==d||d(e),null==h||h(e),e.defaultPrevented||null==p||p())},P);return!function(e,t=globalThis?.document){let n=(0,v.c)(e);s.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{D===f.layers.size-1&&(null==a||a(e),!e.defaultPrevented&&p&&(e.preventDefault(),p()))},P),s.useEffect(()=>{if(w)return o&&(0===f.layersWithOutsidePointerEventsDisabled.size&&(i=P.body.style.pointerEvents,P.body.style.pointerEvents="none"),f.layersWithOutsidePointerEventsDisabled.add(w)),f.layers.add(w),k(),()=>{o&&1===f.layersWithOutsidePointerEventsDisabled.size&&(P.body.style.pointerEvents=i)}},[w,P,o,f]),s.useEffect(()=>()=>{w&&(f.layers.delete(w),f.layersWithOutsidePointerEventsDisabled.delete(w),k())},[w,f]),s.useEffect(()=>{let e=()=>S({});return document.addEventListener(b,e),()=>document.removeEventListener(b,e)},[]),(0,y.jsx)(g.sG.div,{...m,ref:A,style:{pointerEvents:j?V?"auto":"none":void 0,...e.style},onFocusCapture:l(e.onFocusCapture,O.onFocusCapture),onBlurCapture:l(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:l(e.onPointerDownCapture,L.onPointerDownCapture)})});function k(){let e=new CustomEvent(b);document.dispatchEvent(e)}function E(e,t,n,r){let{discrete:i}=r,o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),i?(0,g.hO)(o,s):o.dispatchEvent(s)}w.displayName="DismissableLayer",s.forwardRef((e,t)=>{let n=s.useContext(x),r=s.useRef(null),i=(0,u.s)(t,r);return s.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,y.jsx)(g.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var T="focusScope.autoFocusOnMount",P="focusScope.autoFocusOnUnmount",S={bubbles:!1,cancelable:!0},A=s.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:i,onUnmountAutoFocus:o,...a}=e,[l,c]=s.useState(null),d=(0,v.c)(i),h=(0,v.c)(o),p=s.useRef(null),m=(0,u.s)(t,e=>c(e)),f=s.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;s.useEffect(()=>{if(r){let e=function(e){if(f.paused||!l)return;let t=e.target;l.contains(t)?p.current=t:R(p.current,{select:!0})},t=function(e){if(f.paused||!l)return;let t=e.relatedTarget;null!==t&&(l.contains(t)||R(p.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&R(l)});return l&&n.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,l,f.paused]),s.useEffect(()=>{if(l){D.add(f);let e=document.activeElement;if(!l.contains(e)){let t=new CustomEvent(T,S);l.addEventListener(T,d),l.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(R(r,{select:t}),document.activeElement!==n)return}(M(l).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&R(l))}return()=>{l.removeEventListener(T,d),setTimeout(()=>{let t=new CustomEvent(P,S);l.addEventListener(P,h),l.dispatchEvent(t),t.defaultPrevented||R(null!=e?e:document.body,{select:!0}),l.removeEventListener(P,h),D.remove(f)},0)}}},[l,d,h,f]);let b=s.useCallback(e=>{if(!n&&!r||f.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,i=document.activeElement;if(t&&i){let t=e.currentTarget,[r,o]=function(e){let t=M(e);return[C(t,e),C(t.reverse(),e)]}(t);r&&o?e.shiftKey||i!==o?e.shiftKey&&i===r&&(e.preventDefault(),n&&R(o,{select:!0})):(e.preventDefault(),n&&R(r,{select:!0})):i===t&&e.preventDefault()}},[n,r,f.paused]);return(0,y.jsx)(g.sG.div,{tabIndex:-1,...a,ref:m,onKeyDown:b})});function M(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function C(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function R(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}A.displayName="FocusScope";var D=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=j(e,t)).unshift(t)},remove(t){var n;null==(n=(e=j(e,t))[0])||n.resume()}}}();function j(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var V=n(7650),L=s.forwardRef((e,t)=>{var n,r;let{container:i,...o}=e,[a,l]=s.useState(!1);(0,d.N)(()=>l(!0),[]);let u=i||a&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return u?V.createPortal((0,y.jsx)(g.sG.div,{...o,ref:t}),u):null});L.displayName="Portal";var O=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,i]=s.useState(),o=s.useRef(null),a=s.useRef(e),l=s.useRef("none"),[u,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},s.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return s.useEffect(()=>{let e=N(o.current);l.current="mounted"===u?e:"none"},[u]),(0,d.N)(()=>{let t=o.current,n=a.current;if(n!==e){let r=l.current,i=N(t);e?c("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==i?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),(0,d.N)(()=>{if(r){var e;let t,n=null!=(e=r.ownerDocument.defaultView)?e:window,i=e=>{let i=N(o.current).includes(e.animationName);if(e.target===r&&i&&(c("ANIMATION_END"),!a.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},s=e=>{e.target===r&&(l.current=N(o.current))};return r.addEventListener("animationstart",s),r.addEventListener("animationcancel",i),r.addEventListener("animationend",i),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",s),r.removeEventListener("animationcancel",i),r.removeEventListener("animationend",i)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:s.useCallback(e=>{o.current=e?getComputedStyle(e):null,i(e)},[])}}(t),i="function"==typeof n?n({present:r.isPresent}):s.Children.only(n),o=(0,u.s)(r.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,i=r&&"isReactWarning"in r&&r.isReactWarning;return i?e.ref:(i=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||r.isPresent?s.cloneElement(i,{ref:o}):null};function N(e){return(null==e?void 0:e.animationName)||"none"}O.displayName="Presence";var F=0;function I(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var B=function(){return(B=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function U(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)0>t.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n}Object.create;Object.create;var z=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),W="width-before-scroll-bar";function _(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var $="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,H=new WeakMap;function K(e){return e}var X=function(e){void 0===e&&(e={});var t,n,r,i,o=(t=null,void 0===n&&(n=K),r=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,i);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){i=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var o=function(){var n=t;t=[],n.forEach(e)},s=function(){return Promise.resolve().then(o)};s(),r={push:function(e){t.push(e),s()},filter:function(e){return t=t.filter(e),r}}}});return o.options=B({async:!0,ssr:!1},e),o}(),Y=function(){},q=s.forwardRef(function(e,t){var n,r,i,o,a=s.useRef(null),l=s.useState({onScrollCapture:Y,onWheelCapture:Y,onTouchMoveCapture:Y}),u=l[0],c=l[1],d=e.forwardProps,h=e.children,p=e.className,m=e.removeScrollBar,f=e.enabled,g=e.shards,v=e.sideCar,y=e.noRelative,b=e.noIsolation,x=e.inert,w=e.allowPinchZoom,k=e.as,E=e.gapMode,T=U(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),P=(n=[a,t],r=function(e){return n.forEach(function(t){return _(t,e)})},(i=(0,s.useState)(function(){return{value:null,callback:r,facade:{get current(){return i.value},set current(value){var e=i.value;e!==value&&(i.value=value,i.callback(value,e))}}}})[0]).callback=r,o=i.facade,$(function(){var e=H.get(o);if(e){var t=new Set(e),r=new Set(n),i=o.current;t.forEach(function(e){r.has(e)||_(e,null)}),r.forEach(function(e){t.has(e)||_(e,i)})}H.set(o,n)},[n]),o),S=B(B({},T),u);return s.createElement(s.Fragment,null,f&&s.createElement(v,{sideCar:X,removeScrollBar:m,shards:g,noRelative:y,noIsolation:b,inert:x,setCallbacks:c,allowPinchZoom:!!w,lockRef:a,gapMode:E}),d?s.cloneElement(s.Children.only(h),B(B({},S),{ref:P})):s.createElement(void 0===k?"div":k,B({},S,{className:p,ref:P}),h))});q.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},q.classNames={fullWidth:W,zeroRight:z};var G=function(e){var t=e.sideCar,n=U(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return s.createElement(r,B({},n))};G.isSideCarExport=!0;var Z=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,s;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),s=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(s)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Q=function(){var e=Z();return function(t,n){s.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},J=function(){var e=Q();return function(t){return e(t.styles,t.dynamic),null}},ee={left:0,top:0,right:0,gap:0},et=function(e){return parseInt(e||"",10)||0},en=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],i=t["padding"===e?"paddingRight":"marginRight"];return[et(n),et(r),et(i)]},er=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return ee;var t=en(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},ei=J(),eo="data-scroll-locked",es=function(e,t,n,r){var i=e.left,o=e.top,s=e.right,a=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(a,"px ").concat(r,";\n  }\n  body[").concat(eo,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(i,"px;\n    padding-top: ").concat(o,"px;\n    padding-right: ").concat(s,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(a,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(z," {\n    right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(W," {\n    margin-right: ").concat(a,"px ").concat(r,";\n  }\n  \n  .").concat(z," .").concat(z," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(W," .").concat(W," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(eo,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(a,"px;\n  }\n")},ea=function(){var e=parseInt(document.body.getAttribute(eo)||"0",10);return isFinite(e)?e:0},el=function(){s.useEffect(function(){return document.body.setAttribute(eo,(ea()+1).toString()),function(){var e=ea()-1;e<=0?document.body.removeAttribute(eo):document.body.setAttribute(eo,e.toString())}},[])},eu=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,i=void 0===r?"margin":r;el();var o=s.useMemo(function(){return er(i)},[i]);return s.createElement(ei,{styles:es(o,!t,i,n?"":"!important")})},ec=!1;if("undefined"!=typeof window)try{var ed=Object.defineProperty({},"passive",{get:function(){return ec=!0,!0}});window.addEventListener("test",ed,ed),window.removeEventListener("test",ed,ed)}catch(e){ec=!1}var eh=!!ec&&{passive:!1},ep=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},em=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),ef(e,r)){var i=eg(e,r);if(i[1]>i[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},ef=function(e,t){return"v"===e?ep(t,"overflowY"):ep(t,"overflowX")},eg=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},ev=function(e,t,n,r,i){var o,s=(o=window.getComputedStyle(t).direction,"h"===e&&"rtl"===o?-1:1),a=s*r,l=n.target,u=t.contains(l),c=!1,d=a>0,h=0,p=0;do{if(!l)break;var m=eg(e,l),f=m[0],g=m[1]-m[2]-s*f;(f||g)&&ef(e,l)&&(h+=g,p+=f);var v=l.parentNode;l=v&&v.nodeType===Node.DOCUMENT_FRAGMENT_NODE?v.host:v}while(!u&&l!==document.body||u&&(t.contains(l)||t===l));return d&&(i&&1>Math.abs(h)||!i&&a>h)?c=!0:!d&&(i&&1>Math.abs(p)||!i&&-a>p)&&(c=!0),c},ey=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},eb=function(e){return[e.deltaX,e.deltaY]},ex=function(e){return e&&"current"in e?e.current:e},ew=0,ek=[];let eE=(r=function(e){var t=s.useRef([]),n=s.useRef([0,0]),r=s.useRef(),i=s.useState(ew++)[0],o=s.useState(J)[0],a=s.useRef(e);s.useEffect(function(){a.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(i));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(ex),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(i))}),function(){document.body.classList.remove("block-interactivity-".concat(i)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(i))})}}},[e.inert,e.lockRef.current,e.shards]);var l=s.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var i,o=ey(e),s=n.current,l="deltaX"in e?e.deltaX:s[0]-o[0],u="deltaY"in e?e.deltaY:s[1]-o[1],c=e.target,d=Math.abs(l)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var h=em(d,c);if(!h)return!0;if(h?i=d:(i="v"===d?"h":"v",h=em(d,c)),!h)return!1;if(!r.current&&"changedTouches"in e&&(l||u)&&(r.current=i),!i)return!0;var p=r.current||i;return ev(p,t,e,"h"===p?l:u,!0)},[]),u=s.useCallback(function(e){if(ek.length&&ek[ek.length-1]===o){var n="deltaY"in e?eb(e):ey(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var i=(a.current.shards||[]).map(ex).filter(Boolean).filter(function(t){return t.contains(e.target)});(i.length>0?l(e,i[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=s.useCallback(function(e,n,r,i){var o={name:e,delta:n,target:r,should:i,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(o),setTimeout(function(){t.current=t.current.filter(function(e){return e!==o})},1)},[]),d=s.useCallback(function(e){n.current=ey(e),r.current=void 0},[]),h=s.useCallback(function(t){c(t.type,eb(t),t.target,l(t,e.lockRef.current))},[]),p=s.useCallback(function(t){c(t.type,ey(t),t.target,l(t,e.lockRef.current))},[]);s.useEffect(function(){return ek.push(o),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:p}),document.addEventListener("wheel",u,eh),document.addEventListener("touchmove",u,eh),document.addEventListener("touchstart",d,eh),function(){ek=ek.filter(function(e){return e!==o}),document.removeEventListener("wheel",u,eh),document.removeEventListener("touchmove",u,eh),document.removeEventListener("touchstart",d,eh)}},[]);var m=e.removeScrollBar,f=e.inert;return s.createElement(s.Fragment,null,f?s.createElement(o,{styles:"\n  .block-interactivity-".concat(i," {pointer-events: none;}\n  .allow-interactivity-").concat(i," {pointer-events: all;}\n")}):null,m?s.createElement(eu,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},X.useMedium(r),G);var eT=s.forwardRef(function(e,t){return s.createElement(q,B({},e,{ref:t,sideCar:eE}))});eT.classNames=q.classNames;var eP=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},eS=new WeakMap,eA=new WeakMap,eM={},eC=0,eR=function(e){return e&&(e.host||eR(e.parentNode))},eD=function(e,t,n,r){var i=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=eR(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});eM[n]||(eM[n]=new WeakMap);var o=eM[n],s=[],a=new Set,l=new Set(i),u=function(e){!e||a.has(e)||(a.add(e),u(e.parentNode))};i.forEach(u);var c=function(e){!e||l.has(e)||Array.prototype.forEach.call(e.children,function(e){if(a.has(e))c(e);else try{var t=e.getAttribute(r),i=null!==t&&"false"!==t,l=(eS.get(e)||0)+1,u=(o.get(e)||0)+1;eS.set(e,l),o.set(e,u),s.push(e),1===l&&i&&eA.set(e,!0),1===u&&e.setAttribute(n,"true"),i||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return c(t),a.clear(),eC++,function(){s.forEach(function(e){var t=eS.get(e)-1,i=o.get(e)-1;eS.set(e,t),o.set(e,i),t||(eA.has(e)||e.removeAttribute(r),eA.delete(e)),i||e.removeAttribute(n)}),--eC||(eS=new WeakMap,eS=new WeakMap,eA=new WeakMap,eM={})}},ej=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),i=t||eP(e);return i?(r.push.apply(r,Array.from(i.querySelectorAll("[aria-live], script"))),eD(r,i,n,"aria-hidden")):function(){return null}},eV=n(9708),eL="Dialog",[eO,eN]=(0,c.A)(eL),[eF,eI]=eO(eL),eB=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:i,onOpenChange:o,modal:a=!0}=e,l=s.useRef(null),u=s.useRef(null),[c,d]=function({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,o,a]=function({defaultProp:e,onChange:t}){let[n,r]=s.useState(e),i=s.useRef(n),o=s.useRef(t);return f(()=>{o.current=t},[t]),s.useEffect(()=>{i.current!==n&&(o.current?.(n),i.current=n)},[n,i]),[n,r,o]}({defaultProp:t,onChange:n}),l=void 0!==e,u=l?e:i;{let t=s.useRef(void 0!==e);s.useEffect(()=>{let e=t.current;if(e!==l){let t=l?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,r])}return[u,s.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else o(t)},[l,e,o,a])]}({prop:r,defaultProp:null!=i&&i,onChange:o,caller:eL});return(0,y.jsx)(eF,{scope:t,triggerRef:l,contentRef:u,contentId:m(),titleId:m(),descriptionId:m(),open:c,onOpenChange:d,onOpenToggle:s.useCallback(()=>d(e=>!e),[d]),modal:a,children:n})};eB.displayName=eL;var eU="DialogTrigger";s.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=eI(eU,n),o=(0,u.s)(t,i.triggerRef);return(0,y.jsx)(g.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":e4(i.open),...r,ref:o,onClick:l(e.onClick,i.onOpenToggle)})}).displayName=eU;var ez="DialogPortal",[eW,e_]=eO(ez,{forceMount:void 0}),e$=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:i}=e,o=eI(ez,t);return(0,y.jsx)(eW,{scope:t,forceMount:n,children:s.Children.map(r,e=>(0,y.jsx)(O,{present:n||o.open,children:(0,y.jsx)(L,{asChild:!0,container:i,children:e})}))})};e$.displayName=ez;var eH="DialogOverlay",eK=s.forwardRef((e,t)=>{let n=e_(eH,e.__scopeDialog),{forceMount:r=n.forceMount,...i}=e,o=eI(eH,e.__scopeDialog);return o.modal?(0,y.jsx)(O,{present:r||o.open,children:(0,y.jsx)(eY,{...i,ref:t})}):null});eK.displayName=eH;var eX=(0,eV.TL)("DialogOverlay.RemoveScroll"),eY=s.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=eI(eH,n);return(0,y.jsx)(eT,{as:eX,allowPinchZoom:!0,shards:[i.contentRef],children:(0,y.jsx)(g.sG.div,{"data-state":e4(i.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),eq="DialogContent",eG=s.forwardRef((e,t)=>{let n=e_(eq,e.__scopeDialog),{forceMount:r=n.forceMount,...i}=e,o=eI(eq,e.__scopeDialog);return(0,y.jsx)(O,{present:r||o.open,children:o.modal?(0,y.jsx)(eZ,{...i,ref:t}):(0,y.jsx)(eQ,{...i,ref:t})})});eG.displayName=eq;var eZ=s.forwardRef((e,t)=>{let n=eI(eq,e.__scopeDialog),r=s.useRef(null),i=(0,u.s)(t,n.contentRef,r);return s.useEffect(()=>{let e=r.current;if(e)return ej(e)},[]),(0,y.jsx)(eJ,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:l(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:l(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:l(e.onFocusOutside,e=>e.preventDefault())})}),eQ=s.forwardRef((e,t)=>{let n=eI(eq,e.__scopeDialog),r=s.useRef(!1),i=s.useRef(!1);return(0,y.jsx)(eJ,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,s;null==(o=e.onCloseAutoFocus)||o.call(e,t),t.defaultPrevented||(r.current||null==(s=n.triggerRef.current)||s.focus(),t.preventDefault()),r.current=!1,i.current=!1},onInteractOutside:t=>{var o,s;null==(o=e.onInteractOutside)||o.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let a=t.target;(null==(s=n.triggerRef.current)?void 0:s.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),eJ=s.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:i,onCloseAutoFocus:o,...a}=e,l=eI(eq,n),c=s.useRef(null),d=(0,u.s)(t,c);return s.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:I()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:I()),F++,()=>{1===F&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),F--}},[]),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(A,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:i,onUnmountAutoFocus:o,children:(0,y.jsx)(w,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":e4(l.open),...a,ref:d,onDismiss:()=>l.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(e7,{titleId:l.titleId}),(0,y.jsx)(te,{contentRef:c,descriptionId:l.descriptionId})]})]})}),e0="DialogTitle",e1=s.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=eI(e0,n);return(0,y.jsx)(g.sG.h2,{id:i.titleId,...r,ref:t})});e1.displayName=e0;var e2="DialogDescription";s.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=eI(e2,n);return(0,y.jsx)(g.sG.p,{id:i.descriptionId,...r,ref:t})}).displayName=e2;var e5="DialogClose",e9=s.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=eI(e5,n);return(0,y.jsx)(g.sG.button,{type:"button",...r,ref:t,onClick:l(e.onClick,()=>i.onOpenChange(!1))})});function e4(e){return e?"open":"closed"}e9.displayName=e5;var e6="DialogTitleWarning",[e3,e8]=(0,c.q)(e6,{contentName:eq,titleName:e0,docsSlug:"dialog"}),e7=e=>{let{titleId:t}=e,n=e8(e6),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return s.useEffect(()=>{t&&(document.getElementById(t)||console.error(r))},[r,t]),null},te=e=>{let{contentRef:t,descriptionId:n}=e,r=e8("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return s.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(i))},[i,t,n]),null},tt=eB,tn=e$,tr=eK,ti=eG,to=e1,ts=e9},760:(e,t,n)=>{n.d(t,{N:()=>y});var r=n(5155),i=n(2115),o=n(869),s=n(2885),a=n(7494),l=n(845),u=n(7351),c=n(1508);class d extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=(0,u.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function h(e){let{children:t,isPresent:n,anchorX:o,root:s}=e,a=(0,i.useId)(),l=(0,i.useRef)(null),u=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:h}=(0,i.useContext)(c.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:t,top:r,left:i,right:c}=u.current;if(n||!l.current||!e||!t)return;l.current.dataset.motionPopId=a;let d=document.createElement("style");h&&(d.nonce=h);let p=null!=s?s:document.head;return p.appendChild(d),d.sheet&&d.sheet.insertRule('\n          [data-motion-pop-id="'.concat(a,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===o?"left: ".concat(i):"right: ".concat(c),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{p.removeChild(d),p.contains(d)&&p.removeChild(d)}},[n]),(0,r.jsx)(d,{isPresent:n,childRef:l,sizeRef:u,children:i.cloneElement(t,{ref:l})})}let p=e=>{let{children:t,initial:n,isPresent:o,onExitComplete:a,custom:u,presenceAffectsLayout:c,mode:d,anchorX:p,root:f}=e,g=(0,s.M)(m),v=(0,i.useId)(),y=!0,b=(0,i.useMemo)(()=>(y=!1,{id:v,initial:n,isPresent:o,custom:u,onExitComplete:e=>{for(let t of(g.set(e,!0),g.values()))if(!t)return;a&&a()},register:e=>(g.set(e,!1),()=>g.delete(e))}),[o,g,a]);return c&&y&&(b={...b}),(0,i.useMemo)(()=>{g.forEach((e,t)=>g.set(t,!1))},[o]),i.useEffect(()=>{o||g.size||!a||a()},[o]),"popLayout"===d&&(t=(0,r.jsx)(h,{isPresent:o,anchorX:p,root:f,children:t})),(0,r.jsx)(l.t.Provider,{value:b,children:t})};function m(){return new Map}var f=n(2082);let g=e=>e.key||"";function v(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let y=e=>{let{children:t,custom:n,initial:l=!0,onExitComplete:u,presenceAffectsLayout:c=!0,mode:d="sync",propagate:h=!1,anchorX:m="left",root:y}=e,[b,x]=(0,f.xQ)(h),w=(0,i.useMemo)(()=>v(t),[t]),k=h&&!b?[]:w.map(g),E=(0,i.useRef)(!0),T=(0,i.useRef)(w),P=(0,s.M)(()=>new Map),[S,A]=(0,i.useState)(w),[M,C]=(0,i.useState)(w);(0,a.E)(()=>{E.current=!1,T.current=w;for(let e=0;e<M.length;e++){let t=g(M[e]);k.includes(t)?P.delete(t):!0!==P.get(t)&&P.set(t,!1)}},[M,k.length,k.join("-")]);let R=[];if(w!==S){let e=[...w];for(let t=0;t<M.length;t++){let n=M[t],r=g(n);k.includes(r)||(e.splice(t,0,n),R.push(n))}return"wait"===d&&R.length&&(e=R),C(v(e)),A(w),null}let{forceRender:D}=(0,i.useContext)(o.L);return(0,r.jsx)(r.Fragment,{children:M.map(e=>{let t=g(e),i=(!h||!!b)&&(w===M||k.includes(t));return(0,r.jsx)(p,{isPresent:i,initial:(!E.current||!!l)&&void 0,custom:n,presenceAffectsLayout:c,mode:d,root:y,onExitComplete:i?void 0:()=>{if(!P.has(t))return;P.set(t,!0);let e=!0;P.forEach(t=>{t||(e=!1)}),e&&(null==D||D(),C(T.current),h&&(null==x||x()),u&&u())},anchorX:m,children:e},t)})})}},845:(e,t,n)=>{n.d(t,{t:()=>r});let r=(0,n(2115).createContext)(null)},869:(e,t,n)=>{n.d(t,{L:()=>r});let r=(0,n(2115).createContext)({})},1007:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1264:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},1362:(e,t,n)=>{n.d(t,{D:()=>u,N:()=>c});var r=n(2115),i=(e,t,n,r,i,o,s,a)=>{let l=document.documentElement,u=["light","dark"];function c(t){var n;(Array.isArray(e)?e:[e]).forEach(e=>{let n="class"===e,r=n&&o?i.map(e=>o[e]||e):i;n?(l.classList.remove(...r),l.classList.add(o&&o[t]?o[t]:t)):l.setAttribute(e,t)}),n=t,a&&u.includes(n)&&(l.style.colorScheme=n)}if(r)c(r);else try{let e=localStorage.getItem(t)||n,r=s&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(r)}catch(e){}},o=["light","dark"],s="(prefers-color-scheme: dark)",a=r.createContext(void 0),l={setTheme:e=>{},themes:[]},u=()=>{var e;return null!=(e=r.useContext(a))?e:l},c=e=>r.useContext(a)?r.createElement(r.Fragment,null,e.children):r.createElement(h,{...e}),d=["light","dark"],h=e=>{let{forcedTheme:t,disableTransitionOnChange:n=!1,enableSystem:i=!0,enableColorScheme:l=!0,storageKey:u="theme",themes:c=d,defaultTheme:h=i?"system":"light",attribute:v="data-theme",value:y,children:b,nonce:x,scriptProps:w}=e,[k,E]=r.useState(()=>m(u,h)),[T,P]=r.useState(()=>"system"===k?g():k),S=y?Object.values(y):c,A=r.useCallback(e=>{let t=e;if(!t)return;"system"===e&&i&&(t=g());let r=y?y[t]:t,s=n?f(x):null,a=document.documentElement,u=e=>{"class"===e?(a.classList.remove(...S),r&&a.classList.add(r)):e.startsWith("data-")&&(r?a.setAttribute(e,r):a.removeAttribute(e))};if(Array.isArray(v)?v.forEach(u):u(v),l){let e=o.includes(h)?h:null,n=o.includes(t)?t:e;a.style.colorScheme=n}null==s||s()},[x]),M=r.useCallback(e=>{let t="function"==typeof e?e(k):e;E(t);try{localStorage.setItem(u,t)}catch(e){}},[k]),C=r.useCallback(e=>{P(g(e)),"system"===k&&i&&!t&&A("system")},[k,t]);r.useEffect(()=>{let e=window.matchMedia(s);return e.addListener(C),C(e),()=>e.removeListener(C)},[C]),r.useEffect(()=>{let e=e=>{e.key===u&&(e.newValue?E(e.newValue):M(h))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[M]),r.useEffect(()=>{A(null!=t?t:k)},[t,k]);let R=r.useMemo(()=>({theme:k,setTheme:M,forcedTheme:t,resolvedTheme:"system"===k?T:k,themes:i?[...c,"system"]:c,systemTheme:i?T:void 0}),[k,M,t,T,i,c]);return r.createElement(a.Provider,{value:R},r.createElement(p,{forcedTheme:t,storageKey:u,attribute:v,enableSystem:i,enableColorScheme:l,defaultTheme:h,value:y,themes:c,nonce:x,scriptProps:w}),b)},p=r.memo(e=>{let{forcedTheme:t,storageKey:n,attribute:o,enableSystem:s,enableColorScheme:a,defaultTheme:l,value:u,themes:c,nonce:d,scriptProps:h}=e,p=JSON.stringify([o,n,l,t,c,u,s,a]).slice(1,-1);return r.createElement("script",{...h,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(i.toString(),")(").concat(p,")")}})}),m=(e,t)=>{let n;try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t},f=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},g=e=>(e||(e=window.matchMedia(s)),e.matches?"dark":"light")},1508:(e,t,n)=>{n.d(t,{Q:()=>r});let r=(0,n(2115).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},1976:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},2082:(e,t,n)=>{n.d(t,{xQ:()=>o});var r=n(2115),i=n(845);function o(e=!0){let t=(0,r.useContext)(i.t);if(null===t)return[!0,null];let{isPresent:n,onExitComplete:s,register:a}=t,l=(0,r.useId)();(0,r.useEffect)(()=>{if(e)return a(l)},[e]);let u=(0,r.useCallback)(()=>e&&s&&s(l),[l,s,e]);return!n&&s?[!1,u]:[!0]}},2085:(e,t,n)=>{n.d(t,{F:()=>s});var r=n(2596);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=r.$,s=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return o(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:s,defaultVariants:a}=t,l=Object.keys(s).map(e=>{let t=null==n?void 0:n[e],r=null==a?void 0:a[e];if(null===t)return null;let o=i(t)||i(r);return s[e][o]}),u=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return o(e,l,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...i}=t;return Object.entries(i).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...a,...u}[t]):({...a,...u})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},2098:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},2596:(e,t,n)=>{n.d(t,{$:()=>r});function r(){for(var e,t,n=0,r="",i=arguments.length;n<i;n++)(e=arguments[n])&&(t=function e(t){var n,r,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t)if(Array.isArray(t)){var o=t.length;for(n=0;n<o;n++)t[n]&&(r=e(t[n]))&&(i&&(i+=" "),i+=r)}else for(r in t)t[r]&&(i&&(i+=" "),i+=r);return i}(e))&&(r&&(r+=" "),r+=t);return r}},2664:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return o}});let r=n(9991),i=n(7102);function o(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,i.hasBasePath)(n.pathname)}catch(e){return!1}}},2712:(e,t,n)=>{n.d(t,{N:()=>i});var r=n(2115),i=globalThis?.document?r.useLayoutEffect:()=>{}},2757:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return o},formatWithValidation:function(){return a},urlObjectKeys:function(){return s}});let r=n(6966)._(n(8859)),i=/https?|ftp|gopher|file/;function o(e){let{auth:t,hostname:n}=e,o=e.protocol||"",s=e.pathname||"",a=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:n&&(u=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(r.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return o&&!o.endsWith(":")&&(o+=":"),e.slashes||(!o||i.test(o))&&!1!==u?(u="//"+(u||""),s&&"/"!==s[0]&&(s="/"+s)):u||(u=""),a&&"#"!==a[0]&&(a="#"+a),c&&"?"!==c[0]&&(c="?"+c),""+o+u+(s=s.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+a}let s=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(e){return o(e)}},2885:(e,t,n)=>{n.d(t,{M:()=>i});var r=n(2115);function i(e){let t=(0,r.useRef)(null);return null===t.current&&(t.current=e()),t.current}},2894:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},3096:(e,t,n)=>{n.d(t,{Wx:()=>c});var r=n(2115),i=Object.defineProperty,o=(e,t,n)=>t in e?i(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,s=new Map,a=new WeakMap,l=0,u=void 0;function c(){var e;let{threshold:t,delay:n,trackVisibility:i,rootMargin:o,root:c,triggerOnce:d,skip:h,initialInView:p,fallbackInView:m,onChange:f}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[g,v]=r.useState(null),y=r.useRef(f),[b,x]=r.useState({inView:!!p,entry:void 0});y.current=f,r.useEffect(()=>{let e;if(!h&&g)return e=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:u;if(void 0===window.IntersectionObserver&&void 0!==r){let i=e.getBoundingClientRect();return t(r,{isIntersecting:r,target:e,intersectionRatio:"number"==typeof n.threshold?n.threshold:0,time:0,boundingClientRect:i,intersectionRect:i,rootBounds:i}),()=>{}}let{id:i,observer:o,elements:c}=function(e){let t=Object.keys(e).sort().filter(t=>void 0!==e[t]).map(t=>{var n;return"".concat(t,"_").concat("root"===t?(n=e.root)?(a.has(n)||(l+=1,a.set(n,l.toString())),a.get(n)):"0":e[t])}).toString(),n=s.get(t);if(!n){let r,i=new Map,o=new IntersectionObserver(t=>{t.forEach(t=>{var n;let o=t.isIntersecting&&r.some(e=>t.intersectionRatio>=e);e.trackVisibility&&void 0===t.isVisible&&(t.isVisible=o),null==(n=i.get(t.target))||n.forEach(e=>{e(o,t)})})},e);r=o.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),n={id:t,observer:o,elements:i},s.set(t,n)}return n}(n),d=c.get(e)||[];return c.has(e)||c.set(e,d),d.push(t),o.observe(e),function(){d.splice(d.indexOf(t),1),0===d.length&&(c.delete(e),o.unobserve(e)),0===c.size&&(o.disconnect(),s.delete(i))}}(g,(t,n)=>{x({inView:t,entry:n}),y.current&&y.current(t,n),n.isIntersecting&&d&&e&&(e(),e=void 0)},{root:c,rootMargin:o,threshold:t,trackVisibility:i,delay:n},m),()=>{e&&e()}},[Array.isArray(t)?t.toString():t,g,c,o,d,h,i,m,n]);let w=null==(e=b.entry)?void 0:e.target,k=r.useRef(void 0);g||!w||d||h||k.current===w||(k.current=w,x({inView:!!p,entry:void 0}));let E=[v,b.inView,b.entry];return E.ref=E[0],E.inView=E[1],E.entry=E[2],E}r.Component},3180:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},3509:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},3655:(e,t,n)=>{n.d(t,{hO:()=>l,sG:()=>a});var r=n(2115),i=n(7650),o=n(9708),s=n(5155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,o.TL)(`Primitive.${t}`),i=r.forwardRef((e,r)=>{let{asChild:i,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(i?n:t,{...o,ref:r})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{});function l(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},3786:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},4416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4783:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},5695:(e,t,n)=>{var r=n(8999);n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}})},6081:(e,t,n)=>{n.d(t,{A:()=>s,q:()=>o});var r=n(2115),i=n(5155);function o(e,t){let n=r.createContext(t),o=e=>{let{children:t,...o}=e,s=r.useMemo(()=>o,Object.values(o));return(0,i.jsx)(n.Provider,{value:s,children:t})};return o.displayName=e+"Provider",[o,function(i){let o=r.useContext(n);if(o)return o;if(void 0!==t)return t;throw Error(`\`${i}\` must be used within \`${e}\``)}]}function s(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let i=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return o.scopeName=e,[function(t,o){let s=r.createContext(o),a=n.length;n=[...n,o];let l=t=>{let{scope:n,children:o,...l}=t,u=n?.[e]?.[a]||s,c=r.useMemo(()=>l,Object.values(l));return(0,i.jsx)(u.Provider,{value:c,children:o})};return l.displayName=t+"Provider",[l,function(n,i){let l=i?.[e]?.[a]||s,u=r.useContext(l);if(u)return u;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e)[`__scope${r}`];return{...t,...i}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}(o,...t)]}},6101:(e,t,n)=>{n.d(t,{s:()=>s,t:()=>o});var r=n(2115);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}function s(...e){return r.useCallback(o(...e),e)}},6408:(e,t,n)=>{let r;function i(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function o(e){let t=[{},{}];return e?.values.forEach((e,n)=>{t[0][n]=e.get(),t[1][n]=e.getVelocity()}),t}function s(e,t,n,r){if("function"==typeof t){let[i,s]=o(r);t=t(void 0!==n?n:e.custom,i,s)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,s]=o(r);t=t(void 0!==n?n:e.custom,i,s)}return t}function a(e,t,n){let r=e.getProps();return s(r,t,void 0!==n?n:r.custom,e)}function l(e,t){return e?.[t]??e?.default??e}n.d(t,{P:()=>oA});let u=e=>e,c={},d=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],h={value:null,addProjectionMetrics:null};function p(e,t){let n=!1,r=!0,i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,s=d.reduce((e,n)=>(e[n]=function(e,t){let n=new Set,r=new Set,i=!1,o=!1,s=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){s.has(t)&&(c.schedule(t),e()),l++,t(a)}let c={schedule:(e,t=!1,o=!1)=>{let a=o&&i?n:r;return t&&s.add(e),a.has(e)||a.add(e),e},cancel:e=>{r.delete(e),s.delete(e)},process:e=>{if(a=e,i){o=!0;return}i=!0,[n,r]=[r,n],n.forEach(u),t&&h.value&&h.value.frameloop[t].push(l),l=0,n.clear(),i=!1,o&&(o=!1,c.process(e))}};return c}(o,t?n:void 0),e),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:p,update:m,preRender:f,render:g,postRender:v}=s,y=()=>{let o=c.useManualTiming?i.timestamp:performance.now();n=!1,c.useManualTiming||(i.delta=r?1e3/60:Math.max(Math.min(o-i.timestamp,40),1)),i.timestamp=o,i.isProcessing=!0,a.process(i),l.process(i),u.process(i),p.process(i),m.process(i),f.process(i),g.process(i),v.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(y))},b=()=>{n=!0,r=!0,i.isProcessing||e(y)};return{schedule:d.reduce((e,t)=>{let r=s[t];return e[t]=(e,t=!1,i=!1)=>(n||b(),r.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<d.length;t++)s[d[t]].cancel(e)},state:i,steps:s}}let{schedule:m,cancel:f,state:g,steps:v}=p("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),y=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],b=new Set(y),x=new Set(["width","height","top","left","right","bottom",...y]);function w(e,t){-1===e.indexOf(t)&&e.push(t)}function k(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}class E{constructor(){this.subscriptions=[]}add(e){return w(this.subscriptions,e),()=>k(this.subscriptions,e)}notify(e,t,n){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){let r=this.subscriptions[i];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function T(){r=void 0}let P={now:()=>(void 0===r&&P.set(g.isProcessing||c.useManualTiming?g.timestamp:performance.now()),r),set:e=>{r=e,queueMicrotask(T)}},S=e=>!isNaN(parseFloat(e)),A={current:void 0};class M{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let n=P.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=P.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=S(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new E);let n=this.events[e].add(t);return"change"===e?()=>{n(),m.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return A.current&&A.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=P.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),n?1e3/n*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function C(e,t){return new M(e,t)}let R=e=>Array.isArray(e),D=e=>!!(e&&e.getVelocity);function j(e,t){let n=e.getValue("willChange");if(D(n)&&n.add)return n.add(t);if(!n&&c.WillChange){let n=new c.WillChange("auto");e.addValue("willChange",n),n.add(t)}}let V=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),L="data-"+V("framerAppearId"),O=(e,t)=>n=>t(e(n)),N=(...e)=>e.reduce(O),F=(e,t,n)=>n>t?t:n<e?e:n,I=e=>1e3*e,B=e=>e/1e3,U={layout:0,mainThread:0,waapi:0},z=()=>{},W=()=>{},_=e=>t=>"string"==typeof t&&t.startsWith(e),$=_("--"),H=_("var(--"),K=e=>!!H(e)&&X.test(e.split("/*")[0].trim()),X=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Y={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},q={...Y,transform:e=>F(0,1,e)},G={...Y,default:1},Z=e=>Math.round(1e5*e)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ee=(e,t)=>n=>!!("string"==typeof n&&J.test(n)&&n.startsWith(e)||t&&null!=n&&Object.prototype.hasOwnProperty.call(n,t)),et=(e,t,n)=>r=>{if("string"!=typeof r)return r;let[i,o,s,a]=r.match(Q);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:void 0!==a?parseFloat(a):1}},en=e=>F(0,255,e),er={...Y,transform:e=>Math.round(en(e))},ei={test:ee("rgb","red"),parse:et("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+er.transform(e)+", "+er.transform(t)+", "+er.transform(n)+", "+Z(q.transform(r))+")"},eo={test:ee("#"),parse:function(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:ei.transform},es=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),ea=es("deg"),el=es("%"),eu=es("px"),ec=es("vh"),ed=es("vw"),eh={...el,parse:e=>el.parse(e)/100,transform:e=>el.transform(100*e)},ep={test:ee("hsl","hue"),parse:et("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+el.transform(Z(t))+", "+el.transform(Z(n))+", "+Z(q.transform(r))+")"},em={test:e=>ei.test(e)||eo.test(e)||ep.test(e),parse:e=>ei.test(e)?ei.parse(e):ep.test(e)?ep.parse(e):eo.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?ei.transform(e):ep.transform(e),getAnimatableNone:e=>{let t=em.parse(e);return t.alpha=0,em.transform(t)}},ef=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eg="number",ev="color",ey=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eb(e){let t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[],o=0,s=t.replace(ey,e=>(em.test(e)?(r.color.push(o),i.push(ev),n.push(em.parse(e))):e.startsWith("var(")?(r.var.push(o),i.push("var"),n.push(e)):(r.number.push(o),i.push(eg),n.push(parseFloat(e))),++o,"${}")).split("${}");return{values:n,split:s,indexes:r,types:i}}function ex(e){return eb(e).values}function ew(e){let{split:t,types:n}=eb(e),r=t.length;return e=>{let i="";for(let o=0;o<r;o++)if(i+=t[o],void 0!==e[o]){let t=n[o];t===eg?i+=Z(e[o]):t===ev?i+=em.transform(e[o]):i+=e[o]}return i}}let ek=e=>"number"==typeof e?0:em.test(e)?em.getAnimatableNone(e):e,eE={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(Q)?.length||0)+(e.match(ef)?.length||0)>0},parse:ex,createTransformer:ew,getAnimatableNone:function(e){let t=ex(e);return ew(e)(t.map(ek))}};function eT(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+(t-e)*6*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function eP(e,t){return n=>n>0?t:e}let eS=(e,t,n)=>e+(t-e)*n,eA=(e,t,n)=>{let r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},eM=[eo,ei,ep],eC=e=>eM.find(t=>t.test(e));function eR(e){let t=eC(e);if(z(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let n=t.parse(e);return t===ep&&(n=function({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,n/=100;let i=0,o=0,s=0;if(t/=100){let r=n<.5?n*(1+t):n+t-n*t,a=2*n-r;i=eT(a,r,e+1/3),o=eT(a,r,e),s=eT(a,r,e-1/3)}else i=o=s=n;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*s),alpha:r}}(n)),n}let eD=(e,t)=>{let n=eR(e),r=eR(t);if(!n||!r)return eP(e,t);let i={...n};return e=>(i.red=eA(n.red,r.red,e),i.green=eA(n.green,r.green,e),i.blue=eA(n.blue,r.blue,e),i.alpha=eS(n.alpha,r.alpha,e),ei.transform(i))},ej=new Set(["none","hidden"]);function eV(e,t){return n=>eS(e,t,n)}function eL(e){return"number"==typeof e?eV:"string"==typeof e?K(e)?eP:em.test(e)?eD:eF:Array.isArray(e)?eO:"object"==typeof e?em.test(e)?eD:eN:eP}function eO(e,t){let n=[...e],r=n.length,i=e.map((e,n)=>eL(e)(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}}function eN(e,t){let n={...e,...t},r={};for(let i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=eL(e[i])(e[i],t[i]));return e=>{for(let t in r)n[t]=r[t](e);return n}}let eF=(e,t)=>{let n=eE.createTransformer(t),r=eb(e),i=eb(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?ej.has(e)&&!i.values.length||ej.has(t)&&!r.values.length?function(e,t){return ej.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}(e,t):N(eO(function(e,t){let n=[],r={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){let o=t.types[i],s=e.indexes[o][r[o]],a=e.values[s]??0;n[i]=a,r[o]++}return n}(r,i),i.values),n):(z(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eP(e,t))};function eI(e,t,n){return"number"==typeof e&&"number"==typeof t&&"number"==typeof n?eS(e,t,n):eL(e)(e,t)}let eB=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>m.update(t,e),stop:()=>f(t),now:()=>g.isProcessing?g.timestamp:P.now()}},eU=(e,t,n=10)=>{let r="",i=Math.max(Math.round(t/n),2);for(let t=0;t<i;t++)r+=Math.round(1e4*e(t/(i-1)))/1e4+", ";return`linear(${r.substring(0,r.length-2)})`};function ez(e){let t=0,n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}function eW(e,t,n){var r,i;let o=Math.max(t-5,0);return r=n-e(o),(i=t-o)?1e3/i*r:0}let e_={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function e$(e,t){return e*Math.sqrt(1-t*t)}let eH=["duration","bounce"],eK=["stiffness","damping","mass"];function eX(e,t){return t.some(t=>void 0!==e[t])}function eY(e=e_.visualDuration,t=e_.bounce){let n,r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:o}=r,s=r.keyframes[0],a=r.keyframes[r.keyframes.length-1],l={done:!1,value:s},{stiffness:u,damping:c,mass:d,duration:h,velocity:p,isResolvedFromDuration:m}=function(e){let t={velocity:e_.velocity,stiffness:e_.stiffness,damping:e_.damping,mass:e_.mass,isResolvedFromDuration:!1,...e};if(!eX(e,eK)&&eX(e,eH))if(e.visualDuration){let n=2*Math.PI/(1.2*e.visualDuration),r=n*n,i=2*F(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:e_.mass,stiffness:r,damping:i}}else{let n=function({duration:e=e_.duration,bounce:t=e_.bounce,velocity:n=e_.velocity,mass:r=e_.mass}){let i,o;z(e<=I(e_.maxDuration),"Spring duration must be 10 seconds or less");let s=1-t;s=F(e_.minDamping,e_.maxDamping,s),e=F(e_.minDuration,e_.maxDuration,B(e)),s<1?(i=t=>{let r=t*s,i=r*e;return .001-(r-n)/e$(t,s)*Math.exp(-i)},o=t=>{let r=t*s*e,o=Math.pow(s,2)*Math.pow(t,2)*e,a=Math.exp(-r),l=e$(Math.pow(t,2),s);return(r*n+n-o)*a*(-i(t)+.001>0?-1:1)/l}):(i=t=>-.001+Math.exp(-t*e)*((t-n)*e+1),o=t=>e*e*(n-t)*Math.exp(-t*e));let a=function(e,t,n){let r=n;for(let n=1;n<12;n++)r-=e(r)/t(r);return r}(i,o,5/e);if(e=I(e),isNaN(a))return{stiffness:e_.stiffness,damping:e_.damping,duration:e};{let t=Math.pow(a,2)*r;return{stiffness:t,damping:2*s*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...n,mass:e_.mass}).isResolvedFromDuration=!0}return t}({...r,velocity:-B(r.velocity||0)}),f=p||0,g=c/(2*Math.sqrt(u*d)),v=a-s,y=B(Math.sqrt(u/d)),b=5>Math.abs(v);if(i||(i=b?e_.restSpeed.granular:e_.restSpeed.default),o||(o=b?e_.restDelta.granular:e_.restDelta.default),g<1){let e=e$(y,g);n=t=>a-Math.exp(-g*y*t)*((f+g*y*v)/e*Math.sin(e*t)+v*Math.cos(e*t))}else if(1===g)n=e=>a-Math.exp(-y*e)*(v+(f+y*v)*e);else{let e=y*Math.sqrt(g*g-1);n=t=>{let n=Math.exp(-g*y*t),r=Math.min(e*t,300);return a-n*((f+g*y*v)*Math.sinh(r)+e*v*Math.cosh(r))/e}}let x={calculatedDuration:m&&h||null,next:e=>{let t=n(e);if(m)l.done=e>=h;else{let r=0===e?f:0;g<1&&(r=0===e?I(f):eW(n,e,t));let s=Math.abs(a-t)<=o;l.done=Math.abs(r)<=i&&s}return l.value=l.done?a:t,l},toString:()=>{let e=Math.min(ez(x),2e4),t=eU(t=>x.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return x}function eq({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:c}){let d,h,p=e[0],m={done:!1,value:p},f=e=>void 0!==a&&e<a||void 0!==l&&e>l,g=e=>void 0===a?l:void 0===l||Math.abs(a-e)<Math.abs(l-e)?a:l,v=n*t,y=p+v,b=void 0===s?y:s(y);b!==y&&(v=b-p);let x=e=>-v*Math.exp(-e/r),w=e=>b+x(e),k=e=>{let t=x(e),n=w(e);m.done=Math.abs(t)<=u,m.value=m.done?b:n},E=e=>{f(m.value)&&(d=e,h=eY({keyframes:[m.value,g(m.value)],velocity:eW(w,e,m.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return E(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==d||(t=!0,k(e),E(e)),void 0!==d&&e>=d)?h.next(e-d):(t||k(e),m)}}}eY.applyToOptions=e=>{let t=function(e,t=100,n){let r=n({...e,keyframes:[0,t]}),i=Math.min(ez(r),2e4);return{type:"keyframes",ease:e=>r.next(i*e).value/t,duration:B(i)}}(e,100,eY);return e.ease=t.ease,e.duration=I(t.duration),e.type="keyframes",e};let eG=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function eZ(e,t,n,r){if(e===t&&n===r)return u;let i=t=>(function(e,t,n,r,i){let o,s,a=0;do(o=eG(s=t+(n-t)/2,r,i)-e)>0?n=s:t=s;while(Math.abs(o)>1e-7&&++a<12);return s})(t,0,1,e,n);return e=>0===e||1===e?e:eG(i(e),t,r)}let eQ=eZ(.42,0,1,1),eJ=eZ(0,0,.58,1),e0=eZ(.42,0,.58,1),e1=e=>Array.isArray(e)&&"number"!=typeof e[0],e2=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,e5=e=>t=>1-e(1-t),e9=eZ(.33,1.53,.69,.99),e4=e5(e9),e6=e2(e4),e3=e=>(e*=2)<1?.5*e4(e):.5*(2-Math.pow(2,-10*(e-1))),e8=e=>1-Math.sin(Math.acos(e)),e7=e5(e8),te=e2(e8),tt=e=>Array.isArray(e)&&"number"==typeof e[0],tn={linear:u,easeIn:eQ,easeInOut:e0,easeOut:eJ,circIn:e8,circInOut:te,circOut:e7,backIn:e4,backInOut:e6,backOut:e9,anticipate:e3},tr=e=>"string"==typeof e,ti=e=>{if(tt(e)){W(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,n,r,i]=e;return eZ(t,n,r,i)}return tr(e)?(W(void 0!==tn[e],`Invalid easing type '${e}'`),tn[e]):e},to=(e,t,n)=>{let r=t-e;return 0===r?1:(n-e)/r};function ts({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){var i;let o=e1(r)?r.map(ti):ti(r),s={done:!1,value:t[0]},a=function(e,t,{clamp:n=!0,ease:r,mixer:i}={}){let o=e.length;if(W(o===t.length,"Both input and output ranges must be the same length"),1===o)return()=>t[0];if(2===o&&t[0]===t[1])return()=>t[1];let s=e[0]===e[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());let a=function(e,t,n){let r=[],i=n||c.mix||eI,o=e.length-1;for(let n=0;n<o;n++){let o=i(e[n],e[n+1]);t&&(o=N(Array.isArray(t)?t[n]||u:t,o)),r.push(o)}return r}(t,r,i),l=a.length,d=n=>{if(s&&n<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(n<e[r+1]);r++);let i=to(e[r],e[r+1],n);return a[r](i)};return n?t=>d(F(e[0],e[o-1],t)):d}((i=n&&n.length===t.length?n:function(e){let t=[0];return!function(e,t){let n=e[e.length-1];for(let r=1;r<=t;r++){let i=to(0,t,r);e.push(eS(n,1,i))}}(t,e.length-1),t}(t),i.map(t=>t*e)),t,{ease:Array.isArray(o)?o:t.map(()=>o||e0).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(s.value=a(t),s.done=t>=e,s)}}let ta=e=>null!==e;function tl(e,{repeat:t,repeatType:n="loop"},r,i=1){let o=e.filter(ta),s=i<0||t&&"loop"!==n&&t%2==1?0:o.length-1;return s&&void 0!==r?r:o[s]}let tu={decay:eq,inertia:eq,tween:ts,keyframes:ts,spring:eY};function tc(e){"string"==typeof e.type&&(e.type=tu[e.type])}class td{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let th=e=>e/100;class tp extends td{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==P.now()&&this.tick(P.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},U.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;tc(e);let{type:t=ts,repeat:n=0,repeatDelay:r=0,repeatType:i,velocity:o=0}=e,{keyframes:s}=e,a=t||ts;a!==ts&&"number"!=typeof s[0]&&(this.mixKeyframes=N(th,eI(s[0],s[1])),s=[0,100]);let l=a({...e,keyframes:s});"mirror"===i&&(this.mirroredGenerator=a({...e,keyframes:[...s].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=ez(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(n+1)-r,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:n,totalDuration:r,mixKeyframes:i,mirroredGenerator:o,resolvedDuration:s,calculatedDuration:a}=this;if(null===this.startTime)return n.next(0);let{delay:l=0,keyframes:u,repeat:c,repeatType:d,repeatDelay:h,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-r/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let y=this.currentTime,b=n;if(c){let e=Math.min(this.currentTime,r)/s,t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,(t=Math.min(t,c+1))%2&&("reverse"===d?(n=1-n,h&&(n-=h/s)):"mirror"===d&&(b=o)),y=F(0,1,n)*s}let x=v?{done:!1,value:u[0]}:b.next(y);i&&(x.value=i(x.value));let{done:w}=x;v||null===a||(w=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let k=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return k&&p!==eq&&(x.value=tl(u,this.options,f,this.speed)),m&&m(x.value),k&&this.finish(),x}then(e,t){return this.finished.then(e,t)}get duration(){return B(this.calculatedDuration)}get time(){return B(this.currentTime)}set time(e){e=I(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(P.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=B(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=eB,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=t??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(P.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,U.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let tm=e=>180*e/Math.PI,tf=e=>tv(tm(Math.atan2(e[1],e[0]))),tg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:tf,rotateZ:tf,skewX:e=>tm(Math.atan(e[1])),skewY:e=>tm(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},tv=e=>((e%=360)<0&&(e+=360),e),ty=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),tb=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),tx={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ty,scaleY:tb,scale:e=>(ty(e)+tb(e))/2,rotateX:e=>tv(tm(Math.atan2(e[6],e[5]))),rotateY:e=>tv(tm(Math.atan2(-e[2],e[0]))),rotateZ:tf,rotate:tf,skewX:e=>tm(Math.atan(e[4])),skewY:e=>tm(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function tw(e){return+!!e.includes("scale")}function tk(e,t){let n,r;if(!e||"none"===e)return tw(t);let i=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(i)n=tx,r=i;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=tg,r=t}if(!r)return tw(t);let o=n[t],s=r[1].split(",").map(tT);return"function"==typeof o?o(s):s[o]}let tE=(e,t)=>{let{transform:n="none"}=getComputedStyle(e);return tk(n,t)};function tT(e){return parseFloat(e.trim())}let tP=e=>e===Y||e===eu,tS=new Set(["x","y","z"]),tA=y.filter(e=>!tS.has(e)),tM={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>tk(t,"x"),y:(e,{transform:t})=>tk(t,"y")};tM.translateX=tM.x,tM.translateY=tM.y;let tC=new Set,tR=!1,tD=!1,tj=!1;function tV(){if(tD){let e=Array.from(tC).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),n=new Map;t.forEach(e=>{let t=function(e){let t=[];return tA.forEach(n=>{let r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(+!!n.startsWith("scale")))}),t}(e);t.length&&(n.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=n.get(e);t&&t.forEach(([t,n])=>{e.getValue(t)?.set(n)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tD=!1,tR=!1,tC.forEach(e=>e.complete(tj)),tC.clear()}function tL(){tC.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tD=!0)})}class tO{constructor(e,t,n,r,i,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=n,this.motionValue=r,this.element=i,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(tC.add(this),tR||(tR=!0,m.read(tL),m.resolveKeyframes(tV))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:n,motionValue:r}=this;if(null===e[0]){let i=r?.get(),o=e[e.length-1];if(void 0!==i)e[0]=i;else if(n&&t){let r=n.readValue(t,o);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=o),r&&void 0===i&&r.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),tC.delete(this)}cancel(){"scheduled"===this.state&&(tC.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tN=e=>e.startsWith("--");function tF(e){let t;return()=>(void 0===t&&(t=e()),t)}let tI=tF(()=>void 0!==window.ScrollTimeline),tB={},tU=function(e,t){let n=tF(e);return()=>tB[t]??n()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),tz=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,tW={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tz([0,.65,.55,1]),circOut:tz([.55,0,1,.45]),backIn:tz([.31,.01,.66,-.59]),backOut:tz([.33,1.53,.69,.99])};function t_(e){return"function"==typeof e&&"applyToOptions"in e}class t$ extends td{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:n,keyframes:r,pseudoElement:i,allowFlatten:o=!1,finalKeyframe:s,onComplete:a}=e;this.isPseudoElement=!!i,this.allowFlatten=o,this.options=e,W("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return t_(e)&&tU()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,n,{delay:r=0,duration:i=300,repeat:o=0,repeatType:s="loop",ease:a="easeOut",times:l}={},u){let c={[t]:n};l&&(c.offset=l);let d=function e(t,n){if(t)return"function"==typeof t?tU()?eU(t,n):"ease-out":tt(t)?tz(t):Array.isArray(t)?t.map(t=>e(t,n)||tW.easeOut):tW[t]}(a,i);Array.isArray(d)&&(c.easing=d),h.value&&U.waapi++;let p={delay:r,duration:i,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:o+1,direction:"reverse"===s?"alternate":"normal"};u&&(p.pseudoElement=u);let m=e.animate(c,p);return h.value&&m.finished.finally(()=>{U.waapi--}),m}(t,n,r,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let e=tl(r,this.options,s,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,n){tN(t)?e.style.setProperty(t,n):e.style[t]=n}(t,n,e),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return B(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return B(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=I(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&tI())?(this.animation.timeline=e,u):t(this)}}let tH={anticipate:e3,backInOut:e6,circInOut:te};class tK extends t${constructor(e){!function(e){"string"==typeof e.ease&&e.ease in tH&&(e.ease=tH[e.ease])}(e),tc(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:n,onComplete:r,element:i,...o}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let s=new tp({...o,autoplay:!1}),a=I(this.finishedTime??this.time);t.setWithVelocity(s.sample(a-10).value,s.sample(a).value,10),s.stop()}}let tX=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eE.test(e)||"0"===e)&&!e.startsWith("url("));var tY,tq,tG=n(7351);let tZ=new Set(["opacity","clipPath","filter","transform"]),tQ=tF(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tJ extends td{constructor({autoplay:e=!0,delay:t=0,type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:o="loop",keyframes:s,name:a,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=P.now();let d={autoplay:e,delay:t,type:n,repeat:r,repeatDelay:i,repeatType:o,name:a,motionValue:l,element:u,...c},h=u?.KeyframeResolver||tO;this.keyframeResolver=new h(s,(e,t,n)=>this.onKeyframesResolved(e,t,d,!n),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,n,r){this.keyframeResolver=void 0;let{name:i,type:o,velocity:s,delay:a,isHandoff:l,onUpdate:d}=n;this.resolvedAt=P.now(),!function(e,t,n,r){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let o=e[e.length-1],s=tX(i,t),a=tX(o,t);return z(s===a,`You are trying to animate ${t} from "${i}" to "${o}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${o} via the \`style\` property.`),!!s&&!!a&&(function(e){let t=e[0];if(1===e.length)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}(e)||("spring"===n||t_(n))&&r)}(e,i,o,s)&&((c.instantAnimations||!a)&&d?.(tl(e,n,t)),e[0]=e[e.length-1],n.duration=0,n.repeat=0);let h={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...n,keyframes:e},p=!l&&function(e){let{motionValue:t,name:n,repeatDelay:r,repeatType:i,damping:o,type:s}=e;if(!(0,tG.s)(t?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=t.owner.getProps();return tQ()&&n&&tZ.has(n)&&("transform"!==n||!l)&&!a&&!r&&"mirror"!==i&&0!==o&&"inertia"!==s}(h)?new tK({...h,element:h.motionValue.owner.current}):new tp(h);p.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tj=!0,tL(),tV(),tj=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let t0=e=>null!==e,t1={type:"spring",stiffness:500,damping:25,restSpeed:10},t2=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),t5={type:"keyframes",duration:.8},t9={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},t4=(e,{keyframes:t})=>t.length>2?t5:b.has(e)?e.startsWith("scale")?t2(t[1]):t1:t9,t6=(e,t,n,r={},i,o)=>s=>{let a=l(r,e)||{},u=a.delay||r.delay||0,{elapsed:d=0}=r;d-=I(u);let h={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-d,onUpdate:e=>{t.set(e),a.onUpdate&&a.onUpdate(e)},onComplete:()=>{s(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:o?void 0:i};!function({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(a)&&Object.assign(h,t4(e,h)),h.duration&&(h.duration=I(h.duration)),h.repeatDelay&&(h.repeatDelay=I(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let p=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0===h.delay&&(p=!0)),(c.instantAnimations||c.skipAnimations)&&(p=!0,h.duration=0,h.delay=0),h.allowFlatten=!a.type&&!a.ease,p&&!o&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:n="loop"},r){let i=e.filter(t0),o=t&&"loop"!==n&&t%2==1?0:i.length-1;return i[o]}(h.keyframes,a);if(void 0!==e)return void m.update(()=>{h.onUpdate(e),h.onComplete()})}return a.isSync?new tp(h):new tJ(h)};function t3(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:s,...u}=t;r&&(o=r);let c=[],d=i&&e.animationState&&e.animationState.getState()[i];for(let t in u){let r=e.getValue(t,e.latestValues[t]??null),i=u[t];if(void 0===i||d&&function({protectedKeys:e,needsAnimating:t},n){let r=e.hasOwnProperty(n)&&!0!==t[n];return t[n]=!1,r}(d,t))continue;let s={delay:n,...l(o||{},t)},a=r.get();if(void 0!==a&&!r.isAnimating&&!Array.isArray(i)&&i===a&&!s.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){let n=e.props[L];if(n){let e=window.MotionHandoffAnimation(n,t,m);null!==e&&(s.startTime=e,h=!0)}}j(e,t),r.start(t6(t,r,i,e.shouldReduceMotion&&x.has(t)?{type:!1}:s,e,h));let p=r.animation;p&&c.push(p)}return s&&Promise.all(c).then(()=>{m.update(()=>{s&&function(e,t){let{transitionEnd:n={},transition:r={},...i}=a(e,t)||{};for(let t in i={...i,...n}){var o;let n=R(o=i[t])?o[o.length-1]||0:o;e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,C(n))}}(e,s)})}),c}function t8(e,t,n={}){let r=a(e,t,"exit"===n.type?e.presenceContext?.custom:void 0),{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);let o=r?()=>Promise.all(t3(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:o=0,staggerChildren:s,staggerDirection:a}=i;return function(e,t,n=0,r=0,i=1,o){let s=[],a=(e.variantChildren.size-1)*r,l=1===i?(e=0)=>e*r:(e=0)=>a-e*r;return Array.from(e.variantChildren).sort(t7).forEach((e,r)=>{e.notify("AnimationStart",t),s.push(t8(e,t,{...o,delay:n+l(r)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(s)}(e,t,o+r,s,a,n)}:()=>Promise.resolve(),{when:l}=i;if(!l)return Promise.all([o(),s(n.delay)]);{let[e,t]="beforeChildren"===l?[o,s]:[s,o];return e().then(()=>t())}}function t7(e,t){return e.sortNodePosition(t)}function ne(e,t){if(!Array.isArray(t))return!1;let n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function nt(e){return"string"==typeof e||Array.isArray(e)}let nn=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],nr=["initial",...nn],ni=nr.length,no=[...nn].reverse(),ns=nn.length;function na(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function nl(){return{animate:na(!0),whileInView:na(),whileHover:na(),whileTap:na(),whileDrag:na(),whileFocus:na(),exit:na()}}class nu{constructor(e){this.isMounted=!1,this.node=e}update(){}}class nc extends nu{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:n})=>(function(e,t,n={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>t8(e,t,n)));else if("string"==typeof t)r=t8(e,t,n);else{let i="function"==typeof t?a(e,t,n.custom):t;r=Promise.all(t3(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})})(e,t,n))),n=nl(),r=!0,o=t=>(n,r)=>{let i=a(e,r,"exit"===t?e.presenceContext?.custom:void 0);if(i){let{transition:e,transitionEnd:t,...r}=i;n={...n,...r,...t}}return n};function s(s){let{props:l}=e,u=function e(t){if(!t)return;if(!t.isControllingVariants){let n=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(n.initial=t.props.initial),n}let n={};for(let e=0;e<ni;e++){let r=nr[e],i=t.props[r];(nt(i)||!1===i)&&(n[r]=i)}return n}(e.parent)||{},c=[],d=new Set,h={},p=1/0;for(let t=0;t<ns;t++){var m,f;let a=no[t],g=n[a],v=void 0!==l[a]?l[a]:u[a],y=nt(v),b=a===s?g.isActive:null;!1===b&&(p=t);let x=v===u[a]&&v!==l[a]&&y;if(x&&r&&e.manuallyAnimateOnMount&&(x=!1),g.protectedKeys={...h},!g.isActive&&null===b||!v&&!g.prevProp||i(v)||"boolean"==typeof v)continue;let w=(m=g.prevProp,"string"==typeof(f=v)?f!==m:!!Array.isArray(f)&&!ne(f,m)),k=w||a===s&&g.isActive&&!x&&y||t>p&&y,E=!1,T=Array.isArray(v)?v:[v],P=T.reduce(o(a),{});!1===b&&(P={});let{prevResolvedValues:S={}}=g,A={...S,...P},M=t=>{k=!0,d.has(t)&&(E=!0,d.delete(t)),g.needsAnimating[t]=!0;let n=e.getValue(t);n&&(n.liveStyle=!1)};for(let e in A){let t=P[e],n=S[e];if(h.hasOwnProperty(e))continue;let r=!1;(R(t)&&R(n)?ne(t,n):t===n)?void 0!==t&&d.has(e)?M(e):g.protectedKeys[e]=!0:null!=t?M(e):d.add(e)}g.prevProp=v,g.prevResolvedValues=P,g.isActive&&(h={...h,...P}),r&&e.blockInitialAnimation&&(k=!1);let C=!(x&&w)||E;k&&C&&c.push(...T.map(e=>({animation:e,options:{type:a}})))}if(d.size){let t={};if("boolean"!=typeof l.initial){let n=a(e,Array.isArray(l.initial)?l.initial[0]:l.initial);n&&n.transition&&(t.transition=n.transition)}d.forEach(n=>{let r=e.getBaseTarget(n),i=e.getValue(n);i&&(i.liveStyle=!0),t[n]=r??null}),c.push({animation:t})}let g=!!c.length;return r&&(!1===l.initial||l.initial===l.animate)&&!e.manuallyAnimateOnMount&&(g=!1),r=!1,g?t(c):Promise.resolve()}return{animateChanges:s,setActive:function(t,r){if(n[t].isActive===r)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,r)),n[t].isActive=r;let i=s(t);for(let e in n)n[e].protectedKeys={};return i},setAnimateFunction:function(n){t=n(e)},getState:()=>n,reset:()=>{n=nl(),r=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();i(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let nd=0;class nh extends nu{constructor(){super(...arguments),this.id=nd++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let np={x:!1,y:!1};function nm(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}let nf=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function ng(e){return{point:{x:e.pageX,y:e.pageY}}}let nv=e=>t=>nf(t)&&e(t,ng(t));function ny(e,t,n,r){return nm(e,t,nv(n),r)}function nb({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function nx(e){return e.max-e.min}function nw(e,t,n,r=.5){e.origin=r,e.originPoint=eS(t.min,t.max,e.origin),e.scale=nx(n)/nx(t),e.translate=eS(n.min,n.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function nk(e,t,n,r){nw(e.x,t.x,n.x,r?r.originX:void 0),nw(e.y,t.y,n.y,r?r.originY:void 0)}function nE(e,t,n){e.min=n.min+t.min,e.max=e.min+nx(t)}function nT(e,t,n){e.min=t.min-n.min,e.max=e.min+nx(t)}function nP(e,t,n){nT(e.x,t.x,n.x),nT(e.y,t.y,n.y)}let nS=()=>({translate:0,scale:1,origin:0,originPoint:0}),nA=()=>({x:nS(),y:nS()}),nM=()=>({min:0,max:0}),nC=()=>({x:nM(),y:nM()});function nR(e){return[e("x"),e("y")]}function nD(e){return void 0===e||1===e}function nj({scale:e,scaleX:t,scaleY:n}){return!nD(e)||!nD(t)||!nD(n)}function nV(e){return nj(e)||nL(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function nL(e){var t,n;return(t=e.x)&&"0%"!==t||(n=e.y)&&"0%"!==n}function nO(e,t,n,r,i){return void 0!==i&&(e=r+i*(e-r)),r+n*(e-r)+t}function nN(e,t=0,n=1,r,i){e.min=nO(e.min,t,n,r,i),e.max=nO(e.max,t,n,r,i)}function nF(e,{x:t,y:n}){nN(e.x,t.translate,t.scale,t.originPoint),nN(e.y,n.translate,n.scale,n.originPoint)}function nI(e,t){e.min=e.min+t,e.max=e.max+t}function nB(e,t,n,r,i=.5){let o=eS(e.min,e.max,i);nN(e,t,n,o,r)}function nU(e,t){nB(e.x,t.x,t.scaleX,t.scale,t.originX),nB(e.y,t.y,t.scaleY,t.scale,t.originY)}function nz(e,t){return nb(function(e,t){if(!t)return e;let n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let nW=({current:e})=>e?e.ownerDocument.defaultView:null;function n_(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let n$=(e,t)=>Math.abs(e-t);class nH{constructor(e,t,{transformPagePoint:n,contextWindow:r,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=nY(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){return Math.sqrt(n$(e.x,t.x)**2+n$(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!n)return;let{point:r}=e,{timestamp:i}=g;this.history.push({...r,timestamp:i});let{onStart:o,onMove:s}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),s&&s(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=nK(t,this.transformPagePoint),m.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=nY("pointercancel"===e.type?this.lastMoveEventInfo:nK(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,o),r&&r(e,o)},!nf(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.contextWindow=r||window;let o=nK(ng(e),this.transformPagePoint),{point:s}=o,{timestamp:a}=g;this.history=[{...s,timestamp:a}];let{onSessionStart:l}=t;l&&l(e,nY(o,this.history)),this.removeListeners=N(ny(this.contextWindow,"pointermove",this.handlePointerMove),ny(this.contextWindow,"pointerup",this.handlePointerUp),ny(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),f(this.updatePoint)}}function nK(e,t){return t?{point:t(e.point)}:e}function nX(e,t){return{x:e.x-t.x,y:e.y-t.y}}function nY({point:e},t){return{point:e,delta:nX(e,nq(t)),offset:nX(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null,i=nq(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>I(.1)));)n--;if(!r)return{x:0,y:0};let o=B(i.timestamp-r.timestamp);if(0===o)return{x:0,y:0};let s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(t,.1)}}function nq(e){return e[e.length-1]}function nG(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function nZ(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function nQ(e,t,n){return{min:nJ(e,t),max:nJ(e,n)}}function nJ(e,t){return"number"==typeof e?e:e[t]||0}let n0=new WeakMap;class n1{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=nC(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new nH(e,{onSessionStart:e=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(ng(e).point)},onStart:(e,t)=>{let{drag:n,dragPropagation:r,onDragStart:i}=this.getProps();if(n&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(np[e])return null;else return np[e]=!0,()=>{np[e]=!1};return np.x||np.y?null:(np.x=np.y=!0,()=>{np.x=np.y=!1})}(n),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),nR(e=>{let t=this.getAxisMotionValue(e).get()||0;if(el.test(t)){let{projection:n}=this.visualElement;if(n&&n.layout){let r=n.layout.layoutBox[e];r&&(t=nx(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),i&&m.postRender(()=>i(e,t)),j(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:s}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}(s),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,s),this.updateAxis("y",t.point,s),this.visualElement.render(),o&&o(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>nR(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:nW(this.visualElement)})}stop(e,t){let n=this.isDragging;if(this.cancel(),!n)return;let{velocity:r}=t;this.startAnimation(r);let{onDragEnd:i}=this.getProps();i&&m.postRender(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){let{drag:r}=this.getProps();if(!n||!n2(e,r,this.currentDirection))return;let i=this.getAxisMotionValue(e),o=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(o=function(e,{min:t,max:n},r){return void 0!==t&&e<t?e=r?eS(t,e,r.min):Math.max(e,t):void 0!==n&&e>n&&(e=r?eS(n,e,r.max):Math.min(e,n)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;e&&n_(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(e,{top:t,left:n,bottom:r,right:i}){return{x:nG(e.x,n,i),y:nG(e.y,t,r)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:nQ(e,"left","right"),y:nQ(e,"top","bottom")}}(t),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&nR(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!n_(t))return!1;let r=t.current;W(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(e,t,n){let r=nz(e,n),{scroll:i}=t;return i&&(nI(r.x,i.offset.x),nI(r.y,i.offset.y)),r}(r,i.root,this.visualElement.getTransformPagePoint()),s=(e=i.layout.layoutBox,{x:nZ(e.x,o.x),y:nZ(e.y,o.y)});if(n){let e=n(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(s));this.hasMutatedConstraints=!!e,e&&(s=nb(e))}return s}startAnimation(e){let{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:s}=this.getProps(),a=this.constraints||{};return Promise.all(nR(s=>{if(!n2(s,t,this.currentDirection))return;let l=a&&a[s]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:n?e[s]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(s,u)})).then(s)}startAxisValueAnimation(e,t){let n=this.getAxisMotionValue(e);return j(this.visualElement,e),n.start(t6(e,n,0,t,this.visualElement,!1))}stopAnimation(){nR(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){nR(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,n=this.visualElement.getProps();return n[t]||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){nR(t=>{let{drag:n}=this.getProps();if(!n2(t,n,this.currentDirection))return;let{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){let{min:n,max:o}=r.layout.layoutBox[t];i.set(e[t]-eS(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!n_(t)||!n||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};nR(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let n=t.get();r[e]=function(e,t){let n=.5,r=nx(e),i=nx(t);return i>r?n=to(t.min,t.max-r,e.min):r>i&&(n=to(e.min,e.max-i,t.min)),F(0,1,n)}({min:n,max:n},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),nR(t=>{if(!n2(t,e,null))return;let n=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];n.set(eS(i,o,r[t]))})}addListeners(){if(!this.visualElement.current)return;n0.set(this.visualElement,this);let e=ny(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();n_(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),m.read(t);let i=nm(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(nR(t=>{let n=this.getAxisMotionValue(t);n&&(this.originPoint[t]+=e[t].translate,n.set(n.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),r(),o&&o()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:s=!0}=e;return{...e,drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:o,dragMomentum:s}}}function n2(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}class n5 extends nu{constructor(e){super(e),this.removeGroupControls=u,this.removeListeners=u,this.controls=new n1(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let n9=e=>(t,n)=>{e&&m.postRender(()=>e(t,n))};class n4 extends nu{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(e){this.session=new nH(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nW(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:n9(e),onStart:n9(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&m.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=ny(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var n6=n(5155);let{schedule:n3}=p(queueMicrotask,!1);var n8=n(2115),n7=n(2082),re=n(869);let rt=(0,n8.createContext)({}),rn={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rr(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let ri={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!eu.test(e))return e;else e=parseFloat(e);let n=rr(e,t.target.x),r=rr(e,t.target.y);return`${n}% ${r}%`}},ro={};class rs extends n8.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;for(let e in rl)ro[e]=rl[e],$(e)&&(ro[e].isCSSVariable=!0);i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),rn.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,{projection:o}=n;return o&&(o.isPresent=i,r||e.layoutDependency!==t||void 0===t||e.isPresent!==i?o.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?o.promote():o.relegate()||m.postRender(()=>{let e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),n3.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function ra(e){let[t,n]=(0,n7.xQ)(),r=(0,n8.useContext)(re.L);return(0,n6.jsx)(rs,{...e,layoutGroup:r,switchLayoutGroup:(0,n8.useContext)(rt),isPresent:t,safeToRemove:n})}let rl={borderRadius:{...ri,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ri,borderTopRightRadius:ri,borderBottomLeftRadius:ri,borderBottomRightRadius:ri,boxShadow:{correct:(e,{treeScale:t,projectionDelta:n})=>{let r=eE.parse(e);if(r.length>5)return e;let i=eE.createTransformer(e),o=+("number"!=typeof r[0]),s=n.x.scale*t.x,a=n.y.scale*t.y;r[0+o]/=s,r[1+o]/=a;let l=eS(s,a,.5);return"number"==typeof r[2+o]&&(r[2+o]/=l),"number"==typeof r[3+o]&&(r[3+o]/=l),i(r)}}};var ru=n(6983);function rc(e){return(0,ru.G)(e)&&"ownerSVGElement"in e}let rd=(e,t)=>e.depth-t.depth;class rh{constructor(){this.children=[],this.isDirty=!1}add(e){w(this.children,e),this.isDirty=!0}remove(e){k(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(rd),this.isDirty=!1,this.children.forEach(e)}}function rp(e){return D(e)?e.get():e}let rm=["TopLeft","TopRight","BottomLeft","BottomRight"],rf=rm.length,rg=e=>"string"==typeof e?parseFloat(e):e,rv=e=>"number"==typeof e||eu.test(e);function ry(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let rb=rw(0,.5,e7),rx=rw(.5,.95,u);function rw(e,t,n){return r=>r<e?0:r>t?1:n(to(e,t,r))}function rk(e,t){e.min=t.min,e.max=t.max}function rE(e,t){rk(e.x,t.x),rk(e.y,t.y)}function rT(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function rP(e,t,n,r,i){return e-=t,e=r+1/n*(e-r),void 0!==i&&(e=r+1/i*(e-r)),e}function rS(e,t,[n,r,i],o,s){!function(e,t=0,n=1,r=.5,i,o=e,s=e){if(el.test(t)&&(t=parseFloat(t),t=eS(s.min,s.max,t/100)-s.min),"number"!=typeof t)return;let a=eS(o.min,o.max,r);e===o&&(a-=t),e.min=rP(e.min,t,n,a,i),e.max=rP(e.max,t,n,a,i)}(e,t[n],t[r],t[i],t.scale,o,s)}let rA=["x","scaleX","originX"],rM=["y","scaleY","originY"];function rC(e,t,n,r){rS(e.x,t,rA,n?n.x:void 0,r?r.x:void 0),rS(e.y,t,rM,n?n.y:void 0,r?r.y:void 0)}function rR(e){return 0===e.translate&&1===e.scale}function rD(e){return rR(e.x)&&rR(e.y)}function rj(e,t){return e.min===t.min&&e.max===t.max}function rV(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function rL(e,t){return rV(e.x,t.x)&&rV(e.y,t.y)}function rO(e){return nx(e.x)/nx(e.y)}function rN(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class rF{constructor(){this.members=[]}add(e){w(this.members,e),e.scheduleRender()}remove(e){if(k(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,n=this.members.findIndex(t=>e===t);if(0===n)return!1;for(let e=n;e>=0;e--){let n=this.members[e];if(!1!==n.isPresent){t=n;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rI={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},rB=["","X","Y","Z"],rU={visibility:"hidden"},rz=0;function rW(e,t,n,r){let{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function r_({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(e={},n=t?.()){this.id=rz++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,h.value&&(rI.nodes=rI.calculatedTargetDeltas=rI.calculatedProjections=0),this.nodes.forEach(rK),this.nodes.forEach(rJ),this.nodes.forEach(r0),this.nodes.forEach(rX),h.addProjectionMetrics&&h.addProjectionMetrics(rI)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new rh)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new E),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let n=this.eventHandlers.get(e);n&&n.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=rc(t)&&!(rc(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:n,layout:r,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||n)&&(this.isLayoutDirty=!0),e){let n,r=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){let n=P.now(),r=({timestamp:i})=>{let o=i-n;o>=250&&(f(r),e(o-t))};return m.setup(r,!0),()=>f(r)}(r,250),rn.hasAnimatedSinceResize&&(rn.hasAnimatedSinceResize=!1,this.nodes.forEach(rQ))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&i&&(n||r)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:n,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||i.getDefaultTransition()||r6,{onLayoutAnimationStart:s,onLayoutAnimationComplete:a}=i.getProps(),u=!this.targetLayout||!rL(this.targetLayout,r),c=!t&&n;if(this.options.layoutRoot||this.resumeFrom||c||t&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...l(o,"layout"),onPlay:s,onComplete:a};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,c)}else t||rQ(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),f(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(r1),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:n}=t.options;if(!n)return;let r=n.props[L];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:n}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",m,!(e||n))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rq);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(rG);this.isUpdating||this.nodes.forEach(rG),this.animationCommitId=this.animationId,this.isUpdating=!1,this.nodes.forEach(rZ),this.nodes.forEach(r$),this.nodes.forEach(rH),this.clearAllSnapshots();let e=P.now();g.delta=F(0,1e3/60,e-g.timestamp),g.timestamp=e,g.isProcessing=!0,v.update.process(g),v.preRender.process(g),v.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,n3.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rY),this.sharedNodes.forEach(r2)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,m.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){m.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||nx(this.snapshot.measuredBox.x)||nx(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=nC(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=r(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!rD(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,o=r!==this.prevTransformTemplateValue;e&&this.instance&&(t||nV(this.latestValues)||o)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let n=this.measurePageBox(),r=this.removeElementScroll(n);return e&&(r=this.removeTransform(r)),r7((t=r).x),r7(t.y),{animationId:this.root.animationId,measuredBox:n,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return nC();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(it))){let{scroll:e}=this.root;e&&(nI(t.x,e.offset.x),nI(t.y,e.offset.y))}return t}removeElementScroll(e){let t=nC();if(rE(t,e),this.scroll?.wasRoot)return t;for(let n=0;n<this.path.length;n++){let r=this.path[n],{scroll:i,options:o}=r;r!==this.root&&i&&o.layoutScroll&&(i.wasRoot&&rE(t,e),nI(t.x,i.offset.x),nI(t.y,i.offset.y))}return t}applyTransform(e,t=!1){let n=nC();rE(n,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&nU(n,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),nV(r.latestValues)&&nU(n,r.latestValues)}return nV(this.latestValues)&&nU(n,this.latestValues),n}removeTransform(e){let t=nC();rE(t,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];if(!n.instance||!nV(n.latestValues))continue;nj(n.latestValues)&&n.updateSnapshot();let r=nC();rE(r,n.measurePageBox()),rC(t,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,r)}return nV(this.latestValues)&&rC(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let n=!!this.resumingFrom||this!==t;if(!(e||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:i}=this.options;if(this.layout&&(r||i)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nC(),this.relativeTargetOrigin=nC(),nP(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),rE(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=nC(),this.targetWithTransforms=nC()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var o,s,a;this.forceRelativeParentToResolveTarget(),o=this.target,s=this.relativeTarget,a=this.relativeParent.target,nE(o.x,s.x,a.x),nE(o.y,s.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rE(this.target,this.layout.layoutBox),nF(this.target,this.targetDelta)):rE(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nC(),this.relativeTargetOrigin=nC(),nP(this.relativeTargetOrigin,this.target,e.target),rE(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}h.value&&rI.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||nj(this.parent.latestValues)||nL(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===g.timestamp&&(n=!1),n)return;let{layout:r,layoutId:i}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||i))return;rE(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,s=this.treeScale.y;!function(e,t,n,r=!1){let i,o,s=n.length;if(s){t.x=t.y=1;for(let a=0;a<s;a++){o=(i=n[a]).projectionDelta;let{visualElement:s}=i.options;(!s||!s.props.style||"contents"!==s.props.style.display)&&(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&nU(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,nF(e,o)),r&&nV(i.latestValues)&&nU(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=nC());let{target:a}=e;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rT(this.prevProjectionDelta.x,this.projectionDelta.x),rT(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),nk(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===o&&this.treeScale.y===s&&rN(this.projectionDelta.x,this.prevProjectionDelta.x)&&rN(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),h.value&&rI.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=nA(),this.projectionDelta=nA(),this.projectionDeltaWithTransform=nA()}setAnimationOrigin(e,t=!1){let n,r=this.snapshot,i=r?r.latestValues:{},o={...this.latestValues},s=nA();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let a=nC(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(r4));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(r5(s.x,e.x,r),r5(s.y,e.y,r),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,h,p,m,f,g;nP(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,g=r,r9(p.x,m.x,f.x,g),r9(p.y,m.y,f.y,g),n&&(u=this.relativeTarget,h=n,rj(u.x,h.x)&&rj(u.y,h.y))&&(this.isProjectionDirty=!1),n||(n=nC()),rE(n,this.relativeTarget)}l&&(this.animationValues=o,function(e,t,n,r,i,o){i?(e.opacity=eS(0,n.opacity??1,rb(r)),e.opacityExit=eS(t.opacity??1,0,rx(r))):o&&(e.opacity=eS(t.opacity??1,n.opacity??1,r));for(let i=0;i<rf;i++){let o=`border${rm[i]}Radius`,s=ry(t,o),a=ry(n,o);(void 0!==s||void 0!==a)&&(s||(s=0),a||(a=0),0===s||0===a||rv(s)===rv(a)?(e[o]=Math.max(eS(rg(s),rg(a),r),0),(el.test(a)||el.test(s))&&(e[o]+="%")):e[o]=a)}(t.rotate||n.rotate)&&(e.rotate=eS(t.rotate||0,n.rotate||0,r))}(o,i,this.latestValues,r,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(f(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=m.update(()=>{rn.hasAnimatedSinceResize=!0,U.layout++,this.motionValue||(this.motionValue=C(0)),this.currentAnimation=function(e,t,n){let r=D(e)?e:C(e);return r.start(t6("",r,t,n)),r.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{U.layout--},onComplete:()=>{U.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&ie(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||nC();let t=nx(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;let r=nx(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}rE(t,n),nU(t,i),nk(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new rF),this.sharedNodes.get(e).add(t);let n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:n}={}){let r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:n}=e;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(t=!0),!t)return;let r={};n.z&&rW("z",e,r,this.animationValues);for(let t=0;t<rB.length;t++)rW(`rotate${rB[t]}`,e,r,this.animationValues),rW(`skew${rB[t]}`,e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return rU;let t={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity="",t.pointerEvents=rp(e?.pointerEvents)||"",t.transform=n?n(this.latestValues,""):"none",t;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=rp(e?.pointerEvents)||""),this.hasProjected&&!nV(this.latestValues)&&(t.transform=n?n({},""):"none",this.hasProjected=!1),t}let i=r.animationValues||r.latestValues;this.applyTransformsToTarget(),t.transform=function(e,t,n){let r="",i=e.x.translate/t.x,o=e.y.translate/t.y,s=n?.z||0;if((i||o||s)&&(r=`translate3d(${i}px, ${o}px, ${s}px) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:o,skewX:s,skewY:a}=n;e&&(r=`perspective(${e}px) ${r}`),t&&(r+=`rotate(${t}deg) `),i&&(r+=`rotateX(${i}deg) `),o&&(r+=`rotateY(${o}deg) `),s&&(r+=`skewX(${s}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,i),n&&(t.transform=n(i,t.transform));let{x:o,y:s}=this.projectionDelta;for(let e in t.transformOrigin=`${100*o.origin}% ${100*s.origin}% 0`,r.animationValues?t.opacity=r===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:t.opacity=r===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0,ro){if(void 0===i[e])continue;let{correct:n,applyTo:o,isCSSVariable:s}=ro[e],a="none"===t.transform?i[e]:n(i[e],r);if(o){let e=o.length;for(let n=0;n<e;n++)t[o[n]]=a}else s?this.options.visualElement.renderState.vars[e]=a:t[e]=a}return this.options.layoutId&&(t.pointerEvents=r===this?rp(e?.pointerEvents)||"":"none"),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(rq),this.root.sharedNodes.clear()}}}function r$(e){e.updateLayout()}function rH(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:n,measuredBox:r}=e.layout,{animationType:i}=e.options,o=t.source!==e.layout.source;"size"===i?nR(e=>{let r=o?t.measuredBox[e]:t.layoutBox[e],i=nx(r);r.min=n[e].min,r.max=r.min+i}):ie(i,t.layoutBox,n)&&nR(r=>{let i=o?t.measuredBox[r]:t.layoutBox[r],s=nx(n[r]);i.max=i.min+s,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+s)});let s=nA();nk(s,n,t.layoutBox);let a=nA();o?nk(a,e.applyTransform(r,!0),t.measuredBox):nk(a,n,t.layoutBox);let l=!rD(s),u=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:i,layout:o}=r;if(i&&o){let s=nC();nP(s,t.layoutBox,i.layoutBox);let a=nC();nP(a,n,o.layoutBox),rL(s,a)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=a,e.relativeTargetOrigin=s,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:t,delta:a,layoutDelta:s,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function rK(e){h.value&&rI.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function rX(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function rY(e){e.clearSnapshot()}function rq(e){e.clearMeasurements()}function rG(e){e.isLayoutDirty=!1}function rZ(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function rQ(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function rJ(e){e.resolveTargetDelta()}function r0(e){e.calcProjection()}function r1(e){e.resetSkewAndRotation()}function r2(e){e.removeLeadSnapshot()}function r5(e,t,n){e.translate=eS(t.translate,0,n),e.scale=eS(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function r9(e,t,n,r){e.min=eS(t.min,n.min,r),e.max=eS(t.max,n.max,r)}function r4(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let r6={duration:.45,ease:[.4,0,.1,1]},r3=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),r8=r3("applewebkit/")&&!r3("chrome/")?Math.round:u;function r7(e){e.min=r8(e.min),e.max=r8(e.max)}function ie(e,t,n){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(rO(t)-rO(n)))}function it(e){return e!==e.root&&e.scroll?.wasRoot}let ir=r_({attachResizeListener:(e,t)=>nm(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ii={current:void 0},io=r_({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ii.current){let e=new ir({});e.mount(window),e.setOptions({layoutScroll:!0}),ii.current=e}return ii.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function is(e,t){let n=function(e,t,n){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,n=(void 0)??t.querySelectorAll(e);return n?Array.from(n):[]}return Array.from(e)}(e),r=new AbortController;return[n,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function ia(e){return!("touch"===e.pointerType||np.x||np.y)}function il(e,t,n){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===n);let i=r["onHover"+n];i&&m.postRender(()=>i(t,ng(t)))}class iu extends nu{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=is(e,n),s=e=>{if(!ia(e))return;let{target:n}=e,r=t(n,e);if("function"!=typeof r||!n)return;let o=e=>{ia(e)&&(r(e),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,i)};return r.forEach(e=>{e.addEventListener("pointerenter",s,i)}),o}(e,(e,t)=>(il(this.node,t,"Start"),e=>il(this.node,e,"End"))))}unmount(){}}class ic extends nu{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=N(nm(this.node.current,"focus",()=>this.onFocus()),nm(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let id=(e,t)=>!!t&&(e===t||id(e,t.parentElement)),ih=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),ip=new WeakSet;function im(e){return t=>{"Enter"===t.key&&e(t)}}function ig(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let iv=(e,t)=>{let n=e.currentTarget;if(!n)return;let r=im(()=>{if(ip.has(n))return;ig(n,"down");let e=im(()=>{ig(n,"up")});n.addEventListener("keyup",e,t),n.addEventListener("blur",()=>ig(n,"cancel"),t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function iy(e){return nf(e)&&!(np.x||np.y)}function ib(e,t,n){let{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===n);let i=r["onTap"+("End"===n?"":n)];i&&m.postRender(()=>i(t,ng(t)))}class ix extends nu{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=is(e,n),s=e=>{let r=e.currentTarget;if(!iy(e))return;ip.add(r);let o=t(r,e),s=(e,t)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),ip.has(r)&&ip.delete(r),iy(e)&&"function"==typeof o&&o(e,{success:t})},a=e=>{s(e,r===window||r===document||n.useGlobalTarget||id(r,e.target))},l=e=>{s(e,!1)};window.addEventListener("pointerup",a,i),window.addEventListener("pointercancel",l,i)};return r.forEach(e=>{((n.useGlobalTarget?window:e).addEventListener("pointerdown",s,i),(0,tG.s)(e))&&(e.addEventListener("focus",e=>iv(e,i)),ih.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),o}(e,(e,t)=>(ib(this.node,t,"Start"),(e,{success:t})=>ib(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let iw=new WeakMap,ik=new WeakMap,iE=e=>{let t=iw.get(e.target);t&&t(e)},iT=e=>{e.forEach(iE)},iP={some:0,all:1};class iS extends nu{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:n,threshold:"number"==typeof r?r:iP[r]};return function(e,t,n){let r=function({root:e,...t}){let n=e||document;ik.has(n)||ik.set(n,{});let r=ik.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(iT,{root:e,...t})),r[i]}(t);return iw.set(e,n),r.observe(e),()=>{iw.delete(e),r.unobserve(e)}}(this.node.current,o,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),o=t?n:r;o&&o(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}(e,t))&&this.startObserver()}unmount(){}}let iA=(0,n8.createContext)({strict:!1});var iM=n(1508);let iC=(0,n8.createContext)({});function iR(e){return i(e.animate)||nr.some(t=>nt(e[t]))}function iD(e){return!!(iR(e)||e.variants)}function ij(e){return Array.isArray(e)?e.join(" "):e}var iV=n(8972);let iL={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},iO={};for(let e in iL)iO[e]={isEnabled:t=>iL[e].some(e=>!!t[e])};let iN=Symbol.for("motionComponentSymbol");var iF=n(845),iI=n(7494);function iB(e,{layout:t,layoutId:n}){return b.has(e)||e.startsWith("origin")||(t||void 0!==n)&&(!!ro[e]||"opacity"===e)}let iU=(e,t)=>t&&"number"==typeof e?t.transform(e):e,iz={...Y,transform:Math.round},iW={borderWidth:eu,borderTopWidth:eu,borderRightWidth:eu,borderBottomWidth:eu,borderLeftWidth:eu,borderRadius:eu,radius:eu,borderTopLeftRadius:eu,borderTopRightRadius:eu,borderBottomRightRadius:eu,borderBottomLeftRadius:eu,width:eu,maxWidth:eu,height:eu,maxHeight:eu,top:eu,right:eu,bottom:eu,left:eu,padding:eu,paddingTop:eu,paddingRight:eu,paddingBottom:eu,paddingLeft:eu,margin:eu,marginTop:eu,marginRight:eu,marginBottom:eu,marginLeft:eu,backgroundPositionX:eu,backgroundPositionY:eu,rotate:ea,rotateX:ea,rotateY:ea,rotateZ:ea,scale:G,scaleX:G,scaleY:G,scaleZ:G,skew:ea,skewX:ea,skewY:ea,distance:eu,translateX:eu,translateY:eu,translateZ:eu,x:eu,y:eu,z:eu,perspective:eu,transformPerspective:eu,opacity:q,originX:eh,originY:eh,originZ:eu,zIndex:iz,fillOpacity:q,strokeOpacity:q,numOctaves:iz},i_={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},i$=y.length;function iH(e,t,n){let{style:r,vars:i,transformOrigin:o}=e,s=!1,a=!1;for(let e in t){let n=t[e];if(b.has(e)){s=!0;continue}if($(e)){i[e]=n;continue}{let t=iU(n,iW[e]);e.startsWith("origin")?(a=!0,o[e]=t):r[e]=t}}if(!t.transform&&(s||n?r.transform=function(e,t,n){let r="",i=!0;for(let o=0;o<i$;o++){let s=y[o],a=e[s];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!s.startsWith("scale"):0===parseFloat(a))||n){let e=iU(a,iW[s]);if(!l){i=!1;let t=i_[s]||s;r+=`${t}(${e}) `}n&&(t[s]=e)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}(t,e.transform,n):r.transform&&(r.transform="none")),a){let{originX:e="50%",originY:t="50%",originZ:n=0}=o;r.transformOrigin=`${e} ${t} ${n}`}}let iK=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function iX(e,t,n){for(let r in t)D(t[r])||iB(r,n)||(e[r]=t[r])}let iY={offset:"stroke-dashoffset",array:"stroke-dasharray"},iq={offset:"strokeDashoffset",array:"strokeDasharray"};function iG(e,{attrX:t,attrY:n,attrScale:r,pathLength:i,pathSpacing:o=1,pathOffset:s=0,...a},l,u,c){if(iH(e,a,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:d,style:h}=e;d.transform&&(h.transform=d.transform,delete d.transform),(h.transform||d.transformOrigin)&&(h.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),h.transform&&(h.transformBox=c?.transformBox??"fill-box",delete d.transformBox),void 0!==t&&(d.x=t),void 0!==n&&(d.y=n),void 0!==r&&(d.scale=r),void 0!==i&&function(e,t,n=1,r=0,i=!0){e.pathLength=1;let o=i?iY:iq;e[o.offset]=eu.transform(-r);let s=eu.transform(t),a=eu.transform(n);e[o.array]=`${s} ${a}`}(d,i,o,s,!1)}let iZ=()=>({...iK(),attrs:{}}),iQ=e=>"string"==typeof e&&"svg"===e.toLowerCase(),iJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function i0(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||iJ.has(e)}let i1=e=>!i0(e);try{!function(e){"function"==typeof e&&(i1=t=>t.startsWith("on")?!i0(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let i2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function i5(e){if("string"!=typeof e||e.includes("-"));else if(i2.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var i9=n(2885);let i4=e=>(t,n)=>{let r=(0,n8.useContext)(iC),o=(0,n8.useContext)(iF.t),a=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},n,r,o){return{latestValues:function(e,t,n,r){let o={},a=r(e,{});for(let e in a)o[e]=rp(a[e]);let{initial:l,animate:u}=e,c=iR(e),d=iD(e);t&&d&&!c&&!1!==e.inherit&&(void 0===l&&(l=t.initial),void 0===u&&(u=t.animate));let h=!!n&&!1===n.initial,p=(h=h||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!i(p)){let t=Array.isArray(p)?p:[p];for(let n=0;n<t.length;n++){let r=s(e,t[n]);if(r){let{transitionEnd:e,transition:t,...n}=r;for(let e in n){let t=n[e];if(Array.isArray(t)){let e=h?t.length-1:0;t=t[e]}null!==t&&(o[e]=t)}for(let t in e)o[t]=e[t]}}}return o}(n,r,o,e),renderState:t()}})(e,t,r,o);return n?a():(0,i9.M)(a)};function i6(e,t,n){let{style:r}=e,i={};for(let o in r)(D(r[o])||t.style&&D(t.style[o])||iB(o,e)||n?.getValue(o)?.liveStyle!==void 0)&&(i[o]=r[o]);return i}let i3={useVisualState:i4({scrapeMotionValuesFromProps:i6,createRenderState:iK})};function i8(e,t,n){let r=i6(e,t,n);for(let n in e)(D(e[n])||D(t[n]))&&(r[-1!==y.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=e[n]);return r}let i7={useVisualState:i4({scrapeMotionValuesFromProps:i8,createRenderState:iZ})},oe=e=>t=>t.test(e),ot=[Y,eu,el,ea,ed,ec,{test:e=>"auto"===e,parse:e=>e}],on=e=>ot.find(oe(e)),or=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),oi=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,oo=e=>/^0[^.\s]+$/u.test(e),os=new Set(["brightness","contrast","saturate","opacity"]);function oa(e){let[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=n.match(Q)||[];if(!r)return e;let i=n.replace(r,""),o=+!!os.has(t);return r!==n&&(o*=100),t+"("+o+i+")"}let ol=/\b([a-z-]*)\(.*?\)/gu,ou={...eE,getAnimatableNone:e=>{let t=e.match(ol);return t?t.map(oa).join(" "):e}},oc={...iW,color:em,backgroundColor:em,outlineColor:em,fill:em,stroke:em,borderColor:em,borderTopColor:em,borderRightColor:em,borderBottomColor:em,borderLeftColor:em,filter:ou,WebkitFilter:ou},od=e=>oc[e];function oh(e,t){let n=od(e);return n!==ou&&(n=eE),n.getAnimatableNone?n.getAnimatableNone(t):void 0}let op=new Set(["auto","none","0"]);class om extends tO{constructor(e,t,n,r,i){super(e,t,n,r,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:n}=this;if(!t||!t.current)return;super.readKeyframes();for(let n=0;n<e.length;n++){let r=e[n];if("string"==typeof r&&K(r=r.trim())){let i=function e(t,n,r=1){W(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,o]=function(e){let t=oi.exec(e);if(!t)return[,];let[,n,r,i]=t;return[`--${n??r}`,i]}(t);if(!i)return;let s=window.getComputedStyle(n).getPropertyValue(i);if(s){let e=s.trim();return or(e)?parseFloat(e):e}return K(o)?e(o,n,r+1):o}(r,t.current);void 0!==i&&(e[n]=i),n===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!x.has(n)||2!==e.length)return;let[r,i]=e,o=on(r),s=on(i);if(o!==s)if(tP(o)&&tP(s))for(let t=0;t<e.length;t++){let n=e[t];"string"==typeof n&&(e[t]=parseFloat(n))}else tM[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,n=[];for(let t=0;t<e.length;t++){var r;(null===e[t]||("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||oo(r)))&&n.push(t)}n.length&&function(e,t,n){let r,i=0;for(;i<e.length&&!r;){let t=e[i];"string"==typeof t&&!op.has(t)&&eb(t).values.length&&(r=e[i]),i++}if(r&&n)for(let i of t)e[i]=oh(n,r)}(e,n,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:n}=this;if(!e||!e.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tM[n](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(n,r).jump(r,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:n}=this;if(!e||!e.current)return;let r=e.getValue(t);r&&r.jump(this.measuredOrigin,!1);let i=n.length-1,o=n[i];n[i]=tM[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,n])=>{e.getValue(t).set(n)}),this.resolveNoneKeyframes()}}let of=[...ot,em,eE],og=e=>of.find(oe(e)),ov={current:null},oy={current:!1},ob=new WeakMap,ox=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ow{scrapeMotionValuesFromProps(e,t,n){return{}}constructor({parent:e,props:t,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:i,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tO,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=P.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,m.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=t.initial?{...a}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=s,this.blockInitialAnimation=!!i,this.isControllingVariants=iR(t),this.isVariantNode=iD(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in c){let t=c[e];void 0!==a[e]&&D(t)&&t.set(a[e],!1)}}mount(e){this.current=e,ob.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),oy.current||function(){if(oy.current=!0,iV.B)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>ov.current=e.matches;e.addListener(t),t()}else ov.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ov.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),f(this.notifyUpdate),f(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let n;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=b.has(e);r&&this.onBindTransform&&this.onBindTransform();let i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&m.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(n=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),o(),n&&n(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in iO){let t=iO[e];if(!t)continue;let{isEnabled:n,Feature:r}=t;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):nC()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<ox.length;t++){let n=ox[t];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let r=e["on"+n];r&&(this.propEventSubscriptions[n]=this.on(n,r))}this.prevMotionValues=function(e,t,n){for(let r in t){let i=t[r],o=n[r];if(D(i))e.addValue(r,i);else if(D(o))e.addValue(r,C(i,{owner:e}));else if(o!==i)if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(r);e.addValue(r,C(void 0!==t?t:i,{owner:e}))}}for(let r in n)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let n=this.values.get(e);t!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=C(null===t?void 0:t,{owner:this}),this.addValue(e,n)),n}readValue(e,t){let n=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=n&&("string"==typeof n&&(or(n)||oo(n))?n=parseFloat(n):!og(n)&&eE.test(t)&&(n=oh(e,t)),this.setBaseTarget(e,D(n)?n.get():n)),D(n)?n.get():n}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let r=s(this.props,n,this.presenceContext?.custom);r&&(t=r[e])}if(n&&void 0!==t)return t;let r=this.getBaseTargetFromProps(this.props,e);return void 0===r||D(r)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:r}on(e,t){return this.events[e]||(this.events[e]=new E),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class ok extends ow{constructor(){super(...arguments),this.KeyframeResolver=om}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:n}){delete t[e],delete n[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;D(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function oE(e,{style:t,vars:n},r,i){for(let o in Object.assign(e.style,t,i&&i.getProjectionStyles(r)),n)e.style.setProperty(o,n[o])}class oT extends ok{constructor(){super(...arguments),this.type="html",this.renderInstance=oE}readValueFromInstance(e,t){if(b.has(t))return this.projection?.isProjecting?tw(t):tE(e,t);{let n=window.getComputedStyle(e),r=($(t)?n.getPropertyValue(t):n[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return nz(e,t)}build(e,t,n){iH(e,t,n.transformTemplate)}scrapeMotionValuesFromProps(e,t,n){return i6(e,t,n)}}let oP=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class oS extends ok{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=nC}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(b.has(t)){let e=od(t);return e&&e.default||0}return t=oP.has(t)?t:V(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,n){return i8(e,t,n)}build(e,t,n){iG(e,t,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(e,t,n,r){for(let n in oE(e,t,void 0,r),t.attrs)e.setAttribute(oP.has(n)?n:V(n),t.attrs[n])}mount(e){this.isSVGTag=iQ(e.tagName),super.mount(e)}}let oA=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(n,r)=>"create"===r?e:(t.has(r)||t.set(r,e(r)),t.get(r))})}((tY={animation:{Feature:nc},exit:{Feature:nh},inView:{Feature:iS},tap:{Feature:ix},focus:{Feature:ic},hover:{Feature:iu},pan:{Feature:n4},drag:{Feature:n5,ProjectionNode:io,MeasureLayout:ra},layout:{ProjectionNode:io,MeasureLayout:ra}},tq=(e,t)=>i5(e)?new oS(t):new oT(t,{allowProjection:e!==n8.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function(e){var t,n;let{preloadedFeatures:r,createVisualElement:i,useRender:o,useVisualState:s,Component:a}=e;function l(e,t){var n,r,l;let u,c={...(0,n8.useContext)(iM.Q),...e,layoutId:function(e){let{layoutId:t}=e,n=(0,n8.useContext)(re.L).id;return n&&void 0!==t?n+"-"+t:t}(e)},{isStatic:d}=c,h=function(e){let{initial:t,animate:n}=function(e,t){if(iR(e)){let{initial:t,animate:n}=e;return{initial:!1===t||nt(t)?t:void 0,animate:nt(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,n8.useContext)(iC));return(0,n8.useMemo)(()=>({initial:t,animate:n}),[ij(t),ij(n)])}(e),p=s(e,d);if(!d&&iV.B){r=0,l=0,(0,n8.useContext)(iA).strict;let e=function(e){let{drag:t,layout:n}=iO;if(!t&&!n)return{};let r={...t,...n};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==n?void 0:n.isEnabled(e))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(c);u=e.MeasureLayout,h.visualElement=function(e,t,n,r,i){let{visualElement:o}=(0,n8.useContext)(iC),s=(0,n8.useContext)(iA),a=(0,n8.useContext)(iF.t),l=(0,n8.useContext)(iM.Q).reducedMotion,u=(0,n8.useRef)(null);r=r||s.renderer,!u.current&&r&&(u.current=r(e,{visualState:t,parent:o,props:n,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let c=u.current,d=(0,n8.useContext)(rt);c&&!c.projection&&i&&("html"===c.type||"svg"===c.type)&&function(e,t,n,r){let{layoutId:i,layout:o,drag:s,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!s||a&&n_(a),visualElement:e,animationType:"string"==typeof o?o:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:l,layoutRoot:u})}(u.current,n,i,d);let h=(0,n8.useRef)(!1);(0,n8.useInsertionEffect)(()=>{c&&h.current&&c.update(n,a)});let p=n[L],m=(0,n8.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,iI.E)(()=>{c&&(h.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),n3.render(c.render),m.current&&c.animationState&&c.animationState.animateChanges())}),(0,n8.useEffect)(()=>{c&&(!m.current&&c.animationState&&c.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),c}(a,p,c,i,e.ProjectionNode)}return(0,n6.jsxs)(iC.Provider,{value:h,children:[u&&h.visualElement?(0,n6.jsx)(u,{visualElement:h.visualElement,...c}):null,o(a,e,(n=h.visualElement,(0,n8.useCallback)(e=>{e&&p.onMount&&p.onMount(e),n&&(e?n.mount(e):n.unmount()),t&&("function"==typeof t?t(e):n_(t)&&(t.current=e))},[n])),p,d,h.visualElement)]})}r&&function(e){for(let t in e)iO[t]={...iO[t],...e[t]}}(r),l.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!=(n=null!=(t=a.displayName)?t:a.name)?n:"",")"));let u=(0,n8.forwardRef)(l);return u[iN]=a,u}({...i5(e)?i7:i3,preloadedFeatures:tY,useRender:function(e=!1){return(t,n,r,{latestValues:i},o)=>{let s=(i5(t)?function(e,t,n,r){let i=(0,n8.useMemo)(()=>{let n=iZ();return iG(n,t,iQ(r),e.transformTemplate,e.style),{...n.attrs,style:{...n.style}}},[t]);if(e.style){let t={};iX(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let n={},r=function(e,t){let n=e.style||{},r={};return iX(r,n,e),Object.assign(r,function({transformTemplate:e},t){return(0,n8.useMemo)(()=>{let n=iK();return iH(n,t,e),Object.assign({},n.vars,n.style)},[t])}(e,t)),r}(e,t);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n})(n,i,o,t),a=function(e,t,n){let r={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(i1(i)||!0===n&&i0(i)||!t&&!i0(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(n,"string"==typeof t,e),l=t!==n8.Fragment?{...a,...s,ref:r}:{},{children:u}=n,c=(0,n8.useMemo)(()=>D(u)?u.get():u,[u]);return(0,n8.createElement)(t,{...l,children:c})}}(t),createVisualElement:tq,Component:e})}))},6654:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return i}});let r=n(2115);function i(e,t){let n=(0,r.useRef)(null),i=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=i.current;t&&(i.current=null,t())}else e&&(n.current=o(e,r)),t&&(i.current=o(t,r))},[e,t])}function o(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6874:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return g},useLinkStatus:function(){return y}});let r=n(6966),i=n(5155),o=r._(n(2115)),s=n(2757),a=n(5227),l=n(9818),u=n(6654),c=n(9991),d=n(5929);n(3230);let h=n(4930),p=n(2664),m=n(6634);function f(e){return"string"==typeof e?e:(0,s.formatUrl)(e)}function g(e){let t,n,r,[s,g]=(0,o.useOptimistic)(h.IDLE_LINK_STATUS),y=(0,o.useRef)(null),{href:b,as:x,children:w,prefetch:k=null,passHref:E,replace:T,shallow:P,scroll:S,onClick:A,onMouseEnter:M,onTouchStart:C,legacyBehavior:R=!1,onNavigate:D,ref:j,unstable_dynamicOnHover:V,...L}=e;t=w,R&&("string"==typeof t||"number"==typeof t)&&(t=(0,i.jsx)("a",{children:t}));let O=o.default.useContext(a.AppRouterContext),N=!1!==k,F=null===k?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:I,as:B}=o.default.useMemo(()=>{let e=f(b);return{href:e,as:x?f(x):e}},[b,x]);R&&(n=o.default.Children.only(t));let U=R?n&&"object"==typeof n&&n.ref:j,z=o.default.useCallback(e=>(null!==O&&(y.current=(0,h.mountLinkInstance)(e,I,O,F,N,g)),()=>{y.current&&((0,h.unmountLinkForCurrentNavigation)(y.current),y.current=null),(0,h.unmountPrefetchableInstance)(e)}),[N,I,O,F,g]),W={ref:(0,u.useMergedRef)(z,U),onClick(e){R||"function"!=typeof A||A(e),R&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),O&&(e.defaultPrevented||function(e,t,n,r,i,s,a){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){i&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),o.default.startTransition(()=>{if(a){let e=!1;if(a({preventDefault:()=>{e=!0}}),e)return}(0,m.dispatchNavigateAction)(n||t,i?"replace":"push",null==s||s,r.current)})}}(e,I,B,y,T,S,D))},onMouseEnter(e){R||"function"!=typeof M||M(e),R&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),O&&N&&(0,h.onNavigationIntent)(e.currentTarget,!0===V)},onTouchStart:function(e){R||"function"!=typeof C||C(e),R&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),O&&N&&(0,h.onNavigationIntent)(e.currentTarget,!0===V)}};return(0,c.isAbsoluteUrl)(B)?W.href=B:R&&!E&&("a"!==n.type||"href"in n.props)||(W.href=(0,d.addBasePath)(B)),r=R?o.default.cloneElement(n,W):(0,i.jsx)("a",{...L,...W,children:t}),(0,i.jsx)(v.Provider,{value:s,children:r})}n(3180);let v=(0,o.createContext)(h.IDLE_LINK_STATUS),y=()=>(0,o.useContext)(v);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6983:(e,t,n)=>{n.d(t,{G:()=>r});function r(e){return"object"==typeof e&&null!==e}},7312:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("coffee",[["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M14 2v2",key:"6buw04"}],["path",{d:"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1",key:"pwadti"}],["path",{d:"M6 2v2",key:"colzsn"}]])},7340:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},7351:(e,t,n)=>{n.d(t,{s:()=>i});var r=n(6983);function i(e){return(0,r.G)(e)&&"offsetHeight"in e}},7434:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7494:(e,t,n)=>{n.d(t,{E:()=>i});var r=n(2115);let i=n(8972).B?r.useLayoutEffect:r.useEffect},7576:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},7924:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8175:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},8859:(e,t)=>{function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[n,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(n,r(e));else t.set(n,r(i));return t}function o(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return o},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return i}})},8972:(e,t,n)=>{n.d(t,{B:()=>r});let r="undefined"!=typeof window},9033:(e,t,n)=>{n.d(t,{c:()=>i});var r=n(2115);function i(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9099:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},9621:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]])},9688:(e,t,n)=>{n.d(t,{QP:()=>eu});let r=e=>{let t=a(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:e=>{let n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),i(n,t)||s(e)},getConflictingClassGroupIds:(e,t)=>{let i=n[e]||[];return t&&r[e]?[...i,...r[e]]:i}}},i=(e,t)=>{if(0===e.length)return t.classGroupId;let n=e[0],r=t.nextPart.get(n),o=r?i(e.slice(1),r):void 0;if(o)return o;if(0===t.validators.length)return;let s=e.join("-");return t.validators.find(({validator:e})=>e(s))?.classGroupId},o=/^\[(.+)\]$/,s=e=>{if(o.test(e)){let t=o.exec(e)[1],n=t?.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},a=e=>{let{theme:t,classGroups:n}=e,r={nextPart:new Map,validators:[]};for(let e in n)l(n[e],r,e,t);return r},l=(e,t,n,r)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=n;return}if("function"==typeof e)return c(e)?void l(e(r),t,n,r):void t.validators.push({validator:e,classGroupId:n});Object.entries(e).forEach(([e,i])=>{l(i,u(t,e),n,r)})})},u=(e,t)=>{let n=e;return t.split("-").forEach(e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)}),n},c=e=>e.isThemeGetter,d=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,n=new Map,r=new Map,i=(i,o)=>{n.set(i,o),++t>e&&(t=0,r=n,n=new Map)};return{get(e){let t=n.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(i(e,t),t):void 0},set(e,t){n.has(e)?n.set(e,t):i(e,t)}}},h=e=>{let{prefix:t,experimentalParseClassName:n}=e,r=e=>{let t,n=[],r=0,i=0,o=0;for(let s=0;s<e.length;s++){let a=e[s];if(0===r&&0===i){if(":"===a){n.push(e.slice(o,s)),o=s+1;continue}if("/"===a){t=s;continue}}"["===a?r++:"]"===a?r--:"("===a?i++:")"===a&&i--}let s=0===n.length?e:e.substring(o),a=p(s);return{modifiers:n,hasImportantModifier:a!==s,baseClassName:a,maybePostfixModifierPosition:t&&t>o?t-o:void 0}};if(t){let e=t+":",n=r;r=t=>t.startsWith(e)?n(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(n){let e=r;r=t=>n({className:t,parseClassName:e})}return r},p=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,m=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let n=[],r=[];return e.forEach(e=>{"["===e[0]||t[e]?(n.push(...r.sort(),e),r=[]):r.push(e)}),n.push(...r.sort()),n}},f=e=>({cache:d(e.cacheSize),parseClassName:h(e),sortModifiers:m(e),...r(e)}),g=/\s+/,v=(e,t)=>{let{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:i,sortModifiers:o}=t,s=[],a=e.trim().split(g),l="";for(let e=a.length-1;e>=0;e-=1){let t=a[e],{isExternal:u,modifiers:c,hasImportantModifier:d,baseClassName:h,maybePostfixModifierPosition:p}=n(t);if(u){l=t+(l.length>0?" "+l:l);continue}let m=!!p,f=r(m?h.substring(0,p):h);if(!f){if(!m||!(f=r(h))){l=t+(l.length>0?" "+l:l);continue}m=!1}let g=o(c).join(":"),v=d?g+"!":g,y=v+f;if(s.includes(y))continue;s.push(y);let b=i(f,m);for(let e=0;e<b.length;++e){let t=b[e];s.push(v+t)}l=t+(l.length>0?" "+l:l)}return l};function y(){let e,t,n=0,r="";for(;n<arguments.length;)(e=arguments[n++])&&(t=b(e))&&(r&&(r+=" "),r+=t);return r}let b=e=>{let t;if("string"==typeof e)return e;let n="";for(let r=0;r<e.length;r++)e[r]&&(t=b(e[r]))&&(n&&(n+=" "),n+=t);return n},x=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},w=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,E=/^\d+\/\d+$/,T=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,P=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,S=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,A=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,M=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,C=e=>E.test(e),R=e=>!!e&&!Number.isNaN(Number(e)),D=e=>!!e&&Number.isInteger(Number(e)),j=e=>e.endsWith("%")&&R(e.slice(0,-1)),V=e=>T.test(e),L=()=>!0,O=e=>P.test(e)&&!S.test(e),N=()=>!1,F=e=>A.test(e),I=e=>M.test(e),B=e=>!z(e)&&!X(e),U=e=>ee(e,ei,N),z=e=>w.test(e),W=e=>ee(e,eo,O),_=e=>ee(e,es,R),$=e=>ee(e,en,N),H=e=>ee(e,er,I),K=e=>ee(e,el,F),X=e=>k.test(e),Y=e=>et(e,eo),q=e=>et(e,ea),G=e=>et(e,en),Z=e=>et(e,ei),Q=e=>et(e,er),J=e=>et(e,el,!0),ee=(e,t,n)=>{let r=w.exec(e);return!!r&&(r[1]?t(r[1]):n(r[2]))},et=(e,t,n=!1)=>{let r=k.exec(e);return!!r&&(r[1]?t(r[1]):n)},en=e=>"position"===e||"percentage"===e,er=e=>"image"===e||"url"===e,ei=e=>"length"===e||"size"===e||"bg-size"===e,eo=e=>"length"===e,es=e=>"number"===e,ea=e=>"family-name"===e,el=e=>"shadow"===e;Symbol.toStringTag;let eu=function(e,...t){let n,r,i,o=function(a){return r=(n=f(t.reduce((e,t)=>t(e),e()))).cache.get,i=n.cache.set,o=s,s(a)};function s(e){let t=r(e);if(t)return t;let o=v(e,n);return i(e,o),o}return function(){return o(y.apply(null,arguments))}}(()=>{let e=x("color"),t=x("font"),n=x("text"),r=x("font-weight"),i=x("tracking"),o=x("leading"),s=x("breakpoint"),a=x("container"),l=x("spacing"),u=x("radius"),c=x("shadow"),d=x("inset-shadow"),h=x("text-shadow"),p=x("drop-shadow"),m=x("blur"),f=x("perspective"),g=x("aspect"),v=x("ease"),y=x("animate"),b=()=>["auto","avoid","all","avoid-page","page","left","right","column"],w=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...w(),X,z],E=()=>["auto","hidden","clip","visible","scroll"],T=()=>["auto","contain","none"],P=()=>[X,z,l],S=()=>[C,"full","auto",...P()],A=()=>[D,"none","subgrid",X,z],M=()=>["auto",{span:["full",D,X,z]},D,X,z],O=()=>[D,"auto",X,z],N=()=>["auto","min","max","fr",X,z],F=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],I=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...P()],et=()=>[C,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...P()],en=()=>[e,X,z],er=()=>[...w(),G,$,{position:[X,z]}],ei=()=>["no-repeat",{repeat:["","x","y","space","round"]}],eo=()=>["auto","cover","contain",Z,U,{size:[X,z]}],es=()=>[j,Y,W],ea=()=>["","none","full",u,X,z],el=()=>["",R,Y,W],eu=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ed=()=>[R,j,G,$],eh=()=>["","none",m,X,z],ep=()=>["none",R,X,z],em=()=>["none",R,X,z],ef=()=>[R,X,z],eg=()=>[C,"full",...P()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[V],breakpoint:[V],color:[L],container:[V],"drop-shadow":[V],ease:["in","out","in-out"],font:[B],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[V],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[V],shadow:[V],spacing:["px",R],text:[V],"text-shadow":[V],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",C,z,X,g]}],container:["container"],columns:[{columns:[R,z,X,a]}],"break-after":[{"break-after":b()}],"break-before":[{"break-before":b()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:E()}],"overflow-x":[{"overflow-x":E()}],"overflow-y":[{"overflow-y":E()}],overscroll:[{overscroll:T()}],"overscroll-x":[{"overscroll-x":T()}],"overscroll-y":[{"overscroll-y":T()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:S()}],"inset-x":[{"inset-x":S()}],"inset-y":[{"inset-y":S()}],start:[{start:S()}],end:[{end:S()}],top:[{top:S()}],right:[{right:S()}],bottom:[{bottom:S()}],left:[{left:S()}],visibility:["visible","invisible","collapse"],z:[{z:[D,"auto",X,z]}],basis:[{basis:[C,"full","auto",a,...P()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[R,C,"auto","initial","none",z]}],grow:[{grow:["",R,X,z]}],shrink:[{shrink:["",R,X,z]}],order:[{order:[D,"first","last","none",X,z]}],"grid-cols":[{"grid-cols":A()}],"col-start-end":[{col:M()}],"col-start":[{"col-start":O()}],"col-end":[{"col-end":O()}],"grid-rows":[{"grid-rows":A()}],"row-start-end":[{row:M()}],"row-start":[{"row-start":O()}],"row-end":[{"row-end":O()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":N()}],"auto-rows":[{"auto-rows":N()}],gap:[{gap:P()}],"gap-x":[{"gap-x":P()}],"gap-y":[{"gap-y":P()}],"justify-content":[{justify:[...F(),"normal"]}],"justify-items":[{"justify-items":[...I(),"normal"]}],"justify-self":[{"justify-self":["auto",...I()]}],"align-content":[{content:["normal",...F()]}],"align-items":[{items:[...I(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...I(),{baseline:["","last"]}]}],"place-content":[{"place-content":F()}],"place-items":[{"place-items":[...I(),"baseline"]}],"place-self":[{"place-self":["auto",...I()]}],p:[{p:P()}],px:[{px:P()}],py:[{py:P()}],ps:[{ps:P()}],pe:[{pe:P()}],pt:[{pt:P()}],pr:[{pr:P()}],pb:[{pb:P()}],pl:[{pl:P()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":P()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":P()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[a,"screen",...et()]}],"min-w":[{"min-w":[a,"screen","none",...et()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[s]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",n,Y,W]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,X,_]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",j,z]}],"font-family":[{font:[q,z,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[i,X,z]}],"line-clamp":[{"line-clamp":[R,"none",X,_]}],leading:[{leading:[o,...P()]}],"list-image":[{"list-image":["none",X,z]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",X,z]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:en()}],"text-color":[{text:en()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...eu(),"wavy"]}],"text-decoration-thickness":[{decoration:[R,"from-font","auto",X,W]}],"text-decoration-color":[{decoration:en()}],"underline-offset":[{"underline-offset":[R,"auto",X,z]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:P()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",X,z]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",X,z]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:er()}],"bg-repeat":[{bg:ei()}],"bg-size":[{bg:eo()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},D,X,z],radial:["",X,z],conic:[D,X,z]},Q,H]}],"bg-color":[{bg:en()}],"gradient-from-pos":[{from:es()}],"gradient-via-pos":[{via:es()}],"gradient-to-pos":[{to:es()}],"gradient-from":[{from:en()}],"gradient-via":[{via:en()}],"gradient-to":[{to:en()}],rounded:[{rounded:ea()}],"rounded-s":[{"rounded-s":ea()}],"rounded-e":[{"rounded-e":ea()}],"rounded-t":[{"rounded-t":ea()}],"rounded-r":[{"rounded-r":ea()}],"rounded-b":[{"rounded-b":ea()}],"rounded-l":[{"rounded-l":ea()}],"rounded-ss":[{"rounded-ss":ea()}],"rounded-se":[{"rounded-se":ea()}],"rounded-ee":[{"rounded-ee":ea()}],"rounded-es":[{"rounded-es":ea()}],"rounded-tl":[{"rounded-tl":ea()}],"rounded-tr":[{"rounded-tr":ea()}],"rounded-br":[{"rounded-br":ea()}],"rounded-bl":[{"rounded-bl":ea()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...eu(),"hidden","none"]}],"divide-style":[{divide:[...eu(),"hidden","none"]}],"border-color":[{border:en()}],"border-color-x":[{"border-x":en()}],"border-color-y":[{"border-y":en()}],"border-color-s":[{"border-s":en()}],"border-color-e":[{"border-e":en()}],"border-color-t":[{"border-t":en()}],"border-color-r":[{"border-r":en()}],"border-color-b":[{"border-b":en()}],"border-color-l":[{"border-l":en()}],"divide-color":[{divide:en()}],"outline-style":[{outline:[...eu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[R,X,z]}],"outline-w":[{outline:["",R,Y,W]}],"outline-color":[{outline:en()}],shadow:[{shadow:["","none",c,J,K]}],"shadow-color":[{shadow:en()}],"inset-shadow":[{"inset-shadow":["none",d,J,K]}],"inset-shadow-color":[{"inset-shadow":en()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:en()}],"ring-offset-w":[{"ring-offset":[R,W]}],"ring-offset-color":[{"ring-offset":en()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":en()}],"text-shadow":[{"text-shadow":["none",h,J,K]}],"text-shadow-color":[{"text-shadow":en()}],opacity:[{opacity:[R,X,z]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[R]}],"mask-image-linear-from-pos":[{"mask-linear-from":ed()}],"mask-image-linear-to-pos":[{"mask-linear-to":ed()}],"mask-image-linear-from-color":[{"mask-linear-from":en()}],"mask-image-linear-to-color":[{"mask-linear-to":en()}],"mask-image-t-from-pos":[{"mask-t-from":ed()}],"mask-image-t-to-pos":[{"mask-t-to":ed()}],"mask-image-t-from-color":[{"mask-t-from":en()}],"mask-image-t-to-color":[{"mask-t-to":en()}],"mask-image-r-from-pos":[{"mask-r-from":ed()}],"mask-image-r-to-pos":[{"mask-r-to":ed()}],"mask-image-r-from-color":[{"mask-r-from":en()}],"mask-image-r-to-color":[{"mask-r-to":en()}],"mask-image-b-from-pos":[{"mask-b-from":ed()}],"mask-image-b-to-pos":[{"mask-b-to":ed()}],"mask-image-b-from-color":[{"mask-b-from":en()}],"mask-image-b-to-color":[{"mask-b-to":en()}],"mask-image-l-from-pos":[{"mask-l-from":ed()}],"mask-image-l-to-pos":[{"mask-l-to":ed()}],"mask-image-l-from-color":[{"mask-l-from":en()}],"mask-image-l-to-color":[{"mask-l-to":en()}],"mask-image-x-from-pos":[{"mask-x-from":ed()}],"mask-image-x-to-pos":[{"mask-x-to":ed()}],"mask-image-x-from-color":[{"mask-x-from":en()}],"mask-image-x-to-color":[{"mask-x-to":en()}],"mask-image-y-from-pos":[{"mask-y-from":ed()}],"mask-image-y-to-pos":[{"mask-y-to":ed()}],"mask-image-y-from-color":[{"mask-y-from":en()}],"mask-image-y-to-color":[{"mask-y-to":en()}],"mask-image-radial":[{"mask-radial":[X,z]}],"mask-image-radial-from-pos":[{"mask-radial-from":ed()}],"mask-image-radial-to-pos":[{"mask-radial-to":ed()}],"mask-image-radial-from-color":[{"mask-radial-from":en()}],"mask-image-radial-to-color":[{"mask-radial-to":en()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":w()}],"mask-image-conic-pos":[{"mask-conic":[R]}],"mask-image-conic-from-pos":[{"mask-conic-from":ed()}],"mask-image-conic-to-pos":[{"mask-conic-to":ed()}],"mask-image-conic-from-color":[{"mask-conic-from":en()}],"mask-image-conic-to-color":[{"mask-conic-to":en()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:er()}],"mask-repeat":[{mask:ei()}],"mask-size":[{mask:eo()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",X,z]}],filter:[{filter:["","none",X,z]}],blur:[{blur:eh()}],brightness:[{brightness:[R,X,z]}],contrast:[{contrast:[R,X,z]}],"drop-shadow":[{"drop-shadow":["","none",p,J,K]}],"drop-shadow-color":[{"drop-shadow":en()}],grayscale:[{grayscale:["",R,X,z]}],"hue-rotate":[{"hue-rotate":[R,X,z]}],invert:[{invert:["",R,X,z]}],saturate:[{saturate:[R,X,z]}],sepia:[{sepia:["",R,X,z]}],"backdrop-filter":[{"backdrop-filter":["","none",X,z]}],"backdrop-blur":[{"backdrop-blur":eh()}],"backdrop-brightness":[{"backdrop-brightness":[R,X,z]}],"backdrop-contrast":[{"backdrop-contrast":[R,X,z]}],"backdrop-grayscale":[{"backdrop-grayscale":["",R,X,z]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[R,X,z]}],"backdrop-invert":[{"backdrop-invert":["",R,X,z]}],"backdrop-opacity":[{"backdrop-opacity":[R,X,z]}],"backdrop-saturate":[{"backdrop-saturate":[R,X,z]}],"backdrop-sepia":[{"backdrop-sepia":["",R,X,z]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":P()}],"border-spacing-x":[{"border-spacing-x":P()}],"border-spacing-y":[{"border-spacing-y":P()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",X,z]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[R,"initial",X,z]}],ease:[{ease:["linear","initial",v,X,z]}],delay:[{delay:[R,X,z]}],animate:[{animate:["none",y,X,z]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[f,X,z]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:em()}],"scale-x":[{"scale-x":em()}],"scale-y":[{"scale-y":em()}],"scale-z":[{"scale-z":em()}],"scale-3d":["scale-3d"],skew:[{skew:ef()}],"skew-x":[{"skew-x":ef()}],"skew-y":[{"skew-y":ef()}],transform:[{transform:[X,z,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:en()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:en()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",X,z]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":P()}],"scroll-mx":[{"scroll-mx":P()}],"scroll-my":[{"scroll-my":P()}],"scroll-ms":[{"scroll-ms":P()}],"scroll-me":[{"scroll-me":P()}],"scroll-mt":[{"scroll-mt":P()}],"scroll-mr":[{"scroll-mr":P()}],"scroll-mb":[{"scroll-mb":P()}],"scroll-ml":[{"scroll-ml":P()}],"scroll-p":[{"scroll-p":P()}],"scroll-px":[{"scroll-px":P()}],"scroll-py":[{"scroll-py":P()}],"scroll-ps":[{"scroll-ps":P()}],"scroll-pe":[{"scroll-pe":P()}],"scroll-pt":[{"scroll-pt":P()}],"scroll-pr":[{"scroll-pr":P()}],"scroll-pb":[{"scroll-pb":P()}],"scroll-pl":[{"scroll-pl":P()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",X,z]}],fill:[{fill:["none",...en()]}],"stroke-w":[{stroke:[R,Y,W,_]}],stroke:[{stroke:["none",...en()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9708:(e,t,n)=>{n.d(t,{DX:()=>a,TL:()=>s});var r=n(2115),i=n(6101),o=n(5155);function s(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var s;let e,a,l=(s=n,(a=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(a=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),u=function(e,t){let n={...t};for(let r in t){let i=e[r],o=t[r];/^on[A-Z]/.test(r)?i&&o?n[r]=(...e)=>{let t=o(...e);return i(...e),t}:i&&(n[r]=i):"style"===r?n[r]={...i,...o}:"className"===r&&(n[r]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(u.ref=t?(0,i.t)(t,l):l),r.cloneElement(n,u)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:i,...s}=e,a=r.Children.toArray(i),l=a.find(u);if(l){let e=l.props.children,i=a.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...s,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,i):null})}return(0,o.jsx)(t,{...s,ref:n,children:i})});return n.displayName=`${e}.Slot`,n}var a=s("Slot"),l=Symbol("radix.slottable");function u(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},9881:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]])},9946:(e,t,n)=>{n.d(t,{A:()=>d});var r=n(2115);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),s=e=>{let t=o(e);return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()},l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:s,className:c="",children:d,iconNode:h,...p}=e;return(0,r.createElement)("svg",{ref:t,...u,width:i,height:i,stroke:n,strokeWidth:s?24*Number(o)/Number(i):o,className:a("lucide",c),...!d&&!l(p)&&{"aria-hidden":"true"},...p},[...h.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let n=(0,r.forwardRef)((n,o)=>{let{className:l,...u}=n;return(0,r.createElement)(c,{ref:o,iconNode:t,className:a("lucide-".concat(i(s(e))),"lucide-".concat(e),l),...u})});return n.displayName=s(e),n}},9991:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return v},NormalizeError:function(){return f},PageNotFoundError:function(){return g},SP:function(){return h},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return s},getURL:function(){return a},isAbsoluteUrl:function(){return o},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,i=Array(r),o=0;o<r;o++)i[o]=arguments[o];return n||(n=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>i.test(e);function s(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function a(){let{href:e}=window.location,t=s();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&u(n))return r;if(!r)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let h="undefined"!=typeof performance,p=h&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class f extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class v extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}}}]);