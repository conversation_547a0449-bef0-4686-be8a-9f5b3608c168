(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2905],{5693:(e,t,i)=>{Promise.resolve().then(i.bind(i,24087))},13717:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});let s=(0,i(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},24087:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>p});var s=i(95155),a=i(12115),r=i(35695),c=i(83930),n=i(30285),d=i(66695),l=i(26126),o=i(84616),h=i(69037),u=i(69074),x=i(13717),f=i(62525),m=i(56671);function p(){let e=(0,r.useRouter)(),[t,i]=(0,a.useState)([]),[p,v]=(0,a.useState)(!0);(0,a.useEffect)(()=>{y()},[]);let y=async()=>{try{let e=await fetch("/api/certifications");if(!e.ok)throw Error("Failed to fetch certifications");let t=await e.json();i(t)}catch(e){console.error("Error fetching certifications:",e),m.oR.error("Failed to fetch certifications")}finally{v(!1)}},b=async e=>{(0,m.oR)("Are you sure you want to delete this certification?",{action:{label:"Delete",onClick:async()=>{try{if(!(await fetch("/api/certifications/".concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete certification");i(t.filter(t=>t.id!==e)),m.oR.success("Certification deleted successfully")}catch(e){console.error("Error deleting certification:",e),m.oR.error("Failed to delete certification")}}},cancel:{label:"Cancel",onClick:()=>{}}})},g=async(e,s)=>{try{if(!(await fetch("/api/certifications/".concat(e),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({published:!s})})).ok)throw Error("Failed to update certification");i(t.map(t=>t.id===e?{...t,published:!s}:t)),m.oR.success("Certification ".concat(s?"unpublished":"published"))}catch(e){console.error("Error updating certification:",e),m.oR.error("Failed to update certification")}};return p?(0,s.jsx)(c.DashboardLayout,{children:(0,s.jsx)("div",{className:"container mx-auto py-8",children:(0,s.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,s.jsx)("div",{className:"h-8 bg-gray-300 rounded w-64"}),(0,s.jsx)("div",{className:"grid gap-4",children:[1,2,3,4].map(e=>(0,s.jsx)("div",{className:"h-32 bg-gray-300 rounded"},e))})]})})}):(0,s.jsx)(c.DashboardLayout,{children:(0,s.jsxs)("div",{className:"container mx-auto py-8",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Certifications"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Manage your professional certifications"})]}),(0,s.jsxs)(n.$,{onClick:()=>e.push("/dashboard/certifications/add"),children:[(0,s.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Add Certification"]})]}),(0,s.jsx)("div",{className:"grid gap-6",children:t.map(t=>(0,s.jsxs)(d.Zp,{className:"overflow-hidden",children:[(0,s.jsx)(d.aR,{className:"pb-4",children:(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center",children:t.emoji?(0,s.jsx)("span",{className:"text-2xl",children:t.emoji}):(0,s.jsx)(h.A,{className:"h-6 w-6 text-orange-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d.ZB,{className:"text-xl",children:t.title}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-muted-foreground mt-1",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-1"}),t.issuer]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(u.A,{className:"h-4 w-4 mr-1"}),t.date]}),t.credentialId&&(0,s.jsxs)("div",{className:"text-xs bg-gray-100 px-2 py-1 rounded",children:["ID: ",t.credentialId]})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(l.E,{variant:t.published?"default":"secondary",children:t.published?"Published":"Draft"}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>g(t.id,t.published),children:t.published?"Unpublish":"Publish"}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>e.push("/dashboard/certifications/".concat(t.id)),children:(0,s.jsx)(x.A,{className:"h-4 w-4"})}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>b(t.id),children:(0,s.jsx)(f.A,{className:"h-4 w-4"})})]})]})}),(0,s.jsx)(d.Wu,{children:(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:t.description})})]},t.id))}),0===t.length&&(0,s.jsx)(d.Zp,{children:(0,s.jsx)(d.Wu,{className:"py-8 text-center",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"No certifications found. Add your first certification to get started."})})})]})})}},26126:(e,t,i)=>{"use strict";i.d(t,{E:()=>d});var s=i(95155);i(12115);var a=i(99708),r=i(74466),c=i(59434);let n=(0,r.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:i,asChild:r=!1,...d}=e,l=r?a.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,c.cn)(n({variant:i}),t),...d})}},62525:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});let s=(0,i(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},69074:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});let s=(0,i(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},84616:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});let s=(0,i(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[4854,2108,8858,9368,6671,9008,8441,1684,7358],()=>t(5693)),_N_E=e.O()}]);