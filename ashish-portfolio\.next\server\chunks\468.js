exports.id=468,exports.ids=[468],exports.modules={4780:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i});var r=s(49384),a=s(82348);function i(...e){return(0,a.QP)((0,r.$)(e))}},29523:(e,t,s)=>{"use strict";s.d(t,{$:()=>l});var r=s(60687);s(43210);var a=s(8730),i=s(24224),n=s(4780);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:s,asChild:i=!1,...l}){let d=i?a.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,n.cn)(o({variant:t,size:s,className:e})),...l})}},34711:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},38648:(e,t,s)=>{Promise.resolve().then(s.bind(s,83701)),Promise.resolve().then(s.bind(s,48482))},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>n});var r=s(60687);s(43210);var a=s(4780);function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},48376:(e,t,s)=>{Promise.resolve().then(s.bind(s,96871)),Promise.resolve().then(s.bind(s,64616))},48482:(e,t,s)=>{"use strict";s.d(t,{Toaster:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\sonner.tsx","Toaster")},61135:()=>{},64544:(e,t,s)=>{"use strict";s.d(t,{Navigation:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\navigation.tsx","Navigation")},64616:(e,t,s)=>{"use strict";s.d(t,{Toaster:()=>n});var r=s(60687),a=s(10218),i=s(52581);let n=({...e})=>{let{theme:t="system"}=(0,a.D)();return(0,r.jsx)(i.l$,{theme:t,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},64947:(e,t,s)=>{"use strict";s.d(t,{Footer:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Footer() from the server but Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\footer.tsx","Footer")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},83701:(e,t,s)=>{"use strict";s.d(t,{ThemeProvider:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\theme-provider.tsx","ThemeProvider")},87855:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},89650:(e,t,s)=>{"use strict";s.d(t,{Navigation:()=>K});var r=s(60687),a=s(43210),i=s(85814),n=s.n(i),o=s(10218),l=s(26001),d=s(88920),c=s(32192),m=s(58869),h=s(57800),u=s(10022),p=s(41550),x=s(99270),v=s(21134),g=s(363),f=s(11860),b=s(12941),j=s(29523),w=s(16189),y=s(62157),N=s(98876),k=s(72575),A=s(25334),P=s(54026),C=s(4780);function S({...e}){return(0,r.jsx)(P.bL,{"data-slot":"dialog",...e})}function T({...e}){return(0,r.jsx)(P.ZL,{"data-slot":"dialog-portal",...e})}function D({className:e,...t}){return(0,r.jsx)(P.hJ,{"data-slot":"dialog-overlay",className:(0,C.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function U({className:e,children:t,showCloseButton:s=!0,...a}){return(0,r.jsxs)(T,{"data-slot":"dialog-portal",children:[(0,r.jsx)(D,{}),(0,r.jsxs)(P.UC,{"data-slot":"dialog-content",className:(0,C.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...a,children:[t,s&&(0,r.jsxs)(P.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(f.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function I({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,C.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function z({className:e,...t}){return(0,r.jsx)(P.hE,{"data-slot":"dialog-title",className:(0,C.cn)("text-lg leading-none font-semibold",e),...t})}var _=s(89667),F=s(96834);function E({open:e,onOpenChange:t}){let[s,i]=(0,a.useState)(""),[n,o]=(0,a.useState)(0),d=(0,w.useRouter)(),v=[{id:"home",title:"Home",description:"Go to homepage",icon:c.A,action:()=>d.push("/"),category:"Navigation",keywords:["home","main","landing"]},{id:"about",title:"About",description:"Learn more about me",icon:m.A,action:()=>d.push("/about"),category:"Navigation",keywords:["about","bio","experience","skills"]},{id:"projects",title:"Projects",description:"View my work and projects",icon:h.A,action:()=>d.push("/projects"),category:"Navigation",keywords:["projects","work","portfolio","showcase"]},{id:"blog",title:"Blog",description:"Read my latest articles",icon:u.A,action:()=>d.push("/blog"),category:"Navigation",keywords:["blog","articles","writing","posts"]},{id:"contact",title:"Contact",description:"Get in touch with me",icon:p.A,action:()=>d.push("/contact"),category:"Navigation",keywords:["contact","email","message","hire"]},{id:"github",title:"GitHub",description:"View my GitHub profile",icon:y.A,action:()=>window.open("https://github.com/ashishkamat","_blank"),category:"Social",keywords:["github","code","repositories","open source"]},{id:"linkedin",title:"LinkedIn",description:"Connect with me on LinkedIn",icon:N.A,action:()=>window.open("https://linkedin.com/in/ashishkamat","_blank"),category:"Social",keywords:["linkedin","professional","network","career"]},{id:"twitter",title:"Twitter",description:"Follow me on Twitter",icon:k.A,action:()=>window.open("https://twitter.com/ashishkamat","_blank"),category:"Social",keywords:["twitter","social","updates","thoughts"]},{id:"email",title:"Send Email",description:"Send me an email directly",icon:p.A,action:()=>window.open("mailto:<EMAIL>","_blank"),category:"Quick Actions",keywords:["email","contact","message","hire"]},{id:"resume",title:"Download Resume",description:"Download my latest resume",icon:A.A,action:()=>window.open("/resume.pdf","_blank"),category:"Quick Actions",keywords:["resume","cv","download","hire"]}].filter(e=>{let t=s.toLowerCase();return e.title.toLowerCase().includes(t)||e.description.toLowerCase().includes(t)||e.keywords.some(e=>e.includes(t))}),g=v.reduce((e,t)=>(e[t.category]||(e[t.category]=[]),e[t.category].push(t),e),{}),f=e=>{e.action(),t(!1),i("")};return(0,r.jsx)(S,{open:e,onOpenChange:t,children:(0,r.jsxs)(U,{className:"max-w-2xl p-0 overflow-hidden",children:[(0,r.jsxs)(I,{className:"p-4 pb-0",children:[(0,r.jsx)(z,{className:"sr-only",children:"Command Palette"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(_.p,{placeholder:"Type a command or search...",value:s,onChange:e=>i(e.target.value),className:"pl-10 border-0 focus-visible:ring-0 text-base",autoFocus:!0})]})]}),(0,r.jsx)("div",{className:"max-h-96 overflow-y-auto p-4 pt-0",children:0===Object.keys(g).length?(0,r.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:['No commands found for "',s,'"']}):(0,r.jsx)("div",{className:"space-y-4",children:Object.entries(g).map(([e,t])=>(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wider mb-2 px-2",children:e}),(0,r.jsx)("div",{className:"space-y-1",children:t.map((e,t)=>{let s=v.indexOf(e),a=e.icon;return(0,r.jsxs)(l.P.button,{onClick:()=>f(e),className:`w-full text-left p-3 rounded-lg transition-colors duration-200 flex items-center space-x-3 ${s===n?"bg-accent text-accent-foreground":"hover:bg-accent/50"}`,whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,r.jsx)("div",{className:`p-2 rounded-md ${s===n?"bg-primary text-primary-foreground":"bg-muted"}`,children:(0,r.jsx)(a,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("div",{className:"font-medium",children:e.title}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground truncate",children:e.description})]}),"Social"===e.category&&(0,r.jsx)(A.A,{className:"h-3 w-3 text-muted-foreground"})]},e.id)})})]},e))})}),(0,r.jsxs)("div",{className:"border-t p-3 text-xs text-muted-foreground flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(F.E,{variant:"outline",className:"text-xs px-1.5 py-0.5",children:"↑↓"}),(0,r.jsx)("span",{children:"Navigate"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(F.E,{variant:"outline",className:"text-xs px-1.5 py-0.5",children:"↵"}),(0,r.jsx)("span",{children:"Select"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(F.E,{variant:"outline",className:"text-xs px-1.5 py-0.5",children:"Esc"}),(0,r.jsx)("span",{children:"Close"})]})]}),(0,r.jsxs)("div",{className:"text-muted-foreground/60",children:[v.length," result",1!==v.length?"s":""]})]})]})})}let H=[{name:"Home",href:"/",icon:c.A},{name:"About",href:"/about",icon:m.A},{name:"Projects",href:"/projects",icon:h.A},{name:"Blog",href:"/blog",icon:u.A},{name:"Contact",href:"/contact",icon:p.A}];function K(){let[e,t]=(0,a.useState)(!1),[s,i]=(0,a.useState)(!1),[c,m]=(0,a.useState)(!1),{theme:h,setTheme:u}=(0,o.D)(),[p,w]=(0,a.useState)(!1);return p?(0,r.jsxs)(l.P.header,{className:(0,C.cn)("fixed top-0 left-0 right-0 z-50 transition-all duration-300",s?"bg-background/80 backdrop-blur-md border-b border-border":"bg-transparent"),initial:{y:-100},animate:{y:0},transition:{duration:.5},children:[(0,r.jsxs)("nav",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,r.jsx)(l.P.div,{className:"flex-shrink-0",whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,r.jsx)(n(),{href:"/",className:"text-2xl font-bold gradient-text-blue",children:"AK"})}),(0,r.jsx)("div",{className:"hidden md:block",children:(0,r.jsx)("div",{className:"ml-10 flex items-baseline space-x-4",children:H.map(e=>(0,r.jsx)(l.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,r.jsx)(n(),{href:e.href,className:"text-foreground/80 hover:text-foreground px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 hover:bg-accent",children:e.name})},e.name))})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(j.$,{variant:"ghost",size:"sm",onClick:()=>m(!0),className:"hidden md:flex items-center space-x-2 text-muted-foreground hover:text-foreground",children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"text-sm",children:"Search"}),(0,r.jsxs)("kbd",{className:"pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100",children:[(0,r.jsx)("span",{className:"text-xs",children:"⌘"}),"K"]})]}),(0,r.jsx)(j.$,{variant:"ghost",size:"icon",onClick:()=>{u("dark"===h?"light":"dark")},className:"w-9 h-9",children:"dark"===h?(0,r.jsx)(v.A,{className:"h-4 w-4"}):(0,r.jsx)(g.A,{className:"h-4 w-4"})}),(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsx)(j.$,{variant:"ghost",size:"icon",onClick:()=>t(!e),className:"w-9 h-9",children:e?(0,r.jsx)(f.A,{className:"h-4 w-4"}):(0,r.jsx)(b.A,{className:"h-4 w-4"})})})]})]}),(0,r.jsx)(d.N,{children:e&&(0,r.jsx)(l.P.div,{className:"md:hidden",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:(0,r.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-background/95 backdrop-blur-md rounded-lg mt-2 border border-border",children:H.map(e=>{let s=e.icon;return(0,r.jsx)(l.P.div,{whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,r.jsxs)(n(),{href:e.href,className:"text-foreground/80 hover:text-foreground flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 hover:bg-accent",onClick:()=>t(!1),children:[(0,r.jsx)(s,{className:"h-4 w-4 mr-3"}),e.name]})},e.name)})})})})]}),(0,r.jsx)(E,{open:c,onOpenChange:m})]}):null}},89667:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var r=s(60687);s(43210);var a=s(4780);function i({className:e,type:t,...s}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},94101:(e,t,s)=>{"use strict";s.d(t,{Footer:()=>f});var r=s(60687),a=s(85814),i=s.n(a),n=s(26001),o=s(62157),l=s(98876),d=s(72575),c=s(41550),m=s(67760),h=s(13166),u=s(80375),p=s(2975),x=s(29523);let v={navigation:[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Projects",href:"/projects"},{name:"Blog",href:"/blog"},{name:"Contact",href:"/contact"}],services:[{name:"Full Stack Development",href:"/#services"},{name:"UI/UX Design",href:"/#services"},{name:"Mobile Development",href:"/#services"},{name:"Consulting",href:"/#services"}],resources:[{name:"Blog",href:"/blog"},{name:"Projects",href:"/projects"},{name:"Tech Stack",href:"/#tech-stack"},{name:"Contact",href:"/contact"}]},g=[{name:"GitHub",href:"https://github.com/ash-333",icon:o.A,color:"hover:text-gray-600 dark:hover:text-gray-300"},{name:"LinkedIn",href:"https://www.linkedin.com/in/ashishkamat0/",icon:l.A,color:"hover:text-blue-600"},{name:"Twitter",href:"https://twitter.com/ashishkamat4",icon:d.A,color:"hover:text-blue-500"},{name:"Email",href:"mailto:<EMAIL>",icon:c.A,color:"hover:text-green-600"}];function f(){return(0,r.jsx)("footer",{className:"bg-background border-t border-border",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("div",{className:"py-12 lg:py-16",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5},viewport:{once:!0},children:[(0,r.jsx)(i(),{href:"/",className:"text-2xl font-bold gradient-text-blue mb-4 inline-block",children:"Ashish Kamat"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-6 max-w-md",children:"Full Stack Developer & UI/UX Designer passionate about creating innovative digital experiences that make a difference."}),(0,r.jsx)("div",{className:"flex space-x-4",children:g.map((e,t)=>{let s=e.icon;return(0,r.jsx)(n.P.a,{href:e.href,target:"_blank",rel:"noopener noreferrer",className:`p-2 rounded-lg bg-muted hover:bg-accent transition-all duration-300 ${e.color}`,whileHover:{scale:1.1},whileTap:{scale:.9},initial:{opacity:0,scale:0},whileInView:{opacity:1,scale:1},transition:{duration:.3,delay:.1*t},viewport:{once:!0},children:(0,r.jsx)(s,{className:"h-5 w-5"})},e.name)})})]})}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1},viewport:{once:!0},children:[(0,r.jsx)("h3",{className:"font-semibold text-foreground mb-4",children:"Navigation"}),(0,r.jsx)("ul",{className:"space-y-3",children:v.navigation.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:e.href,className:"text-muted-foreground hover:text-foreground transition-colors duration-200",children:e.name})},e.name))})]}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.2},viewport:{once:!0},children:[(0,r.jsx)("h3",{className:"font-semibold text-foreground mb-4",children:"Services"}),(0,r.jsx)("ul",{className:"space-y-3",children:v.services.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:e.href,className:"text-muted-foreground hover:text-foreground transition-colors duration-200",children:e.name})},e.name))})]}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.3},viewport:{once:!0},children:[(0,r.jsx)("h3",{className:"font-semibold text-foreground mb-4",children:"Resources"}),(0,r.jsx)("ul",{className:"space-y-3",children:v.resources.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:e.href,className:"text-muted-foreground hover:text-foreground transition-colors duration-200",children:e.name})},e.name))})]})]})}),(0,r.jsx)(n.P.div,{className:"py-6 border-t border-border",initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.5,delay:.4},viewport:{once:!0},children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-muted-foreground",children:[(0,r.jsx)("span",{children:"\xa9 2024 Ashish Kamat. Made with"}),(0,r.jsx)(m.A,{className:"h-4 w-4 text-red-500 animate-pulse"}),(0,r.jsx)("span",{children:"and"}),(0,r.jsx)(h.A,{className:"h-4 w-4 text-amber-600"}),(0,r.jsx)("span",{children:"in Kathmandu"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-muted-foreground",children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Built with Next.js & Tailwind CSS"})]}),(0,r.jsx)(x.$,{variant:"outline",size:"icon",onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},className:"rounded-full hover:scale-110 transition-transform duration-200",children:(0,r.jsx)(p.A,{className:"h-4 w-4"})})]})]})}),(0,r.jsx)(n.P.div,{className:"text-center py-4 border-t border-border/50",initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.5,delay:.6},viewport:{once:!0},children:(0,r.jsxs)("p",{className:"text-xs text-muted-foreground/70",children:["\uD83D\uDE80 This website is powered by"," ",(0,r.jsxs)("span",{className:"font-mono bg-muted px-1 py-0.5 rounded text-xs",children:[Math.floor(100*Math.random()),"% coffee"]})," ","and"," ",(0,r.jsxs)("span",{className:"font-mono bg-muted px-1 py-0.5 rounded text-xs",children:[Math.floor(100*Math.random()),"% passion"]})]})})]})})}},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m,metadata:()=>c});var r=s(37413),a=s(22376),i=s.n(a),n=s(68726),o=s.n(n);s(61135);var l=s(48482),d=s(83701);let c={title:"Ashish Kamat - Full Stack Developer & UI/UX Designer",description:"Passionate full-stack developer and UI/UX designer creating innovative digital experiences. Specializing in React, Next.js, TypeScript, and modern web technologies.",keywords:["Ashish Kamat","Full Stack Developer","UI/UX Designer","React","Next.js","TypeScript","Web Development","Backend Development","Portfolio"],authors:[{name:"Ashish Kamat"}],creator:"Ashish Kamat",openGraph:{type:"website",locale:"en_US",url:"https://ashishkamat.com.np",title:"Ashish Kamat - Full Stack Developer & UI/UX Designer",description:"Passionate full-stack developer and UI/UX designer creating innovative digital experiences.",siteName:"Ashish Kamat Portfolio"},twitter:{card:"summary_large_image",title:"Ashish Kamat - Full Stack Developer & UI/UX Designer",description:"Passionate full-stack developer and UI/UX designer creating innovative digital experiences.",creator:"@ashishkamat"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function m({children:e}){return(0,r.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,r.jsx)("body",{className:`${i().variable} ${o().variable} antialiased`,children:(0,r.jsxs)(d.ThemeProvider,{attribute:"class",defaultTheme:"dark",enableSystem:!0,disableTransitionOnChange:!0,children:[e,(0,r.jsx)(l.Toaster,{})]})})})}},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var r=s(60687);s(43210);var a=s(8730),i=s(24224),n=s(4780);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:s=!1,...i}){let l=s?a.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(o({variant:t}),e),...i})}},96871:(e,t,s)=>{"use strict";s.d(t,{ThemeProvider:()=>i});var r=s(60687);s(43210);var a=s(10218);function i({children:e,...t}){return(0,r.jsx)(a.N,{...t,children:e})}}};