"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9008],{30285:(e,t,a)=>{a.d(t,{$:()=>d});var n=a(95155);a(12115);var r=a(99708),s=a(74466),i=a(59434);let o=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:a,size:s,asChild:d=!1,...l}=e,c=d?r.DX:"button";return(0,n.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:a,size:s,className:t})),...l})}},59434:(e,t,a)=>{a.d(t,{KE:()=>m,_C:()=>u,cn:()=>l,z9:()=>c});var n=a(52596),r=a(39688),s=a(96262),i=a.n(s),o=a(60430),d=a.n(o);function l(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,n.$)(t))}function c(e){return i()(e,{lower:!0,strict:!0,remove:/[*+~.()'"!:@]/g})}function u(e){return Math.ceil(d()(e).minutes)}function m(e){try{let t=JSON.parse(e);if("doc"===t.type&&t.content)return function e(t){let a="";for(let n of t)"text"===n.type?a+=n.text||"":n.content&&(a+=e(n.content)),["paragraph","heading","listItem"].includes(n.type)&&(a+=" ");return a.trim()}(t.content)}catch(e){}return e.replace(/<[^>]*>/g," ").replace(/\s+/g," ").trim()}},66695:(e,t,a)=>{a.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>s,aR:()=>i});var n=a(95155);a(12115);var r=a(59434);function s(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function i(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function o(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function d(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}},83930:(e,t,a)=>{a.d(t,{DashboardLayout:()=>T});var n=a(95155),r=a(12108),s=a(35695),i=a(12115),o=a(6874),d=a.n(o),l=a(59434),c=a(73783),u=a(14395),m=a(57434),f=a(92657),x=a(17576),h=a(29621),g=a(48136),v=a(87949),p=a(69037),b=a(81497),j=a(72713),y=a(381);let w=[{name:"Dashboard",href:"/dashboard",icon:c.A},{name:"Projects",href:"/dashboard/projects",icon:u.A},{name:"Blog Posts",href:"/dashboard/blog",icon:m.A},{name:"Blog Preview",href:"/blog",icon:f.A},{name:"Services",href:"/dashboard/services",icon:x.A},{name:"Tech Stack",href:"/dashboard/tech-stack",icon:h.A},{name:"Experience",href:"/dashboard/experiences",icon:g.A},{name:"Education",href:"/dashboard/education",icon:v.A},{name:"Certifications",href:"/dashboard/certifications",icon:p.A},{name:"Testimonials",href:"/dashboard/testimonials",icon:b.A},{name:"Analytics",href:"/dashboard/analytics",icon:j.A},{name:"Settings",href:"/dashboard/settings",icon:y.A}];function N(){let e=(0,s.usePathname)();return(0,n.jsxs)("div",{className:"flex h-full w-64 flex-col bg-gray-50 dark:bg-gray-900",children:[(0,n.jsx)("div",{className:"flex h-16 items-center px-6",children:(0,n.jsx)("h1",{className:"text-xl font-bold",children:"Portfolio CMS"})}),(0,n.jsx)("nav",{className:"flex-1 space-y-1 px-3 py-4",children:w.map(t=>{let a=e===t.href;return(0,n.jsxs)(d(),{href:t.href,className:(0,l.cn)("flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors",a?"bg-primary text-primary-foreground":"text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800"),children:[(0,n.jsx)(t.icon,{className:"mr-3 h-5 w-5"}),t.name]},t.name)})})]})}var k=a(30285),A=a(9449);function _(e){let{...t}=e;return(0,n.jsx)(A.bL,{"data-slot":"dropdown-menu",...t})}function z(e){let{...t}=e;return(0,n.jsx)(A.l9,{"data-slot":"dropdown-menu-trigger",...t})}function C(e){let{className:t,sideOffset:a=4,...r}=e;return(0,n.jsx)(A.ZL,{children:(0,n.jsx)(A.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...r})})}function P(e){let{className:t,inset:a,variant:r="default",...s}=e;return(0,n.jsx)(A.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":r,className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...s})}function S(e){let{className:t,inset:a,...r}=e;return(0,n.jsx)(A.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,l.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...r})}function E(e){let{className:t,...a}=e;return(0,n.jsx)(A.wv,{"data-slot":"dropdown-menu-separator",className:(0,l.cn)("bg-border -mx-1 my-1 h-px",t),...a})}var L=a(54011);function U(e){let{className:t,...a}=e;return(0,n.jsx)(L.bL,{"data-slot":"avatar",className:(0,l.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a})}function B(e){let{className:t,...a}=e;return(0,n.jsx)(L.H4,{"data-slot":"avatar-fallback",className:(0,l.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...a})}var M=a(71007),D=a(34835);function O(){var e,t,a,s,i,o;let{data:d}=(0,r.useSession)();return(0,n.jsxs)("header",{className:"flex h-16 items-center justify-between border-b bg-white px-6 dark:bg-gray-950",children:[(0,n.jsx)("div",{className:"flex items-center space-x-4",children:(0,n.jsx)("h2",{className:"text-lg font-semibold",children:"Content Management"})}),(0,n.jsx)("div",{className:"flex items-center space-x-4",children:(0,n.jsxs)(_,{children:[(0,n.jsx)(z,{asChild:!0,children:(0,n.jsx)(k.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,n.jsx)(U,{className:"h-8 w-8",children:(0,n.jsx)(B,{children:(null==d||null==(t=d.user)||null==(e=t.name)?void 0:e.charAt(0))||(null==d||null==(s=d.user)||null==(a=s.email)?void 0:a.charAt(0))||"U"})})})}),(0,n.jsxs)(C,{className:"w-56",align:"end",forceMount:!0,children:[(0,n.jsx)(S,{className:"font-normal",children:(0,n.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,n.jsx)("p",{className:"text-sm font-medium leading-none",children:(null==d||null==(i=d.user)?void 0:i.name)||"User"}),(0,n.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:null==d||null==(o=d.user)?void 0:o.email})]})}),(0,n.jsx)(E,{}),(0,n.jsxs)(P,{children:[(0,n.jsx)(M.A,{className:"mr-2 h-4 w-4"}),(0,n.jsx)("span",{children:"Profile"})]}),(0,n.jsx)(E,{}),(0,n.jsxs)(P,{onClick:()=>{(0,r.signOut)({callbackUrl:"/auth/signin"})},children:[(0,n.jsx)(D.A,{className:"mr-2 h-4 w-4"}),(0,n.jsx)("span",{children:"Log out"})]})]})]})})]})}function T(e){let{children:t}=e,{data:a,status:o}=(0,r.useSession)(),d=(0,s.useRouter)();return((0,i.useEffect)(()=>{if("loading"!==o&&!a)return void d.push("/auth/signin")},[a,o,d]),"loading"===o)?(0,n.jsx)("div",{className:"flex h-screen items-center justify-center",children:(0,n.jsx)("div",{className:"text-lg",children:"Loading..."})}):a?(0,n.jsxs)("div",{className:"flex h-screen bg-gray-100 dark:bg-gray-900",children:[(0,n.jsx)(N,{}),(0,n.jsxs)("div",{className:"flex flex-1 flex-col overflow-hidden",children:[(0,n.jsx)(O,{}),(0,n.jsx)("main",{className:"flex-1 overflow-auto p-6",children:t})]})]}):null}}}]);