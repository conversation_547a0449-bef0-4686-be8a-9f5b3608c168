(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6411],{423:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(4701);let i=r.bP.create({name:"tableRow",addOptions:()=>({HTMLAttributes:{}}),content:"(tableCell | tableHeader)*",tableRole:"row",parseHTML:()=>[{tag:"tr"}],renderHTML({HTMLAttributes:e}){return["tr",(0,r.KV)(this.options.HTMLAttributes,e),0]}})},808:(e,t,n)=>{"use strict";n.d(t,{$L:()=>O,Ln:()=>g,N0:()=>A,Um:()=>T,Wg:()=>m,X9:()=>s,dL:()=>D,jP:()=>w,n9:()=>C,oM:()=>k,zy:()=>M});var r=n(10156);class i{constructor(e,t,n){this.pos=e,this.delInfo=t,this.recover=n}get deleted(){return(8&this.delInfo)>0}get deletedBefore(){return(5&this.delInfo)>0}get deletedAfter(){return(6&this.delInfo)>0}get deletedAcross(){return(4&this.delInfo)>0}}class o{constructor(e,t=!1){if(this.ranges=e,this.inverted=t,!e.length&&o.empty)return o.empty}recover(e){let t=0,n=65535&e;if(!this.inverted)for(let e=0;e<n;e++)t+=this.ranges[3*e+2]-this.ranges[3*e+1];return this.ranges[3*n]+t+(e-(65535&e))/65536}mapResult(e,t=1){return this._map(e,t,!1)}map(e,t=1){return this._map(e,t,!0)}_map(e,t,n){let r=0,o=this.inverted?2:1,s=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?r:0);if(a>e)break;let h=this.ranges[l+o],c=this.ranges[l+s],d=a+h;if(e<=d){let o=h?e==a?-1:e==d?1:t:t,s=a+r+(o<0?0:c);if(n)return s;let u=e==(t<0?a:d)?null:l/3+(e-a)*65536,p=e==a?2:e==d?1:4;return(t<0?e!=a:e!=d)&&(p|=8),new i(s,p,u)}r+=c-h}return n?e+r:new i(e+r,0,null)}touches(e,t){let n=0,r=65535&t,i=this.inverted?2:1,o=this.inverted?1:2;for(let t=0;t<this.ranges.length;t+=3){let s=this.ranges[t]-(this.inverted?n:0);if(s>e)break;let l=this.ranges[t+i];if(e<=s+l&&t==3*r)return!0;n+=this.ranges[t+o]-l}return!1}forEach(e){let t=this.inverted?2:1,n=this.inverted?1:2;for(let r=0,i=0;r<this.ranges.length;r+=3){let o=this.ranges[r],s=o-(this.inverted?i:0),l=o+(this.inverted?0:i),a=this.ranges[r+t],h=this.ranges[r+n];e(s,s+a,l,l+h),i+=h-a}}invert(){return new o(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(e){return 0==e?o.empty:new o(e<0?[0,-e,0]:[0,0,e])}}o.empty=new o([]);class s{constructor(e,t,n=0,r=e?e.length:0){this.mirror=t,this.from=n,this.to=r,this._maps=e||[],this.ownData=!(e||t)}get maps(){return this._maps}slice(e=0,t=this.maps.length){return new s(this._maps,this.mirror,e,t)}appendMap(e,t){this.ownData||(this._maps=this._maps.slice(),this.mirror=this.mirror&&this.mirror.slice(),this.ownData=!0),this.to=this._maps.push(e),null!=t&&this.setMirror(this._maps.length-1,t)}appendMapping(e){for(let t=0,n=this._maps.length;t<e._maps.length;t++){let r=e.getMirror(t);this.appendMap(e._maps[t],null!=r&&r<t?n+r:void 0)}}getMirror(e){if(this.mirror){for(let t=0;t<this.mirror.length;t++)if(this.mirror[t]==e)return this.mirror[t+(t%2?-1:1)]}}setMirror(e,t){this.mirror||(this.mirror=[]),this.mirror.push(e,t)}appendMappingInverted(e){for(let t=e.maps.length-1,n=this._maps.length+e._maps.length;t>=0;t--){let r=e.getMirror(t);this.appendMap(e._maps[t].invert(),null!=r&&r>t?n-r-1:void 0)}}invert(){let e=new s;return e.appendMappingInverted(this),e}map(e,t=1){if(this.mirror)return this._map(e,t,!0);for(let n=this.from;n<this.to;n++)e=this._maps[n].map(e,t);return e}mapResult(e,t=1){return this._map(e,t,!1)}_map(e,t,n){let r=0;for(let n=this.from;n<this.to;n++){let i=this._maps[n].mapResult(e,t);if(null!=i.recover){let t=this.getMirror(n);if(null!=t&&t>n&&t<this.to){n=t,e=this._maps[t].recover(i.recover);continue}}r|=i.delInfo,e=i.pos}return n?e:new i(e,r,null)}}let l=Object.create(null);class a{getMap(){return o.empty}merge(e){return null}static fromJSON(e,t){if(!t||!t.stepType)throw RangeError("Invalid input for Step.fromJSON");let n=l[t.stepType];if(!n)throw RangeError(`No step type ${t.stepType} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in l)throw RangeError("Duplicate use of step JSON ID "+e);return l[e]=t,t.prototype.jsonID=e,t}}class h{constructor(e,t){this.doc=e,this.failed=t}static ok(e){return new h(e,null)}static fail(e){return new h(null,e)}static fromReplace(e,t,n,i){try{return h.ok(e.replace(t,n,i))}catch(e){if(e instanceof r.vI)return h.fail(e.message);throw e}}}function c(e,t,n){let i=[];for(let r=0;r<e.childCount;r++){let o=e.child(r);o.content.size&&(o=o.copy(c(o.content,t,o))),o.isInline&&(o=t(o,n,r)),i.push(o)}return r.FK.fromArray(i)}class d extends a{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=e.resolve(this.from),i=n.node(n.sharedDepth(this.to)),o=new r.Ji(c(t.content,(e,t)=>e.isAtom&&t.type.allowsMarkType(this.mark.type)?e.mark(this.mark.addToSet(e.marks)):e,i),t.openStart,t.openEnd);return h.fromReplace(e,this.from,this.to,o)}invert(){return new u(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new d(t.pos,n.pos,this.mark)}merge(e){return e instanceof d&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new d(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw RangeError("Invalid input for AddMarkStep.fromJSON");return new d(t.from,t.to,e.markFromJSON(t.mark))}}a.jsonID("addMark",d);class u extends a{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=new r.Ji(c(t.content,e=>e.mark(this.mark.removeFromSet(e.marks)),e),t.openStart,t.openEnd);return h.fromReplace(e,this.from,this.to,n)}invert(){return new d(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new u(t.pos,n.pos,this.mark)}merge(e){return e instanceof u&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new u(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw RangeError("Invalid input for RemoveMarkStep.fromJSON");return new u(t.from,t.to,e.markFromJSON(t.mark))}}a.jsonID("removeMark",u);class p extends a{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return h.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.addToSet(t.marks));return h.fromReplace(e,this.pos,this.pos+1,new r.Ji(r.FK.from(n),0,+!t.isLeaf))}invert(e){let t=e.nodeAt(this.pos);if(t){let e=this.mark.addToSet(t.marks);if(e.length==t.marks.length){for(let n=0;n<t.marks.length;n++)if(!t.marks[n].isInSet(e))return new p(this.pos,t.marks[n]);return new p(this.pos,this.mark)}}return new f(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new p(t.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if("number"!=typeof t.pos)throw RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new p(t.pos,e.markFromJSON(t.mark))}}a.jsonID("addNodeMark",p);class f extends a{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return h.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.removeFromSet(t.marks));return h.fromReplace(e,this.pos,this.pos+1,new r.Ji(r.FK.from(n),0,+!t.isLeaf))}invert(e){let t=e.nodeAt(this.pos);return t&&this.mark.isInSet(t.marks)?new p(this.pos,this.mark):this}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new f(t.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if("number"!=typeof t.pos)throw RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new f(t.pos,e.markFromJSON(t.mark))}}a.jsonID("removeNodeMark",f);class g extends a{constructor(e,t,n,r=!1){super(),this.from=e,this.to=t,this.slice=n,this.structure=r}apply(e){return this.structure&&y(e,this.from,this.to)?h.fail("Structure replace would overwrite content"):h.fromReplace(e,this.from,this.to,this.slice)}getMap(){return new o([this.from,this.to-this.from,this.slice.size])}invert(e){return new g(this.from,this.from+this.slice.size,e.slice(this.from,this.to))}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deletedAcross&&n.deletedAcross?null:new g(t.pos,Math.max(t.pos,n.pos),this.slice,this.structure)}merge(e){if(!(e instanceof g)||e.structure||this.structure)return null;if(this.from+this.slice.size!=e.from||this.slice.openEnd||e.slice.openStart)if(e.to!=this.from||this.slice.openStart||e.slice.openEnd)return null;else{let t=this.slice.size+e.slice.size==0?r.Ji.empty:new r.Ji(e.slice.content.append(this.slice.content),e.slice.openStart,this.slice.openEnd);return new g(e.from,this.to,t,this.structure)}{let t=this.slice.size+e.slice.size==0?r.Ji.empty:new r.Ji(this.slice.content.append(e.slice.content),this.slice.openStart,e.slice.openEnd);return new g(this.from,this.to+(e.to-e.from),t,this.structure)}}toJSON(){let e={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw RangeError("Invalid input for ReplaceStep.fromJSON");return new g(t.from,t.to,r.Ji.fromJSON(e,t.slice),!!t.structure)}}a.jsonID("replace",g);class m extends a{constructor(e,t,n,r,i,o,s=!1){super(),this.from=e,this.to=t,this.gapFrom=n,this.gapTo=r,this.slice=i,this.insert=o,this.structure=s}apply(e){if(this.structure&&(y(e,this.from,this.gapFrom)||y(e,this.gapTo,this.to)))return h.fail("Structure gap-replace would overwrite content");let t=e.slice(this.gapFrom,this.gapTo);if(t.openStart||t.openEnd)return h.fail("Gap is not a flat range");let n=this.slice.insertAt(this.insert,t.content);return n?h.fromReplace(e,this.from,this.to,n):h.fail("Content does not fit in gap")}getMap(){return new o([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(e){let t=this.gapTo-this.gapFrom;return new m(this.from,this.from+this.slice.size+t,this.from+this.insert,this.from+this.insert+t,e.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1),r=this.from==this.gapFrom?t.pos:e.map(this.gapFrom,-1),i=this.to==this.gapTo?n.pos:e.map(this.gapTo,1);return t.deletedAcross&&n.deletedAcross||r<t.pos||i>n.pos?null:new m(t.pos,n.pos,r,i,this.slice,this.insert,this.structure)}toJSON(){let e={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to||"number"!=typeof t.gapFrom||"number"!=typeof t.gapTo||"number"!=typeof t.insert)throw RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new m(t.from,t.to,t.gapFrom,t.gapTo,r.Ji.fromJSON(e,t.slice),t.insert,!!t.structure)}}function y(e,t,n){let r=e.resolve(t),i=n-t,o=r.depth;for(;i>0&&o>0&&r.indexAfter(o)==r.node(o).childCount;)o--,i--;if(i>0){let e=r.node(o).maybeChild(r.indexAfter(o));for(;i>0;){if(!e||e.isLeaf)return!0;e=e.firstChild,i--}}return!1}function b(e,t,n,i=n.contentMatch,o=!0){let s=e.doc.nodeAt(t),l=[],a=t+1;for(let t=0;t<s.childCount;t++){let h=s.child(t),c=a+h.nodeSize,d=i.matchType(h.type);if(d){i=d;for(let t=0;t<h.marks.length;t++)n.allowsMarkType(h.marks[t].type)||e.step(new u(a,c,h.marks[t]));if(o&&h.isText&&"pre"!=n.whitespace){let e,t=/\r?\n|\r/g,i;for(;e=t.exec(h.text);)i||(i=new r.Ji(r.FK.from(n.schema.text(" ",n.allowedMarks(h.marks))),0,0)),l.push(new g(a+e.index,a+e.index+e[0].length,i))}}else l.push(new g(a,c,r.Ji.empty));a=c}if(!i.validEnd){let t=i.fillBefore(r.FK.empty,!0);e.replace(a,a,new r.Ji(t,0,0))}for(let t=l.length-1;t>=0;t--)e.step(l[t])}function w(e){let t=e.parent.content.cutByIndex(e.startIndex,e.endIndex);for(let n=e.depth;;--n){let r=e.$from.node(n),i=e.$from.index(n),o=e.$to.indexAfter(n);if(n<e.depth&&r.canReplace(i,o,t))return n;if(0==n||r.type.spec.isolating||!((0==i||r.canReplace(i,r.childCount))&&(o==r.childCount||r.canReplace(0,o))))break}return null}function k(e,t,n=null,r=e){let i=function(e,t){let{parent:n,startIndex:r,endIndex:i}=e,o=n.contentMatchAt(r).findWrapping(t);if(!o)return null;let s=o.length?o[0]:t;return n.canReplaceWith(r,i,s)?o:null}(e,t),o=i&&function(e,t){let{parent:n,startIndex:r,endIndex:i}=e,o=n.child(r),s=t.contentMatch.findWrapping(o.type);if(!s)return null;let l=(s.length?s[s.length-1]:t).contentMatch;for(let e=r;l&&e<i;e++)l=l.matchType(n.child(e).type);return l&&l.validEnd?s:null}(r,t);return o?i.map(v).concat({type:t,attrs:n}).concat(o.map(v)):null}function v(e){return{type:e,attrs:null}}function x(e,t,n,r){t.forEach((i,o)=>{if(i.isText){let s,l=/\r?\n|\r/g;for(;s=l.exec(i.text);){let i=e.mapping.slice(r).map(n+1+o+s.index);e.replaceWith(i,i+1,t.type.schema.linebreakReplacement.create())}}})}function S(e,t,n,r){t.forEach((i,o)=>{if(i.type==i.type.schema.linebreakReplacement){let i=e.mapping.slice(r).map(n+1+o);e.replaceWith(i,i+1,t.type.schema.text("\n"))}})}function M(e,t,n=1,r){let i=e.resolve(t),o=i.depth-n,s=r&&r[r.length-1]||i.parent;if(o<0||i.parent.type.spec.isolating||!i.parent.canReplace(i.index(),i.parent.childCount)||!s.type.validContent(i.parent.content.cutByIndex(i.index(),i.parent.childCount)))return!1;for(let e=i.depth-1,t=n-2;e>o;e--,t--){let n=i.node(e),o=i.index(e);if(n.type.spec.isolating)return!1;let s=n.content.cutByIndex(o,n.childCount),l=r&&r[t+1];l&&(s=s.replaceChild(0,l.type.create(l.attrs)));let a=r&&r[t]||n;if(!n.canReplace(o+1,n.childCount)||!a.type.validContent(s))return!1}let l=i.indexAfter(o),a=r&&r[0];return i.node(o).canReplaceWith(l,l,a?a.type:i.node(o+1).type)}function C(e,t){let n=e.resolve(t),r=n.index();return E(n.nodeBefore,n.nodeAfter)&&n.parent.canReplace(r,r+1)}function E(e,t){return!!(e&&t&&!e.isLeaf&&function(e,t){t.content.size||e.type.compatibleContent(t.type);let n=e.contentMatchAt(e.childCount),{linebreakReplacement:r}=e.type.schema;for(let i=0;i<t.childCount;i++){let o=t.child(i),s=o.type==r?e.type.schema.nodes.text:o.type;if(!(n=n.matchType(s))||!e.type.allowsMarks(o.marks))return!1}return n.validEnd}(e,t))}function A(e,t,n=-1){let r=e.resolve(t);for(let e=r.depth;;e--){let i,o,s=r.index(e);if(e==r.depth?(i=r.nodeBefore,o=r.nodeAfter):n>0?(i=r.node(e+1),s++,o=r.node(e).maybeChild(s)):(i=r.node(e).maybeChild(s-1),o=r.node(e+1)),i&&!i.isTextblock&&E(i,o)&&r.node(e).canReplace(s,s+1))return t;if(0==e)break;t=n<0?r.before(e):r.after(e)}}function T(e,t,n){let r=e.resolve(t);if(!n.content.size)return t;let i=n.content;for(let e=0;e<n.openStart;e++)i=i.firstChild.content;for(let e=1;e<=(0==n.openStart&&n.size?2:1);e++)for(let t=r.depth;t>=0;t--){let n=t==r.depth?0:r.pos<=(r.start(t+1)+r.end(t+1))/2?-1:1,o=r.index(t)+ +(n>0),s=r.node(t),l=!1;if(1==e)l=s.canReplace(o,o,i);else{let e=s.contentMatchAt(o).findWrapping(i.firstChild.type);l=e&&s.canReplaceWith(o,o,e[0])}if(l)return 0==n?r.pos:n<0?r.before(t+1):r.after(t+1)}return null}function O(e,t,n=t,i=r.Ji.empty){if(t==n&&!i.size)return null;let o=e.resolve(t),s=e.resolve(n);return R(o,s,i)?new g(t,n,i):new N(o,s,i).fit()}function R(e,t,n){return!n.openStart&&!n.openEnd&&e.start()==t.start()&&e.parent.canReplace(e.index(),t.index(),n.content)}a.jsonID("replaceAround",m);class N{constructor(e,t,n){this.$from=e,this.$to=t,this.unplaced=n,this.frontier=[],this.placed=r.FK.empty;for(let t=0;t<=e.depth;t++){let n=e.node(t);this.frontier.push({type:n.type,match:n.contentMatchAt(e.indexAfter(t))})}for(let t=e.depth;t>0;t--)this.placed=r.FK.from(e.node(t).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let e=this.findFittable();e?this.placeNodes(e):this.openMore()||this.dropNode()}let e=this.mustMoveInline(),t=this.placed.size-this.depth-this.$from.depth,n=this.$from,i=this.close(e<0?this.$to:n.doc.resolve(e));if(!i)return null;let o=this.placed,s=n.depth,l=i.depth;for(;s&&l&&1==o.childCount;)o=o.firstChild.content,s--,l--;let a=new r.Ji(o,s,l);return e>-1?new m(n.pos,e,this.$to.pos,this.$to.end(),a,t):a.size||n.pos!=this.$to.pos?new g(n.pos,i.pos,a):null}findFittable(){let e=this.unplaced.openStart;for(let t=this.unplaced.content,n=0,r=this.unplaced.openEnd;n<e;n++){let i=t.firstChild;if(t.childCount>1&&(r=0),i.type.spec.isolating&&r<=n){e=n;break}t=i.content}for(let t=1;t<=2;t++)for(let n=1==t?e:this.unplaced.openStart;n>=0;n--){let e,i=null,o=(n?(i=_(this.unplaced.content,n-1).firstChild).content:this.unplaced.content).firstChild;for(let e=this.depth;e>=0;e--){let{type:s,match:l}=this.frontier[e],a,h=null;if(1==t&&(o?l.matchType(o.type)||(h=l.fillBefore(r.FK.from(o),!1)):i&&s.compatibleContent(i.type)))return{sliceDepth:n,frontierDepth:e,parent:i,inject:h};if(2==t&&o&&(a=l.findWrapping(o.type)))return{sliceDepth:n,frontierDepth:e,parent:i,wrap:a};if(i&&l.matchType(i.type))break}}}openMore(){let{content:e,openStart:t,openEnd:n}=this.unplaced,i=_(e,t);return!!i.childCount&&!i.firstChild.isLeaf&&(this.unplaced=new r.Ji(e,t+1,Math.max(n,i.size+t>=e.size-n?t+1:0)),!0)}dropNode(){let{content:e,openStart:t,openEnd:n}=this.unplaced,i=_(e,t);if(i.childCount<=1&&t>0){let o=e.size-t<=t+i.size;this.unplaced=new r.Ji(I(e,t-1,1),t-1,o?t-1:n)}else this.unplaced=new r.Ji(I(e,t,1),t,n)}placeNodes({sliceDepth:e,frontierDepth:t,parent:n,inject:i,wrap:o}){for(;this.depth>t;)this.closeFrontierNode();if(o)for(let e=0;e<o.length;e++)this.openFrontierNode(o[e]);let s=this.unplaced,l=n?n.content:s.content,a=s.openStart-e,h=0,c=[],{match:d,type:u}=this.frontier[t];if(i){for(let e=0;e<i.childCount;e++)c.push(i.child(e));d=d.matchFragment(i)}let p=l.size+e-(s.content.size-s.openEnd);for(;h<l.childCount;){let e=l.child(h),t=d.matchType(e.type);if(!t)break;(++h>1||0==a||e.content.size)&&(d=t,c.push(function e(t,n,i){if(n<=0)return t;let o=t.content;return n>1&&(o=o.replaceChild(0,e(o.firstChild,n-1,1==o.childCount?i-1:0))),n>0&&(o=t.type.contentMatch.fillBefore(o).append(o),i<=0&&(o=o.append(t.type.contentMatch.matchFragment(o).fillBefore(r.FK.empty,!0)))),t.copy(o)}(e.mark(u.allowedMarks(e.marks)),1==h?a:0,h==l.childCount?p:-1)))}let f=h==l.childCount;f||(p=-1),this.placed=L(this.placed,t,r.FK.from(c)),this.frontier[t].match=d,f&&p<0&&n&&n.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let e=0,t=l;e<p;e++){let e=t.lastChild;this.frontier.push({type:e.type,match:e.contentMatchAt(e.childCount)}),t=e.content}this.unplaced=f?0==e?r.Ji.empty:new r.Ji(I(s.content,e-1,1),e-1,p<0?s.openEnd:e-1):new r.Ji(I(s.content,e,h),s.openStart,s.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return -1;let e=this.frontier[this.depth],t;if(!e.type.isTextblock||!z(this.$to,this.$to.depth,e.type,e.match,!1)||this.$to.depth==this.depth&&(t=this.findCloseLevel(this.$to))&&t.depth==this.depth)return -1;let{depth:n}=this.$to,r=this.$to.after(n);for(;n>1&&r==this.$to.end(--n);)++r;return r}findCloseLevel(e){e:for(let t=Math.min(this.depth,e.depth);t>=0;t--){let{match:n,type:r}=this.frontier[t],i=t<e.depth&&e.end(t+1)==e.pos+(e.depth-(t+1)),o=z(e,t,r,n,i);if(o){for(let n=t-1;n>=0;n--){let{match:t,type:r}=this.frontier[n],i=z(e,n,r,t,!0);if(!i||i.childCount)continue e}return{depth:t,fit:o,move:i?e.doc.resolve(e.after(t+1)):e}}}}close(e){let t=this.findCloseLevel(e);if(!t)return null;for(;this.depth>t.depth;)this.closeFrontierNode();t.fit.childCount&&(this.placed=L(this.placed,t.depth,t.fit)),e=t.move;for(let n=t.depth+1;n<=e.depth;n++){let t=e.node(n),r=t.type.contentMatch.fillBefore(t.content,!0,e.index(n));this.openFrontierNode(t.type,t.attrs,r)}return e}openFrontierNode(e,t=null,n){let i=this.frontier[this.depth];i.match=i.match.matchType(e),this.placed=L(this.placed,this.depth,r.FK.from(e.create(t,n))),this.frontier.push({type:e,match:e.contentMatch})}closeFrontierNode(){let e=this.frontier.pop().match.fillBefore(r.FK.empty,!0);e.childCount&&(this.placed=L(this.placed,this.frontier.length,e))}}function I(e,t,n){return 0==t?e.cutByIndex(n,e.childCount):e.replaceChild(0,e.firstChild.copy(I(e.firstChild.content,t-1,n)))}function L(e,t,n){return 0==t?e.append(n):e.replaceChild(e.childCount-1,e.lastChild.copy(L(e.lastChild.content,t-1,n)))}function _(e,t){for(let n=0;n<t;n++)e=e.firstChild.content;return e}function z(e,t,n,r,i){let o=e.node(t),s=i?e.indexAfter(t):e.index(t);if(s==o.childCount&&!n.compatibleContent(o.type))return null;let l=r.fillBefore(o.content,!0,s);return l&&!function(e,t,n){for(let r=n;r<t.childCount;r++)if(!e.allowsMarks(t.child(r).marks))return!0;return!1}(n,o.content,s)?l:null}function $(e,t){let n=[],r=Math.min(e.depth,t.depth);for(let i=r;i>=0;i--){let r=e.start(i);if(r<e.pos-(e.depth-i)||t.end(i)>t.pos+(t.depth-i)||e.node(i).type.spec.isolating||t.node(i).type.spec.isolating)break;(r==t.start(i)||i==e.depth&&i==t.depth&&e.parent.inlineContent&&t.parent.inlineContent&&i&&t.start(i-1)==r-1)&&n.push(i)}return n}class j extends a{constructor(e,t,n){super(),this.pos=e,this.attr=t,this.value=n}apply(e){let t=e.nodeAt(this.pos);if(!t)return h.fail("No node at attribute step's position");let n=Object.create(null);for(let e in t.attrs)n[e]=t.attrs[e];n[this.attr]=this.value;let i=t.type.create(n,null,t.marks);return h.fromReplace(e,this.pos,this.pos+1,new r.Ji(r.FK.from(i),0,+!t.isLeaf))}getMap(){return o.empty}invert(e){return new j(this.pos,this.attr,e.nodeAt(this.pos).attrs[this.attr])}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new j(t.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(e,t){if("number"!=typeof t.pos||"string"!=typeof t.attr)throw RangeError("Invalid input for AttrStep.fromJSON");return new j(t.pos,t.attr,t.value)}}a.jsonID("attr",j);class B extends a{constructor(e,t){super(),this.attr=e,this.value=t}apply(e){let t=Object.create(null);for(let n in e.attrs)t[n]=e.attrs[n];t[this.attr]=this.value;let n=e.type.create(t,e.content,e.marks);return h.ok(n)}getMap(){return o.empty}invert(e){return new B(this.attr,e.attrs[this.attr])}map(e){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(e,t){if("string"!=typeof t.attr)throw RangeError("Invalid input for DocAttrStep.fromJSON");return new B(t.attr,t.value)}}a.jsonID("docAttr",B);let H=class extends Error{};(H=function e(t){let n=Error.call(this,t);return n.__proto__=e.prototype,n}).prototype=Object.create(Error.prototype),H.prototype.constructor=H,H.prototype.name="TransformError";class D{constructor(e){this.doc=e,this.steps=[],this.docs=[],this.mapping=new s}get before(){return this.docs.length?this.docs[0]:this.doc}step(e){let t=this.maybeStep(e);if(t.failed)throw new H(t.failed);return this}maybeStep(e){let t=e.apply(this.doc);return t.failed||this.addStep(e,t.doc),t}get docChanged(){return this.steps.length>0}addStep(e,t){this.docs.push(this.doc),this.steps.push(e),this.mapping.appendMap(e.getMap()),this.doc=t}replace(e,t=e,n=r.Ji.empty){let i=O(this.doc,e,t,n);return i&&this.step(i),this}replaceWith(e,t,n){return this.replace(e,t,new r.Ji(r.FK.from(n),0,0))}delete(e,t){return this.replace(e,t,r.Ji.empty)}insert(e,t){return this.replaceWith(e,e,t)}replaceRange(e,t,n){return!function(e,t,n,i){if(!i.size)return e.deleteRange(t,n);let o=e.doc.resolve(t),s=e.doc.resolve(n);if(R(o,s,i))return e.step(new g(t,n,i));let l=$(o,e.doc.resolve(n));0==l[l.length-1]&&l.pop();let a=-(o.depth+1);l.unshift(a);for(let e=o.depth,t=o.pos-1;e>0;e--,t--){let n=o.node(e).type.spec;if(n.defining||n.definingAsContext||n.isolating)break;l.indexOf(e)>-1?a=e:o.before(e)==t&&l.splice(1,0,-e)}let h=l.indexOf(a),c=[],d=i.openStart;for(let e=i.content,t=0;;t++){let n=e.firstChild;if(c.push(n),t==i.openStart)break;e=n.content}for(let e=d-1;e>=0;e--){var u;let t=c[e],n=(u=t.type).spec.defining||u.spec.definingForContent;if(n&&!t.sameMarkup(o.node(Math.abs(a)-1)))d=e;else if(n||!t.type.isTextblock)break}for(let t=i.openStart;t>=0;t--){let a=(t+d+1)%(i.openStart+1),u=c[a];if(u)for(let t=0;t<l.length;t++){let c=l[(t+h)%l.length],d=!0;c<0&&(d=!1,c=-c);let p=o.node(c-1),f=o.index(c-1);if(p.canReplaceWith(f,f,u.type,u.marks))return e.replace(o.before(c),d?s.after(c):n,new r.Ji(function e(t,n,i,o,s){if(n<i){let r=t.firstChild;t=t.replaceChild(0,r.copy(e(r.content,n+1,i,o,r)))}if(n>o){let e=s.contentMatchAt(0),n=e.fillBefore(t).append(t);t=n.append(e.matchFragment(n).fillBefore(r.FK.empty,!0))}return t}(i.content,0,i.openStart,a),a,i.openEnd))}}let p=e.steps.length;for(let r=l.length-1;r>=0&&(e.replace(t,n,i),!(e.steps.length>p));r--){let e=l[r];e<0||(t=o.before(e),n=s.after(e))}}(this,e,t,n),this}replaceRangeWith(e,t,n){var i=e,o=t;if(!n.isInline&&i==o&&this.doc.resolve(i).parent.content.size){let e=function(e,t,n){let r=e.resolve(t);if(r.parent.canReplaceWith(r.index(),r.index(),n))return t;if(0==r.parentOffset)for(let e=r.depth-1;e>=0;e--){let t=r.index(e);if(r.node(e).canReplaceWith(t,t,n))return r.before(e+1);if(t>0)return null}if(r.parentOffset==r.parent.content.size)for(let e=r.depth-1;e>=0;e--){let t=r.indexAfter(e);if(r.node(e).canReplaceWith(t,t,n))return r.after(e+1);if(t<r.node(e).childCount)break}return null}(this.doc,i,n.type);null!=e&&(i=o=e)}return this.replaceRange(i,o,new r.Ji(r.FK.from(n),0,0)),this}deleteRange(e,t){return!function(e,t,n){let r=e.doc.resolve(t),i=e.doc.resolve(n),o=$(r,i);for(let t=0;t<o.length;t++){let n=o[t],s=t==o.length-1;if(s&&0==n||r.node(n).type.contentMatch.validEnd)return e.delete(r.start(n),i.end(n));if(n>0&&(s||r.node(n-1).canReplace(r.index(n-1),i.indexAfter(n-1))))return e.delete(r.before(n),i.after(n))}for(let o=1;o<=r.depth&&o<=i.depth;o++)if(t-r.start(o)==r.depth-o&&n>r.end(o)&&i.end(o)-n!=i.depth-o&&r.start(o-1)==i.start(o-1)&&r.node(o-1).canReplace(r.index(o-1),i.index(o-1)))return e.delete(r.before(o),n);e.delete(t,n)}(this,e,t),this}lift(e,t){return!function(e,t,n){let{$from:i,$to:o,depth:s}=t,l=i.before(s+1),a=o.after(s+1),h=l,c=a,d=r.FK.empty,u=0;for(let e=s,t=!1;e>n;e--)t||i.index(e)>0?(t=!0,d=r.FK.from(i.node(e).copy(d)),u++):h--;let p=r.FK.empty,f=0;for(let e=s,t=!1;e>n;e--)t||o.after(e+1)<o.end(e)?(t=!0,p=r.FK.from(o.node(e).copy(p)),f++):c++;e.step(new m(h,c,l,a,new r.Ji(d.append(p),u,f),d.size-u,!0))}(this,e,t),this}join(e,t=1){return!function(e,t,n){let i=null,{linebreakReplacement:o}=e.doc.type.schema,s=e.doc.resolve(t-n),l=s.node().type;if(o&&l.inlineContent){let e="pre"==l.whitespace,t=!!l.contentMatch.matchType(o);e&&!t?i=!1:!e&&t&&(i=!0)}let a=e.steps.length;if(!1===i){let r=e.doc.resolve(t+n);S(e,r.node(),r.before(),a)}l.inlineContent&&b(e,t+n-1,l,s.node().contentMatchAt(s.index()),null==i);let h=e.mapping.slice(a),c=h.map(t-n);if(e.step(new g(c,h.map(t+n,-1),r.Ji.empty,!0)),!0===i){let t=e.doc.resolve(c);x(e,t.node(),t.before(),e.steps.length)}}(this,e,t),this}wrap(e,t){return!function(e,t,n){let i=r.FK.empty;for(let e=n.length-1;e>=0;e--){if(i.size){let t=n[e].type.contentMatch.matchFragment(i);if(!t||!t.validEnd)throw RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}i=r.FK.from(n[e].type.create(n[e].attrs,i))}let o=t.start,s=t.end;e.step(new m(o,s,o,s,new r.Ji(i,0,0),n.length,!0))}(this,e,t),this}setBlockType(e,t=e,n,i=null){var o=this;if(!n.isTextblock)throw RangeError("Type given to setBlockType should be a textblock");let s=o.steps.length;return o.doc.nodesBetween(e,t,(e,t)=>{var l,a,h;let c,d,u="function"==typeof i?i(e):i;if(e.isTextblock&&!e.hasMarkup(n,u)&&(l=o.doc,a=o.mapping.slice(s).map(t),h=n,d=(c=l.resolve(a)).index(),c.parent.canReplaceWith(d,d+1,h))){let i=null;if(n.schema.linebreakReplacement){let e="pre"==n.whitespace,t=!!n.contentMatch.matchType(n.schema.linebreakReplacement);e&&!t?i=!1:!e&&t&&(i=!0)}!1===i&&S(o,e,t,s),b(o,o.mapping.slice(s).map(t,1),n,void 0,null===i);let l=o.mapping.slice(s),a=l.map(t,1),h=l.map(t+e.nodeSize,1);return o.step(new m(a,h,a+1,h-1,new r.Ji(r.FK.from(n.create(u,null,e.marks)),0,0),1,!0)),!0===i&&x(o,e,t,s),!1}}),this}setNodeMarkup(e,t,n=null,i){return!function(e,t,n,i,o){let s=e.doc.nodeAt(t);if(!s)throw RangeError("No node at given position");n||(n=s.type);let l=n.create(i,null,o||s.marks);if(s.isLeaf)return e.replaceWith(t,t+s.nodeSize,l);if(!n.validContent(s.content))throw RangeError("Invalid content for node type "+n.name);e.step(new m(t,t+s.nodeSize,t+1,t+s.nodeSize-1,new r.Ji(r.FK.from(l),0,0),1,!0))}(this,e,t,n,i),this}setNodeAttribute(e,t,n){return this.step(new j(e,t,n)),this}setDocAttribute(e,t){return this.step(new B(e,t)),this}addNodeMark(e,t){return this.step(new p(e,t)),this}removeNodeMark(e,t){let n=this.doc.nodeAt(e);if(!n)throw RangeError("No node at position "+e);if(t instanceof r.CU)t.isInSet(n.marks)&&this.step(new f(e,t));else{let r=n.marks,i,o=[];for(;i=t.isInSet(r);)o.push(new f(e,i)),r=i.removeFromSet(r);for(let e=o.length-1;e>=0;e--)this.step(o[e])}return this}split(e,t=1,n){return!function(e,t,n=1,i){let o=e.doc.resolve(t),s=r.FK.empty,l=r.FK.empty;for(let e=o.depth,t=o.depth-n,a=n-1;e>t;e--,a--){s=r.FK.from(o.node(e).copy(s));let t=i&&i[a];l=r.FK.from(t?t.type.create(t.attrs,l):o.node(e).copy(l))}e.step(new g(t,t,new r.Ji(s.append(l),n,n),!0))}(this,e,t,n),this}addMark(e,t,n){var r;let i,o,s,l;return r=this,s=[],l=[],r.doc.nodesBetween(e,t,(r,a,h)=>{if(!r.isInline)return;let c=r.marks;if(!n.isInSet(c)&&h.type.allowsMarkType(n.type)){let h=Math.max(a,e),p=Math.min(a+r.nodeSize,t),f=n.addToSet(c);for(let e=0;e<c.length;e++)c[e].isInSet(f)||(i&&i.to==h&&i.mark.eq(c[e])?i.to=p:s.push(i=new u(h,p,c[e])));o&&o.to==h?o.to=p:l.push(o=new d(h,p,n))}}),s.forEach(e=>r.step(e)),l.forEach(e=>r.step(e)),this}removeMark(e,t,n){var i;let o,s;return i=this,o=[],s=0,i.doc.nodesBetween(e,t,(i,l)=>{if(!i.isInline)return;s++;let a=null;if(n instanceof r.sX){let e=i.marks,t;for(;t=n.isInSet(e);)(a||(a=[])).push(t),e=t.removeFromSet(e)}else n?n.isInSet(i.marks)&&(a=[n]):a=i.marks;if(a&&a.length){let n=Math.min(l+i.nodeSize,t);for(let t=0;t<a.length;t++){let r=a[t],i;for(let e=0;e<o.length;e++){let t=o[e];t.step==s-1&&r.eq(o[e].style)&&(i=t)}i?(i.to=n,i.step=s):o.push({style:r,from:Math.max(l,e),to:n,step:s})}}}),o.forEach(e=>i.step(new u(e.from,e.to,e.style))),this}clearIncompatible(e,t,n){return b(this,e,t,n),this}}},4589:(e,t,n)=>{"use strict";n.d(t,{A:()=>r}),n(66377);let r=n(4701).YY.create({name:"color",addOptions:()=>({types:["textStyle"]}),addGlobalAttributes(){return[{types:this.options.types,attributes:{color:{default:null,parseHTML:e=>{var t;return null==(t=e.style.color)?void 0:t.replace(/['"]+/g,"")},renderHTML:e=>e.color?{style:`color: ${e.color}`}:{}}}}]},addCommands:()=>({setColor:e=>({chain:t})=>t().setMark("textStyle",{color:e}).run(),unsetColor:()=>({chain:e})=>e().setMark("textStyle",{color:null}).removeEmptyTextStyle().run()})})},4652:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>s});var r=n(4701);let i=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))$/,o=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))/g,s=r.CU.create({name:"highlight",addOptions:()=>({multicolor:!1,HTMLAttributes:{}}),addAttributes(){return this.options.multicolor?{color:{default:null,parseHTML:e=>e.getAttribute("data-color")||e.style.backgroundColor,renderHTML:e=>e.color?{"data-color":e.color,style:`background-color: ${e.color}; color: inherit`}:{}}}:{}},parseHTML:()=>[{tag:"mark"}],renderHTML({HTMLAttributes:e}){return["mark",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setHighlight:e=>({commands:t})=>t.setMark(this.name,e),toggleHighlight:e=>({commands:t})=>t.toggleMark(this.name,e),unsetHighlight:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-h":()=>this.editor.commands.toggleHighlight()}},addInputRules(){return[(0,r.OX)({find:i,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:o,type:this.type})]}})},8772:e=>{class t{constructor(e){void 0===e.data&&(e.data={}),this.data=e.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}}function n(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function r(e,...t){let n=Object.create(null);for(let t in e)n[t]=e[t];return t.forEach(function(e){for(let t in e)n[t]=e[t]}),n}let i=e=>!!e.scope,o=(e,{prefix:t})=>{if(e.startsWith("language:"))return e.replace("language:","language-");if(e.includes(".")){let n=e.split(".");return[`${t}${n.shift()}`,...n.map((e,t)=>`${e}${"_".repeat(t+1)}`)].join(" ")}return`${t}${e}`};class s{constructor(e,t){this.buffer="",this.classPrefix=t.classPrefix,e.walk(this)}addText(e){this.buffer+=n(e)}openNode(e){if(!i(e))return;let t=o(e.scope,{prefix:this.classPrefix});this.span(t)}closeNode(e){i(e)&&(this.buffer+="</span>")}value(){return this.buffer}span(e){this.buffer+=`<span class="${e}">`}}let l=(e={})=>{let t={children:[]};return Object.assign(t,e),t};class a{constructor(){this.rootNode=l(),this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(e){this.top.children.push(e)}openNode(e){let t=l({scope:e});this.add(t),this.stack.push(t)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(e){return this.constructor._walk(e,this.rootNode)}static _walk(e,t){return"string"==typeof t?e.addText(t):t.children&&(e.openNode(t),t.children.forEach(t=>this._walk(e,t)),e.closeNode(t)),e}static _collapse(e){"string"!=typeof e&&e.children&&(e.children.every(e=>"string"==typeof e)?e.children=[e.children.join("")]:e.children.forEach(e=>{a._collapse(e)}))}}class h extends a{constructor(e){super(),this.options=e}addText(e){""!==e&&this.add(e)}startScope(e){this.openNode(e)}endScope(){this.closeNode()}__addSublanguage(e,t){let n=e.root;t&&(n.scope=`language:${t}`),this.add(n)}toHTML(){return new s(this,this.options).value()}finalize(){return this.closeAllNodes(),!0}}function c(e){return e?"string"==typeof e?e:e.source:null}function d(e){return f("(?=",e,")")}function u(e){return f("(?:",e,")*")}function p(e){return f("(?:",e,")?")}function f(...e){return e.map(e=>c(e)).join("")}function g(...e){return"("+(function(e){let t=e[e.length-1];return"object"==typeof t&&t.constructor===Object?(e.splice(e.length-1,1),t):{}}(e).capture?"":"?:")+e.map(e=>c(e)).join("|")+")"}function m(e){return RegExp(e.toString()+"|").exec("").length-1}let y=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function b(e,{joinWith:t}){let n=0;return e.map(e=>{let t=n+=1,r=c(e),i="";for(;r.length>0;){let e=y.exec(r);if(!e){i+=r;break}i+=r.substring(0,e.index),r=r.substring(e.index+e[0].length),"\\"===e[0][0]&&e[1]?i+="\\"+String(Number(e[1])+t):(i+=e[0],"("===e[0]&&n++)}return i}).map(e=>`(${e})`).join(t)}let w="[a-zA-Z]\\w*",k="[a-zA-Z_]\\w*",v="\\b\\d+(\\.\\d+)?",x="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",S="\\b(0b[01]+)",M={begin:"\\\\[\\s\\S]",relevance:0},C=function(e,t,n={}){let i=r({scope:"comment",begin:e,end:t,contains:[]},n);i.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0});let o=g("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return i.contains.push({begin:f(/[ ]+/,"(",o,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),i},E=C("//","$"),A=C("/\\*","\\*/"),T=C("#","$");var O=Object.freeze({__proto__:null,APOS_STRING_MODE:{scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[M]},BACKSLASH_ESCAPE:M,BINARY_NUMBER_MODE:{scope:"number",begin:S,relevance:0},BINARY_NUMBER_RE:S,COMMENT:C,C_BLOCK_COMMENT_MODE:A,C_LINE_COMMENT_MODE:E,C_NUMBER_MODE:{scope:"number",begin:x,relevance:0},C_NUMBER_RE:x,END_SAME_AS_BEGIN:function(e){return Object.assign(e,{"on:begin":(e,t)=>{t.data._beginMatch=e[1]},"on:end":(e,t)=>{t.data._beginMatch!==e[1]&&t.ignoreMatch()}})},HASH_COMMENT_MODE:T,IDENT_RE:w,MATCH_NOTHING_RE:/\b\B/,METHOD_GUARD:{begin:"\\.\\s*"+k,relevance:0},NUMBER_MODE:{scope:"number",begin:v,relevance:0},NUMBER_RE:v,PHRASAL_WORDS_MODE:{begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},QUOTE_STRING_MODE:{scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[M]},REGEXP_MODE:{scope:"regexp",begin:/\/(?=[^/\n]*\/)/,end:/\/[gimuy]*/,contains:[M,{begin:/\[/,end:/\]/,relevance:0,contains:[M]}]},RE_STARTERS_RE:"!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",SHEBANG:(e={})=>{let t=/^#![ ]*\//;return e.binary&&(e.begin=f(t,/.*\b/,e.binary,/\b.*/)),r({scope:"meta",begin:t,end:/$/,relevance:0,"on:begin":(e,t)=>{0!==e.index&&t.ignoreMatch()}},e)},TITLE_MODE:{scope:"title",begin:w,relevance:0},UNDERSCORE_IDENT_RE:k,UNDERSCORE_TITLE_MODE:{scope:"title",begin:k,relevance:0}});function R(e,t){"."===e.input[e.index-1]&&t.ignoreMatch()}function N(e,t){void 0!==e.className&&(e.scope=e.className,delete e.className)}function I(e,t){t&&e.beginKeywords&&(e.begin="\\b("+e.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",e.__beforeBegin=R,e.keywords=e.keywords||e.beginKeywords,delete e.beginKeywords,void 0===e.relevance&&(e.relevance=0))}function L(e,t){Array.isArray(e.illegal)&&(e.illegal=g(...e.illegal))}function _(e,t){if(e.match){if(e.begin||e.end)throw Error("begin & end are not supported with match");e.begin=e.match,delete e.match}}function z(e,t){void 0===e.relevance&&(e.relevance=1)}let $=(e,t)=>{if(!e.beforeMatch)return;if(e.starts)throw Error("beforeMatch cannot be used with starts");let n=Object.assign({},e);Object.keys(e).forEach(t=>{delete e[t]}),e.keywords=n.keywords,e.begin=f(n.beforeMatch,d(n.begin)),e.starts={relevance:0,contains:[Object.assign(n,{endsParent:!0})]},e.relevance=0,delete n.beforeMatch},j=["of","and","for","in","not","or","if","then","parent","list","value"],B={},H=e=>{console.error(e)},D=(e,...t)=>{console.log(`WARN: ${e}`,...t)},P=(e,t)=>{B[`${e}/${t}`]||(console.log(`Deprecated as of ${e}. ${t}`),B[`${e}/${t}`]=!0)},F=Error();function J(e,t,{key:n}){let r=0,i=e[n],o={},s={};for(let e=1;e<=t.length;e++)s[e+r]=i[e],o[e+r]=!0,r+=m(t[e-1]);e[n]=s,e[n]._emit=o,e[n]._multi=!0}function K(e){if(e.scope&&"object"==typeof e.scope&&null!==e.scope&&(e.beginScope=e.scope,delete e.scope),"string"==typeof e.beginScope&&(e.beginScope={_wrap:e.beginScope}),"string"==typeof e.endScope&&(e.endScope={_wrap:e.endScope}),Array.isArray(e.begin)){if(e.skip||e.excludeBegin||e.returnBegin)throw H("skip, excludeBegin, returnBegin not compatible with beginScope: {}"),F;if("object"!=typeof e.beginScope||null===e.beginScope)throw H("beginScope must be object"),F;J(e,e.begin,{key:"beginScope"}),e.begin=b(e.begin,{joinWith:""})}if(Array.isArray(e.end)){if(e.skip||e.excludeEnd||e.returnEnd)throw H("skip, excludeEnd, returnEnd not compatible with endScope: {}"),F;if("object"!=typeof e.endScope||null===e.endScope)throw H("endScope must be object"),F;J(e,e.end,{key:"endScope"}),e.end=b(e.end,{joinWith:""})}}class W extends Error{constructor(e,t){super(e),this.name="HTMLInjectionError",this.html=t}}let U=Symbol("nomatch"),V=function(e){let i=Object.create(null),o=Object.create(null),s=[],l=!0,a="Could not find the language '{}', did you forget to load/include a language module?",y={disableAutodetect:!0,name:"Plain text",contains:[]},w={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:h};function k(e){return w.noHighlightRe.test(e)}function v(e,t,n){let r="",i="";"object"==typeof t?(r=e,n=t.ignoreIllegals,i=t.language):(P("10.7.0","highlight(lang, code, ...args) has been deprecated."),P("10.7.0","Please use highlight(code, options) instead.\nhttps://github.com/highlightjs/highlight.js/issues/2277"),i=e,r=t),void 0===n&&(n=!0);let o={code:r,language:i};B("before:highlight",o);let s=o.result?o.result:x(o.language,o.code,n);return s.code=o.code,B("after:highlight",s),s}function x(e,o,s,h){let d=Object.create(null);function u(){if(!T.keywords)return void R.addText(D);let e=0;T.keywordPatternRe.lastIndex=0;let t=T.keywordPatternRe.exec(D),n="";for(;t;){n+=D.substring(e,t.index);let r=M.case_insensitive?t[0].toLowerCase():t[0],i=T.keywords[r];if(i){let[e,o]=i;if(R.addText(n),n="",d[r]=(d[r]||0)+1,d[r]<=7&&(P+=o),e.startsWith("_"))n+=t[0];else{let n=M.classNameAliases[e]||e;f(t[0],n)}}else n+=t[0];e=T.keywordPatternRe.lastIndex,t=T.keywordPatternRe.exec(D)}n+=D.substring(e),R.addText(n)}function p(){null!=T.subLanguage?function(){if(""===D)return;let e=null;if("string"==typeof T.subLanguage){if(!i[T.subLanguage])return R.addText(D);e=x(T.subLanguage,D,!0,O[T.subLanguage]),O[T.subLanguage]=e._top}else e=S(D,T.subLanguage.length?T.subLanguage:null);T.relevance>0&&(P+=e.relevance),R.__addSublanguage(e._emitter,e.language)}():u(),D=""}function f(e,t){""!==e&&(R.startScope(t),R.addText(e),R.endScope())}function g(e,t){let n=1,r=t.length-1;for(;n<=r;){if(!e._emit[n]){n++;continue}let r=M.classNameAliases[e[n]]||e[n],i=t[n];r?f(i,r):(D=i,u(),D=""),n++}}function y(e,t){return e.scope&&"string"==typeof e.scope&&R.openNode(M.classNameAliases[e.scope]||e.scope),e.beginScope&&(e.beginScope._wrap?(f(D,M.classNameAliases[e.beginScope._wrap]||e.beginScope._wrap),D=""):e.beginScope._multi&&(g(e.beginScope,t),D="")),T=Object.create(e,{parent:{value:T}})}let k={};function v(n,r){let i=r&&r[0];if(D+=n,null==i)return p(),0;if("begin"===k.type&&"end"===r.type&&k.index===r.index&&""===i){if(D+=o.slice(r.index,r.index+1),!l){let t=Error(`0 width match regex (${e})`);throw t.languageName=e,t.badRule=k.rule,t}return 1}if(k=r,"begin"===r.type){let e=r[0],n=r.rule,i=new t(n);for(let t of[n.__beforeBegin,n["on:begin"]])if(t&&(t(r,i),i.isMatchIgnored))return 0===T.matcher.regexIndex?(D+=e[0],1):(W=!0,0);return n.skip?D+=e:(n.excludeBegin&&(D+=e),p(),n.returnBegin||n.excludeBegin||(D=e)),y(n,r),n.returnBegin?0:e.length}if("illegal"!==r.type||s){if("end"===r.type){let e=function(e){let n=e[0],r=o.substring(e.index),i=function e(n,r,i){let o=function(e,t){let n=e&&e.exec(t);return n&&0===n.index}(n.endRe,i);if(o){if(n["on:end"]){let e=new t(n);n["on:end"](r,e),e.isMatchIgnored&&(o=!1)}if(o){for(;n.endsParent&&n.parent;)n=n.parent;return n}}if(n.endsWithParent)return e(n.parent,r,i)}(T,e,r);if(!i)return U;let s=T;T.endScope&&T.endScope._wrap?(p(),f(n,T.endScope._wrap)):T.endScope&&T.endScope._multi?(p(),g(T.endScope,e)):s.skip?D+=n:(s.returnEnd||s.excludeEnd||(D+=n),p(),s.excludeEnd&&(D=n));do T.scope&&R.closeNode(),T.skip||T.subLanguage||(P+=T.relevance),T=T.parent;while(T!==i.parent);return i.starts&&y(i.starts,e),s.returnEnd?0:n.length}(r);if(e!==U)return e}}else{let e=Error('Illegal lexeme "'+i+'" for mode "'+(T.scope||"<unnamed>")+'"');throw e.mode=T,e}if("illegal"===r.type&&""===i)return D+="\n",1;if(J>1e5&&J>3*r.index)throw Error("potential infinite loop, way more iterations than matches");return D+=i,i.length}let M=A(e);if(!M)throw H(a.replace("{}",e)),Error('Unknown language: "'+e+'"');let C=function(e){function t(t,n){return RegExp(c(t),"m"+(e.case_insensitive?"i":"")+(e.unicodeRegex?"u":"")+(n?"g":""))}class n{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(e,t){t.position=this.position++,this.matchIndexes[this.matchAt]=t,this.regexes.push([t,e]),this.matchAt+=m(e)+1}compile(){0===this.regexes.length&&(this.exec=()=>null);let e=this.regexes.map(e=>e[1]);this.matcherRe=t(b(e,{joinWith:"|"}),!0),this.lastIndex=0}exec(e){this.matcherRe.lastIndex=this.lastIndex;let t=this.matcherRe.exec(e);if(!t)return null;let n=t.findIndex((e,t)=>t>0&&void 0!==e),r=this.matchIndexes[n];return t.splice(0,n),Object.assign(t,r)}}class i{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(e){if(this.multiRegexes[e])return this.multiRegexes[e];let t=new n;return this.rules.slice(e).forEach(([e,n])=>t.addRule(e,n)),t.compile(),this.multiRegexes[e]=t,t}resumingScanAtSamePosition(){return 0!==this.regexIndex}considerAll(){this.regexIndex=0}addRule(e,t){this.rules.push([e,t]),"begin"===t.type&&this.count++}exec(e){let t=this.getMatcher(this.regexIndex);t.lastIndex=this.lastIndex;let n=t.exec(e);if(this.resumingScanAtSamePosition())if(n&&n.index===this.lastIndex);else{let t=this.getMatcher(0);t.lastIndex=this.lastIndex+1,n=t.exec(e)}return n&&(this.regexIndex+=n.position+1,this.regexIndex===this.count&&this.considerAll()),n}}if(e.compilerExtensions||(e.compilerExtensions=[]),e.contains&&e.contains.includes("self"))throw Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return e.classNameAliases=r(e.classNameAliases||{}),function n(o,s){if(o.isCompiled)return o;[N,_,K,$].forEach(e=>e(o,s)),e.compilerExtensions.forEach(e=>e(o,s)),o.__beforeBegin=null,[I,L,z].forEach(e=>e(o,s)),o.isCompiled=!0;let l=null;return"object"==typeof o.keywords&&o.keywords.$pattern&&(o.keywords=Object.assign({},o.keywords),l=o.keywords.$pattern,delete o.keywords.$pattern),l=l||/\w+/,o.keywords&&(o.keywords=function e(t,n,r="keyword"){let i=Object.create(null);return"string"==typeof t?o(r,t.split(" ")):Array.isArray(t)?o(r,t):Object.keys(t).forEach(function(r){Object.assign(i,e(t[r],n,r))}),i;function o(e,t){n&&(t=t.map(e=>e.toLowerCase())),t.forEach(function(t){var n,r,o;let s=t.split("|");i[s[0]]=[e,(n=s[0],(r=s[1])?Number(r):+(o=n,!j.includes(o.toLowerCase())))]})}}(o.keywords,e.case_insensitive)),o.keywordPatternRe=t(l,!0),s&&(o.begin||(o.begin=/\B|\b/),o.beginRe=t(o.begin),o.end||o.endsWithParent||(o.end=/\B|\b/),o.end&&(o.endRe=t(o.end)),o.terminatorEnd=c(o.end)||"",o.endsWithParent&&s.terminatorEnd&&(o.terminatorEnd+=(o.end?"|":"")+s.terminatorEnd)),o.illegal&&(o.illegalRe=t(o.illegal)),o.contains||(o.contains=[]),o.contains=[].concat(...o.contains.map(function(e){var t;return((t="self"===e?o:e).variants&&!t.cachedVariants&&(t.cachedVariants=t.variants.map(function(e){return r(t,{variants:null},e)})),t.cachedVariants)?t.cachedVariants:!function e(t){return!!t&&(t.endsWithParent||e(t.starts))}(t)?Object.isFrozen(t)?r(t):t:r(t,{starts:t.starts?r(t.starts):null})})),o.contains.forEach(function(e){n(e,o)}),o.starts&&n(o.starts,s),o.matcher=function(e){let t=new i;return e.contains.forEach(e=>t.addRule(e.begin,{rule:e,type:"begin"})),e.terminatorEnd&&t.addRule(e.terminatorEnd,{type:"end"}),e.illegal&&t.addRule(e.illegal,{type:"illegal"}),t}(o),o}(e)}(M),E="",T=h||C,O={},R=new w.__emitter(w),B=[];for(let e=T;e!==M;e=e.parent)e.scope&&B.unshift(e.scope);B.forEach(e=>R.openNode(e));let D="",P=0,F=0,J=0,W=!1;try{if(M.__emitTokens)M.__emitTokens(o,R);else{for(T.matcher.considerAll();;){J++,W?W=!1:T.matcher.considerAll(),T.matcher.lastIndex=F;let e=T.matcher.exec(o);if(!e)break;let t=o.substring(F,e.index),n=v(t,e);F=e.index+n}v(o.substring(F))}return R.finalize(),E=R.toHTML(),{language:e,value:E,relevance:P,illegal:!1,_emitter:R,_top:T}}catch(t){if(t.message&&t.message.includes("Illegal"))return{language:e,value:n(o),illegal:!0,relevance:0,_illegalBy:{message:t.message,index:F,context:o.slice(F-100,F+100),mode:t.mode,resultSoFar:E},_emitter:R};if(l)return{language:e,value:n(o),illegal:!1,relevance:0,errorRaised:t,_emitter:R,_top:T};throw t}}function S(e,t){t=t||w.languages||Object.keys(i);let r=function(e){let t={value:n(e),illegal:!1,relevance:0,_top:y,_emitter:new w.__emitter(w)};return t._emitter.addText(e),t}(e),o=t.filter(A).filter(R).map(t=>x(t,e,!1));o.unshift(r);let[s,l]=o.sort((e,t)=>{if(e.relevance!==t.relevance)return t.relevance-e.relevance;if(e.language&&t.language){if(A(e.language).supersetOf===t.language)return 1;else if(A(t.language).supersetOf===e.language)return -1}return 0});return s.secondBest=l,s}function M(e){let t=null,n=function(e){let t=e.className+" ";t+=e.parentNode?e.parentNode.className:"";let n=w.languageDetectRe.exec(t);if(n){let t=A(n[1]);return t||(D(a.replace("{}",n[1])),D("Falling back to no-highlight mode for this block.",e)),t?n[1]:"no-highlight"}return t.split(/\s+/).find(e=>k(e)||A(e))}(e);if(k(n))return;if(B("before:highlightElement",{el:e,language:n}),e.dataset.highlighted)return void console.log("Element previously highlighted. To highlight again, first unset `dataset.highlighted`.",e);if(e.children.length>0&&(w.ignoreUnescapedHTML||(console.warn("One of your code blocks includes unescaped HTML. This is a potentially serious security risk."),console.warn("https://github.com/highlightjs/highlight.js/wiki/security"),console.warn("The element with unescaped HTML:"),console.warn(e)),w.throwUnescapedHTML))throw new W("One of your code blocks includes unescaped HTML.",e.innerHTML);let r=e.textContent,i=n?v(r,{language:n,ignoreIllegals:!0}):S(r);e.innerHTML=i.value,e.dataset.highlighted="yes";var s=i.language;let l=n&&o[n]||s;e.classList.add("hljs"),e.classList.add(`language-${l}`),e.result={language:i.language,re:i.relevance,relevance:i.relevance},i.secondBest&&(e.secondBest={language:i.secondBest.language,relevance:i.secondBest.relevance}),B("after:highlightElement",{el:e,result:i,text:r})}let C=!1;function E(){if("loading"===document.readyState){C||window.addEventListener("DOMContentLoaded",function(){E()},!1),C=!0;return}document.querySelectorAll(w.cssSelector).forEach(M)}function A(e){return i[e=(e||"").toLowerCase()]||i[o[e]]}function T(e,{languageName:t}){"string"==typeof e&&(e=[e]),e.forEach(e=>{o[e.toLowerCase()]=t})}function R(e){let t=A(e);return t&&!t.disableAutodetect}function B(e,t){s.forEach(function(n){n[e]&&n[e](t)})}for(let t in Object.assign(e,{highlight:v,highlightAuto:S,highlightAll:E,highlightElement:M,highlightBlock:function(e){return P("10.7.0","highlightBlock will be removed entirely in v12.0"),P("10.7.0","Please use highlightElement now."),M(e)},configure:function(e){w=r(w,e)},initHighlighting:()=>{E(),P("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")},initHighlightingOnLoad:function(){E(),P("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")},registerLanguage:function(t,n){let r=null;try{r=n(e)}catch(e){if(H("Language definition for '{}' could not be registered.".replace("{}",t)),l)H(e);else throw e;r=y}r.name||(r.name=t),i[t]=r,r.rawDefinition=n.bind(null,e),r.aliases&&T(r.aliases,{languageName:t})},unregisterLanguage:function(e){for(let t of(delete i[e],Object.keys(o)))o[t]===e&&delete o[t]},listLanguages:function(){return Object.keys(i)},getLanguage:A,registerAliases:T,autoDetection:R,inherit:r,addPlugin:function(e){var t;(t=e)["before:highlightBlock"]&&!t["before:highlightElement"]&&(t["before:highlightElement"]=e=>{t["before:highlightBlock"](Object.assign({block:e.el},e))}),t["after:highlightBlock"]&&!t["after:highlightElement"]&&(t["after:highlightElement"]=e=>{t["after:highlightBlock"](Object.assign({block:e.el},e))}),s.push(e)},removePlugin:function(e){let t=s.indexOf(e);-1!==t&&s.splice(t,1)}}),e.debugMode=function(){l=!1},e.safeMode=function(){l=!0},e.versionString="11.11.1",e.regex={concat:f,lookahead:d,either:g,optional:p,anyNumberOfTimes:u},O)"object"==typeof O[t]&&function e(t){return t instanceof Map?t.clear=t.delete=t.set=function(){throw Error("map is read-only")}:t instanceof Set&&(t.add=t.clear=t.delete=function(){throw Error("set is read-only")}),Object.freeze(t),Object.getOwnPropertyNames(t).forEach(n=>{let r=t[n],i=typeof r;"object"!==i&&"function"!==i||Object.isFrozen(r)||e(r)}),t}(O[t]);return Object.assign(e,O),e},q=V({});q.newInstance=()=>V({}),e.exports=q,q.HighlightJS=q,q.default=q},9727:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("bold",[["path",{d:"M6 12h9a4 4 0 0 1 0 8H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h7a4 4 0 0 1 0 8",key:"mg9rjx"}]])},10034:(e,t,n)=>{"use strict";n.d(t,{$:()=>o});var r=n(8772);let i={};function o(e){let t=r.newInstance();return e&&l(e),{highlight:n,highlightAuto:function(e,r){let s,l=(r||i).subset||o(),a=-1,h=0;for(;++a<l.length;){let i=l[a];if(!t.getLanguage(i))continue;let o=n(i,e,r);o.data&&void 0!==o.data.relevance&&o.data.relevance>h&&(h=o.data.relevance,s=o)}return s||{type:"root",children:[],data:{language:void 0,relevance:h}}},listLanguages:o,register:l,registerAlias:function(e,n){if("string"==typeof e)t.registerAliases("string"==typeof n?n:[...n],{languageName:e});else{let n;for(n in e)if(Object.hasOwn(e,n)){let r=e[n];t.registerAliases("string"==typeof r?r:[...r],{languageName:n})}}},registered:function(e){return!!t.getLanguage(e)}};function n(e,n,r){let o=r||i,l="string"==typeof o.prefix?o.prefix:"hljs-";if(!t.getLanguage(e))throw Error("Unknown language: `"+e+"` is not registered");t.configure({__emitter:s,classPrefix:l});let a=t.highlight(n,{ignoreIllegals:!0,language:e});if(a.errorRaised)throw Error("Could not highlight with `Highlight.js`",{cause:a.errorRaised});let h=a._emitter.root,c=h.data;return c.language=a.language,c.relevance=a.relevance,h}function o(){return t.listLanguages()}function l(e,n){if("string"==typeof e)t.registerLanguage(e,n);else{let n;for(n in e)Object.hasOwn(e,n)&&t.registerLanguage(n,e[n])}}}class s{constructor(e){this.options=e,this.root={type:"root",children:[],data:{language:void 0,relevance:0}},this.stack=[this.root]}addText(e){if(""===e)return;let t=this.stack[this.stack.length-1],n=t.children[t.children.length-1];n&&"text"===n.type?n.value+=e:t.children.push({type:"text",value:e})}startScope(e){this.openNode(String(e))}endScope(){this.closeNode()}__addSublanguage(e,t){let n=this.stack[this.stack.length-1],r=e.root.children;t?n.children.push({type:"element",tagName:"span",properties:{className:[t]},children:r}):n.children.push(...r)}openNode(e){let t=this,n=e.split(".").map(function(e,n){return n?e+"_".repeat(n):t.options.classPrefix+e}),r=this.stack[this.stack.length-1],i={type:"element",tagName:"span",properties:{className:n},children:[]};r.children.push(i),this.stack.push(i)}closeNode(){this.stack.pop()}finalize(){}toHTML(){return""}}},10156:(e,t,n)=>{"use strict";function r(e){this.content=e}n.d(t,{S4:()=>D,ZF:()=>G,FK:()=>i,CU:()=>a,sX:()=>j,bP:()=>S,u$:()=>v,vI:()=>h,Sj:()=>B,Ji:()=>c}),r.prototype={constructor:r,find:function(e){for(var t=0;t<this.content.length;t+=2)if(this.content[t]===e)return t;return -1},get:function(e){var t=this.find(e);return -1==t?void 0:this.content[t+1]},update:function(e,t,n){var i=n&&n!=e?this.remove(n):this,o=i.find(e),s=i.content.slice();return -1==o?s.push(n||e,t):(s[o+1]=t,n&&(s[o]=n)),new r(s)},remove:function(e){var t=this.find(e);if(-1==t)return this;var n=this.content.slice();return n.splice(t,2),new r(n)},addToStart:function(e,t){return new r([e,t].concat(this.remove(e).content))},addToEnd:function(e,t){var n=this.remove(e).content.slice();return n.push(e,t),new r(n)},addBefore:function(e,t,n){var i=this.remove(t),o=i.content.slice(),s=i.find(e);return o.splice(-1==s?o.length:s,0,t,n),new r(o)},forEach:function(e){for(var t=0;t<this.content.length;t+=2)e(this.content[t],this.content[t+1])},prepend:function(e){return(e=r.from(e)).size?new r(e.content.concat(this.subtract(e).content)):this},append:function(e){return(e=r.from(e)).size?new r(this.subtract(e).content.concat(e.content)):this},subtract:function(e){var t=this;e=r.from(e);for(var n=0;n<e.content.length;n+=2)t=t.remove(e.content[n]);return t},toObject:function(){var e={};return this.forEach(function(t,n){e[t]=n}),e},get size(){return this.content.length>>1}},r.from=function(e){if(e instanceof r)return e;var t=[];if(e)for(var n in e)t.push(n,e[n]);return new r(t)};class i{constructor(e,t){if(this.content=e,this.size=t||0,null==t)for(let t=0;t<e.length;t++)this.size+=e[t].nodeSize}nodesBetween(e,t,n,r=0,i){for(let o=0,s=0;s<t;o++){let l=this.content[o],a=s+l.nodeSize;if(a>e&&!1!==n(l,r+s,i||null,o)&&l.content.size){let i=s+1;l.nodesBetween(Math.max(0,e-i),Math.min(l.content.size,t-i),n,r+i)}s=a}}descendants(e){this.nodesBetween(0,this.size,e)}textBetween(e,t,n,r){let i="",o=!0;return this.nodesBetween(e,t,(s,l)=>{let a=s.isText?s.text.slice(Math.max(e,l)-l,t-l):s.isLeaf?r?"function"==typeof r?r(s):r:s.type.spec.leafText?s.type.spec.leafText(s):"":"";s.isBlock&&(s.isLeaf&&a||s.isTextblock)&&n&&(o?o=!1:i+=n),i+=a},0),i}append(e){if(!e.size)return this;if(!this.size)return e;let t=this.lastChild,n=e.firstChild,r=this.content.slice(),o=0;for(t.isText&&t.sameMarkup(n)&&(r[r.length-1]=t.withText(t.text+n.text),o=1);o<e.content.length;o++)r.push(e.content[o]);return new i(r,this.size+e.size)}cut(e,t=this.size){if(0==e&&t==this.size)return this;let n=[],r=0;if(t>e)for(let i=0,o=0;o<t;i++){let s=this.content[i],l=o+s.nodeSize;l>e&&((o<e||l>t)&&(s=s.isText?s.cut(Math.max(0,e-o),Math.min(s.text.length,t-o)):s.cut(Math.max(0,e-o-1),Math.min(s.content.size,t-o-1))),n.push(s),r+=s.nodeSize),o=l}return new i(n,r)}cutByIndex(e,t){return e==t?i.empty:0==e&&t==this.content.length?this:new i(this.content.slice(e,t))}replaceChild(e,t){let n=this.content[e];if(n==t)return this;let r=this.content.slice(),o=this.size+t.nodeSize-n.nodeSize;return r[e]=t,new i(r,o)}addToStart(e){return new i([e].concat(this.content),this.size+e.nodeSize)}addToEnd(e){return new i(this.content.concat(e),this.size+e.nodeSize)}eq(e){if(this.content.length!=e.content.length)return!1;for(let t=0;t<this.content.length;t++)if(!this.content[t].eq(e.content[t]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(e){let t=this.content[e];if(!t)throw RangeError("Index "+e+" out of range for "+this);return t}maybeChild(e){return this.content[e]||null}forEach(e){for(let t=0,n=0;t<this.content.length;t++){let r=this.content[t];e(r,n,t),n+=r.nodeSize}}findDiffStart(e,t=0){return function e(t,n,r){for(let i=0;;i++){if(i==t.childCount||i==n.childCount)return t.childCount==n.childCount?null:r;let o=t.child(i),s=n.child(i);if(o==s){r+=o.nodeSize;continue}if(!o.sameMarkup(s))return r;if(o.isText&&o.text!=s.text){for(let e=0;o.text[e]==s.text[e];e++)r++;return r}if(o.content.size||s.content.size){let t=e(o.content,s.content,r+1);if(null!=t)return t}r+=o.nodeSize}}(this,e,t)}findDiffEnd(e,t=this.size,n=e.size){return function e(t,n,r,i){for(let o=t.childCount,s=n.childCount;;){if(0==o||0==s)return o==s?null:{a:r,b:i};let l=t.child(--o),a=n.child(--s),h=l.nodeSize;if(l==a){r-=h,i-=h;continue}if(!l.sameMarkup(a))return{a:r,b:i};if(l.isText&&l.text!=a.text){let e=0,t=Math.min(l.text.length,a.text.length);for(;e<t&&l.text[l.text.length-e-1]==a.text[a.text.length-e-1];)e++,r--,i--;return{a:r,b:i}}if(l.content.size||a.content.size){let t=e(l.content,a.content,r-1,i-1);if(t)return t}r-=h,i-=h}}(this,e,t,n)}findIndex(e,t=-1){if(0==e)return s(0,e);if(e==this.size)return s(this.content.length,e);if(e>this.size||e<0)throw RangeError(`Position ${e} outside of fragment (${this})`);for(let n=0,r=0;;n++){let i=r+this.child(n).nodeSize;if(i>=e){if(i==e||t>0)return s(n+1,i);return s(n,r)}r=i}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(e=>e.toJSON()):null}static fromJSON(e,t){if(!t)return i.empty;if(!Array.isArray(t))throw RangeError("Invalid input for Fragment.fromJSON");return new i(t.map(e.nodeFromJSON))}static fromArray(e){if(!e.length)return i.empty;let t,n=0;for(let r=0;r<e.length;r++){let i=e[r];n+=i.nodeSize,r&&i.isText&&e[r-1].sameMarkup(i)?(t||(t=e.slice(0,r)),t[t.length-1]=i.withText(t[t.length-1].text+i.text)):t&&t.push(i)}return new i(t||e,n)}static from(e){if(!e)return i.empty;if(e instanceof i)return e;if(Array.isArray(e))return this.fromArray(e);if(e.attrs)return new i([e],e.nodeSize);throw RangeError("Can not convert "+e+" to a Fragment"+(e.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}i.empty=new i([],0);let o={index:0,offset:0};function s(e,t){return o.index=e,o.offset=t,o}function l(e,t){if(e===t)return!0;if(!(e&&"object"==typeof e)||!(t&&"object"==typeof t))return!1;let n=Array.isArray(e);if(Array.isArray(t)!=n)return!1;if(n){if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!l(e[n],t[n]))return!1}else{for(let n in e)if(!(n in t)||!l(e[n],t[n]))return!1;for(let n in t)if(!(n in e))return!1}return!0}class a{constructor(e,t){this.type=e,this.attrs=t}addToSet(e){let t,n=!1;for(let r=0;r<e.length;r++){let i=e[r];if(this.eq(i))return e;if(this.type.excludes(i.type))t||(t=e.slice(0,r));else{if(i.type.excludes(this.type))return e;!n&&i.type.rank>this.type.rank&&(t||(t=e.slice(0,r)),t.push(this),n=!0),t&&t.push(i)}}return t||(t=e.slice()),n||t.push(this),t}removeFromSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return e.slice(0,t).concat(e.slice(t+1));return e}isInSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return!0;return!1}eq(e){return this==e||this.type==e.type&&l(this.attrs,e.attrs)}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return e}static fromJSON(e,t){if(!t)throw RangeError("Invalid input for Mark.fromJSON");let n=e.marks[t.type];if(!n)throw RangeError(`There is no mark type ${t.type} in this schema`);let r=n.create(t.attrs);return n.checkAttrs(r.attrs),r}static sameSet(e,t){if(e==t)return!0;if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!e[n].eq(t[n]))return!1;return!0}static setFrom(e){if(!e||Array.isArray(e)&&0==e.length)return a.none;if(e instanceof a)return[e];let t=e.slice();return t.sort((e,t)=>e.type.rank-t.type.rank),t}}a.none=[];class h extends Error{}class c{constructor(e,t,n){this.content=e,this.openStart=t,this.openEnd=n}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(e,t){let n=function e(t,n,r,i){let{index:o,offset:s}=t.findIndex(n),l=t.maybeChild(o);if(s==n||l.isText)return i&&!i.canReplace(o,o,r)?null:t.cut(0,n).append(r).append(t.cut(n));let a=e(l.content,n-s-1,r);return a&&t.replaceChild(o,l.copy(a))}(this.content,e+this.openStart,t);return n&&new c(n,this.openStart,this.openEnd)}removeBetween(e,t){return new c(function e(t,n,r){let{index:i,offset:o}=t.findIndex(n),s=t.maybeChild(i),{index:l,offset:a}=t.findIndex(r);if(o==n||s.isText){if(a!=r&&!t.child(l).isText)throw RangeError("Removing non-flat range");return t.cut(0,n).append(t.cut(r))}if(i!=l)throw RangeError("Removing non-flat range");return t.replaceChild(i,s.copy(e(s.content,n-o-1,r-o-1)))}(this.content,e+this.openStart,t+this.openStart),this.openStart,this.openEnd)}eq(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}static fromJSON(e,t){if(!t)return c.empty;let n=t.openStart||0,r=t.openEnd||0;if("number"!=typeof n||"number"!=typeof r)throw RangeError("Invalid input for Slice.fromJSON");return new c(i.fromJSON(e,t.content),n,r)}static maxOpen(e,t=!0){let n=0,r=0;for(let r=e.firstChild;r&&!r.isLeaf&&(t||!r.type.spec.isolating);r=r.firstChild)n++;for(let n=e.lastChild;n&&!n.isLeaf&&(t||!n.type.spec.isolating);n=n.lastChild)r++;return new c(e,n,r)}}function d(e,t){if(!t.type.compatibleContent(e.type))throw new h("Cannot join "+t.type.name+" onto "+e.type.name)}function u(e,t,n){let r=e.node(n);return d(r,t.node(n)),r}function p(e,t){let n=t.length-1;n>=0&&e.isText&&e.sameMarkup(t[n])?t[n]=e.withText(t[n].text+e.text):t.push(e)}function f(e,t,n,r){let i=(t||e).node(n),o=0,s=t?t.index(n):i.childCount;e&&(o=e.index(n),e.depth>n?o++:e.textOffset&&(p(e.nodeAfter,r),o++));for(let e=o;e<s;e++)p(i.child(e),r);t&&t.depth==n&&t.textOffset&&p(t.nodeBefore,r)}function g(e,t){return e.type.checkContent(t),e.copy(t)}function m(e,t,n){let r=[];return f(null,e,n,r),e.depth>n&&p(g(u(e,t,n+1),m(e,t,n+1)),r),f(t,null,n,r),new i(r)}c.empty=new c(i.empty,0,0);class y{constructor(e,t,n){this.pos=e,this.path=t,this.parentOffset=n,this.depth=t.length/3-1}resolveDepth(e){return null==e?this.depth:e<0?this.depth+e:e}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(e){return this.path[3*this.resolveDepth(e)]}index(e){return this.path[3*this.resolveDepth(e)+1]}indexAfter(e){return e=this.resolveDepth(e),this.index(e)+(e!=this.depth||this.textOffset?1:0)}start(e){return 0==(e=this.resolveDepth(e))?0:this.path[3*e-1]+1}end(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}before(e){if(!(e=this.resolveDepth(e)))throw RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]}after(e){if(!(e=this.resolveDepth(e)))throw RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]+this.path[3*e].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let e=this.parent,t=this.index(this.depth);if(t==e.childCount)return null;let n=this.pos-this.path[this.path.length-1],r=e.child(t);return n?e.child(t).cut(n):r}get nodeBefore(){let e=this.index(this.depth),t=this.pos-this.path[this.path.length-1];return t?this.parent.child(e).cut(0,t):0==e?null:this.parent.child(e-1)}posAtIndex(e,t){t=this.resolveDepth(t);let n=this.path[3*t],r=0==t?0:this.path[3*t-1]+1;for(let t=0;t<e;t++)r+=n.child(t).nodeSize;return r}marks(){let e=this.parent,t=this.index();if(0==e.content.size)return a.none;if(this.textOffset)return e.child(t).marks;let n=e.maybeChild(t-1),r=e.maybeChild(t);if(!n){let e=n;n=r,r=e}let i=n.marks;for(var o=0;o<i.length;o++)!1!==i[o].type.spec.inclusive||r&&i[o].isInSet(r.marks)||(i=i[o--].removeFromSet(i));return i}marksAcross(e){let t=this.parent.maybeChild(this.index());if(!t||!t.isInline)return null;let n=t.marks,r=e.parent.maybeChild(e.index());for(var i=0;i<n.length;i++)!1!==n[i].type.spec.inclusive||r&&n[i].isInSet(r.marks)||(n=n[i--].removeFromSet(n));return n}sharedDepth(e){for(let t=this.depth;t>0;t--)if(this.start(t)<=e&&this.end(t)>=e)return t;return 0}blockRange(e=this,t){if(e.pos<this.pos)return e.blockRange(this);for(let n=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);n>=0;n--)if(e.pos<=this.end(n)&&(!t||t(this.node(n))))return new v(this,e,n);return null}sameParent(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}max(e){return e.pos>this.pos?e:this}min(e){return e.pos<this.pos?e:this}toString(){let e="";for(let t=1;t<=this.depth;t++)e+=(e?"/":"")+this.node(t).type.name+"_"+this.index(t-1);return e+":"+this.parentOffset}static resolve(e,t){if(!(t>=0&&t<=e.content.size))throw RangeError("Position "+t+" out of range");let n=[],r=0,i=t;for(let t=e;;){let{index:e,offset:o}=t.content.findIndex(i),s=i-o;if(n.push(t,e,r+o),!s||(t=t.child(e)).isText)break;i=s-1,r+=o+1}return new y(t,n,i)}static resolveCached(e,t){let n=k.get(e);if(n)for(let e=0;e<n.elts.length;e++){let r=n.elts[e];if(r.pos==t)return r}else k.set(e,n=new b);let r=n.elts[n.i]=y.resolve(e,t);return n.i=(n.i+1)%w,r}}class b{constructor(){this.elts=[],this.i=0}}let w=12,k=new WeakMap;class v{constructor(e,t,n){this.$from=e,this.$to=t,this.depth=n}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}let x=Object.create(null);class S{constructor(e,t,n,r=a.none){this.type=e,this.attrs=t,this.marks=r,this.content=n||i.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(e){return this.content.child(e)}maybeChild(e){return this.content.maybeChild(e)}forEach(e){this.content.forEach(e)}nodesBetween(e,t,n,r=0){this.content.nodesBetween(e,t,n,r,this)}descendants(e){this.nodesBetween(0,this.content.size,e)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(e,t,n,r){return this.content.textBetween(e,t,n,r)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}sameMarkup(e){return this.hasMarkup(e.type,e.attrs,e.marks)}hasMarkup(e,t,n){return this.type==e&&l(this.attrs,t||e.defaultAttrs||x)&&a.sameSet(this.marks,n||a.none)}copy(e=null){return e==this.content?this:new S(this.type,this.attrs,e,this.marks)}mark(e){return e==this.marks?this:new S(this.type,this.attrs,this.content,e)}cut(e,t=this.content.size){return 0==e&&t==this.content.size?this:this.copy(this.content.cut(e,t))}slice(e,t=this.content.size,n=!1){if(e==t)return c.empty;let r=this.resolve(e),i=this.resolve(t),o=n?0:r.sharedDepth(t),s=r.start(o);return new c(r.node(o).content.cut(r.pos-s,i.pos-s),r.depth-o,i.depth-o)}replace(e,t,n){var r=this.resolve(e),o=this.resolve(t);if(n.openStart>r.depth)throw new h("Inserted content deeper than insertion position");if(r.depth-n.openStart!=o.depth-n.openEnd)throw new h("Inconsistent open depths");return function e(t,n,r,o){let s=t.index(o),l=t.node(o);if(s==n.index(o)&&o<t.depth-r.openStart){let i=e(t,n,r,o+1);return l.copy(l.content.replaceChild(s,i))}if(!r.content.size)return g(l,m(t,n,o));if(r.openStart||r.openEnd||t.depth!=o||n.depth!=o){let{start:e,end:s}=function(e,t){let n=t.depth-e.openStart,r=t.node(n).copy(e.content);for(let e=n-1;e>=0;e--)r=t.node(e).copy(i.from(r));return{start:r.resolveNoCache(e.openStart+n),end:r.resolveNoCache(r.content.size-e.openEnd-n)}}(r,t);return g(l,function e(t,n,r,o,s){let l=t.depth>s&&u(t,n,s+1),a=o.depth>s&&u(r,o,s+1),h=[];return f(null,t,s,h),l&&a&&n.index(s)==r.index(s)?(d(l,a),p(g(l,e(t,n,r,o,s+1)),h)):(l&&p(g(l,m(t,n,s+1)),h),f(n,r,s,h),a&&p(g(a,m(r,o,s+1)),h)),f(o,null,s,h),new i(h)}(t,e,s,n,o))}{let e=t.parent,i=e.content;return g(e,i.cut(0,t.parentOffset).append(r.content).append(i.cut(n.parentOffset)))}}(r,o,n,0)}nodeAt(e){for(let t=this;;){let{index:n,offset:r}=t.content.findIndex(e);if(!(t=t.maybeChild(n)))return null;if(r==e||t.isText)return t;e-=r+1}}childAfter(e){let{index:t,offset:n}=this.content.findIndex(e);return{node:this.content.maybeChild(t),index:t,offset:n}}childBefore(e){if(0==e)return{node:null,index:0,offset:0};let{index:t,offset:n}=this.content.findIndex(e);if(n<e)return{node:this.content.child(t),index:t,offset:n};let r=this.content.child(t-1);return{node:r,index:t-1,offset:n-r.nodeSize}}resolve(e){return y.resolveCached(this,e)}resolveNoCache(e){return y.resolve(this,e)}rangeHasMark(e,t,n){let r=!1;return t>e&&this.nodesBetween(e,t,e=>(n.isInSet(e.marks)&&(r=!0),!r)),r}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),C(this.marks,e)}contentMatchAt(e){let t=this.type.contentMatch.matchFragment(this.content,0,e);if(!t)throw Error("Called contentMatchAt on a node with invalid content");return t}canReplace(e,t,n=i.empty,r=0,o=n.childCount){let s=this.contentMatchAt(e).matchFragment(n,r,o),l=s&&s.matchFragment(this.content,t);if(!l||!l.validEnd)return!1;for(let e=r;e<o;e++)if(!this.type.allowsMarks(n.child(e).marks))return!1;return!0}canReplaceWith(e,t,n,r){if(r&&!this.type.allowsMarks(r))return!1;let i=this.contentMatchAt(e).matchType(n),o=i&&i.matchFragment(this.content,t);return!!o&&o.validEnd}canAppend(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let e=a.none;for(let t=0;t<this.marks.length;t++){let n=this.marks[t];n.type.checkAttrs(n.attrs),e=n.addToSet(e)}if(!a.sameSet(e,this.marks))throw RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(e=>e.type.name)}`);this.content.forEach(e=>e.check())}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map(e=>e.toJSON())),e}static fromJSON(e,t){let n;if(!t)throw RangeError("Invalid input for Node.fromJSON");if(t.marks){if(!Array.isArray(t.marks))throw RangeError("Invalid mark data for Node.fromJSON");n=t.marks.map(e.markFromJSON)}if("text"==t.type){if("string"!=typeof t.text)throw RangeError("Invalid text node in JSON");return e.text(t.text,n)}let r=i.fromJSON(e,t.content),o=e.nodeType(t.type).create(t.attrs,r,n);return o.type.checkAttrs(o.attrs),o}}S.prototype.text=void 0;class M extends S{constructor(e,t,n,r){if(super(e,t,null,r),!n)throw RangeError("Empty text nodes are not allowed");this.text=n}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):C(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(e,t){return this.text.slice(e,t)}get nodeSize(){return this.text.length}mark(e){return e==this.marks?this:new M(this.type,this.attrs,this.text,e)}withText(e){return e==this.text?this:new M(this.type,this.attrs,e,this.marks)}cut(e=0,t=this.text.length){return 0==e&&t==this.text.length?this:this.withText(this.text.slice(e,t))}eq(e){return this.sameMarkup(e)&&this.text==e.text}toJSON(){let e=super.toJSON();return e.text=this.text,e}}function C(e,t){for(let n=e.length-1;n>=0;n--)t=e[n].type.name+"("+t+")";return t}class E{constructor(e){this.validEnd=e,this.next=[],this.wrapCache=[]}static parse(e,t){var n;let r,i=new A(e,t);if(null==i.next)return E.empty;let o=function e(t){let n=[];do n.push(function(t){let n=[];do n.push(function(t){let n=function(t){if(t.eat("(")){let n=e(t);return t.eat(")")||t.err("Missing closing paren"),n}if(/\W/.test(t.next))t.err("Unexpected token '"+t.next+"'");else{let e=(function(e,t){let n=e.nodeTypes,r=n[t];if(r)return[r];let i=[];for(let e in n){let r=n[e];r.isInGroup(t)&&i.push(r)}return 0==i.length&&e.err("No node type or group '"+t+"' found"),i})(t,t.next).map(e=>(null==t.inline?t.inline=e.isInline:t.inline!=e.isInline&&t.err("Mixing inline and block content"),{type:"name",value:e}));return t.pos++,1==e.length?e[0]:{type:"choice",exprs:e}}}(t);for(;;)if(t.eat("+"))n={type:"plus",expr:n};else if(t.eat("*"))n={type:"star",expr:n};else if(t.eat("?"))n={type:"opt",expr:n};else if(t.eat("{"))n=function(e,t){let n=T(e),r=n;return e.eat(",")&&(r="}"!=e.next?T(e):-1),e.eat("}")||e.err("Unclosed braced range"),{type:"range",min:n,max:r,expr:t}}(t,n);else break;return n}(t));while(t.next&&")"!=t.next&&"|"!=t.next);return 1==n.length?n[0]:{type:"seq",exprs:n}}(t));while(t.eat("|"));return 1==n.length?n[0]:{type:"choice",exprs:n}}(i);i.next&&i.err("Unexpected trailing text");let s=(n=function(e){let t=[[]];return i(function e(t,o){if("choice"==t.type)return t.exprs.reduce((t,n)=>t.concat(e(n,o)),[]);if("seq"==t.type)for(let r=0;;r++){let s=e(t.exprs[r],o);if(r==t.exprs.length-1)return s;i(s,o=n())}else if("star"==t.type){let s=n();return r(o,s),i(e(t.expr,s),s),[r(s)]}else if("plus"==t.type){let s=n();return i(e(t.expr,o),s),i(e(t.expr,s),s),[r(s)]}else if("opt"==t.type)return[r(o)].concat(e(t.expr,o));else if("range"==t.type){let s=o;for(let r=0;r<t.min;r++){let r=n();i(e(t.expr,s),r),s=r}if(-1==t.max)i(e(t.expr,s),s);else for(let o=t.min;o<t.max;o++){let o=n();r(s,o),i(e(t.expr,s),o),s=o}return[r(s)]}else if("name"==t.type)return[r(o,void 0,t.value)];else throw Error("Unknown expr type")}(e,0),n()),t;function n(){return t.push([])-1}function r(e,n,r){let i={term:r,to:n};return t[e].push(i),i}function i(e,t){e.forEach(e=>e.to=t)}}(o),r=Object.create(null),function e(t){let i=[];t.forEach(e=>{n[e].forEach(({term:e,to:t})=>{let r;if(e){for(let t=0;t<i.length;t++)i[t][0]==e&&(r=i[t][1]);R(n,t).forEach(t=>{r||i.push([e,r=[]]),-1==r.indexOf(t)&&r.push(t)})}})});let o=r[t.join(",")]=new E(t.indexOf(n.length-1)>-1);for(let t=0;t<i.length;t++){let n=i[t][1].sort(O);o.next.push({type:i[t][0],next:r[n.join(",")]||e(n)})}return o}(R(n,0)));return function(e,t){for(let n=0,r=[e];n<r.length;n++){let e=r[n],i=!e.validEnd,o=[];for(let t=0;t<e.next.length;t++){let{type:n,next:s}=e.next[t];o.push(n.name),i&&!(n.isText||n.hasRequiredAttrs())&&(i=!1),-1==r.indexOf(s)&&r.push(s)}i&&t.err("Only non-generatable nodes ("+o.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}(s,i),s}matchType(e){for(let t=0;t<this.next.length;t++)if(this.next[t].type==e)return this.next[t].next;return null}matchFragment(e,t=0,n=e.childCount){let r=this;for(let i=t;r&&i<n;i++)r=r.matchType(e.child(i).type);return r}get inlineContent(){return 0!=this.next.length&&this.next[0].type.isInline}get defaultType(){for(let e=0;e<this.next.length;e++){let{type:t}=this.next[e];if(!(t.isText||t.hasRequiredAttrs()))return t}return null}compatible(e){for(let t=0;t<this.next.length;t++)for(let n=0;n<e.next.length;n++)if(this.next[t].type==e.next[n].type)return!0;return!1}fillBefore(e,t=!1,n=0){let r=[this];return function o(s,l){let a=s.matchFragment(e,n);if(a&&(!t||a.validEnd))return i.from(l.map(e=>e.createAndFill()));for(let e=0;e<s.next.length;e++){let{type:t,next:n}=s.next[e];if(!(t.isText||t.hasRequiredAttrs())&&-1==r.indexOf(n)){r.push(n);let e=o(n,l.concat(t));if(e)return e}}return null}(this,[])}findWrapping(e){for(let t=0;t<this.wrapCache.length;t+=2)if(this.wrapCache[t]==e)return this.wrapCache[t+1];let t=this.computeWrapping(e);return this.wrapCache.push(e,t),t}computeWrapping(e){let t=Object.create(null),n=[{match:this,type:null,via:null}];for(;n.length;){let r=n.shift(),i=r.match;if(i.matchType(e)){let e=[];for(let t=r;t.type;t=t.via)e.push(t.type);return e.reverse()}for(let e=0;e<i.next.length;e++){let{type:o,next:s}=i.next[e];o.isLeaf||o.hasRequiredAttrs()||o.name in t||r.type&&!s.validEnd||(n.push({match:o.contentMatch,type:o,via:r}),t[o.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(e){if(e>=this.next.length)throw RangeError(`There's no ${e}th edge in this content match`);return this.next[e]}toString(){let e=[];return!function t(n){e.push(n);for(let r=0;r<n.next.length;r++)-1==e.indexOf(n.next[r].next)&&t(n.next[r].next)}(this),e.map((t,n)=>{let r=n+(t.validEnd?"*":" ")+" ";for(let n=0;n<t.next.length;n++)r+=(n?", ":"")+t.next[n].type.name+"->"+e.indexOf(t.next[n].next);return r}).join("\n")}}E.empty=new E(!0);class A{constructor(e,t){this.string=e,this.nodeTypes=t,this.inline=null,this.pos=0,this.tokens=e.split(/\s*(?=\b|\W|$)/),""==this.tokens[this.tokens.length-1]&&this.tokens.pop(),""==this.tokens[0]&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(e){return this.next==e&&(this.pos++||!0)}err(e){throw SyntaxError(e+" (in content expression '"+this.string+"')")}}function T(e){/\D/.test(e.next)&&e.err("Expected number, got '"+e.next+"'");let t=Number(e.next);return e.pos++,t}function O(e,t){return t-e}function R(e,t){let n=[];return function t(r){let i=e[r];if(1==i.length&&!i[0].term)return t(i[0].to);n.push(r);for(let e=0;e<i.length;e++){let{term:r,to:o}=i[e];r||-1!=n.indexOf(o)||t(o)}}(t),n.sort(O)}function N(e){let t=Object.create(null);for(let n in e){let r=e[n];if(!r.hasDefault)return null;t[n]=r.default}return t}function I(e,t){let n=Object.create(null);for(let r in e){let i=t&&t[r];if(void 0===i){let t=e[r];if(t.hasDefault)i=t.default;else throw RangeError("No value supplied for attribute "+r)}n[r]=i}return n}function L(e,t,n,r){for(let r in t)if(!(r in e))throw RangeError(`Unsupported attribute ${r} for ${n} of type ${r}`);for(let n in e){let r=e[n];r.validate&&r.validate(t[n])}}function _(e,t){let n=Object.create(null);if(t)for(let r in t)n[r]=new $(e,r,t[r]);return n}class z{constructor(e,t,n){this.name=e,this.schema=t,this.spec=n,this.markSet=null,this.groups=n.group?n.group.split(" "):[],this.attrs=_(e,n.attrs),this.defaultAttrs=N(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(n.inline||"text"==e),this.isText="text"==e}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==E.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(e){return this.groups.indexOf(e)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}compatibleContent(e){return this==e||this.contentMatch.compatible(e.contentMatch)}computeAttrs(e){return!e&&this.defaultAttrs?this.defaultAttrs:I(this.attrs,e)}create(e=null,t,n){if(this.isText)throw Error("NodeType.create can't construct text nodes");return new S(this,this.computeAttrs(e),i.from(t),a.setFrom(n))}createChecked(e=null,t,n){return t=i.from(t),this.checkContent(t),new S(this,this.computeAttrs(e),t,a.setFrom(n))}createAndFill(e=null,t,n){if(e=this.computeAttrs(e),(t=i.from(t)).size){let e=this.contentMatch.fillBefore(t);if(!e)return null;t=e.append(t)}let r=this.contentMatch.matchFragment(t),o=r&&r.fillBefore(i.empty,!0);return o?new S(this,e,t.append(o),a.setFrom(n)):null}validContent(e){let t=this.contentMatch.matchFragment(e);if(!t||!t.validEnd)return!1;for(let t=0;t<e.childCount;t++)if(!this.allowsMarks(e.child(t).marks))return!1;return!0}checkContent(e){if(!this.validContent(e))throw RangeError(`Invalid content for node ${this.name}: ${e.toString().slice(0,50)}`)}checkAttrs(e){L(this.attrs,e,"node",this.name)}allowsMarkType(e){return null==this.markSet||this.markSet.indexOf(e)>-1}allowsMarks(e){if(null==this.markSet)return!0;for(let t=0;t<e.length;t++)if(!this.allowsMarkType(e[t].type))return!1;return!0}allowedMarks(e){let t;if(null==this.markSet)return e;for(let n=0;n<e.length;n++)this.allowsMarkType(e[n].type)?t&&t.push(e[n]):t||(t=e.slice(0,n));return t?t.length?t:a.none:e}static compile(e,t){let n=Object.create(null);e.forEach((e,r)=>n[e]=new z(e,t,r));let r=t.spec.topNode||"doc";if(!n[r])throw RangeError("Schema is missing its top node type ('"+r+"')");if(!n.text)throw RangeError("Every schema needs a 'text' type");for(let e in n.text.attrs)throw RangeError("The text node type should not have attributes");return n}}class ${constructor(e,t,n){this.hasDefault=Object.prototype.hasOwnProperty.call(n,"default"),this.default=n.default,this.validate="string"==typeof n.validate?function(e,t,n){let r=n.split("|");return n=>{let i=null===n?"null":typeof n;if(0>r.indexOf(i))throw RangeError(`Expected value of type ${r} for attribute ${t} on type ${e}, got ${i}`)}}(e,t,n.validate):n.validate}get isRequired(){return!this.hasDefault}}class j{constructor(e,t,n,r){this.name=e,this.rank=t,this.schema=n,this.spec=r,this.attrs=_(e,r.attrs),this.excluded=null;let i=N(this.attrs);this.instance=i?new a(this,i):null}create(e=null){return!e&&this.instance?this.instance:new a(this,I(this.attrs,e))}static compile(e,t){let n=Object.create(null),r=0;return e.forEach((e,i)=>n[e]=new j(e,r++,t,i)),n}removeFromSet(e){for(var t=0;t<e.length;t++)e[t].type==this&&(e=e.slice(0,t).concat(e.slice(t+1)),t--);return e}isInSet(e){for(let t=0;t<e.length;t++)if(e[t].type==this)return e[t]}checkAttrs(e){L(this.attrs,e,"mark",this.name)}excludes(e){return this.excluded.indexOf(e)>-1}}class B{constructor(e){this.linebreakReplacement=null,this.cached=Object.create(null);let t=this.spec={};for(let n in e)t[n]=e[n];t.nodes=r.from(e.nodes),t.marks=r.from(e.marks||{}),this.nodes=z.compile(this.spec.nodes,this),this.marks=j.compile(this.spec.marks,this);let n=Object.create(null);for(let e in this.nodes){if(e in this.marks)throw RangeError(e+" can not be both a node and a mark");let t=this.nodes[e],r=t.spec.content||"",i=t.spec.marks;if(t.contentMatch=n[r]||(n[r]=E.parse(r,this.nodes)),t.inlineContent=t.contentMatch.inlineContent,t.spec.linebreakReplacement){if(this.linebreakReplacement)throw RangeError("Multiple linebreak nodes defined");if(!t.isInline||!t.isLeaf)throw RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=t}t.markSet="_"==i?null:i?H(this,i.split(" ")):""!=i&&t.inlineContent?null:[]}for(let e in this.marks){let t=this.marks[e],n=t.spec.excludes;t.excluded=null==n?[t]:""==n?[]:H(this,n.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(e,t=null,n,r){if("string"==typeof e)e=this.nodeType(e);else if(e instanceof z){if(e.schema!=this)throw RangeError("Node type from different schema used ("+e.name+")")}else throw RangeError("Invalid node type: "+e);return e.createChecked(t,n,r)}text(e,t){let n=this.nodes.text;return new M(n,n.defaultAttrs,e,a.setFrom(t))}mark(e,t){return"string"==typeof e&&(e=this.marks[e]),e.create(t)}nodeFromJSON(e){return S.fromJSON(this,e)}markFromJSON(e){return a.fromJSON(this,e)}nodeType(e){let t=this.nodes[e];if(!t)throw RangeError("Unknown node type: "+e);return t}}function H(e,t){let n=[];for(let r=0;r<t.length;r++){let i=t[r],o=e.marks[i],s=o;if(o)n.push(o);else for(let t in e.marks){let r=e.marks[t];("_"==i||r.spec.group&&r.spec.group.split(" ").indexOf(i)>-1)&&n.push(s=r)}if(!s)throw SyntaxError("Unknown mark type: '"+t[r]+"'")}return n}class D{constructor(e,t){this.schema=e,this.rules=t,this.tags=[],this.styles=[];let n=this.matchedStyles=[];t.forEach(e=>{if(null!=e.tag)this.tags.push(e);else if(null!=e.style){let t=/[^=]*/.exec(e.style)[0];0>n.indexOf(t)&&n.push(t),this.styles.push(e)}}),this.normalizeLists=!this.tags.some(t=>{if(!/^(ul|ol)\b/.test(t.tag)||!t.node)return!1;let n=e.nodes[t.node];return n.contentMatch.matchType(n)})}parse(e,t={}){let n=new U(this,t,!1);return n.addAll(e,a.none,t.from,t.to),n.finish()}parseSlice(e,t={}){let n=new U(this,t,!0);return n.addAll(e,a.none,t.from,t.to),c.maxOpen(n.finish())}matchTag(e,t,n){for(let o=n?this.tags.indexOf(n)+1:0;o<this.tags.length;o++){var r,i;let n=this.tags[o];if(r=e,i=n.tag,(r.matches||r.msMatchesSelector||r.webkitMatchesSelector||r.mozMatchesSelector).call(r,i)&&(void 0===n.namespace||e.namespaceURI==n.namespace)&&(!n.context||t.matchesContext(n.context))){if(n.getAttrs){let t=n.getAttrs(e);if(!1===t)continue;n.attrs=t||void 0}return n}}}matchStyle(e,t,n,r){for(let i=r?this.styles.indexOf(r)+1:0;i<this.styles.length;i++){let r=this.styles[i],o=r.style;if(0==o.indexOf(e)&&(!r.context||n.matchesContext(r.context))&&(!(o.length>e.length)||61==o.charCodeAt(e.length)&&o.slice(e.length+1)==t)){if(r.getAttrs){let e=r.getAttrs(t);if(!1===e)continue;r.attrs=e||void 0}return r}}}static schemaRules(e){let t=[];function n(e){let n=null==e.priority?50:e.priority,r=0;for(;r<t.length;r++){let e=t[r];if((null==e.priority?50:e.priority)<n)break}t.splice(r,0,e)}for(let t in e.marks){let r=e.marks[t].spec.parseDOM;r&&r.forEach(e=>{n(e=V(e)),e.mark||e.ignore||e.clearMark||(e.mark=t)})}for(let t in e.nodes){let r=e.nodes[t].spec.parseDOM;r&&r.forEach(e=>{n(e=V(e)),e.node||e.ignore||e.mark||(e.node=t)})}return t}static fromSchema(e){return e.cached.domParser||(e.cached.domParser=new D(e,D.schemaRules(e)))}}let P={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},F={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},J={ol:!0,ul:!0};function K(e,t,n){return null!=t?!!t|2*("full"===t):e&&"pre"==e.whitespace?3:-5&n}class W{constructor(e,t,n,r,i,o){this.type=e,this.attrs=t,this.marks=n,this.solid=r,this.options=o,this.content=[],this.activeMarks=a.none,this.match=i||(4&o?null:e.contentMatch)}findWrapping(e){if(!this.match){if(!this.type)return[];let t=this.type.contentMatch.fillBefore(i.from(e));if(t)this.match=this.type.contentMatch.matchFragment(t);else{let t=this.type.contentMatch,n;return(n=t.findWrapping(e.type))?(this.match=t,n):null}}return this.match.findWrapping(e.type)}finish(e){if(!(1&this.options)){let e=this.content[this.content.length-1],t;e&&e.isText&&(t=/[ \t\r\n\u000c]+$/.exec(e.text))&&(e.text.length==t[0].length?this.content.pop():this.content[this.content.length-1]=e.withText(e.text.slice(0,e.text.length-t[0].length)))}let t=i.from(this.content);return!e&&this.match&&(t=t.append(this.match.fillBefore(i.empty,!0))),this.type?this.type.create(this.attrs,t,this.marks):t}inlineContext(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!P.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}}class U{constructor(e,t,n){this.parser=e,this.options=t,this.isOpen=n,this.open=0,this.localPreserveWS=!1;let r=t.topNode,i,o=K(null,t.preserveWhitespace,0)|4*!!n;i=r?new W(r.type,r.attrs,a.none,!0,t.topMatch||r.type.contentMatch,o):n?new W(null,null,a.none,!0,null,o):new W(e.schema.topNodeType,null,a.none,!0,null,o),this.nodes=[i],this.find=t.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(e,t){3==e.nodeType?this.addTextNode(e,t):1==e.nodeType&&this.addElement(e,t)}addTextNode(e,t){let n=e.nodeValue,r=this.top,i=2&r.options?"full":this.localPreserveWS||(1&r.options)>0;if("full"===i||r.inlineContext(e)||/[^ \t\r\n\u000c]/.test(n)){if(i)n="full"!==i?n.replace(/\r?\n|\r/g," "):n.replace(/\r\n?/g,"\n");else if(n=n.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(n)&&this.open==this.nodes.length-1){let t=r.content[r.content.length-1],i=e.previousSibling;(!t||i&&"BR"==i.nodeName||t.isText&&/[ \t\r\n\u000c]$/.test(t.text))&&(n=n.slice(1))}n&&this.insertNode(this.parser.schema.text(n),t,!/\S/.test(n)),this.findInText(e)}else this.findInside(e)}addElement(e,t,n){let r=this.localPreserveWS,i=this.top;("PRE"==e.tagName||/pre/.test(e.style&&e.style.whiteSpace))&&(this.localPreserveWS=!0);let o=e.nodeName.toLowerCase(),s;J.hasOwnProperty(o)&&this.parser.normalizeLists&&function(e){for(let t=e.firstChild,n=null;t;t=t.nextSibling){let e=1==t.nodeType?t.nodeName.toLowerCase():null;e&&J.hasOwnProperty(e)&&n?(n.appendChild(t),t=n):"li"==e?n=t:e&&(n=null)}}(e);let l=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(s=this.parser.matchTag(e,this,n));t:if(l?l.ignore:F.hasOwnProperty(o))this.findInside(e),this.ignoreFallback(e,t);else if(!l||l.skip||l.closeParent){l&&l.closeParent?this.open=Math.max(0,this.open-1):l&&l.skip.nodeType&&(e=l.skip);let n,r=this.needsBlock;if(P.hasOwnProperty(o))i.content.length&&i.content[0].isInline&&this.open&&(this.open--,i=this.top),n=!0,i.type||(this.needsBlock=!0);else if(!e.firstChild){this.leafFallback(e,t);break t}let s=l&&l.skip?t:this.readStyles(e,t);s&&this.addAll(e,s),n&&this.sync(i),this.needsBlock=r}else{let n=this.readStyles(e,t);n&&this.addElementByRule(e,l,n,!1===l.consuming?s:void 0)}this.localPreserveWS=r}leafFallback(e,t){"BR"==e.nodeName&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode("\n"),t)}ignoreFallback(e,t){"BR"!=e.nodeName||this.top.type&&this.top.type.inlineContent||this.findPlace(this.parser.schema.text("-"),t,!0)}readStyles(e,t){let n=e.style;if(n&&n.length)for(let e=0;e<this.parser.matchedStyles.length;e++){let r=this.parser.matchedStyles[e],i=n.getPropertyValue(r);if(i)for(let e;;){let n=this.parser.matchStyle(r,i,this,e);if(!n)break;if(n.ignore)return null;if(t=n.clearMark?t.filter(e=>!n.clearMark(e)):t.concat(this.parser.schema.marks[n.mark].create(n.attrs)),!1===n.consuming)e=n;else break}}return t}addElementByRule(e,t,n,r){let i,o;if(t.node)if((o=this.parser.schema.nodes[t.node]).isLeaf)this.insertNode(o.create(t.attrs),n,"BR"==e.nodeName)||this.leafFallback(e,n);else{let e=this.enter(o,t.attrs||null,n,t.preserveWhitespace);e&&(i=!0,n=e)}else{let e=this.parser.schema.marks[t.mark];n=n.concat(e.create(t.attrs))}let s=this.top;if(o&&o.isLeaf)this.findInside(e);else if(r)this.addElement(e,n,r);else if(t.getContent)this.findInside(e),t.getContent(e,this.parser.schema).forEach(e=>this.insertNode(e,n,!1));else{let r=e;"string"==typeof t.contentElement?r=e.querySelector(t.contentElement):"function"==typeof t.contentElement?r=t.contentElement(e):t.contentElement&&(r=t.contentElement),this.findAround(e,r,!0),this.addAll(r,n),this.findAround(e,r,!1)}i&&this.sync(s)&&this.open--}addAll(e,t,n,r){let i=n||0;for(let o=n?e.childNodes[n]:e.firstChild,s=null==r?null:e.childNodes[r];o!=s;o=o.nextSibling,++i)this.findAtPoint(e,i),this.addDOM(o,t);this.findAtPoint(e,i)}findPlace(e,t,n){let r,i;for(let t=this.open,o=0;t>=0;t--){let s=this.nodes[t],l=s.findWrapping(e);if(l&&(!r||r.length>l.length+o)&&(r=l,i=s,!l.length))break;if(s.solid){if(n)break;o+=2}}if(!r)return null;this.sync(i);for(let e=0;e<r.length;e++)t=this.enterInner(r[e],null,t,!1);return t}insertNode(e,t,n){if(e.isInline&&this.needsBlock&&!this.top.type){let e=this.textblockFromContext();e&&(t=this.enterInner(e,null,t))}let r=this.findPlace(e,t,n);if(r){this.closeExtra();let t=this.top;t.match&&(t.match=t.match.matchType(e.type));let n=a.none;for(let i of r.concat(e.marks))(t.type?t.type.allowsMarkType(i.type):q(i.type,e.type))&&(n=i.addToSet(n));return t.content.push(e.mark(n)),!0}return!1}enter(e,t,n,r){let i=this.findPlace(e.create(t),n,!1);return i&&(i=this.enterInner(e,t,n,!0,r)),i}enterInner(e,t,n,r=!1,i){this.closeExtra();let o=this.top;o.match=o.match&&o.match.matchType(e);let s=K(e,i,o.options);4&o.options&&0==o.content.length&&(s|=4);let l=a.none;return n=n.filter(t=>(o.type?!o.type.allowsMarkType(t.type):!q(t.type,e))||(l=t.addToSet(l),!1)),this.nodes.push(new W(e,t,l,r,null,s)),this.open++,n}closeExtra(e=!1){let t=this.nodes.length-1;if(t>this.open){for(;t>this.open;t--)this.nodes[t-1].content.push(this.nodes[t].finish(e));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!!(this.isOpen||this.options.topOpen))}sync(e){for(let t=this.open;t>=0;t--)if(this.nodes[t]==e)return this.open=t,!0;else this.localPreserveWS&&(this.nodes[t].options|=1);return!1}get currentPos(){this.closeExtra();let e=0;for(let t=this.open;t>=0;t--){let n=this.nodes[t].content;for(let t=n.length-1;t>=0;t--)e+=n[t].nodeSize;t&&e++}return e}findAtPoint(e,t){if(this.find)for(let n=0;n<this.find.length;n++)this.find[n].node==e&&this.find[n].offset==t&&(this.find[n].pos=this.currentPos)}findInside(e){if(this.find)for(let t=0;t<this.find.length;t++)null==this.find[t].pos&&1==e.nodeType&&e.contains(this.find[t].node)&&(this.find[t].pos=this.currentPos)}findAround(e,t,n){if(e!=t&&this.find)for(let r=0;r<this.find.length;r++)null==this.find[r].pos&&1==e.nodeType&&e.contains(this.find[r].node)&&t.compareDocumentPosition(this.find[r].node)&(n?2:4)&&(this.find[r].pos=this.currentPos)}findInText(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].node==e&&(this.find[t].pos=this.currentPos-(e.nodeValue.length-this.find[t].offset))}matchesContext(e){if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);let t=e.split("/"),n=this.options.context,r=!this.isOpen&&(!n||n.parent.type==this.nodes[0].type),i=-(n?n.depth+1:0)+ +!r,o=(e,s)=>{for(;e>=0;e--){let l=t[e];if(""==l){if(e==t.length-1||0==e)continue;for(;s>=i;s--)if(o(e-1,s))return!0;return!1}{let e=s>0||0==s&&r?this.nodes[s].type:n&&s>=i?n.node(s-i).type:null;if(!e||e.name!=l&&!e.isInGroup(l))return!1;s--}}return!0};return o(t.length-1,this.open)}textblockFromContext(){let e=this.options.context;if(e)for(let t=e.depth;t>=0;t--){let n=e.node(t).contentMatchAt(e.indexAfter(t)).defaultType;if(n&&n.isTextblock&&n.defaultAttrs)return n}for(let e in this.parser.schema.nodes){let t=this.parser.schema.nodes[e];if(t.isTextblock&&t.defaultAttrs)return t}}}function V(e){let t={};for(let n in e)t[n]=e[n];return t}function q(e,t){let n=t.schema.nodes;for(let r in n){let i=n[r];if(!i.allowsMarkType(e))continue;let o=[],s=e=>{o.push(e);for(let n=0;n<e.edgeCount;n++){let{type:r,next:i}=e.edge(n);if(r==t||0>o.indexOf(i)&&s(i))return!0}};if(s(i.contentMatch))return!0}}class G{constructor(e,t){this.nodes=e,this.marks=t}serializeFragment(e,t={},n){n||(n=Z(t).createDocumentFragment());let r=n,i=[];return e.forEach(e=>{if(i.length||e.marks.length){let n=0,o=0;for(;n<i.length&&o<e.marks.length;){let t=e.marks[o];if(!this.marks[t.type.name]){o++;continue}if(!t.eq(i[n][0])||!1===t.type.spec.spanning)break;n++,o++}for(;n<i.length;)r=i.pop()[1];for(;o<e.marks.length;){let n=e.marks[o++],s=this.serializeMark(n,e.isInline,t);s&&(i.push([n,r]),r.appendChild(s.dom),r=s.contentDOM||s.dom)}}r.appendChild(this.serializeNodeInner(e,t))}),n}serializeNodeInner(e,t){let{dom:n,contentDOM:r}=Q(Z(t),this.nodes[e.type.name](e),null,e.attrs);if(r){if(e.isLeaf)throw RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(e.content,t,r)}return n}serializeNode(e,t={}){let n=this.serializeNodeInner(e,t);for(let r=e.marks.length-1;r>=0;r--){let i=this.serializeMark(e.marks[r],e.isInline,t);i&&((i.contentDOM||i.dom).appendChild(n),n=i.dom)}return n}serializeMark(e,t,n={}){let r=this.marks[e.type.name];return r&&Q(Z(n),r(e,t),null,e.attrs)}static renderSpec(e,t,n=null,r){return Q(e,t,n,r)}static fromSchema(e){return e.cached.domSerializer||(e.cached.domSerializer=new G(this.nodesFromSchema(e),this.marksFromSchema(e)))}static nodesFromSchema(e){let t=X(e.nodes);return t.text||(t.text=e=>e.text),t}static marksFromSchema(e){return X(e.marks)}}function X(e){let t={};for(let n in e){let r=e[n].spec.toDOM;r&&(t[n]=r)}return t}function Z(e){return e.document||window.document}let Y=new WeakMap;function Q(e,t,n,r){let i,o,s;if("string"==typeof t)return{dom:e.createTextNode(t)};if(null!=t.nodeType)return{dom:t};if(t.dom&&null!=t.dom.nodeType)return t;let l=t[0],a;if("string"!=typeof l)throw RangeError("Invalid array passed to renderSpec");if(r&&(void 0===(o=Y.get(r))&&Y.set(r,(s=null,!function e(t){if(t&&"object"==typeof t)if(Array.isArray(t))if("string"==typeof t[0])s||(s=[]),s.push(t);else for(let n=0;n<t.length;n++)e(t[n]);else for(let n in t)e(t[n])}(r),o=s)),a=o)&&a.indexOf(t)>-1)throw RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let h=l.indexOf(" ");h>0&&(n=l.slice(0,h),l=l.slice(h+1));let c=n?e.createElementNS(n,l):e.createElement(l),d=t[1],u=1;if(d&&"object"==typeof d&&null==d.nodeType&&!Array.isArray(d)){for(let e in u=2,d)if(null!=d[e]){let t=e.indexOf(" ");t>0?c.setAttributeNS(e.slice(0,t),e.slice(t+1),d[e]):c.setAttribute(e,d[e])}}for(let o=u;o<t.length;o++){let s=t[o];if(0===s){if(o<t.length-1||o>u)throw RangeError("Content hole must be the only child of its parent node");return{dom:c,contentDOM:c}}{let{dom:t,contentDOM:o}=Q(e,s,n,r);if(c.appendChild(t),o){if(i)throw RangeError("Multiple content holes");i=o}}}return{dom:c,contentDOM:i}}},14186:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},15448:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]])},15968:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},19144:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("italic",[["line",{x1:"19",x2:"10",y1:"4",y2:"4",key:"15jd3p"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20",key:"bu0au3"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20",key:"uljnxc"}]])},22705:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("heading-2",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M21 18h-4c0-4 4-3 4-6 0-1.5-2-2.5-4-1",key:"9jr5yi"}]])},28440:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("heading-1",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"m17 12 3-2v8",key:"1hhhft"}]])},32643:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("strikethrough",[["path",{d:"M16 4H9a3 3 0 0 0-2.83 4",key:"43sutm"}],["path",{d:"M14 12a4 4 0 0 1 0 8H6",key:"nlfj13"}],["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}]])},35383:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>l,NG:()=>l});var r=n(4701),i=n(52571);let o=/^```([a-z]+)?[\s\n]$/,s=/^~~~([a-z]+)?[\s\n]$/,l=r.bP.create({name:"codeBlock",addOptions:()=>({languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}),content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:e=>{var t;let{languageClassPrefix:n}=this.options,r=[...(null==(t=e.firstElementChild)?void 0:t.classList)||[]].filter(e=>e.startsWith(n)).map(e=>e.replace(n,""))[0];return r||null},rendered:!1}}},parseHTML:()=>[{tag:"pre",preserveWhitespace:"full"}],renderHTML({node:e,HTMLAttributes:t}){return["pre",(0,r.KV)(this.options.HTMLAttributes,t),["code",{class:e.attrs.language?this.options.languageClassPrefix+e.attrs.language:null},0]]},addCommands(){return{setCodeBlock:e=>({commands:t})=>t.setNode(this.name,e),toggleCodeBlock:e=>({commands:t})=>t.toggleNode(this.name,"paragraph",e)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{let{empty:e,$anchor:t}=this.editor.state.selection,n=1===t.pos;return!!e&&t.parent.type.name===this.name&&(!!n||!t.parent.textContent.length)&&this.editor.commands.clearNodes()},Enter:({editor:e})=>{if(!this.options.exitOnTripleEnter)return!1;let{state:t}=e,{selection:n}=t,{$from:r,empty:i}=n;if(!i||r.parent.type!==this.type)return!1;let o=r.parentOffset===r.parent.nodeSize-2,s=r.parent.textContent.endsWith("\n\n");return!!o&&!!s&&e.chain().command(({tr:e})=>(e.delete(r.pos-2,r.pos),!0)).exitCode().run()},ArrowDown:({editor:e})=>{if(!this.options.exitOnArrowDown)return!1;let{state:t}=e,{selection:n,doc:r}=t,{$from:o,empty:s}=n;if(!s||o.parent.type!==this.type||o.parentOffset!==o.parent.nodeSize-2)return!1;let l=o.after();return void 0!==l&&(r.nodeAt(l)?e.commands.command(({tr:e})=>(e.setSelection(i.LN.near(r.resolve(l))),!0)):e.commands.exitCode())}}},addInputRules(){return[(0,r.JJ)({find:o,type:this.type,getAttributes:e=>({language:e[1]})}),(0,r.JJ)({find:s,type:this.type,getAttributes:e=>({language:e[1]})})]},addProseMirrorPlugins(){return[new i.k_({key:new i.hs("codeBlockVSCodeHandler"),props:{handlePaste:(e,t)=>{if(!t.clipboardData||this.editor.isActive(this.type.name))return!1;let n=t.clipboardData.getData("text/plain"),r=t.clipboardData.getData("vscode-editor-data"),o=r?JSON.parse(r):void 0,s=null==o?void 0:o.mode;if(!n||!s)return!1;let{tr:l,schema:a}=e.state,h=a.text(n.replace(/\r\n?/g,"\n"));return l.replaceSelectionWith(this.type.create({language:s},h)),l.selection.$from.parent.type!==this.type&&l.setSelection(i.U3.near(l.doc.resolve(Math.max(0,l.selection.from-2)))),l.setMeta("paste",!0),e.dispatch(l),!0}}})]}})},36761:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>eJ});var r=n(4701);let i=(e,t)=>{for(let n in t)e[n]=t[n];return e},o="numeric",s="ascii",l="alpha",a="asciinumeric",h="alphanumeric",c="domain",d="emoji",u="whitespace";function p(e,t,n){for(let r in t[o]&&(t[a]=!0,t[h]=!0),t[s]&&(t[a]=!0,t[l]=!0),t[a]&&(t[h]=!0),t[l]&&(t[h]=!0),t[h]&&(t[c]=!0),t[d]&&(t[c]=!0),t){let t=(r in n||(n[r]=[]),n[r]);0>t.indexOf(e)&&t.push(e)}}function f(e=null){this.j={},this.jr=[],this.jd=null,this.t=e}f.groups={},f.prototype={accepts(){return!!this.t},go(e){let t=this.j[e];if(t)return t;for(let t=0;t<this.jr.length;t++){let n=this.jr[t][0],r=this.jr[t][1];if(r&&n.test(e))return r}return this.jd},has(e,t=!1){return t?e in this.j:!!this.go(e)},ta(e,t,n,r){for(let i=0;i<e.length;i++)this.tt(e[i],t,n,r)},tr(e,t,n,r){let i;return r=r||f.groups,t&&t.j?i=t:(i=new f(t),n&&r&&p(t,n,r)),this.jr.push([e,i]),i},ts(e,t,n,r){let i=this,o=e.length;if(!o)return i;for(let t=0;t<o-1;t++)i=i.tt(e[t]);return i.tt(e[o-1],t,n,r)},tt(e,t,n,r){if(r=r||f.groups,t&&t.j)return this.j[e]=t,t;let o,s=this.go(e);return s?(i((o=new f).j,s.j),o.jr.push.apply(o.jr,s.jr),o.jd=s.jd,o.t=s.t):o=new f,t&&(r&&(o.t&&"string"==typeof o.t?p(t,i(function(e,t){let n={};for(let r in t)t[r].indexOf(e)>=0&&(n[r]=!0);return n}(o.t,r),n),r):n&&p(t,n,r)),o.t=t),this.j[e]=o,o}};let g=(e,t,n,r,i)=>e.ta(t,n,r,i),m=(e,t,n,r,i)=>e.tr(t,n,r,i),y=(e,t,n,r,i)=>e.ts(t,n,r,i),b=(e,t,n,r,i)=>e.tt(t,n,r,i),w="WORD",k="UWORD",v="ASCIINUMERICAL",x="ALPHANUMERICAL",S="LOCALHOST",M="UTLD",C="SCHEME",E="SLASH_SCHEME",A="OPENBRACE",T="CLOSEBRACE",O="OPENBRACKET",R="CLOSEBRACKET",N="OPENPAREN",I="CLOSEPAREN",L="OPENANGLEBRACKET",_="CLOSEANGLEBRACKET",z="FULLWIDTHLEFTPAREN",$="FULLWIDTHRIGHTPAREN",j="LEFTCORNERBRACKET",B="RIGHTCORNERBRACKET",H="LEFTWHITECORNERBRACKET",D="RIGHTWHITECORNERBRACKET",P="FULLWIDTHLESSTHAN",F="FULLWIDTHGREATERTHAN",J="AMPERSAND",K="APOSTROPHE",W="ASTERISK",U="BACKSLASH",V="BACKTICK",q="CARET",G="COLON",X="COMMA",Z="DOLLAR",Y="EQUALS",Q="EXCLAMATION",ee="HYPHEN",et="PERCENT",en="PIPE",er="PLUS",ei="POUND",eo="QUERY",es="QUOTE",el="FULLWIDTHMIDDLEDOT",ea="SEMI",eh="SLASH",ec="TILDE",ed="UNDERSCORE",eu="EMOJI";var ep=Object.freeze({__proto__:null,ALPHANUMERICAL:x,AMPERSAND:J,APOSTROPHE:K,ASCIINUMERICAL:v,ASTERISK:W,AT:"AT",BACKSLASH:U,BACKTICK:V,CARET:q,CLOSEANGLEBRACKET:_,CLOSEBRACE:T,CLOSEBRACKET:R,CLOSEPAREN:I,COLON:G,COMMA:X,DOLLAR:Z,DOT:"DOT",EMOJI:eu,EQUALS:Y,EXCLAMATION:Q,FULLWIDTHGREATERTHAN:F,FULLWIDTHLEFTPAREN:z,FULLWIDTHLESSTHAN:P,FULLWIDTHMIDDLEDOT:el,FULLWIDTHRIGHTPAREN:$,HYPHEN:ee,LEFTCORNERBRACKET:j,LEFTWHITECORNERBRACKET:H,LOCALHOST:S,NL:"NL",NUM:"NUM",OPENANGLEBRACKET:L,OPENBRACE:A,OPENBRACKET:O,OPENPAREN:N,PERCENT:et,PIPE:en,PLUS:er,POUND:ei,QUERY:eo,QUOTE:es,RIGHTCORNERBRACKET:B,RIGHTWHITECORNERBRACKET:D,SCHEME:C,SEMI:ea,SLASH:eh,SLASH_SCHEME:E,SYM:"SYM",TILDE:ec,TLD:"TLD",UNDERSCORE:ed,UTLD:M,UWORD:k,WORD:w,WS:"WS"});let ef=/[a-z]/,eg=/\p{L}/u,em=/\p{Emoji}/u,ey=/\d/,eb=/\s/,ew=null,ek=null;function ev(e,t){let n=function(e){let t=[],n=e.length,r=0;for(;r<n;){let i,o=e.charCodeAt(r),s=o<55296||o>56319||r+1===n||(i=e.charCodeAt(r+1))<56320||i>57343?e[r]:e.slice(r,r+2);t.push(s),r+=s.length}return t}(t.replace(/[A-Z]/g,e=>e.toLowerCase())),r=n.length,i=[],o=0,s=0;for(;s<r;){let l=e,a=null,h=0,c=null,d=-1,u=-1;for(;s<r&&(a=l.go(n[s]));)(l=a).accepts()?(d=0,u=0,c=l):d>=0&&(d+=n[s].length,u++),h+=n[s].length,o+=n[s].length,s++;o-=d,s-=u,h-=d,i.push({t:c.t,v:t.slice(o-h,o),s:o-h,e:o})}return i}function ex(e,t,n,r,i){let o,s=t.length;for(let n=0;n<s-1;n++){let s=t[n];e.j[s]?o=e.j[s]:((o=new f(r)).jr=i.slice(),e.j[s]=o),e=o}return(o=new f(n)).jr=i.slice(),e.j[t[s-1]]=o,o}function eS(e){let t=[],n=[],r=0;for(;r<e.length;){let i=0;for(;"0123456789".indexOf(e[r+i])>=0;)i++;if(i>0){t.push(n.join(""));for(let t=parseInt(e.substring(r,r+i),10);t>0;t--)n.pop();r+=i}else n.push(e[r]),r++}return t}let eM={defaultProtocol:"http",events:null,format:eE,formatHref:eE,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function eC(e,t=null){let n=i({},eM);e&&(n=i(n,e instanceof eC?e.o:e));let r=n.ignoreTags,o=[];for(let e=0;e<r.length;e++)o.push(r[e].toUpperCase());this.o=n,t&&(this.defaultRender=t),this.ignoreTags=o}function eE(e){return e}function eA(e,t){this.t="token",this.v=e,this.tk=t}function eT(e,t){class n extends eA{constructor(t,n){super(t,n),this.t=e}}for(let e in t)n.prototype[e]=t[e];return n.t=e,n}eC.prototype={o:eM,ignoreTags:[],defaultRender:e=>e,check(e){return this.get("validate",e.toString(),e)},get(e,t,n){let r=null!=t,i=this.o[e];return i&&("object"==typeof i?"function"==typeof(i=n.t in i?i[n.t]:eM[e])&&r&&(i=i(t,n)):"function"==typeof i&&r&&(i=i(t,n.t,n))),i},getObj(e,t,n){let r=this.o[e];return"function"==typeof r&&null!=t&&(r=r(t,n.t,n)),r},render(e){let t=e.render(this);return(this.get("render",null,e)||this.defaultRender)(t,e.t,e)}},eA.prototype={isLink:!1,toString(){return this.v},toHref(e){return this.toString()},toFormattedString(e){let t=this.toString(),n=e.get("truncate",t,this),r=e.get("format",t,this);return n&&r.length>n?r.substring(0,n)+"…":r},toFormattedHref(e){return e.get("formatHref",this.toHref(e.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(e=eM.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(e),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(e){return{type:this.t,value:this.toFormattedString(e),isLink:this.isLink,href:this.toFormattedHref(e),start:this.startIndex(),end:this.endIndex()}},validate(e){return e.get("validate",this.toString(),this)},render(e){let t=this.toHref(e.get("defaultProtocol")),n=e.get("formatHref",t,this),r=e.get("tagName",t,this),o=this.toFormattedString(e),s={},l=e.get("className",t,this),a=e.get("target",t,this),h=e.get("rel",t,this),c=e.getObj("attributes",t,this),d=e.getObj("events",t,this);return s.href=n,l&&(s.class=l),a&&(s.target=a),h&&(s.rel=h),c&&i(s,c),{tagName:r,attributes:s,content:o,eventListeners:d}}};let eO=eT("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),eR=eT("text"),eN=eT("nl"),eI=eT("url",{isLink:!0,toHref(e=eM.defaultProtocol){return this.hasProtocol()?this.v:`${e}://${this.v}`},hasProtocol(){let e=this.tk;return e.length>=2&&e[0].t!==S&&e[1].t===G}}),eL=e=>new f(e);function e_(e,t,n){let r=n[0].s,i=n[n.length-1].e;return new e(t.slice(r,i),n)}let ez="undefined"!=typeof console&&console&&console.warn||(()=>{}),e$={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function ej(e,t=!1){if(e$.initialized&&ez(`linkifyjs: already initialized - will not register custom scheme "${e}" until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(e))throw Error(`linkifyjs: incorrect scheme format.
1. Must only contain digits, lowercase ASCII letters or "-"
2. Cannot start or end with "-"
3. "-" cannot repeat`);e$.customSchemes.push([e,t])}function eB(e){return e$.initialized||function(){e$.scanner=function(e=[]){let t={};f.groups=t;let n=new f;null==ew&&(ew=eS("aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5m\xf6gensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2")),null==ek&&(ek=eS("ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2")),b(n,"'",K),b(n,"{",A),b(n,"}",T),b(n,"[",O),b(n,"]",R),b(n,"(",N),b(n,")",I),b(n,"<",L),b(n,">",_),b(n,"（",z),b(n,"）",$),b(n,"「",j),b(n,"」",B),b(n,"『",H),b(n,"』",D),b(n,"＜",P),b(n,"＞",F),b(n,"&",J),b(n,"*",W),b(n,"@","AT"),b(n,"`",V),b(n,"^",q),b(n,":",G),b(n,",",X),b(n,"$",Z),b(n,".","DOT"),b(n,"=",Y),b(n,"!",Q),b(n,"-",ee),b(n,"%",et),b(n,"|",en),b(n,"+",er),b(n,"#",ei),b(n,"?",eo),b(n,'"',es),b(n,"/",eh),b(n,";",ea),b(n,"~",ec),b(n,"_",ed),b(n,"\\",U),b(n,"・",el);let r=m(n,ey,"NUM",{[o]:!0});m(r,ey,r);let g=m(r,ef,v,{[a]:!0}),ev=m(r,eg,x,{[h]:!0}),eM=m(n,ef,w,{[s]:!0});m(eM,ey,g),m(eM,ef,eM),m(g,ey,g),m(g,ef,g);let eC=m(n,eg,k,{[l]:!0});m(eC,ef),m(eC,ey,ev),m(eC,eg,eC),m(ev,ey,ev),m(ev,ef),m(ev,eg,ev);let eE=b(n,"\n","NL",{[u]:!0}),eA=b(n,"\r","WS",{[u]:!0}),eT=m(n,eb,"WS",{[u]:!0});b(n,"￼",eT),b(eA,"\n",eE),b(eA,"￼",eT),m(eA,eb,eT),b(eT,"\r"),b(eT,"\n"),m(eT,eb,eT),b(eT,"￼",eT);let eO=m(n,em,eu,{[d]:!0});b(eO,"#"),m(eO,em,eO),b(eO,"️",eO);let eR=b(eO,"‍");b(eR,"#"),m(eR,em,eO);let eN=[[ef,eM],[ey,g]],eI=[[ef,null],[eg,eC],[ey,ev]];for(let e=0;e<ew.length;e++)ex(n,ew[e],"TLD",w,eN);for(let e=0;e<ek.length;e++)ex(n,ek[e],M,k,eI);p("TLD",{tld:!0,ascii:!0},t),p(M,{utld:!0,alpha:!0},t),ex(n,"file",C,w,eN),ex(n,"mailto",C,w,eN),ex(n,"http",E,w,eN),ex(n,"https",E,w,eN),ex(n,"ftp",E,w,eN),ex(n,"ftps",E,w,eN),p(C,{scheme:!0,ascii:!0},t),p(E,{slashscheme:!0,ascii:!0},t),e=e.sort((e,t)=>e[0]>t[0]?1:-1);for(let t=0;t<e.length;t++){let r=e[t][0],i=e[t][1]?{scheme:!0}:{slashscheme:!0};r.indexOf("-")>=0?i[c]=!0:ef.test(r)?ey.test(r)?i[a]=!0:i[s]=!0:i[o]=!0,y(n,r,r,i)}return y(n,"localhost",S,{ascii:!0}),n.jd=new f("SYM"),{start:n,tokens:i({groups:t},ep)}}(e$.customSchemes);for(let e=0;e<e$.tokenQueue.length;e++)e$.tokenQueue[e][1]({scanner:e$.scanner});e$.parser=function({groups:e}){let t=e.domain.concat([J,W,"AT",U,V,q,Z,Y,ee,"NUM",et,en,er,ei,eh,"SYM",ec,ed]),n=[K,G,X,"DOT",Q,et,eo,es,ea,L,_,A,T,R,O,N,I,z,$,j,B,H,D,P,F],r=[J,K,W,U,V,q,Z,Y,ee,A,T,et,en,er,ei,eo,eh,"SYM",ec,ed],i=eL(),o=b(i,ec);g(o,r,o),g(o,e.domain,o);let s=eL(),l=eL(),a=eL();g(i,e.domain,s),g(i,e.scheme,l),g(i,e.slashscheme,a),g(s,r,o),g(s,e.domain,s);let h=b(s,"AT");b(o,"AT",h),b(l,"AT",h),b(a,"AT",h);let c=b(o,"DOT");g(c,r,o),g(c,e.domain,o);let d=eL();g(h,e.domain,d),g(d,e.domain,d);let u=b(d,"DOT");g(u,e.domain,d);let p=eL(eO);g(u,e.tld,p),g(u,e.utld,p),b(h,S,p);let f=b(d,ee);b(f,ee,f),g(f,e.domain,d),g(p,e.domain,d),b(p,"DOT",u),b(p,ee,f),g(b(p,G),e.numeric,eO);let m=b(s,ee),y=b(s,"DOT");b(m,ee,m),g(m,e.domain,s),g(y,r,o),g(y,e.domain,s);let w=eL(eI);g(y,e.tld,w),g(y,e.utld,w),g(w,e.domain,s),g(w,r,o),b(w,"DOT",y),b(w,ee,m),b(w,"AT",h);let k=b(w,G),v=eL(eI);g(k,e.numeric,v);let x=eL(eI),M=eL();g(x,t,x),g(x,n,M),g(M,t,x),g(M,n,M),b(w,eh,x),b(v,eh,x);let C=b(l,G),E=b(a,G),el=b(E,eh),eu=b(el,eh);g(l,e.domain,s),b(l,"DOT",y),b(l,ee,m),g(a,e.domain,s),b(a,"DOT",y),b(a,ee,m),g(C,e.domain,x),b(C,eh,x),b(C,eo,x),g(eu,e.domain,x),g(eu,t,x),b(eu,eh,x);let ef=[[A,T],[O,R],[N,I],[L,_],[z,$],[j,B],[H,D],[P,F]];for(let e=0;e<ef.length;e++){let[r,i]=ef[e],o=b(x,r);b(M,r,o),b(o,i,x);let s=eL(eI);g(o,t,s);let l=eL();g(o,n),g(s,t,s),g(s,n,l),g(l,t,s),g(l,n,l),b(s,i,x),b(l,i,x)}return b(i,S,w),b(i,"NL",eN),{start:i,tokens:ep}}(e$.scanner.tokens);for(let e=0;e<e$.pluginQueue.length;e++)e$.pluginQueue[e][1]({scanner:e$.scanner,parser:e$.parser});e$.initialized=!0}(),function(e,t,n){let r=n.length,i=0,o=[],s=[];for(;i<r;){let l=e,a=null,h=null,c=0,d=null,u=-1;for(;i<r&&!(a=l.go(n[i].t));)s.push(n[i++]);for(;i<r&&(h=a||l.go(n[i].t));)a=null,(l=h).accepts()?(u=0,d=l):u>=0&&u++,i++,c++;if(u<0)(i-=c)<r&&(s.push(n[i]),i++);else{s.length>0&&(o.push(e_(eR,t,s)),s=[]),i-=u,c-=u;let e=d.t,r=n.slice(i-c,i);o.push(e_(e,t,r))}}return s.length>0&&o.push(e_(eR,t,s)),o}(e$.parser.start,e,ev(e$.scanner.start,e))}function eH(e,t=null,n=null){if(t&&"object"==typeof t){if(n)throw Error(`linkifyjs: Invalid link type ${t}; must be a string`);n=t,t=null}let r=new eC(n),i=eB(e),o=[];for(let e=0;e<i.length;e++){let n=i[e];n.isLink&&(!t||n.t===t)&&r.check(n)&&o.push(n.toFormattedObject(r))}return o}eB.scan=ev;var eD=n(52571);let eP=/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g;function eF(e,t){let n=["http","https","ftp","ftps","mailto","tel","callto","sms","cid","xmpp"];return t&&t.forEach(e=>{let t="string"==typeof e?e:e.scheme;t&&n.push(t)}),!e||e.replace(eP,"").match(RegExp(`^(?:(?:${n.join("|")}):|[^a-z]|[a-z0-9+.-]+(?:[^a-z+.-:]|$))`,"i"))}let eJ=r.CU.create({name:"link",priority:1e3,keepOnSplit:!1,exitable:!0,onCreate(){this.options.validate&&!this.options.shouldAutoLink&&(this.options.shouldAutoLink=this.options.validate,console.warn("The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.")),this.options.protocols.forEach(e=>{if("string"==typeof e)return void ej(e);ej(e.scheme,e.optionalSlashes)})},onDestroy(){f.groups={},e$.scanner=null,e$.parser=null,e$.tokenQueue=[],e$.pluginQueue=[],e$.customSchemes=[],e$.initialized=!1},inclusive(){return this.options.autolink},addOptions:()=>({openOnClick:!0,linkOnPaste:!0,autolink:!0,protocols:[],defaultProtocol:"http",HTMLAttributes:{target:"_blank",rel:"noopener noreferrer nofollow",class:null},isAllowedUri:(e,t)=>!!eF(e,t.protocols),validate:e=>!!e,shouldAutoLink:e=>!!e}),addAttributes(){return{href:{default:null,parseHTML:e=>e.getAttribute("href")},target:{default:this.options.HTMLAttributes.target},rel:{default:this.options.HTMLAttributes.rel},class:{default:this.options.HTMLAttributes.class}}},parseHTML(){return[{tag:"a[href]",getAttrs:e=>{let t=e.getAttribute("href");return!!t&&!!this.options.isAllowedUri(t,{defaultValidate:e=>!!eF(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&null}}]},renderHTML({HTMLAttributes:e}){return this.options.isAllowedUri(e.href,{defaultValidate:e=>!!eF(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?["a",(0,r.KV)(this.options.HTMLAttributes,e),0]:["a",(0,r.KV)(this.options.HTMLAttributes,{...e,href:""}),0]},addCommands(){return{setLink:e=>({chain:t})=>{let{href:n}=e;return!!this.options.isAllowedUri(n,{defaultValidate:e=>!!eF(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&t().setMark(this.name,e).setMeta("preventAutolink",!0).run()},toggleLink:e=>({chain:t})=>{let{href:n}=e;return!!this.options.isAllowedUri(n,{defaultValidate:e=>!!eF(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&t().toggleMark(this.name,e,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()},unsetLink:()=>({chain:e})=>e().unsetMark(this.name,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()}},addPasteRules(){return[(0,r.Zc)({find:e=>{let t=[];if(e){let{protocols:n,defaultProtocol:r}=this.options,i=eH(e).filter(e=>e.isLink&&this.options.isAllowedUri(e.value,{defaultValidate:e=>!!eF(e,n),protocols:n,defaultProtocol:r}));i.length&&i.forEach(e=>t.push({text:e.value,data:{href:e.href},index:e.start}))}return t},type:this.type,getAttributes:e=>{var t;return{href:null==(t=e.data)?void 0:t.href}}})]},addProseMirrorPlugins(){var e,t,n;let i=[],{protocols:o,defaultProtocol:s}=this.options;return this.options.autolink&&i.push((e={type:this.type,defaultProtocol:this.options.defaultProtocol,validate:e=>this.options.isAllowedUri(e,{defaultValidate:e=>!!eF(e,o),protocols:o,defaultProtocol:s}),shouldAutoLink:this.options.shouldAutoLink},new eD.k_({key:new eD.hs("autolink"),appendTransaction:(t,n,i)=>{let o=t.some(e=>e.docChanged)&&!n.doc.eq(i.doc),s=t.some(e=>e.getMeta("preventAutolink"));if(!o||s)return;let{tr:l}=i,a=(0,r.T7)(n.doc,[...t]);if((0,r.FF)(a).forEach(({newRange:t})=>{let n,o,s=(0,r.Nx)(i.doc,t,e=>e.isTextblock);if(s.length>1?(n=s[0],o=i.doc.textBetween(n.pos,n.pos+n.node.nodeSize,void 0," ")):s.length&&i.doc.textBetween(t.from,t.to," "," ").endsWith(" ")&&(n=s[0],o=i.doc.textBetween(n.pos,t.to,void 0," ")),n&&o){let t=o.split(" ").filter(e=>""!==e);if(t.length<=0)return!1;let s=t[t.length-1],a=n.pos+o.lastIndexOf(s);if(!s)return!1;let h=eB(s).map(t=>t.toObject(e.defaultProtocol));if(!(1===h.length?h[0].isLink:3===h.length&&!!h[1].isLink&&["()","[]"].includes(h[0].value+h[2].value)))return!1;h.filter(e=>e.isLink).map(e=>({...e,from:a+e.start+1,to:a+e.end+1})).filter(e=>!i.schema.marks.code||!i.doc.rangeHasMark(e.from,e.to,i.schema.marks.code)).filter(t=>e.validate(t.value)).filter(t=>e.shouldAutoLink(t.value)).forEach(t=>{(0,r.hO)(t.from,t.to,i.doc).some(t=>t.mark.type===e.type)||l.addMark(t.from,t.to,e.type.create({href:t.href}))})}}),l.steps.length)return l}}))),!0===this.options.openOnClick&&i.push((t={type:this.type},new eD.k_({key:new eD.hs("handleClickLink"),props:{handleClick:(e,n,i)=>{var o,s;if(0!==i.button||!e.editable)return!1;let l=i.target,a=[];for(;"DIV"!==l.nodeName;)a.push(l),l=l.parentNode;if(!a.find(e=>"A"===e.nodeName))return!1;let h=(0,r.gu)(e.state,t.type.name),c=i.target,d=null!=(o=null==c?void 0:c.href)?o:h.href,u=null!=(s=null==c?void 0:c.target)?s:h.target;return!!c&&!!d&&(window.open(d,u),!0)}}}))),this.options.linkOnPaste&&i.push((n={editor:this.editor,defaultProtocol:this.options.defaultProtocol,type:this.type},new eD.k_({key:new eD.hs("handlePasteLink"),props:{handlePaste:(e,t,r)=>{let{state:i}=e,{selection:o}=i,{empty:s}=o;if(s)return!1;let l="";r.content.forEach(e=>{l+=e.textContent});let a=eH(l,{defaultProtocol:n.defaultProtocol}).find(e=>e.isLink&&e.value===l);return!!l&&!!a&&n.editor.commands.setMark(n.type,{href:a.href})}}}))),i}})},38164:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},40224:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]])},43557:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(4701);let i=r.bP.create({name:"tableCell",addOptions:()=>({HTMLAttributes:{}}),content:"block+",addAttributes:()=>({colspan:{default:1},rowspan:{default:1},colwidth:{default:null,parseHTML:e=>{let t=e.getAttribute("colwidth");return t?t.split(",").map(e=>parseInt(e,10)):null}}}),tableRole:"cell",isolating:!0,parseHTML:()=>[{tag:"td"}],renderHTML({HTMLAttributes:e}){return["td",(0,r.KV)(this.options.HTMLAttributes,e),0]}})},45254:(e,t,n)=>{"use strict";n.d(t,{A:()=>ei});var r=n(35383),i=n(4701),o=n(52571),s=n(42695);class l{constructor(e){void 0===e.data&&(e.data={}),this.data=e.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}}function a(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function h(e,...t){let n=Object.create(null);for(let t in e)n[t]=e[t];return t.forEach(function(e){for(let t in e)n[t]=e[t]}),n}let c=e=>!!e.scope,d=(e,{prefix:t})=>{if(e.startsWith("language:"))return e.replace("language:","language-");if(e.includes(".")){let n=e.split(".");return[`${t}${n.shift()}`,...n.map((e,t)=>`${e}${"_".repeat(t+1)}`)].join(" ")}return`${t}${e}`};class u{constructor(e,t){this.buffer="",this.classPrefix=t.classPrefix,e.walk(this)}addText(e){this.buffer+=a(e)}openNode(e){if(!c(e))return;let t=d(e.scope,{prefix:this.classPrefix});this.span(t)}closeNode(e){c(e)&&(this.buffer+="</span>")}value(){return this.buffer}span(e){this.buffer+=`<span class="${e}">`}}let p=(e={})=>{let t={children:[]};return Object.assign(t,e),t};class f{constructor(){this.rootNode=p(),this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(e){this.top.children.push(e)}openNode(e){let t=p({scope:e});this.add(t),this.stack.push(t)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(e){return this.constructor._walk(e,this.rootNode)}static _walk(e,t){return"string"==typeof t?e.addText(t):t.children&&(e.openNode(t),t.children.forEach(t=>this._walk(e,t)),e.closeNode(t)),e}static _collapse(e){"string"!=typeof e&&e.children&&(e.children.every(e=>"string"==typeof e)?e.children=[e.children.join("")]:e.children.forEach(e=>{f._collapse(e)}))}}class g extends f{constructor(e){super(),this.options=e}addText(e){""!==e&&this.add(e)}startScope(e){this.openNode(e)}endScope(){this.closeNode()}__addSublanguage(e,t){let n=e.root;t&&(n.scope=`language:${t}`),this.add(n)}toHTML(){return new u(this,this.options).value()}finalize(){return this.closeAllNodes(),!0}}function m(e){return e?"string"==typeof e?e:e.source:null}function y(e){return k("(?=",e,")")}function b(e){return k("(?:",e,")*")}function w(e){return k("(?:",e,")?")}function k(...e){return e.map(e=>m(e)).join("")}function v(...e){return"("+(function(e){let t=e[e.length-1];return"object"==typeof t&&t.constructor===Object?(e.splice(e.length-1,1),t):{}}(e).capture?"":"?:")+e.map(e=>m(e)).join("|")+")"}function x(e){return RegExp(e.toString()+"|").exec("").length-1}let S=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function M(e,{joinWith:t}){let n=0;return e.map(e=>{let t=n+=1,r=m(e),i="";for(;r.length>0;){let e=S.exec(r);if(!e){i+=r;break}i+=r.substring(0,e.index),r=r.substring(e.index+e[0].length),"\\"===e[0][0]&&e[1]?i+="\\"+String(Number(e[1])+t):(i+=e[0],"("===e[0]&&n++)}return i}).map(e=>`(${e})`).join(t)}let C="[a-zA-Z]\\w*",E="[a-zA-Z_]\\w*",A="\\b\\d+(\\.\\d+)?",T="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",O="\\b(0b[01]+)",R={begin:"\\\\[\\s\\S]",relevance:0},N=function(e,t,n={}){let r=h({scope:"comment",begin:e,end:t,contains:[]},n);r.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0});let i=v("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return r.contains.push({begin:k(/[ ]+/,"(",i,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),r},I=N("//","$"),L=N("/\\*","\\*/"),_=N("#","$");var z=Object.freeze({__proto__:null,APOS_STRING_MODE:{scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[R]},BACKSLASH_ESCAPE:R,BINARY_NUMBER_MODE:{scope:"number",begin:O,relevance:0},BINARY_NUMBER_RE:O,COMMENT:N,C_BLOCK_COMMENT_MODE:L,C_LINE_COMMENT_MODE:I,C_NUMBER_MODE:{scope:"number",begin:T,relevance:0},C_NUMBER_RE:T,END_SAME_AS_BEGIN:function(e){return Object.assign(e,{"on:begin":(e,t)=>{t.data._beginMatch=e[1]},"on:end":(e,t)=>{t.data._beginMatch!==e[1]&&t.ignoreMatch()}})},HASH_COMMENT_MODE:_,IDENT_RE:C,MATCH_NOTHING_RE:/\b\B/,METHOD_GUARD:{begin:"\\.\\s*"+E,relevance:0},NUMBER_MODE:{scope:"number",begin:A,relevance:0},NUMBER_RE:A,PHRASAL_WORDS_MODE:{begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},QUOTE_STRING_MODE:{scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[R]},REGEXP_MODE:{scope:"regexp",begin:/\/(?=[^/\n]*\/)/,end:/\/[gimuy]*/,contains:[R,{begin:/\[/,end:/\]/,relevance:0,contains:[R]}]},RE_STARTERS_RE:"!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",SHEBANG:(e={})=>{let t=/^#![ ]*\//;return e.binary&&(e.begin=k(t,/.*\b/,e.binary,/\b.*/)),h({scope:"meta",begin:t,end:/$/,relevance:0,"on:begin":(e,t)=>{0!==e.index&&t.ignoreMatch()}},e)},TITLE_MODE:{scope:"title",begin:C,relevance:0},UNDERSCORE_IDENT_RE:E,UNDERSCORE_TITLE_MODE:{scope:"title",begin:E,relevance:0}});function $(e,t){"."===e.input[e.index-1]&&t.ignoreMatch()}function j(e,t){void 0!==e.className&&(e.scope=e.className,delete e.className)}function B(e,t){t&&e.beginKeywords&&(e.begin="\\b("+e.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",e.__beforeBegin=$,e.keywords=e.keywords||e.beginKeywords,delete e.beginKeywords,void 0===e.relevance&&(e.relevance=0))}function H(e,t){Array.isArray(e.illegal)&&(e.illegal=v(...e.illegal))}function D(e,t){if(e.match){if(e.begin||e.end)throw Error("begin & end are not supported with match");e.begin=e.match,delete e.match}}function P(e,t){void 0===e.relevance&&(e.relevance=1)}let F=(e,t)=>{if(!e.beforeMatch)return;if(e.starts)throw Error("beforeMatch cannot be used with starts");let n=Object.assign({},e);Object.keys(e).forEach(t=>{delete e[t]}),e.keywords=n.keywords,e.begin=k(n.beforeMatch,y(n.begin)),e.starts={relevance:0,contains:[Object.assign(n,{endsParent:!0})]},e.relevance=0,delete n.beforeMatch},J=["of","and","for","in","not","or","if","then","parent","list","value"],K={},W=e=>{console.error(e)},U=(e,...t)=>{console.log(`WARN: ${e}`,...t)},V=(e,t)=>{K[`${e}/${t}`]||(console.log(`Deprecated as of ${e}. ${t}`),K[`${e}/${t}`]=!0)},q=Error();function G(e,t,{key:n}){let r=0,i=e[n],o={},s={};for(let e=1;e<=t.length;e++)s[e+r]=i[e],o[e+r]=!0,r+=x(t[e-1]);e[n]=s,e[n]._emit=o,e[n]._multi=!0}function X(e){if(e.scope&&"object"==typeof e.scope&&null!==e.scope&&(e.beginScope=e.scope,delete e.scope),"string"==typeof e.beginScope&&(e.beginScope={_wrap:e.beginScope}),"string"==typeof e.endScope&&(e.endScope={_wrap:e.endScope}),Array.isArray(e.begin)){if(e.skip||e.excludeBegin||e.returnBegin)throw W("skip, excludeBegin, returnBegin not compatible with beginScope: {}"),q;if("object"!=typeof e.beginScope||null===e.beginScope)throw W("beginScope must be object"),q;G(e,e.begin,{key:"beginScope"}),e.begin=M(e.begin,{joinWith:""})}if(Array.isArray(e.end)){if(e.skip||e.excludeEnd||e.returnEnd)throw W("skip, excludeEnd, returnEnd not compatible with endScope: {}"),q;if("object"!=typeof e.endScope||null===e.endScope)throw W("endScope must be object"),q;G(e,e.end,{key:"endScope"}),e.end=M(e.end,{joinWith:""})}}class Z extends Error{constructor(e,t){super(e),this.name="HTMLInjectionError",this.html=t}}let Y=Symbol("nomatch"),Q=function(e){let t=Object.create(null),n=Object.create(null),r=[],i=!0,o="Could not find the language '{}', did you forget to load/include a language module?",s={disableAutodetect:!0,name:"Plain text",contains:[]},c={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:g};function d(e){return c.noHighlightRe.test(e)}function u(e,t,n){let r="",i="";"object"==typeof t?(r=e,n=t.ignoreIllegals,i=t.language):(V("10.7.0","highlight(lang, code, ...args) has been deprecated."),V("10.7.0","Please use highlight(code, options) instead.\nhttps://github.com/highlightjs/highlight.js/issues/2277"),i=e,r=t),void 0===n&&(n=!0);let o={code:r,language:i};R("before:highlight",o);let s=o.result?o.result:p(o.language,o.code,n);return s.code=o.code,R("after:highlight",s),s}function p(e,n,r,s){let d=Object.create(null);function u(){if(!T.keywords)return void R.addText(I);let e=0;T.keywordPatternRe.lastIndex=0;let t=T.keywordPatternRe.exec(I),n="";for(;t;){n+=I.substring(e,t.index);let r=S.case_insensitive?t[0].toLowerCase():t[0],i=T.keywords[r];if(i){let[e,o]=i;if(R.addText(n),n="",d[r]=(d[r]||0)+1,d[r]<=7&&(L+=o),e.startsWith("_"))n+=t[0];else{let n=S.classNameAliases[e]||e;y(t[0],n)}}else n+=t[0];e=T.keywordPatternRe.lastIndex,t=T.keywordPatternRe.exec(I)}n+=I.substring(e),R.addText(n)}function g(){null!=T.subLanguage?function(){if(""===I)return;let e=null;if("string"==typeof T.subLanguage){if(!t[T.subLanguage])return R.addText(I);e=p(T.subLanguage,I,!0,O[T.subLanguage]),O[T.subLanguage]=e._top}else e=f(I,T.subLanguage.length?T.subLanguage:null);T.relevance>0&&(L+=e.relevance),R.__addSublanguage(e._emitter,e.language)}():u(),I=""}function y(e,t){""!==e&&(R.startScope(t),R.addText(e),R.endScope())}function b(e,t){let n=1,r=t.length-1;for(;n<=r;){if(!e._emit[n]){n++;continue}let r=S.classNameAliases[e[n]]||e[n],i=t[n];r?y(i,r):(I=i,u(),I=""),n++}}function w(e,t){return e.scope&&"string"==typeof e.scope&&R.openNode(S.classNameAliases[e.scope]||e.scope),e.beginScope&&(e.beginScope._wrap?(y(I,S.classNameAliases[e.beginScope._wrap]||e.beginScope._wrap),I=""):e.beginScope._multi&&(b(e.beginScope,t),I="")),T=Object.create(e,{parent:{value:T}})}let k={};function v(t,o){let s=o&&o[0];if(I+=t,null==s)return g(),0;if("begin"===k.type&&"end"===o.type&&k.index===o.index&&""===s){if(I+=n.slice(o.index,o.index+1),!i){let t=Error(`0 width match regex (${e})`);throw t.languageName=e,t.badRule=k.rule,t}return 1}if(k=o,"begin"===o.type){let e=o[0],t=o.rule,n=new l(t);for(let r of[t.__beforeBegin,t["on:begin"]])if(r&&(r(o,n),n.isMatchIgnored))return 0===T.matcher.regexIndex?(I+=e[0],1):($=!0,0);return t.skip?I+=e:(t.excludeBegin&&(I+=e),g(),t.returnBegin||t.excludeBegin||(I=e)),w(t,o),t.returnBegin?0:e.length}if("illegal"!==o.type||r){if("end"===o.type){let e=function(e){let t=e[0],r=n.substring(e.index),i=function e(t,n,r){let i=function(e,t){let n=e&&e.exec(t);return n&&0===n.index}(t.endRe,r);if(i){if(t["on:end"]){let e=new l(t);t["on:end"](n,e),e.isMatchIgnored&&(i=!1)}if(i){for(;t.endsParent&&t.parent;)t=t.parent;return t}}if(t.endsWithParent)return e(t.parent,n,r)}(T,e,r);if(!i)return Y;let o=T;T.endScope&&T.endScope._wrap?(g(),y(t,T.endScope._wrap)):T.endScope&&T.endScope._multi?(g(),b(T.endScope,e)):o.skip?I+=t:(o.returnEnd||o.excludeEnd||(I+=t),g(),o.excludeEnd&&(I=t));do T.scope&&R.closeNode(),T.skip||T.subLanguage||(L+=T.relevance),T=T.parent;while(T!==i.parent);return i.starts&&w(i.starts,e),o.returnEnd?0:t.length}(o);if(e!==Y)return e}}else{let e=Error('Illegal lexeme "'+s+'" for mode "'+(T.scope||"<unnamed>")+'"');throw e.mode=T,e}if("illegal"===o.type&&""===s)return 1;if(z>1e5&&z>3*o.index)throw Error("potential infinite loop, way more iterations than matches");return I+=s,s.length}let S=A(e);if(!S)throw W(o.replace("{}",e)),Error('Unknown language: "'+e+'"');let C=function(e){function t(t,n){return RegExp(m(t),"m"+(e.case_insensitive?"i":"")+(e.unicodeRegex?"u":"")+(n?"g":""))}class n{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(e,t){t.position=this.position++,this.matchIndexes[this.matchAt]=t,this.regexes.push([t,e]),this.matchAt+=x(e)+1}compile(){0===this.regexes.length&&(this.exec=()=>null);let e=this.regexes.map(e=>e[1]);this.matcherRe=t(M(e,{joinWith:"|"}),!0),this.lastIndex=0}exec(e){this.matcherRe.lastIndex=this.lastIndex;let t=this.matcherRe.exec(e);if(!t)return null;let n=t.findIndex((e,t)=>t>0&&void 0!==e),r=this.matchIndexes[n];return t.splice(0,n),Object.assign(t,r)}}class r{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(e){if(this.multiRegexes[e])return this.multiRegexes[e];let t=new n;return this.rules.slice(e).forEach(([e,n])=>t.addRule(e,n)),t.compile(),this.multiRegexes[e]=t,t}resumingScanAtSamePosition(){return 0!==this.regexIndex}considerAll(){this.regexIndex=0}addRule(e,t){this.rules.push([e,t]),"begin"===t.type&&this.count++}exec(e){let t=this.getMatcher(this.regexIndex);t.lastIndex=this.lastIndex;let n=t.exec(e);if(this.resumingScanAtSamePosition())if(n&&n.index===this.lastIndex);else{let t=this.getMatcher(0);t.lastIndex=this.lastIndex+1,n=t.exec(e)}return n&&(this.regexIndex+=n.position+1,this.regexIndex===this.count&&this.considerAll()),n}}if(e.compilerExtensions||(e.compilerExtensions=[]),e.contains&&e.contains.includes("self"))throw Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return e.classNameAliases=h(e.classNameAliases||{}),function n(i,o){if(i.isCompiled)return i;[j,D,X,F].forEach(e=>e(i,o)),e.compilerExtensions.forEach(e=>e(i,o)),i.__beforeBegin=null,[B,H,P].forEach(e=>e(i,o)),i.isCompiled=!0;let s=null;return"object"==typeof i.keywords&&i.keywords.$pattern&&(i.keywords=Object.assign({},i.keywords),s=i.keywords.$pattern,delete i.keywords.$pattern),s=s||/\w+/,i.keywords&&(i.keywords=function e(t,n,r="keyword"){let i=Object.create(null);return"string"==typeof t?o(r,t.split(" ")):Array.isArray(t)?o(r,t):Object.keys(t).forEach(function(r){Object.assign(i,e(t[r],n,r))}),i;function o(e,t){n&&(t=t.map(e=>e.toLowerCase())),t.forEach(function(t){var n,r,o;let s=t.split("|");i[s[0]]=[e,(n=s[0],(r=s[1])?Number(r):+(o=n,!J.includes(o.toLowerCase())))]})}}(i.keywords,e.case_insensitive)),i.keywordPatternRe=t(s,!0),o&&(i.begin||(i.begin=/\B|\b/),i.beginRe=t(i.begin),i.end||i.endsWithParent||(i.end=/\B|\b/),i.end&&(i.endRe=t(i.end)),i.terminatorEnd=m(i.end)||"",i.endsWithParent&&o.terminatorEnd&&(i.terminatorEnd+=(i.end?"|":"")+o.terminatorEnd)),i.illegal&&(i.illegalRe=t(i.illegal)),i.contains||(i.contains=[]),i.contains=[].concat(...i.contains.map(function(e){var t;return((t="self"===e?i:e).variants&&!t.cachedVariants&&(t.cachedVariants=t.variants.map(function(e){return h(t,{variants:null},e)})),t.cachedVariants)?t.cachedVariants:!function e(t){return!!t&&(t.endsWithParent||e(t.starts))}(t)?Object.isFrozen(t)?h(t):t:h(t,{starts:t.starts?h(t.starts):null})})),i.contains.forEach(function(e){n(e,i)}),i.starts&&n(i.starts,o),i.matcher=function(e){let t=new r;return e.contains.forEach(e=>t.addRule(e.begin,{rule:e,type:"begin"})),e.terminatorEnd&&t.addRule(e.terminatorEnd,{type:"end"}),e.illegal&&t.addRule(e.illegal,{type:"illegal"}),t}(i),i}(e)}(S),E="",T=s||C,O={},R=new c.__emitter(c),N=[];for(let e=T;e!==S;e=e.parent)e.scope&&N.unshift(e.scope);N.forEach(e=>R.openNode(e));let I="",L=0,_=0,z=0,$=!1;try{if(S.__emitTokens)S.__emitTokens(n,R);else{for(T.matcher.considerAll();;){z++,$?$=!1:T.matcher.considerAll(),T.matcher.lastIndex=_;let e=T.matcher.exec(n);if(!e)break;let t=n.substring(_,e.index),r=v(t,e);_=e.index+r}v(n.substring(_))}return R.finalize(),E=R.toHTML(),{language:e,value:E,relevance:L,illegal:!1,_emitter:R,_top:T}}catch(t){if(t.message&&t.message.includes("Illegal"))return{language:e,value:a(n),illegal:!0,relevance:0,_illegalBy:{message:t.message,index:_,context:n.slice(_-100,_+100),mode:t.mode,resultSoFar:E},_emitter:R};if(i)return{language:e,value:a(n),illegal:!1,relevance:0,errorRaised:t,_emitter:R,_top:T};throw t}}function f(e,n){n=n||c.languages||Object.keys(t);let r=function(e){let t={value:a(e),illegal:!1,relevance:0,_top:s,_emitter:new c.__emitter(c)};return t._emitter.addText(e),t}(e),i=n.filter(A).filter(O).map(t=>p(t,e,!1));i.unshift(r);let[o,l]=i.sort((e,t)=>{if(e.relevance!==t.relevance)return t.relevance-e.relevance;if(e.language&&t.language){if(A(e.language).supersetOf===t.language)return 1;else if(A(t.language).supersetOf===e.language)return -1}return 0});return o.secondBest=l,o}function S(e){let t=null,r=function(e){let t=e.className+" ";t+=e.parentNode?e.parentNode.className:"";let n=c.languageDetectRe.exec(t);if(n){let t=A(n[1]);return t||(U(o.replace("{}",n[1])),U("Falling back to no-highlight mode for this block.",e)),t?n[1]:"no-highlight"}return t.split(/\s+/).find(e=>d(e)||A(e))}(e);if(d(r))return;if(R("before:highlightElement",{el:e,language:r}),e.dataset.highlighted)return void console.log("Element previously highlighted. To highlight again, first unset `dataset.highlighted`.",e);if(e.children.length>0&&(c.ignoreUnescapedHTML||(console.warn("One of your code blocks includes unescaped HTML. This is a potentially serious security risk."),console.warn("https://github.com/highlightjs/highlight.js/wiki/security"),console.warn("The element with unescaped HTML:"),console.warn(e)),c.throwUnescapedHTML))throw new Z("One of your code blocks includes unescaped HTML.",e.innerHTML);let i=e.textContent,s=r?u(i,{language:r,ignoreIllegals:!0}):f(i);e.innerHTML=s.value,e.dataset.highlighted="yes";var l=s.language;let a=r&&n[r]||l;e.classList.add("hljs"),e.classList.add(`language-${a}`),e.result={language:s.language,re:s.relevance,relevance:s.relevance},s.secondBest&&(e.secondBest={language:s.secondBest.language,relevance:s.secondBest.relevance}),R("after:highlightElement",{el:e,result:s,text:i})}let C=!1;function E(){if("loading"===document.readyState){C=!0;return}document.querySelectorAll(c.cssSelector).forEach(S)}function A(e){return t[e=(e||"").toLowerCase()]||t[n[e]]}function T(e,{languageName:t}){"string"==typeof e&&(e=[e]),e.forEach(e=>{n[e.toLowerCase()]=t})}function O(e){let t=A(e);return t&&!t.disableAutodetect}function R(e,t){r.forEach(function(n){n[e]&&n[e](t)})}for(let o in"undefined"!=typeof window&&window.addEventListener&&window.addEventListener("DOMContentLoaded",function(){C&&E()},!1),Object.assign(e,{highlight:u,highlightAuto:f,highlightAll:E,highlightElement:S,highlightBlock:function(e){return V("10.7.0","highlightBlock will be removed entirely in v12.0"),V("10.7.0","Please use highlightElement now."),S(e)},configure:function(e){c=h(c,e)},initHighlighting:()=>{E(),V("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")},initHighlightingOnLoad:function(){E(),V("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")},registerLanguage:function(n,r){let o=null;try{o=r(e)}catch(e){if(W("Language definition for '{}' could not be registered.".replace("{}",n)),i)W(e);else throw e;o=s}o.name||(o.name=n),t[n]=o,o.rawDefinition=r.bind(null,e),o.aliases&&T(o.aliases,{languageName:n})},unregisterLanguage:function(e){for(let r of(delete t[e],Object.keys(n)))n[r]===e&&delete n[r]},listLanguages:function(){return Object.keys(t)},getLanguage:A,registerAliases:T,autoDetection:O,inherit:h,addPlugin:function(e){var t;(t=e)["before:highlightBlock"]&&!t["before:highlightElement"]&&(t["before:highlightElement"]=e=>{t["before:highlightBlock"](Object.assign({block:e.el},e))}),t["after:highlightBlock"]&&!t["after:highlightElement"]&&(t["after:highlightElement"]=e=>{t["after:highlightBlock"](Object.assign({block:e.el},e))}),r.push(e)},removePlugin:function(e){let t=r.indexOf(e);-1!==t&&r.splice(t,1)}}),e.debugMode=function(){i=!1},e.safeMode=function(){i=!0},e.versionString="11.10.0",e.regex={concat:k,lookahead:y,either:v,optional:w,anyNumberOfTimes:b},z)"object"==typeof z[o]&&function e(t){return t instanceof Map?t.clear=t.delete=t.set=function(){throw Error("map is read-only")}:t instanceof Set&&(t.add=t.clear=t.delete=function(){throw Error("set is read-only")}),Object.freeze(t),Object.getOwnPropertyNames(t).forEach(n=>{let r=t[n],i=typeof r;"object"!==i&&"function"!==i||Object.isFrozen(r)||e(r)}),t}(z[o]);return Object.assign(e,z),e},ee=Q({});ee.newInstance=()=>Q({}),ee.HighlightJS=ee,ee.default=ee;var et=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(ee);function en(e){return e.value||e.children||[]}function er({doc:e,name:t,lowlight:n,defaultLanguage:r}){let o=[];return(0,i.xe)(e,e=>e.type.name===t).forEach(e=>{var t;let i=e.pos+1,l=e.node.attrs.language||r,a=n.listLanguages();(function e(t,n=[]){return t.map(t=>{let r=[...n,...t.properties?t.properties.className:[]];return t.children?e(t.children,r):{text:t.value,classes:r}}).flat()})(l&&(a.includes(l)||et.getLanguage(l)||(null==(t=n.registered)?void 0:t.call(n,l)))?en(n.highlight(l,e.node.textContent)):en(n.highlightAuto(e.node.textContent))).forEach(e=>{let t=i+e.text.length;if(e.classes.length){let n=s.NZ.inline(i,t,{class:e.classes.join(" ")});o.push(n)}i=t})}),s.zF.create(e,o)}let ei=r.Ay.extend({addOptions(){var e;return{...null==(e=this.parent)?void 0:e.call(this),lowlight:{},languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}},addProseMirrorPlugins(){var e;return[...(null==(e=this.parent)?void 0:e.call(this))||[],function({name:e,lowlight:t,defaultLanguage:n}){if(!["highlight","highlightAuto","listLanguages"].every(e=>"function"==typeof t[e]))throw Error("You should provide an instance of lowlight to use the code-block-lowlight extension");let r=new o.k_({key:new o.hs("lowlight"),state:{init:(r,{doc:i})=>er({doc:i,name:e,lowlight:t,defaultLanguage:n}),apply:(r,o,s,l)=>{let a=s.selection.$head.parent.type.name,h=l.selection.$head.parent.type.name,c=(0,i.xe)(s.doc,t=>t.type.name===e),d=(0,i.xe)(l.doc,t=>t.type.name===e);return r.docChanged&&([a,h].includes(e)||d.length!==c.length||r.steps.some(e=>void 0!==e.from&&void 0!==e.to&&c.some(t=>t.pos>=e.from&&t.pos+t.node.nodeSize<=e.to)))?er({doc:r.doc,name:e,lowlight:t,defaultLanguage:n}):o.map(r.mapping,r.doc)}},props:{decorations:e=>r.getState(e)}});return r}({name:this.name,lowlight:this.options.lowlight,defaultLanguage:this.options.defaultLanguage})]}})},48932:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("redo",[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]])},52571:(e,t,n)=>{"use strict";n.d(t,{$t:()=>S,LN:()=>s,U3:()=>c,hs:()=>A,i5:()=>f,k_:()=>M,nh:()=>u,yn:()=>l});var r=n(10156),i=n(808);let o=Object.create(null);class s{constructor(e,t,n){this.$anchor=e,this.$head=t,this.ranges=n||[new l(e.min(t),e.max(t))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let e=this.ranges;for(let t=0;t<e.length;t++)if(e[t].$from.pos!=e[t].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(e,t=r.Ji.empty){let n=t.content.lastChild,i=null;for(let e=0;e<t.openEnd;e++)i=n,n=n.lastChild;let o=e.steps.length,s=this.ranges;for(let l=0;l<s.length;l++){let{$from:a,$to:h}=s[l],c=e.mapping.slice(o);e.replaceRange(c.map(a.pos),c.map(h.pos),l?r.Ji.empty:t),0==l&&y(e,o,(n?n.isInline:i&&i.isTextblock)?-1:1)}}replaceWith(e,t){let n=e.steps.length,r=this.ranges;for(let i=0;i<r.length;i++){let{$from:o,$to:s}=r[i],l=e.mapping.slice(n),a=l.map(o.pos),h=l.map(s.pos);i?e.deleteRange(a,h):(e.replaceRangeWith(a,h,t),y(e,n,t.isInline?-1:1))}}static findFrom(e,t,n=!1){let r=e.parent.inlineContent?new c(e):m(e.node(0),e.parent,e.pos,e.index(),t,n);if(r)return r;for(let r=e.depth-1;r>=0;r--){let i=t<0?m(e.node(0),e.node(r),e.before(r+1),e.index(r),t,n):m(e.node(0),e.node(r),e.after(r+1),e.index(r)+1,t,n);if(i)return i}return null}static near(e,t=1){return this.findFrom(e,t)||this.findFrom(e,-t)||new f(e.node(0))}static atStart(e){return m(e,e,0,0,1)||new f(e)}static atEnd(e){return m(e,e,e.content.size,e.childCount,-1)||new f(e)}static fromJSON(e,t){if(!t||!t.type)throw RangeError("Invalid input for Selection.fromJSON");let n=o[t.type];if(!n)throw RangeError(`No selection type ${t.type} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in o)throw RangeError("Duplicate use of selection JSON ID "+e);return o[e]=t,t.prototype.jsonID=e,t}getBookmark(){return c.between(this.$anchor,this.$head).getBookmark()}}s.prototype.visible=!0;class l{constructor(e,t){this.$from=e,this.$to=t}}let a=!1;function h(e){a||e.parent.inlineContent||(a=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+e.parent.type.name+")"))}class c extends s{constructor(e,t=e){h(e),h(t),super(e,t)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(e,t){let n=e.resolve(t.map(this.head));if(!n.parent.inlineContent)return s.near(n);let r=e.resolve(t.map(this.anchor));return new c(r.parent.inlineContent?r:n,n)}replace(e,t=r.Ji.empty){if(super.replace(e,t),t==r.Ji.empty){let t=this.$from.marksAcross(this.$to);t&&e.ensureMarks(t)}}eq(e){return e instanceof c&&e.anchor==this.anchor&&e.head==this.head}getBookmark(){return new d(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(e,t){if("number"!=typeof t.anchor||"number"!=typeof t.head)throw RangeError("Invalid input for TextSelection.fromJSON");return new c(e.resolve(t.anchor),e.resolve(t.head))}static create(e,t,n=t){let r=e.resolve(t);return new this(r,n==t?r:e.resolve(n))}static between(e,t,n){let r=e.pos-t.pos;if((!n||r)&&(n=r>=0?1:-1),!t.parent.inlineContent){let e=s.findFrom(t,n,!0)||s.findFrom(t,-n,!0);if(!e)return s.near(t,n);t=e.$head}return e.parent.inlineContent||(0==r?e=t:(e=(s.findFrom(e,-n,!0)||s.findFrom(e,n,!0)).$anchor).pos<t.pos!=r<0&&(e=t)),new c(e,t)}}s.jsonID("text",c);class d{constructor(e,t){this.anchor=e,this.head=t}map(e){return new d(e.map(this.anchor),e.map(this.head))}resolve(e){return c.between(e.resolve(this.anchor),e.resolve(this.head))}}class u extends s{constructor(e){let t=e.nodeAfter;super(e,e.node(0).resolve(e.pos+t.nodeSize)),this.node=t}map(e,t){let{deleted:n,pos:r}=t.mapResult(this.anchor),i=e.resolve(r);return n?s.near(i):new u(i)}content(){return new r.Ji(r.FK.from(this.node),0,0)}eq(e){return e instanceof u&&e.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new p(this.anchor)}static fromJSON(e,t){if("number"!=typeof t.anchor)throw RangeError("Invalid input for NodeSelection.fromJSON");return new u(e.resolve(t.anchor))}static create(e,t){return new u(e.resolve(t))}static isSelectable(e){return!e.isText&&!1!==e.type.spec.selectable}}u.prototype.visible=!1,s.jsonID("node",u);class p{constructor(e){this.anchor=e}map(e){let{deleted:t,pos:n}=e.mapResult(this.anchor);return t?new d(n,n):new p(n)}resolve(e){let t=e.resolve(this.anchor),n=t.nodeAfter;return n&&u.isSelectable(n)?new u(t):s.near(t)}}class f extends s{constructor(e){super(e.resolve(0),e.resolve(e.content.size))}replace(e,t=r.Ji.empty){if(t==r.Ji.empty){e.delete(0,e.doc.content.size);let t=s.atStart(e.doc);t.eq(e.selection)||e.setSelection(t)}else super.replace(e,t)}toJSON(){return{type:"all"}}static fromJSON(e){return new f(e)}map(e){return new f(e)}eq(e){return e instanceof f}getBookmark(){return g}}s.jsonID("all",f);let g={map(){return this},resolve:e=>new f(e)};function m(e,t,n,r,i,o=!1){if(t.inlineContent)return c.create(e,n);for(let s=r-(i>0?0:1);i>0?s<t.childCount:s>=0;s+=i){let r=t.child(s);if(r.isAtom){if(!o&&u.isSelectable(r))return u.create(e,n-(i<0?r.nodeSize:0))}else{let t=m(e,r,n+i,i<0?r.childCount:0,i,o);if(t)return t}n+=r.nodeSize*i}return null}function y(e,t,n){let r,o=e.steps.length-1;if(o<t)return;let l=e.steps[o];(l instanceof i.Ln||l instanceof i.Wg)&&(e.mapping.maps[o].forEach((e,t,n,i)=>{null==r&&(r=i)}),e.setSelection(s.near(e.doc.resolve(r),n)))}class b extends i.dL{constructor(e){super(e.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=e.selection,this.storedMarks=e.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(e){if(e.$from.doc!=this.doc)throw RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=(1|this.updated)&-3,this.storedMarks=null,this}get selectionSet(){return(1&this.updated)>0}setStoredMarks(e){return this.storedMarks=e,this.updated|=2,this}ensureMarks(e){return r.CU.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}addStoredMark(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(2&this.updated)>0}addStep(e,t){super.addStep(e,t),this.updated=-3&this.updated,this.storedMarks=null}setTime(e){return this.time=e,this}replaceSelection(e){return this.selection.replace(this,e),this}replaceSelectionWith(e,t=!0){let n=this.selection;return t&&(e=e.mark(this.storedMarks||(n.empty?n.$from.marks():n.$from.marksAcross(n.$to)||r.CU.none))),n.replaceWith(this,e),this}deleteSelection(){return this.selection.replace(this),this}insertText(e,t,n){let r=this.doc.type.schema;if(null==t)return e?this.replaceSelectionWith(r.text(e),!0):this.deleteSelection();{if(null==n&&(n=t),n=null==n?t:n,!e)return this.deleteRange(t,n);let i=this.storedMarks;if(!i){let e=this.doc.resolve(t);i=n==t?e.marks():e.marksAcross(this.doc.resolve(n))}return this.replaceRangeWith(t,n,r.text(e,i)),this.selection.empty||this.setSelection(s.near(this.selection.$to)),this}}setMeta(e,t){return this.meta["string"==typeof e?e:e.key]=t,this}getMeta(e){return this.meta["string"==typeof e?e:e.key]}get isGeneric(){for(let e in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=4,this}get scrolledIntoView(){return(4&this.updated)>0}}function w(e,t){return t&&e?e.bind(t):e}class k{constructor(e,t,n){this.name=e,this.init=w(t.init,n),this.apply=w(t.apply,n)}}let v=[new k("doc",{init:e=>e.doc||e.schema.topNodeType.createAndFill(),apply:e=>e.doc}),new k("selection",{init:(e,t)=>e.selection||s.atStart(t.doc),apply:e=>e.selection}),new k("storedMarks",{init:e=>e.storedMarks||null,apply:(e,t,n,r)=>r.selection.$cursor?e.storedMarks:null}),new k("scrollToSelection",{init:()=>0,apply:(e,t)=>e.scrolledIntoView?t+1:t})];class x{constructor(e,t){this.schema=e,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=v.slice(),t&&t.forEach(e=>{if(this.pluginsByKey[e.key])throw RangeError("Adding different instances of a keyed plugin ("+e.key+")");this.plugins.push(e),this.pluginsByKey[e.key]=e,e.spec.state&&this.fields.push(new k(e.key,e.spec.state,e))})}}class S{constructor(e){this.config=e}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(e){return this.applyTransaction(e).state}filterTransaction(e,t=-1){for(let n=0;n<this.config.plugins.length;n++)if(n!=t){let t=this.config.plugins[n];if(t.spec.filterTransaction&&!t.spec.filterTransaction.call(t,e,this))return!1}return!0}applyTransaction(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};let t=[e],n=this.applyInner(e),r=null;for(;;){let i=!1;for(let o=0;o<this.config.plugins.length;o++){let s=this.config.plugins[o];if(s.spec.appendTransaction){let l=r?r[o].n:0,a=r?r[o].state:this,h=l<t.length&&s.spec.appendTransaction.call(s,l?t.slice(l):t,a,n);if(h&&n.filterTransaction(h,o)){if(h.setMeta("appendedTransaction",e),!r){r=[];for(let e=0;e<this.config.plugins.length;e++)r.push(e<o?{state:n,n:t.length}:{state:this,n:0})}t.push(h),n=n.applyInner(h),i=!0}r&&(r[o]={state:n,n:t.length})}}if(!i)return{state:n,transactions:t}}}applyInner(e){if(!e.before.eq(this.doc))throw RangeError("Applying a mismatched transaction");let t=new S(this.config),n=this.config.fields;for(let r=0;r<n.length;r++){let i=n[r];t[i.name]=i.apply(e,this[i.name],this,t)}return t}get tr(){return new b(this)}static create(e){let t=new x(e.doc?e.doc.type.schema:e.schema,e.plugins),n=new S(t);for(let r=0;r<t.fields.length;r++)n[t.fields[r].name]=t.fields[r].init(e,n);return n}reconfigure(e){let t=new x(this.schema,e.plugins),n=t.fields,r=new S(t);for(let t=0;t<n.length;t++){let i=n[t].name;r[i]=this.hasOwnProperty(i)?this[i]:n[t].init(e,r)}return r}toJSON(e){let t={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(t.storedMarks=this.storedMarks.map(e=>e.toJSON())),e&&"object"==typeof e)for(let n in e){if("doc"==n||"selection"==n)throw RangeError("The JSON fields `doc` and `selection` are reserved");let r=e[n],i=r.spec.state;i&&i.toJSON&&(t[n]=i.toJSON.call(r,this[r.key]))}return t}static fromJSON(e,t,n){if(!t)throw RangeError("Invalid input for EditorState.fromJSON");if(!e.schema)throw RangeError("Required config field 'schema' missing");let i=new x(e.schema,e.plugins),o=new S(i);return i.fields.forEach(i=>{if("doc"==i.name)o.doc=r.bP.fromJSON(e.schema,t.doc);else if("selection"==i.name)o.selection=s.fromJSON(o.doc,t.selection);else if("storedMarks"==i.name)t.storedMarks&&(o.storedMarks=t.storedMarks.map(e.schema.markFromJSON));else{if(n)for(let r in n){let s=n[r],l=s.spec.state;if(s.key==i.name&&l&&l.fromJSON&&Object.prototype.hasOwnProperty.call(t,r)){o[i.name]=l.fromJSON.call(s,e,t[r],o);return}}o[i.name]=i.init(e,o)}}),o}}class M{constructor(e){this.spec=e,this.props={},e.props&&function e(t,n,r){for(let i in t){let o=t[i];o instanceof Function?o=o.bind(n):"handleDOMEvents"==i&&(o=e(o,n,{})),r[i]=o}return r}(e.props,this,this.props),this.key=e.key?e.key.key:E("plugin")}getState(e){return e[this.key]}}let C=Object.create(null);function E(e){return e in C?e+"$"+ ++C[e]:(C[e]=0,e+"$")}class A{constructor(e="key"){this.key=E(e)}get(e){return e.config.pluginsByKey[this.key]}getState(e){return e[this.key]}}},61514:(e,t,n)=>{"use strict";n.d(t,{$B:()=>c,Sd:()=>a,T2:()=>h});var r=n(808),i=n(10156);let o=["ol",0],s=["ul",0],l=["li",0];function a(e,t=null){return function(n,o){let{$from:s,$to:l}=n.selection,a=s.blockRange(l);if(!a)return!1;let h=o?n.tr:null;return!!function(e,t,n,o=null){let s=!1,l=t,a=t.$from.doc;if(t.depth>=2&&t.$from.node(t.depth-1).type.compatibleContent(n)&&0==t.startIndex){if(0==t.$from.index(t.depth-1))return!1;let e=a.resolve(t.start-2);l=new i.u$(e,e,t.depth),t.endIndex<t.parent.childCount&&(t=new i.u$(t.$from,a.resolve(t.$to.end(t.depth)),t.depth)),s=!0}let h=(0,r.oM)(l,n,o,t);return!!h&&(e&&function(e,t,n,o,s){let l=i.FK.empty;for(let e=n.length-1;e>=0;e--)l=i.FK.from(n[e].type.create(n[e].attrs,l));e.step(new r.Wg(t.start-2*!!o,t.end,t.start,t.end,new i.Ji(l,0,0),n.length,!0));let a=0;for(let e=0;e<n.length;e++)n[e].type==s&&(a=e+1);let h=n.length-a,c=t.start+n.length-2*!!o,d=t.parent;for(let n=t.startIndex,i=t.endIndex,o=!0;n<i;n++,o=!1)!o&&(0,r.zy)(e.doc,c,h)&&(e.split(c,h),c+=2*h),c+=d.child(n).nodeSize}(e,t,h,s,n),!0)}(h,a,e,t)&&(o&&o(h.scrollIntoView()),!0)}}function h(e){return function(t,n){let{$from:o,$to:s}=t.selection,l=o.blockRange(s,t=>t.childCount>0&&t.firstChild.type==e);return!!l&&(!n||(o.node(l.depth-1).type==e?function(e,t,n,o){let s=e.tr,l=o.end,a=o.$to.end(o.depth);l<a&&(s.step(new r.Wg(l-1,a,l,a,new i.Ji(i.FK.from(n.create(null,o.parent.copy())),1,0),1,!0)),o=new i.u$(s.doc.resolve(o.$from.pos),s.doc.resolve(a),o.depth));let h=(0,r.jP)(o);if(null==h)return!1;s.lift(o,h);let c=s.doc.resolve(s.mapping.map(l,-1)-1);return(0,r.n9)(s.doc,c.pos)&&c.nodeBefore.type==c.nodeAfter.type&&s.join(c.pos),t(s.scrollIntoView()),!0}(t,n,e,l):function(e,t,n){let o=e.tr,s=n.parent;for(let e=n.end,t=n.endIndex-1,r=n.startIndex;t>r;t--)e-=s.child(t).nodeSize,o.delete(e-1,e+1);let l=o.doc.resolve(n.start),a=l.nodeAfter;if(o.mapping.map(n.end)!=n.start+l.nodeAfter.nodeSize)return!1;let h=0==n.startIndex,c=n.endIndex==s.childCount,d=l.node(-1),u=l.index(-1);if(!d.canReplace(u+ +!h,u+1,a.content.append(c?i.FK.empty:i.FK.from(s))))return!1;let p=l.pos,f=p+a.nodeSize;return o.step(new r.Wg(p-!!h,f+ +!!c,p+1,f-1,new i.Ji((h?i.FK.empty:i.FK.from(s.copy(i.FK.empty))).append(c?i.FK.empty:i.FK.from(s.copy(i.FK.empty))),+!h,+!c),+!h)),t(o.scrollIntoView()),!0}(t,n,l)))}}function c(e){return function(t,n){let{$from:o,$to:s}=t.selection,l=o.blockRange(s,t=>t.childCount>0&&t.firstChild.type==e);if(!l)return!1;let a=l.startIndex;if(0==a)return!1;let h=l.parent,c=h.child(a-1);if(c.type!=e)return!1;if(n){let o=c.lastChild&&c.lastChild.type==h.type,s=i.FK.from(o?e.create():null),a=new i.Ji(i.FK.from(e.create(null,i.FK.from(h.type.create(null,s)))),o?3:1,0),d=l.start,u=l.end;n(t.tr.step(new r.Wg(d-(o?3:1),u,d,u,a,1,!0)).scrollIntoView())}return!0}}},65112:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]])},66377:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(4701);let i=e=>{if(!e.children.length)return;let t=e.querySelectorAll("span");t&&t.forEach(e=>{var t,n;let r=e.getAttribute("style"),i=null==(n=null==(t=e.parentElement)?void 0:t.closest("span"))?void 0:n.getAttribute("style");e.setAttribute("style",`${i};${r}`)})},o=r.CU.create({name:"textStyle",priority:101,addOptions:()=>({HTMLAttributes:{},mergeNestedSpanStyles:!1}),parseHTML(){return[{tag:"span",getAttrs:e=>!!e.hasAttribute("style")&&(this.options.mergeNestedSpanStyles&&i(e),{})}]},renderHTML({HTMLAttributes:e}){return["span",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{removeEmptyTextStyle:()=>({tr:e})=>{let{selection:t}=e;return e.doc.nodesBetween(t.from,t.to,(t,n)=>{if(t.isTextblock)return!0;t.marks.filter(e=>e.type===this.type).some(e=>Object.values(e.attrs).some(e=>!!e))||e.removeMark(n,n+t.nodeSize,this.type)}),!0}}}})},74347:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("highlighter",[["path",{d:"m9 11-6 6v3h9l3-3",key:"1a3l36"}],["path",{d:"m22 12-4.6 4.6a2 2 0 0 1-2.8 0l-5.2-5.2a2 2 0 0 1 0-2.8L14 4",key:"14a9rk"}]])},75109:(e,t,n)=>{"use strict";n.d(t,{$Z:()=>g,hG:()=>C});var r,i,o=n(12115),s=n(47650),l=n(4701),a={exports:{}},h={};a.exports=function(){if(r)return h;r=1;var e="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},t=o.useState,n=o.useEffect,i=o.useLayoutEffect,s=o.useDebugValue;function l(t){var n=t.getSnapshot;t=t.value;try{var r=n();return!e(t,r)}catch(e){return!0}}var a="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,r){var o=r(),a=t({inst:{value:o,getSnapshot:r}}),h=a[0].inst,c=a[1];return i(function(){h.value=o,h.getSnapshot=r,l(h)&&c({inst:h})},[e,o,r]),n(function(){return l(h)&&c({inst:h}),e(function(){l(h)&&c({inst:h})})},[e]),s(o),o};return h.useSyncExternalStore=void 0!==o.useSyncExternalStore?o.useSyncExternalStore:a,h}();var c=a.exports;let d=(...e)=>t=>{e.forEach(e=>{"function"==typeof e?e(t):e&&(e.current=t)})},u=({contentComponent:e})=>{let t=c.useSyncExternalStore(e.subscribe,e.getSnapshot,e.getServerSnapshot);return o.createElement(o.Fragment,null,Object.values(t))};class p extends o.Component{constructor(e){var t;super(e),this.editorContentRef=o.createRef(),this.initialized=!1,this.state={hasContentComponentInitialized:!!(null==(t=e.editor)?void 0:t.contentComponent)}}componentDidMount(){this.init()}componentDidUpdate(){this.init()}init(){let e=this.props.editor;if(e&&!e.isDestroyed&&e.options.element){if(e.contentComponent)return;let t=this.editorContentRef.current;t.append(...e.options.element.childNodes),e.setOptions({element:t}),e.contentComponent=function(){let e=new Set,t={};return{subscribe:t=>(e.add(t),()=>{e.delete(t)}),getSnapshot:()=>t,getServerSnapshot:()=>t,setRenderer(n,r){t={...t,[n]:s.createPortal(r.reactElement,r.element,n)},e.forEach(e=>e())},removeRenderer(n){let r={...t};delete r[n],t=r,e.forEach(e=>e())}}}(),this.state.hasContentComponentInitialized||(this.unsubscribeToContentComponent=e.contentComponent.subscribe(()=>{this.setState(e=>e.hasContentComponentInitialized?e:{hasContentComponentInitialized:!0}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent()})),e.createNodeViews(),this.initialized=!0}}componentWillUnmount(){let e=this.props.editor;if(!e||(this.initialized=!1,e.isDestroyed||e.view.setProps({nodeViews:{}}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent(),e.contentComponent=null,!e.options.element.firstChild))return;let t=document.createElement("div");t.append(...e.options.element.childNodes),e.setOptions({element:t})}render(){let{editor:e,innerRef:t,...n}=this.props;return o.createElement(o.Fragment,null,o.createElement("div",{ref:d(t,this.editorContentRef),...n}),(null==e?void 0:e.contentComponent)&&o.createElement(u,{contentComponent:e.contentComponent}))}}let f=(0,o.forwardRef)((e,t)=>{let n=o.useMemo(()=>Math.floor(0xffffffff*Math.random()).toString(),[e.editor]);return o.createElement(p,{key:n,innerRef:t,...e})}),g=o.memo(f);var m=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;if(Array.isArray(t)){if((r=t.length)!=n.length)return!1;for(i=r;0!=i--;)if(!e(t[i],n[i]))return!1;return!0}if(t instanceof Map&&n instanceof Map){if(t.size!==n.size)return!1;for(i of t.entries())if(!n.has(i[0]))return!1;for(i of t.entries())if(!e(i[1],n.get(i[0])))return!1;return!0}if(t instanceof Set&&n instanceof Set){if(t.size!==n.size)return!1;for(i of t.entries())if(!n.has(i[0]))return!1;return!0}if(ArrayBuffer.isView(t)&&ArrayBuffer.isView(n)){if((r=t.length)!=n.length)return!1;for(i=r;0!=i--;)if(t[i]!==n[i])return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((r=(o=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(i=r;0!=i--;)if(!Object.prototype.hasOwnProperty.call(n,o[i]))return!1;for(i=r;0!=i--;){var r,i,o,s=o[i];if(("_owner"!==s||!t.$$typeof)&&!e(t[s],n[s]))return!1}return!0}return t!=t&&n!=n}),y={exports:{}},b={};y.exports=function(){if(i)return b;i=1;var e="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},t=c.useSyncExternalStore,n=o.useRef,r=o.useEffect,s=o.useMemo,l=o.useDebugValue;return b.useSyncExternalStoreWithSelector=function(i,o,a,h,c){var d=n(null);if(null===d.current){var u={hasValue:!1,value:null};d.current=u}else u=d.current;var p=t(i,(d=s(function(){function t(t){if(!i){if(i=!0,n=t,t=h(t),void 0!==c&&u.hasValue){var o=u.value;if(c(o,t))return r=o}return r=t}if(o=r,e(n,t))return o;var s=h(t);return void 0!==c&&c(o,s)?o:(n=t,r=s)}var n,r,i=!1,s=void 0===a?null:a;return[function(){return t(o())},null===s?void 0:function(){return t(s())}]},[o,a,h,c]))[0],d[1]);return r(function(){u.hasValue=!0,u.value=p},[p]),l(p),p},b}();var w=y.exports;let k="undefined"!=typeof window?o.useLayoutEffect:o.useEffect;class v{constructor(e){this.transactionNumber=0,this.lastTransactionNumber=0,this.subscribers=new Set,this.editor=e,this.lastSnapshot={editor:e,transactionNumber:0},this.getSnapshot=this.getSnapshot.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.watch=this.watch.bind(this),this.subscribe=this.subscribe.bind(this)}getSnapshot(){return this.transactionNumber===this.lastTransactionNumber||(this.lastTransactionNumber=this.transactionNumber,this.lastSnapshot={editor:this.editor,transactionNumber:this.transactionNumber}),this.lastSnapshot}getServerSnapshot(){return{editor:null,transactionNumber:0}}subscribe(e){return this.subscribers.add(e),()=>{this.subscribers.delete(e)}}watch(e){if(this.editor=e,this.editor){let e=()=>{this.transactionNumber+=1,this.subscribers.forEach(e=>e())},t=this.editor;return t.on("transaction",e),()=>{t.off("transaction",e)}}}}let x="undefined"==typeof window,S=x||!!("undefined"!=typeof window&&window.next);class M{constructor(e){this.editor=null,this.subscriptions=new Set,this.isComponentMounted=!1,this.previousDeps=null,this.instanceId="",this.options=e,this.subscriptions=new Set,this.setEditor(this.getInitialEditor()),this.scheduleDestroy(),this.getEditor=this.getEditor.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.subscribe=this.subscribe.bind(this),this.refreshEditorInstance=this.refreshEditorInstance.bind(this),this.scheduleDestroy=this.scheduleDestroy.bind(this),this.onRender=this.onRender.bind(this),this.createEditor=this.createEditor.bind(this)}setEditor(e){this.editor=e,this.instanceId=Math.random().toString(36).slice(2,9),this.subscriptions.forEach(e=>e())}getInitialEditor(){return void 0===this.options.current.immediatelyRender?x||S?null:this.createEditor():(this.options.current.immediatelyRender,this.options.current.immediatelyRender?this.createEditor():null)}createEditor(){let e={...this.options.current,onBeforeCreate:(...e)=>{var t,n;return null==(n=(t=this.options.current).onBeforeCreate)?void 0:n.call(t,...e)},onBlur:(...e)=>{var t,n;return null==(n=(t=this.options.current).onBlur)?void 0:n.call(t,...e)},onCreate:(...e)=>{var t,n;return null==(n=(t=this.options.current).onCreate)?void 0:n.call(t,...e)},onDestroy:(...e)=>{var t,n;return null==(n=(t=this.options.current).onDestroy)?void 0:n.call(t,...e)},onFocus:(...e)=>{var t,n;return null==(n=(t=this.options.current).onFocus)?void 0:n.call(t,...e)},onSelectionUpdate:(...e)=>{var t,n;return null==(n=(t=this.options.current).onSelectionUpdate)?void 0:n.call(t,...e)},onTransaction:(...e)=>{var t,n;return null==(n=(t=this.options.current).onTransaction)?void 0:n.call(t,...e)},onUpdate:(...e)=>{var t,n;return null==(n=(t=this.options.current).onUpdate)?void 0:n.call(t,...e)},onContentError:(...e)=>{var t,n;return null==(n=(t=this.options.current).onContentError)?void 0:n.call(t,...e)},onDrop:(...e)=>{var t,n;return null==(n=(t=this.options.current).onDrop)?void 0:n.call(t,...e)},onPaste:(...e)=>{var t,n;return null==(n=(t=this.options.current).onPaste)?void 0:n.call(t,...e)}};return new l.KE(e)}getEditor(){return this.editor}getServerSnapshot(){return null}subscribe(e){return this.subscriptions.add(e),()=>{this.subscriptions.delete(e)}}static compareOptions(e,t){return Object.keys(e).every(n=>!!["onCreate","onBeforeCreate","onDestroy","onUpdate","onTransaction","onFocus","onBlur","onSelectionUpdate","onContentError","onDrop","onPaste"].includes(n)||("extensions"===n&&e.extensions&&t.extensions?e.extensions.length===t.extensions.length&&e.extensions.every((e,n)=>{var r;return e===(null==(r=t.extensions)?void 0:r[n])}):e[n]===t[n]))}onRender(e){return()=>(this.isComponentMounted=!0,clearTimeout(this.scheduledDestructionTimeout),this.editor&&!this.editor.isDestroyed&&0===e.length?M.compareOptions(this.options.current,this.editor.options)||this.editor.setOptions({...this.options.current,editable:this.editor.isEditable}):this.refreshEditorInstance(e),()=>{this.isComponentMounted=!1,this.scheduleDestroy()})}refreshEditorInstance(e){if(this.editor&&!this.editor.isDestroyed){if(null===this.previousDeps){this.previousDeps=e;return}if(this.previousDeps.length===e.length&&this.previousDeps.every((t,n)=>t===e[n]))return}this.editor&&!this.editor.isDestroyed&&this.editor.destroy(),this.setEditor(this.createEditor()),this.previousDeps=e}scheduleDestroy(){let e=this.instanceId,t=this.editor;this.scheduledDestructionTimeout=setTimeout(()=>{if(this.isComponentMounted&&this.instanceId===e){t&&t.setOptions(this.options.current);return}t&&!t.isDestroyed&&(t.destroy(),this.instanceId===e&&this.setEditor(null))},1)}}function C(e={},t=[]){let n=(0,o.useRef)(e);n.current=e;let[r]=(0,o.useState)(()=>new M(n)),i=c.useSyncExternalStore(r.subscribe,r.getEditor,r.getServerSnapshot);return(0,o.useDebugValue)(i),(0,o.useEffect)(r.onRender(t)),!function(e){var t;let[n]=(0,o.useState)(()=>new v(e.editor)),r=w.useSyncExternalStoreWithSelector(n.subscribe,n.getSnapshot,n.getServerSnapshot,e.selector,null!=(t=e.equalityFn)?t:m);k(()=>n.watch(e.editor),[e.editor,n]),(0,o.useDebugValue)(r)}({editor:i,selector:({transactionNumber:t})=>!1===e.shouldRerenderOnTransaction?null:e.immediatelyRender&&0===t?0:t+1}),i}let E=((0,o.createContext)({editor:null}).Consumer,(0,o.createContext)({onDragStart:void 0})),A=()=>(0,o.useContext)(E);function T(e){return!!("function"==typeof e&&e.prototype&&e.prototype.isReactComponent)}function O(e){return!!("object"==typeof e&&e.$$typeof&&("Symbol(react.forward_ref)"===e.$$typeof.toString()||"react.forward_ref"===e.$$typeof.description))}o.forwardRef((e,t)=>{let{onDragStart:n}=A(),r=e.as||"div";return o.createElement(r,{...e,ref:t,"data-node-view-wrapper":"",onDragStart:n,style:{whiteSpace:"normal",...e.style}})});class R{constructor(e,{editor:t,props:n={},as:r="div",className:i=""}){this.ref=null,this.id=Math.floor(0xffffffff*Math.random()).toString(),this.component=e,this.editor=t,this.props=n,this.element=document.createElement(r),this.element.classList.add("react-renderer"),i&&this.element.classList.add(...i.split(" ")),this.editor.isInitialized?flushSync(()=>{this.render()}):this.render()}render(){var e;let t=this.component,n=this.props,r=this.editor,i=function(){try{if(version)return parseInt(version.split(".")[0],10)>=19}catch{}return!1}(),o=function(e){if(T(e)||O(e))return!0;if("object"==typeof e&&e.$$typeof&&("Symbol(react.memo)"===e.$$typeof.toString()||"react.memo"===e.$$typeof.description)){let t=e.type;if(t)return T(t)||O(t)}return!1}(t),s={...n};s.ref&&!(i||o)&&delete s.ref,!s.ref&&(i||o)&&(s.ref=e=>{this.ref=e}),this.reactElement=React.createElement(t,{...s}),null==(e=null==r?void 0:r.contentComponent)||e.setRenderer(this.id,this)}updateProps(e={}){this.props={...this.props,...e},this.render()}destroy(){var e;let t=this.editor;null==(e=null==t?void 0:t.contentComponent)||e.removeRenderer(this.id)}updateAttributes(e){Object.keys(e).forEach(t=>{this.element.setAttribute(t,e[t])})}}},79030:(e,t,n)=>{"use strict";n.d(t,{h:()=>i});var r=n(4701);let i=r.bP.create({name:"tableHeader",addOptions:()=>({HTMLAttributes:{}}),content:"block+",addAttributes:()=>({colspan:{default:1},rowspan:{default:1},colwidth:{default:null,parseHTML:e=>{let t=e.getAttribute("colwidth");return t?t.split(",").map(e=>parseInt(e,10)):null}}}),tableRole:"header_cell",isolating:!0,parseHTML:()=>[{tag:"th"}],renderHTML({HTMLAttributes:e}){return["th",(0,r.KV)(this.options.HTMLAttributes,e),0]}})},80353:(e,t,n)=>{"use strict";n.d(t,{A:()=>eg});var r=n(4701);let i=/^\s*>\s$/,o=r.bP.create({name:"blockquote",addOptions:()=>({HTMLAttributes:{}}),content:"block+",group:"block",defining:!0,parseHTML:()=>[{tag:"blockquote"}],renderHTML({HTMLAttributes:e}){return["blockquote",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setBlockquote:()=>({commands:e})=>e.wrapIn(this.name),toggleBlockquote:()=>({commands:e})=>e.toggleWrap(this.name),unsetBlockquote:()=>({commands:e})=>e.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[(0,r.tG)({find:i,type:this.type})]}}),s=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,l=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,a=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,h=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,c=r.CU.create({name:"bold",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:e=>"normal"!==e.style.fontWeight&&null},{style:"font-weight=400",clearMark:e=>e.type.name===this.name},{style:"font-weight",getAttrs:e=>/^(bold(er)?|[5-9]\d{2,})$/.test(e)&&null}]},renderHTML({HTMLAttributes:e}){return["strong",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setBold:()=>({commands:e})=>e.setMark(this.name),toggleBold:()=>({commands:e})=>e.toggleMark(this.name),unsetBold:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[(0,r.OX)({find:s,type:this.type}),(0,r.OX)({find:a,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:l,type:this.type}),(0,r.Zc)({find:h,type:this.type})]}}),d="textStyle",u=/^\s*([-+*])\s$/,p=r.bP.create({name:"bulletList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML:()=>[{tag:"ul"}],renderHTML({HTMLAttributes:e}){return["ul",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{toggleBulletList:()=>({commands:e,chain:t})=>this.options.keepAttributes?t().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(d)).run():e.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let e=(0,r.tG)({find:u,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(e=(0,r.tG)({find:u,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(d),editor:this.editor})),[e]}}),f=/(^|[^`])`([^`]+)`(?!`)/,g=/(^|[^`])`([^`]+)`(?!`)/g,m=r.CU.create({name:"code",addOptions:()=>({HTMLAttributes:{}}),excludes:"_",code:!0,exitable:!0,parseHTML:()=>[{tag:"code"}],renderHTML({HTMLAttributes:e}){return["code",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setCode:()=>({commands:e})=>e.setMark(this.name),toggleCode:()=>({commands:e})=>e.toggleMark(this.name),unsetCode:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[(0,r.OX)({find:f,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:g,type:this.type})]}});var y=n(35383);let b=r.bP.create({name:"doc",topNode:!0,content:"block+"});var w=n(52571),k=n(808);class v{constructor(e,t){var n;this.editorView=e,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=null!=(n=t.width)?n:1,this.color=!1===t.color?void 0:t.color||"black",this.class=t.class,this.handlers=["dragover","dragend","drop","dragleave"].map(t=>{let n=e=>{this[t](e)};return e.dom.addEventListener(t,n),{name:t,handler:n}})}destroy(){this.handlers.forEach(({name:e,handler:t})=>this.editorView.dom.removeEventListener(e,t))}update(e,t){null!=this.cursorPos&&t.doc!=e.state.doc&&(this.cursorPos>e.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(e){e!=this.cursorPos&&(this.cursorPos=e,null==e?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let e,t,n=this.editorView.state.doc.resolve(this.cursorPos),r=!n.parent.inlineContent,i,o=this.editorView.dom,s=o.getBoundingClientRect(),l=s.width/o.offsetWidth,a=s.height/o.offsetHeight;if(r){let e=n.nodeBefore,t=n.nodeAfter;if(e||t){let n=this.editorView.nodeDOM(this.cursorPos-(e?e.nodeSize:0));if(n){let r=n.getBoundingClientRect(),o=e?r.bottom:r.top;e&&t&&(o=(o+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2);let s=this.width/2*a;i={left:r.left,right:r.right,top:o-s,bottom:o+s}}}}if(!i){let e=this.editorView.coordsAtPos(this.cursorPos),t=this.width/2*l;i={left:e.left-t,right:e.left+t,top:e.top,bottom:e.bottom}}let h=this.editorView.dom.offsetParent;if(!this.element&&(this.element=h.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",r),this.element.classList.toggle("prosemirror-dropcursor-inline",!r),h&&(h!=document.body||"static"!=getComputedStyle(h).position)){let n=h.getBoundingClientRect(),r=n.width/h.offsetWidth,i=n.height/h.offsetHeight;e=n.left-h.scrollLeft*r,t=n.top-h.scrollTop*i}else e=-pageXOffset,t=-pageYOffset;this.element.style.left=(i.left-e)/l+"px",this.element.style.top=(i.top-t)/a+"px",this.element.style.width=(i.right-i.left)/l+"px",this.element.style.height=(i.bottom-i.top)/a+"px"}scheduleRemoval(e){clearTimeout(this.timeout),this.timeout=setTimeout(()=>this.setCursor(null),e)}dragover(e){if(!this.editorView.editable)return;let t=this.editorView.posAtCoords({left:e.clientX,top:e.clientY}),n=t&&t.inside>=0&&this.editorView.state.doc.nodeAt(t.inside),r=n&&n.type.spec.disableDropCursor,i="function"==typeof r?r(this.editorView,t,e):r;if(t&&!i){let e=t.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let t=(0,k.Um)(this.editorView.state.doc,e,this.editorView.dragging.slice);null!=t&&(e=t)}this.setCursor(e),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(e){this.editorView.dom.contains(e.relatedTarget)||this.setCursor(null)}}let x=r.YY.create({name:"dropCursor",addOptions:()=>({color:"currentColor",width:1,class:void 0}),addProseMirrorPlugins(){return[function(e={}){return new w.k_({view:t=>new v(t,e)})}(this.options)]}});var S=n(96770),M=n(10156),C=n(42695);class E extends w.LN{constructor(e){super(e,e)}map(e,t){let n=e.resolve(t.map(this.head));return E.valid(n)?new E(n):w.LN.near(n)}content(){return M.Ji.empty}eq(e){return e instanceof E&&e.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(e,t){if("number"!=typeof t.pos)throw RangeError("Invalid input for GapCursor.fromJSON");return new E(e.resolve(t.pos))}getBookmark(){return new A(this.anchor)}static valid(e){let t=e.parent;if(t.isTextblock||!function(e){for(let t=e.depth;t>=0;t--){let n=e.index(t),r=e.node(t);if(0==n){if(r.type.spec.isolating)return!0;continue}for(let e=r.child(n-1);;e=e.lastChild){if(0==e.childCount&&!e.inlineContent||e.isAtom||e.type.spec.isolating)return!0;if(e.inlineContent)return!1}}return!0}(e)||!function(e){for(let t=e.depth;t>=0;t--){let n=e.indexAfter(t),r=e.node(t);if(n==r.childCount){if(r.type.spec.isolating)return!0;continue}for(let e=r.child(n);;e=e.firstChild){if(0==e.childCount&&!e.inlineContent||e.isAtom||e.type.spec.isolating)return!0;if(e.inlineContent)return!1}}return!0}(e))return!1;let n=t.type.spec.allowGapCursor;if(null!=n)return n;let r=t.contentMatchAt(e.index()).defaultType;return r&&r.isTextblock}static findGapCursorFrom(e,t,n=!1){n:for(;;){if(!n&&E.valid(e))return e;let r=e.pos,i=null;for(let n=e.depth;;n--){let o=e.node(n);if(t>0?e.indexAfter(n)<o.childCount:e.index(n)>0){i=o.child(t>0?e.indexAfter(n):e.index(n)-1);break}if(0==n)return null;r+=t;let s=e.doc.resolve(r);if(E.valid(s))return s}for(;;){let o=t>0?i.firstChild:i.lastChild;if(!o){if(i.isAtom&&!i.isText&&!w.nh.isSelectable(i)){e=e.doc.resolve(r+i.nodeSize*t),n=!1;continue n}break}i=o,r+=t;let s=e.doc.resolve(r);if(E.valid(s))return s}return null}}}E.prototype.visible=!1,E.findFrom=E.findGapCursorFrom,w.LN.jsonID("gapcursor",E);class A{constructor(e){this.pos=e}map(e){return new A(e.map(this.pos))}resolve(e){let t=e.resolve(this.pos);return E.valid(t)?new E(t):w.LN.near(t)}}let T=(0,S.K)({ArrowLeft:O("horiz",-1),ArrowRight:O("horiz",1),ArrowUp:O("vert",-1),ArrowDown:O("vert",1)});function O(e,t){let n="vert"==e?t>0?"down":"up":t>0?"right":"left";return function(e,r,i){let o=e.selection,s=t>0?o.$to:o.$from,l=o.empty;if(o instanceof w.U3){if(!i.endOfTextblock(n)||0==s.depth)return!1;l=!1,s=e.doc.resolve(t>0?s.after():s.before())}let a=E.findGapCursorFrom(s,t,l);return!!a&&(r&&r(e.tr.setSelection(new E(a))),!0)}}function R(e,t,n){if(!e||!e.editable)return!1;let r=e.state.doc.resolve(t);if(!E.valid(r))return!1;let i=e.posAtCoords({left:n.clientX,top:n.clientY});return!(i&&i.inside>-1&&w.nh.isSelectable(e.state.doc.nodeAt(i.inside)))&&(e.dispatch(e.state.tr.setSelection(new E(r))),!0)}function N(e,t){if("insertCompositionText"!=t.inputType||!(e.state.selection instanceof E))return!1;let{$from:n}=e.state.selection,r=n.parent.contentMatchAt(n.index()).findWrapping(e.state.schema.nodes.text);if(!r)return!1;let i=M.FK.empty;for(let e=r.length-1;e>=0;e--)i=M.FK.from(r[e].createAndFill(null,i));let o=e.state.tr.replace(n.pos,n.pos,new M.Ji(i,0,0));return o.setSelection(w.U3.near(o.doc.resolve(n.pos+1))),e.dispatch(o),!1}function I(e){if(!(e.selection instanceof E))return null;let t=document.createElement("div");return t.className="ProseMirror-gapcursor",C.zF.create(e.doc,[C.NZ.widget(e.selection.head,t,{key:"gapcursor"})])}let L=r.YY.create({name:"gapCursor",addProseMirrorPlugins:()=>[new w.k_({props:{decorations:I,createSelectionBetween:(e,t,n)=>t.pos==n.pos&&E.valid(n)?new E(n):null,handleClick:R,handleKeyDown:T,handleDOMEvents:{beforeinput:N}}})],extendNodeSchema(e){var t;let n={name:e.name,options:e.options,storage:e.storage};return{allowGapCursor:null!=(t=(0,r.gk)((0,r.iI)(e,"allowGapCursor",n)))?t:null}}}),_=r.bP.create({name:"hardBreak",addOptions:()=>({keepMarks:!0,HTMLAttributes:{}}),inline:!0,group:"inline",selectable:!1,linebreakReplacement:!0,parseHTML:()=>[{tag:"br"}],renderHTML({HTMLAttributes:e}){return["br",(0,r.KV)(this.options.HTMLAttributes,e)]},renderText:()=>"\n",addCommands(){return{setHardBreak:()=>({commands:e,chain:t,state:n,editor:r})=>e.first([()=>e.exitCode(),()=>e.command(()=>{let{selection:e,storedMarks:i}=n;if(e.$from.parent.type.spec.isolating)return!1;let{keepMarks:o}=this.options,{splittableMarks:s}=r.extensionManager,l=i||e.$to.parentOffset&&e.$from.marks();return t().insertContent({type:this.name}).command(({tr:e,dispatch:t})=>{if(t&&l&&o){let t=l.filter(e=>s.includes(e.type.name));e.ensureMarks(t)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),z=r.bP.create({name:"heading",addOptions:()=>({levels:[1,2,3,4,5,6],HTMLAttributes:{}}),content:"inline*",group:"block",defining:!0,addAttributes:()=>({level:{default:1,rendered:!1}}),parseHTML(){return this.options.levels.map(e=>({tag:`h${e}`,attrs:{level:e}}))},renderHTML({node:e,HTMLAttributes:t}){let n=this.options.levels.includes(e.attrs.level)?e.attrs.level:this.options.levels[0];return[`h${n}`,(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setHeading:e=>({commands:t})=>!!this.options.levels.includes(e.level)&&t.setNode(this.name,e),toggleHeading:e=>({commands:t})=>!!this.options.levels.includes(e.level)&&t.toggleNode(this.name,"paragraph",e)}},addKeyboardShortcuts(){return this.options.levels.reduce((e,t)=>({...e,...{[`Mod-Alt-${t}`]:()=>this.editor.commands.toggleHeading({level:t})}}),{})},addInputRules(){return this.options.levels.map(e=>(0,r.JJ)({find:RegExp(`^(#{${Math.min(...this.options.levels)},${e}})\\s$`),type:this.type,getAttributes:{level:e}}))}});var $=function(){};$.prototype.append=function(e){return e.length?(e=$.from(e),!this.length&&e||e.length<200&&this.leafAppend(e)||this.length<200&&e.leafPrepend(this)||this.appendInner(e)):this},$.prototype.prepend=function(e){return e.length?$.from(e).append(this):this},$.prototype.appendInner=function(e){return new B(this,e)},$.prototype.slice=function(e,t){return(void 0===e&&(e=0),void 0===t&&(t=this.length),e>=t)?$.empty:this.sliceInner(Math.max(0,e),Math.min(this.length,t))},$.prototype.get=function(e){if(!(e<0)&&!(e>=this.length))return this.getInner(e)},$.prototype.forEach=function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=this.length),t<=n?this.forEachInner(e,t,n,0):this.forEachInvertedInner(e,t,n,0)},$.prototype.map=function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=this.length);var r=[];return this.forEach(function(t,n){return r.push(e(t,n))},t,n),r},$.from=function(e){return e instanceof $?e:e&&e.length?new j(e):$.empty};var j=function(e){function t(t){e.call(this),this.values=t}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var n={length:{configurable:!0},depth:{configurable:!0}};return t.prototype.flatten=function(){return this.values},t.prototype.sliceInner=function(e,n){return 0==e&&n==this.length?this:new t(this.values.slice(e,n))},t.prototype.getInner=function(e){return this.values[e]},t.prototype.forEachInner=function(e,t,n,r){for(var i=t;i<n;i++)if(!1===e(this.values[i],r+i))return!1},t.prototype.forEachInvertedInner=function(e,t,n,r){for(var i=t-1;i>=n;i--)if(!1===e(this.values[i],r+i))return!1},t.prototype.leafAppend=function(e){if(this.length+e.length<=200)return new t(this.values.concat(e.flatten()))},t.prototype.leafPrepend=function(e){if(this.length+e.length<=200)return new t(e.flatten().concat(this.values))},n.length.get=function(){return this.values.length},n.depth.get=function(){return 0},Object.defineProperties(t.prototype,n),t}($);$.empty=new j([]);var B=function(e){function t(t,n){e.call(this),this.left=t,this.right=n,this.length=t.length+n.length,this.depth=Math.max(t.depth,n.depth)+1}return e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t,t.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},t.prototype.getInner=function(e){return e<this.left.length?this.left.get(e):this.right.get(e-this.left.length)},t.prototype.forEachInner=function(e,t,n,r){var i=this.left.length;if(t<i&&!1===this.left.forEachInner(e,t,Math.min(n,i),r)||n>i&&!1===this.right.forEachInner(e,Math.max(t-i,0),Math.min(this.length,n)-i,r+i))return!1},t.prototype.forEachInvertedInner=function(e,t,n,r){var i=this.left.length;if(t>i&&!1===this.right.forEachInvertedInner(e,t-i,Math.max(n,i)-i,r+i)||n<i&&!1===this.left.forEachInvertedInner(e,Math.min(t,i),n,r))return!1},t.prototype.sliceInner=function(e,t){if(0==e&&t==this.length)return this;var n=this.left.length;return t<=n?this.left.slice(e,t):e>=n?this.right.slice(e-n,t-n):this.left.slice(e,n).append(this.right.slice(0,t-n))},t.prototype.leafAppend=function(e){var n=this.right.leafAppend(e);if(n)return new t(this.left,n)},t.prototype.leafPrepend=function(e){var n=this.left.leafPrepend(e);if(n)return new t(n,this.right)},t.prototype.appendInner=function(e){return this.left.depth>=Math.max(this.right.depth,e.depth)+1?new t(this.left,new t(this.right,e)):new t(this,e)},t}($);class H{constructor(e,t){this.items=e,this.eventCount=t}popEvent(e,t){let n,r,i,o;if(0==this.eventCount)return null;let s=this.items.length;for(;;s--)if(this.items.get(s-1).selection){--s;break}t&&(r=(n=this.remapping(s,this.items.length)).maps.length);let l=e.tr,a=[],h=[];return this.items.forEach((e,t)=>{if(!e.step){n||(r=(n=this.remapping(s,t+1)).maps.length),r--,h.push(e);return}if(n){h.push(new D(e.map));let t=e.step.map(n.slice(r)),i;t&&l.maybeStep(t).doc&&(i=l.mapping.maps[l.mapping.maps.length-1],a.push(new D(i,void 0,void 0,a.length+h.length))),r--,i&&n.appendMap(i,r)}else l.maybeStep(e.step);if(e.selection)return i=n?e.selection.map(n.slice(r)):e.selection,o=new H(this.items.slice(0,s).append(h.reverse().concat(a)),this.eventCount-1),!1},this.items.length,0),{remaining:o,transform:l,selection:i}}addTransform(e,t,n,r){var i,o;let s,l=[],a=this.eventCount,h=this.items,c=!r&&h.length?h.get(h.length-1):null;for(let n=0;n<e.steps.length;n++){let i=e.steps[n].invert(e.docs[n]),o=new D(e.mapping.maps[n],i,t),s;(s=c&&c.merge(o))&&(o=s,n?l.pop():h=h.slice(0,h.length-1)),l.push(o),t&&(a++,t=void 0),r||(c=o)}let d=a-n.depth;return d>F&&(i=h,o=d,i.forEach((e,t)=>{if(e.selection&&0==o--)return s=t,!1}),h=i.slice(s),a-=d),new H(h.append(l),a)}remapping(e,t){let n=new k.X9;return this.items.forEach((t,r)=>{let i=null!=t.mirrorOffset&&r-t.mirrorOffset>=e?n.maps.length-t.mirrorOffset:void 0;n.appendMap(t.map,i)},e,t),n}addMaps(e){return 0==this.eventCount?this:new H(this.items.append(e.map(e=>new D(e))),this.eventCount)}rebased(e,t){if(!this.eventCount)return this;let n=[],r=Math.max(0,this.items.length-t),i=e.mapping,o=e.steps.length,s=this.eventCount;this.items.forEach(e=>{e.selection&&s--},r);let l=t;this.items.forEach(t=>{let r=i.getMirror(--l);if(null==r)return;o=Math.min(o,r);let a=i.maps[r];if(t.step){let o=e.steps[r].invert(e.docs[r]),h=t.selection&&t.selection.map(i.slice(l+1,r));h&&s++,n.push(new D(a,o,h))}else n.push(new D(a))},r);let a=[];for(let e=t;e<o;e++)a.push(new D(i.maps[e]));let h=new H(this.items.slice(0,r).append(a).append(n),s);return h.emptyItemCount()>500&&(h=h.compress(this.items.length-n.length)),h}emptyItemCount(){let e=0;return this.items.forEach(t=>{!t.step&&e++}),e}compress(e=this.items.length){let t=this.remapping(0,e),n=t.maps.length,r=[],i=0;return this.items.forEach((o,s)=>{if(s>=e)r.push(o),o.selection&&i++;else if(o.step){let e=o.step.map(t.slice(n)),s=e&&e.getMap();if(n--,s&&t.appendMap(s,n),e){let l=o.selection&&o.selection.map(t.slice(n));l&&i++;let a=new D(s.invert(),e,l),h,c=r.length-1;(h=r.length&&r[c].merge(a))?r[c]=h:r.push(a)}}else o.map&&n--},this.items.length,0),new H($.from(r.reverse()),i)}}H.empty=new H($.empty,0);class D{constructor(e,t,n,r){this.map=e,this.step=t,this.selection=n,this.mirrorOffset=r}merge(e){if(this.step&&e.step&&!e.selection){let t=e.step.merge(this.step);if(t)return new D(t.getMap().invert(),t,this.selection)}}}class P{constructor(e,t,n,r,i){this.done=e,this.undone=t,this.prevRanges=n,this.prevTime=r,this.prevComposition=i}}let F=20;function J(e){let t=[];for(let n=e.length-1;n>=0&&0==t.length;n--)e[n].forEach((e,n,r,i)=>t.push(r,i));return t}function K(e,t){if(!e)return null;let n=[];for(let r=0;r<e.length;r+=2){let i=t.map(e[r],1),o=t.map(e[r+1],-1);i<=o&&n.push(i,o)}return n}let W=!1,U=null;function V(e){let t=e.plugins;if(U!=t){W=!1,U=t;for(let e=0;e<t.length;e++)if(t[e].spec.historyPreserveItems){W=!0;break}}return W}let q=new w.hs("history"),G=new w.hs("closeHistory");function X(e,t){return(n,r)=>{let i=q.getState(n);if(!i||0==(e?i.undone:i.done).eventCount)return!1;if(r){let o=function(e,t,n){let r=V(t),i=q.get(t).spec.config,o=(n?e.undone:e.done).popEvent(t,r);if(!o)return null;let s=o.selection.resolve(o.transform.doc),l=(n?e.done:e.undone).addTransform(o.transform,t.selection.getBookmark(),i,r),a=new P(n?l:o.remaining,n?o.remaining:l,null,0,-1);return o.transform.setSelection(s).setMeta(q,{redo:n,historyState:a})}(i,n,e);o&&r(t?o.scrollIntoView():o)}return!0}}let Z=X(!1,!0),Y=X(!0,!0);X(!1,!1),X(!0,!1);let Q=r.YY.create({name:"history",addOptions:()=>({depth:100,newGroupDelay:500}),addCommands:()=>({undo:()=>({state:e,dispatch:t})=>Z(e,t),redo:()=>({state:e,dispatch:t})=>Y(e,t)}),addProseMirrorPlugins(){return[function(e={}){return e={depth:e.depth||100,newGroupDelay:e.newGroupDelay||500},new w.k_({key:q,state:{init:()=>new P(H.empty,H.empty,null,0,-1),apply:(t,n,r)=>(function(e,t,n,r){let i=n.getMeta(q),o;if(i)return i.historyState;n.getMeta(G)&&(e=new P(e.done,e.undone,null,0,-1));let s=n.getMeta("appendedTransaction");if(0==n.steps.length)return e;if(s&&s.getMeta(q))if(s.getMeta(q).redo)return new P(e.done.addTransform(n,void 0,r,V(t)),e.undone,J(n.mapping.maps),e.prevTime,e.prevComposition);else return new P(e.done,e.undone.addTransform(n,void 0,r,V(t)),null,e.prevTime,e.prevComposition);if(!1===n.getMeta("addToHistory")||s&&!1===s.getMeta("addToHistory"))if(o=n.getMeta("rebased"))return new P(e.done.rebased(n,o),e.undone.rebased(n,o),K(e.prevRanges,n.mapping),e.prevTime,e.prevComposition);else return new P(e.done.addMaps(n.mapping.maps),e.undone.addMaps(n.mapping.maps),K(e.prevRanges,n.mapping),e.prevTime,e.prevComposition);{let i=n.getMeta("composition"),o=0==e.prevTime||!s&&e.prevComposition!=i&&(e.prevTime<(n.time||0)-r.newGroupDelay||!function(e,t){if(!t)return!1;if(!e.docChanged)return!0;let n=!1;return e.mapping.maps[0].forEach((e,r)=>{for(let i=0;i<t.length;i+=2)e<=t[i+1]&&r>=t[i]&&(n=!0)}),n}(n,e.prevRanges)),l=s?K(e.prevRanges,n.mapping):J(n.mapping.maps);return new P(e.done.addTransform(n,o?t.selection.getBookmark():void 0,r,V(t)),H.empty,l,n.time,null==i?e.prevComposition:i)}})(n,r,t,e)},config:e,props:{handleDOMEvents:{beforeinput(e,t){let n=t.inputType,r="historyUndo"==n?Z:"historyRedo"==n?Y:null;return!!r&&(t.preventDefault(),r(e.state,e.dispatch))}}}})}(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}}),ee=r.bP.create({name:"horizontalRule",addOptions:()=>({HTMLAttributes:{}}),group:"block",parseHTML:()=>[{tag:"hr"}],renderHTML({HTMLAttributes:e}){return["hr",(0,r.KV)(this.options.HTMLAttributes,e)]},addCommands(){return{setHorizontalRule:()=>({chain:e,state:t})=>{if(!(0,r.AB)(t,t.schema.nodes[this.name]))return!1;let{selection:n}=t,{$from:i,$to:o}=n,s=e();return 0===i.parentOffset?s.insertContentAt({from:Math.max(i.pos-1,0),to:o.pos},{type:this.name}):(0,r.BQ)(n)?s.insertContentAt(o.pos,{type:this.name}):s.insertContent({type:this.name}),s.command(({tr:e,dispatch:t})=>{var n;if(t){let{$to:t}=e.selection,r=t.end();if(t.nodeAfter)t.nodeAfter.isTextblock?e.setSelection(w.U3.create(e.doc,t.pos+1)):t.nodeAfter.isBlock?e.setSelection(w.nh.create(e.doc,t.pos)):e.setSelection(w.U3.create(e.doc,t.pos));else{let i=null==(n=t.parent.type.contentMatch.defaultType)?void 0:n.create();i&&(e.insert(r,i),e.setSelection(w.U3.create(e.doc,r+1)))}e.scrollIntoView()}return!0}).run()}}},addInputRules(){return[(0,r.jT)({find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type})]}}),et=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,en=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,er=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,ei=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,eo=r.CU.create({name:"italic",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:e=>"normal"!==e.style.fontStyle&&null},{style:"font-style=normal",clearMark:e=>e.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:e}){return["em",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setItalic:()=>({commands:e})=>e.setMark(this.name),toggleItalic:()=>({commands:e})=>e.toggleMark(this.name),unsetItalic:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[(0,r.OX)({find:et,type:this.type}),(0,r.OX)({find:er,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:en,type:this.type}),(0,r.Zc)({find:ei,type:this.type})]}}),es=r.bP.create({name:"listItem",addOptions:()=>({HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}),content:"paragraph block*",defining:!0,parseHTML:()=>[{tag:"li"}],renderHTML({HTMLAttributes:e}){return["li",(0,r.KV)(this.options.HTMLAttributes,e),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),el="textStyle",ea=/^(\d+)\.\s$/,eh=r.bP.create({name:"orderedList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes:()=>({start:{default:1,parseHTML:e=>e.hasAttribute("start")?parseInt(e.getAttribute("start")||"",10):1},type:{default:null,parseHTML:e=>e.getAttribute("type")}}),parseHTML:()=>[{tag:"ol"}],renderHTML({HTMLAttributes:e}){let{start:t,...n}=e;return 1===t?["ol",(0,r.KV)(this.options.HTMLAttributes,n),0]:["ol",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{toggleOrderedList:()=>({commands:e,chain:t})=>this.options.keepAttributes?t().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(el)).run():e.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let e=(0,r.tG)({find:ea,type:this.type,getAttributes:e=>({start:+e[1]}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(e=(0,r.tG)({find:ea,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:e=>({start:+e[1],...this.editor.getAttributes(el)}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1],editor:this.editor})),[e]}}),ec=r.bP.create({name:"paragraph",priority:1e3,addOptions:()=>({HTMLAttributes:{}}),group:"block",content:"inline*",parseHTML:()=>[{tag:"p"}],renderHTML({HTMLAttributes:e}){return["p",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setParagraph:()=>({commands:e})=>e.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),ed=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,eu=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,ep=r.CU.create({name:"strike",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:e=>!!e.includes("line-through")&&{}}],renderHTML({HTMLAttributes:e}){return["s",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setStrike:()=>({commands:e})=>e.setMark(this.name),toggleStrike:()=>({commands:e})=>e.toggleMark(this.name),unsetStrike:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[(0,r.OX)({find:ed,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:eu,type:this.type})]}}),ef=r.bP.create({name:"text",group:"inline"}),eg=r.YY.create({name:"starterKit",addExtensions(){let e=[];return!1!==this.options.bold&&e.push(c.configure(this.options.bold)),!1!==this.options.blockquote&&e.push(o.configure(this.options.blockquote)),!1!==this.options.bulletList&&e.push(p.configure(this.options.bulletList)),!1!==this.options.code&&e.push(m.configure(this.options.code)),!1!==this.options.codeBlock&&e.push(y.NG.configure(this.options.codeBlock)),!1!==this.options.document&&e.push(b.configure(this.options.document)),!1!==this.options.dropcursor&&e.push(x.configure(this.options.dropcursor)),!1!==this.options.gapcursor&&e.push(L.configure(this.options.gapcursor)),!1!==this.options.hardBreak&&e.push(_.configure(this.options.hardBreak)),!1!==this.options.heading&&e.push(z.configure(this.options.heading)),!1!==this.options.history&&e.push(Q.configure(this.options.history)),!1!==this.options.horizontalRule&&e.push(ee.configure(this.options.horizontalRule)),!1!==this.options.italic&&e.push(eo.configure(this.options.italic)),!1!==this.options.listItem&&e.push(es.configure(this.options.listItem)),!1!==this.options.orderedList&&e.push(eh.configure(this.options.orderedList)),!1!==this.options.paragraph&&e.push(ec.configure(this.options.paragraph)),!1!==this.options.strike&&e.push(ep.configure(this.options.strike)),!1!==this.options.text&&e.push(ef.configure(this.options.text)),e}})},81891:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>o});var r=n(4701);let i=/(?:^|\s)(!\[(.+|:?)]\((\S+)(?:(?:\s+)["'](\S+)["'])?\))$/,o=r.bP.create({name:"image",addOptions:()=>({inline:!1,allowBase64:!1,HTMLAttributes:{}}),inline(){return this.options.inline},group(){return this.options.inline?"inline":"block"},draggable:!0,addAttributes:()=>({src:{default:null},alt:{default:null},title:{default:null}}),parseHTML(){return[{tag:this.options.allowBase64?"img[src]":'img[src]:not([src^="data:"])'}]},renderHTML({HTMLAttributes:e}){return["img",(0,r.KV)(this.options.HTMLAttributes,e)]},addCommands(){return{setImage:e=>({commands:t})=>t.insertContent({type:this.name,attrs:e})}},addInputRules(){return[(0,r.jT)({find:i,type:this.type,getAttributes:e=>{let[,,t,n,r]=e;return{src:n,alt:t,title:r}}})]}})},87489:(e,t,n)=>{"use strict";n.d(t,{b:()=>h});var r=n(12115),i=n(63655),o=n(95155),s="horizontal",l=["horizontal","vertical"],a=r.forwardRef((e,t)=>{var n;let{decorative:r,orientation:a=s,...h}=e,c=(n=a,l.includes(n))?a:s;return(0,o.jsx)(i.sG.div,{"data-orientation":c,...r?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...h,ref:t})});a.displayName="Separator";var h=a},89140:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("list-ordered",[["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]])},90192:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>ed});var r,i,o=n(4701),s=n(52571),l=n(10156),a=n(42695),h=n(96770),c=n(808);if("undefined"!=typeof WeakMap){let e=new WeakMap;r=t=>e.get(t),i=(t,n)=>(e.set(t,n),n)}else{let e=[],t=0;r=t=>{for(let n=0;n<e.length;n+=2)if(e[n]==t)return e[n+1]},i=(n,r)=>(10==t&&(t=0),e[t++]=n,e[t++]=r)}var d=class{constructor(e,t,n,r){this.width=e,this.height=t,this.map=n,this.problems=r}findCell(e){for(let t=0;t<this.map.length;t++){let n=this.map[t];if(n!=e)continue;let r=t%this.width,i=t/this.width|0,o=r+1,s=i+1;for(let e=1;o<this.width&&this.map[t+e]==n;e++)o++;for(let e=1;s<this.height&&this.map[t+this.width*e]==n;e++)s++;return{left:r,top:i,right:o,bottom:s}}throw RangeError(`No cell with offset ${e} found`)}colCount(e){for(let t=0;t<this.map.length;t++)if(this.map[t]==e)return t%this.width;throw RangeError(`No cell with offset ${e} found`)}nextCell(e,t,n){let{left:r,right:i,top:o,bottom:s}=this.findCell(e);return"horiz"==t?(n<0?0==r:i==this.width)?null:this.map[o*this.width+(n<0?r-1:i)]:(n<0?0==o:s==this.height)?null:this.map[r+this.width*(n<0?o-1:s)]}rectBetween(e,t){let{left:n,right:r,top:i,bottom:o}=this.findCell(e),{left:s,right:l,top:a,bottom:h}=this.findCell(t);return{left:Math.min(n,s),top:Math.min(i,a),right:Math.max(r,l),bottom:Math.max(o,h)}}cellsInRect(e){let t=[],n={};for(let r=e.top;r<e.bottom;r++)for(let i=e.left;i<e.right;i++){let o=r*this.width+i,s=this.map[o];!n[s]&&(n[s]=!0,i==e.left&&i&&this.map[o-1]==s||r==e.top&&r&&this.map[o-this.width]==s||t.push(s))}return t}positionAt(e,t,n){for(let r=0,i=0;;r++){let o=i+n.child(r).nodeSize;if(r==e){let n=t+e*this.width,r=(e+1)*this.width;for(;n<r&&this.map[n]<i;)n++;return n==r?o-1:this.map[n]}i=o}}static get(e){return r(e)||i(e,function(e){if("table"!=e.type.spec.tableRole)throw RangeError("Not a table node: "+e.type.name);let t=function(e){let t=-1,n=!1;for(let r=0;r<e.childCount;r++){let i=e.child(r),o=0;if(n)for(let t=0;t<r;t++){let n=e.child(t);for(let e=0;e<n.childCount;e++){let i=n.child(e);t+i.attrs.rowspan>r&&(o+=i.attrs.colspan)}}for(let e=0;e<i.childCount;e++){let t=i.child(e);o+=t.attrs.colspan,t.attrs.rowspan>1&&(n=!0)}-1==t?t=o:t!=o&&(t=Math.max(t,o))}return t}(e),n=e.childCount,r=[],i=0,o=null,s=[];for(let e=0,i=t*n;e<i;e++)r[e]=0;for(let l=0,a=0;l<n;l++){let h=e.child(l);a++;for(let e=0;;e++){for(;i<r.length&&0!=r[i];)i++;if(e==h.childCount)break;let c=h.child(e),{colspan:d,rowspan:u,colwidth:p}=c.attrs;for(let e=0;e<u;e++){if(e+l>=n){(o||(o=[])).push({type:"overlong_rowspan",pos:a,n:u-e});break}let h=i+e*t;for(let e=0;e<d;e++){0==r[h+e]?r[h+e]=a:(o||(o=[])).push({type:"collision",row:l,pos:a,n:d-e});let n=p&&p[e];if(n){let r=(h+e)%t*2,i=s[r];null==i||i!=n&&1==s[r+1]?(s[r]=n,s[r+1]=1):i==n&&s[r+1]++}}}i+=d,a+=c.nodeSize}let c=(l+1)*t,d=0;for(;i<c;)0==r[i++]&&d++;d&&(o||(o=[])).push({type:"missing",row:l,n:d}),a++}(0===t||0===n)&&(o||(o=[])).push({type:"zero_sized"});let l=new d(t,n,r,o),a=!1;for(let e=0;!a&&e<s.length;e+=2)null!=s[e]&&s[e+1]<n&&(a=!0);return a&&function(e,t,n){e.problems||(e.problems=[]);let r={};for(let i=0;i<e.map.length;i++){let o=e.map[i];if(r[o])continue;r[o]=!0;let s=n.nodeAt(o);if(!s)throw RangeError(`No cell with offset ${o} found`);let l=null,a=s.attrs;for(let n=0;n<a.colspan;n++){let r=t[2*((i+n)%e.width)];null==r||a.colwidth&&a.colwidth[n]==r||((l||(l=function(e){if(e.colwidth)return e.colwidth.slice();let t=[];for(let n=0;n<e.colspan;n++)t.push(0);return t}(a)))[n]=r)}l&&e.problems.unshift({type:"colwidth mismatch",pos:o,colwidth:l})}}(l,s,e),l}(e))}};function u(e){let t=e.cached.tableNodeTypes;if(!t)for(let n in t=e.cached.tableNodeTypes={},e.nodes){let r=e.nodes[n],i=r.spec.tableRole;i&&(t[i]=r)}return t}var p=new s.hs("selectingCells");function f(e){for(let t=e.depth-1;t>0;t--)if("row"==e.node(t).type.spec.tableRole)return e.node(0).resolve(e.before(t+1));return null}function g(e){let t=e.selection.$head;for(let e=t.depth;e>0;e--)if("row"==t.node(e).type.spec.tableRole)return!0;return!1}function m(e){let t=e.selection;if("$anchorCell"in t&&t.$anchorCell)return t.$anchorCell.pos>t.$headCell.pos?t.$anchorCell:t.$headCell;if("node"in t&&t.node&&"cell"==t.node.type.spec.tableRole)return t.$anchor;let n=f(t.$head)||function(e){for(let t=e.nodeAfter,n=e.pos;t;t=t.firstChild,n++){let r=t.type.spec.tableRole;if("cell"==r||"header_cell"==r)return e.doc.resolve(n)}for(let t=e.nodeBefore,n=e.pos;t;t=t.lastChild,n--){let r=t.type.spec.tableRole;if("cell"==r||"header_cell"==r)return e.doc.resolve(n-t.nodeSize)}}(t.$head);if(n)return n;throw RangeError(`No cell found around position ${t.head}`)}function y(e){return"row"==e.parent.type.spec.tableRole&&!!e.nodeAfter}function b(e,t){return e.depth==t.depth&&e.pos>=t.start(-1)&&e.pos<=t.end(-1)}function w(e,t,n){let r=e.node(-1),i=d.get(r),o=e.start(-1),s=i.nextCell(e.pos-o,t,n);return null==s?null:e.node(0).resolve(o+s)}function k(e,t,n=1){let r={...e,colspan:e.colspan-n};return r.colwidth&&(r.colwidth=r.colwidth.slice(),r.colwidth.splice(t,n),r.colwidth.some(e=>e>0)||(r.colwidth=null)),r}function v(e,t,n=1){let r={...e,colspan:e.colspan+n};if(r.colwidth){r.colwidth=r.colwidth.slice();for(let e=0;e<n;e++)r.colwidth.splice(t,0,0)}return r}var x=class e extends s.LN{constructor(e,t=e){let n=e.node(-1),r=d.get(n),i=e.start(-1),o=r.rectBetween(e.pos-i,t.pos-i),l=e.node(0),a=r.cellsInRect(o).filter(e=>e!=t.pos-i);a.unshift(t.pos-i);let h=a.map(e=>{let t=n.nodeAt(e);if(!t)throw RangeError(`No cell with offset ${e} found`);let r=i+e+1;return new s.yn(l.resolve(r),l.resolve(r+t.content.size))});super(h[0].$from,h[0].$to,h),this.$anchorCell=e,this.$headCell=t}map(t,n){let r=t.resolve(n.map(this.$anchorCell.pos)),i=t.resolve(n.map(this.$headCell.pos));if(y(r)&&y(i)&&b(r,i)){let t=this.$anchorCell.node(-1)!=r.node(-1);return t&&this.isRowSelection()?e.rowSelection(r,i):t&&this.isColSelection()?e.colSelection(r,i):new e(r,i)}return s.U3.between(r,i)}content(){let e=this.$anchorCell.node(-1),t=d.get(e),n=this.$anchorCell.start(-1),r=t.rectBetween(this.$anchorCell.pos-n,this.$headCell.pos-n),i={},o=[];for(let n=r.top;n<r.bottom;n++){let s=[];for(let o=n*t.width+r.left,l=r.left;l<r.right;l++,o++){let n=t.map[o];if(i[n])continue;i[n]=!0;let l=t.findCell(n),a=e.nodeAt(n);if(!a)throw RangeError(`No cell with offset ${n} found`);let h=r.left-l.left,c=l.right-r.right;if(h>0||c>0){let e=a.attrs;if(h>0&&(e=k(e,0,h)),c>0&&(e=k(e,e.colspan-c,c)),l.left<r.left){if(!(a=a.type.createAndFill(e)))throw RangeError(`Could not create cell with attrs ${JSON.stringify(e)}`)}else a=a.type.create(e,a.content)}if(l.top<r.top||l.bottom>r.bottom){let e={...a.attrs,rowspan:Math.min(l.bottom,r.bottom)-Math.max(l.top,r.top)};a=l.top<r.top?a.type.createAndFill(e):a.type.create(e,a.content)}s.push(a)}o.push(e.child(n).copy(l.FK.from(s)))}let s=this.isColSelection()&&this.isRowSelection()?e:o;return new l.Ji(l.FK.from(s),1,1)}replace(e,t=l.Ji.empty){let n=e.steps.length,r=this.ranges;for(let i=0;i<r.length;i++){let{$from:o,$to:s}=r[i],a=e.mapping.slice(n);e.replace(a.map(o.pos),a.map(s.pos),i?l.Ji.empty:t)}let i=s.LN.findFrom(e.doc.resolve(e.mapping.slice(n).map(this.to)),-1);i&&e.setSelection(i)}replaceWith(e,t){this.replace(e,new l.Ji(l.FK.from(t),0,0))}forEachCell(e){let t=this.$anchorCell.node(-1),n=d.get(t),r=this.$anchorCell.start(-1),i=n.cellsInRect(n.rectBetween(this.$anchorCell.pos-r,this.$headCell.pos-r));for(let n=0;n<i.length;n++)e(t.nodeAt(i[n]),r+i[n])}isColSelection(){let e=this.$anchorCell.index(-1),t=this.$headCell.index(-1);return!(Math.min(e,t)>0)&&Math.max(e+this.$anchorCell.nodeAfter.attrs.rowspan,t+this.$headCell.nodeAfter.attrs.rowspan)==this.$headCell.node(-1).childCount}static colSelection(t,n=t){let r=t.node(-1),i=d.get(r),o=t.start(-1),s=i.findCell(t.pos-o),l=i.findCell(n.pos-o),a=t.node(0);return s.top<=l.top?(s.top>0&&(t=a.resolve(o+i.map[s.left])),l.bottom<i.height&&(n=a.resolve(o+i.map[i.width*(i.height-1)+l.right-1]))):(l.top>0&&(n=a.resolve(o+i.map[l.left])),s.bottom<i.height&&(t=a.resolve(o+i.map[i.width*(i.height-1)+s.right-1]))),new e(t,n)}isRowSelection(){let e=this.$anchorCell.node(-1),t=d.get(e),n=this.$anchorCell.start(-1),r=t.colCount(this.$anchorCell.pos-n),i=t.colCount(this.$headCell.pos-n);return!(Math.min(r,i)>0)&&Math.max(r+this.$anchorCell.nodeAfter.attrs.colspan,i+this.$headCell.nodeAfter.attrs.colspan)==t.width}eq(t){return t instanceof e&&t.$anchorCell.pos==this.$anchorCell.pos&&t.$headCell.pos==this.$headCell.pos}static rowSelection(t,n=t){let r=t.node(-1),i=d.get(r),o=t.start(-1),s=i.findCell(t.pos-o),l=i.findCell(n.pos-o),a=t.node(0);return s.left<=l.left?(s.left>0&&(t=a.resolve(o+i.map[s.top*i.width])),l.right<i.width&&(n=a.resolve(o+i.map[i.width*(l.top+1)-1]))):(l.left>0&&(n=a.resolve(o+i.map[l.top*i.width])),s.right<i.width&&(t=a.resolve(o+i.map[i.width*(s.top+1)-1]))),new e(t,n)}toJSON(){return{type:"cell",anchor:this.$anchorCell.pos,head:this.$headCell.pos}}static fromJSON(t,n){return new e(t.resolve(n.anchor),t.resolve(n.head))}static create(t,n,r=n){return new e(t.resolve(n),t.resolve(r))}getBookmark(){return new S(this.$anchorCell.pos,this.$headCell.pos)}};x.prototype.visible=!1,s.LN.jsonID("cell",x);var S=class e{constructor(e,t){this.anchor=e,this.head=t}map(t){return new e(t.map(this.anchor),t.map(this.head))}resolve(e){let t=e.resolve(this.anchor),n=e.resolve(this.head);return"row"==t.parent.type.spec.tableRole&&"row"==n.parent.type.spec.tableRole&&t.index()<t.parent.childCount&&n.index()<n.parent.childCount&&b(t,n)?new x(t,n):s.LN.near(n,1)}};function M(e){if(!(e.selection instanceof x))return null;let t=[];return e.selection.forEachCell((e,n)=>{t.push(a.NZ.node(n,n+e.nodeSize,{class:"selectedCell"}))}),a.zF.create(e.doc,t)}var C=new s.hs("fix-tables");function E(e,t){let n,r=(t,r)=>{"table"==t.type.spec.tableRole&&(n=function(e,t,n,r){let i,o,s=d.get(t);if(!s.problems)return r;r||(r=e.tr);let l=[];for(let e=0;e<s.height;e++)l.push(0);for(let e=0;e<s.problems.length;e++){let i=s.problems[e];if("collision"==i.type){let e=t.nodeAt(i.pos);if(!e)continue;let o=e.attrs;for(let e=0;e<o.rowspan;e++)l[i.row+e]+=i.n;r.setNodeMarkup(r.mapping.map(n+1+i.pos),null,k(o,o.colspan-i.n,i.n))}else if("missing"==i.type)l[i.row]+=i.n;else if("overlong_rowspan"==i.type){let e=t.nodeAt(i.pos);if(!e)continue;r.setNodeMarkup(r.mapping.map(n+1+i.pos),null,{...e.attrs,rowspan:e.attrs.rowspan-i.n})}else if("colwidth mismatch"==i.type){let e=t.nodeAt(i.pos);if(!e)continue;r.setNodeMarkup(r.mapping.map(n+1+i.pos),null,{...e.attrs,colwidth:i.colwidth})}else if("zero_sized"==i.type){let e=r.mapping.map(n);r.delete(e,e+t.nodeSize)}}for(let e=0;e<l.length;e++)l[e]&&(null==i&&(i=e),o=e);for(let a=0,h=n+1;a<s.height;a++){let n=t.child(a),s=h+n.nodeSize,c=l[a];if(c>0){let t="cell";n.firstChild&&(t=n.firstChild.type.spec.tableRole);let l=[];for(let n=0;n<c;n++){let n=u(e.schema)[t].createAndFill();n&&l.push(n)}let d=(0==a||i==a-1)&&o==a?h+1:s-1;r.insert(r.mapping.map(d),l)}h=s}return r.setMeta(C,{fixTables:!0})}(e,t,r,n))};return t?t.doc!=e.doc&&function e(t,n,r,i){let o=t.childCount,s=n.childCount;r:for(let l=0,a=0;l<s;l++){let s=n.child(l);for(let e=a,n=Math.min(o,l+3);e<n;e++)if(t.child(e)==s){a=e+1,r+=s.nodeSize;continue r}i(s,r),a<o&&t.child(a).sameMarkup(s)?e(t.child(a),s,r+1,i):s.nodesBetween(0,s.content.size,i,r+1),r+=s.nodeSize}}(t.doc,e.doc,0,r):e.doc.descendants(r),n}function A(e){let t=e.selection,n=m(e),r=n.node(-1),i=n.start(-1),o=d.get(r);return{...t instanceof x?o.rectBetween(t.$anchorCell.pos-i,t.$headCell.pos-i):o.findCell(n.pos-i),tableStart:i,map:o,table:r}}function T(e,{map:t,tableStart:n,table:r},i){let o=i>0?-1:0;(function(e,t,n){let r=u(t.type.schema).header_cell;for(let i=0;i<e.height;i++)if(t.nodeAt(e.map[n+i*e.width]).type!=r)return!1;return!0})(t,r,i+o)&&(o=0==i||i==t.width?null:0);for(let s=0;s<t.height;s++){let l=s*t.width+i;if(i>0&&i<t.width&&t.map[l-1]==t.map[l]){let o=t.map[l],a=r.nodeAt(o);e.setNodeMarkup(e.mapping.map(n+o),null,v(a.attrs,i-t.colCount(o))),s+=a.attrs.rowspan-1}else{let a=null==o?u(r.type.schema).cell:r.nodeAt(t.map[l+o]).type,h=t.positionAt(s,i,r);e.insert(e.mapping.map(n+h),a.createAndFill())}}return e}function O(e,{map:t,tableStart:n,table:r},i){var o;let s=n;for(let e=0;e<i;e++)s+=r.child(e).nodeSize;let l=[],a=i>0?-1:0;(function(e,t,n){var r;let i=u(t.type.schema).header_cell;for(let o=0;o<e.width;o++)if((null==(r=t.nodeAt(e.map[o+n*e.width]))?void 0:r.type)!=i)return!1;return!0})(t,r,i+a)&&(a=0==i||i==t.height?null:0);for(let s=0,h=t.width*i;s<t.width;s++,h++)if(i>0&&i<t.height&&t.map[h]==t.map[h-t.width]){let i=t.map[h],o=r.nodeAt(i).attrs;e.setNodeMarkup(n+i,null,{...o,rowspan:o.rowspan+1}),s+=o.colspan-1}else{let e=null==a?u(r.type.schema).cell:null==(o=r.nodeAt(t.map[h+a*t.width]))?void 0:o.type,n=null==e?void 0:e.createAndFill();n&&l.push(n)}return e.insert(s,u(r.type.schema).row.create(null,l)),e}function R(e){let t=e.content;return 1==t.childCount&&t.child(0).isTextblock&&0==t.child(0).childCount}function N(e,t){let n=e.selection;if(!(n instanceof x)||n.$anchorCell.pos==n.$headCell.pos)return!1;let r=A(e),{map:i}=r;if(function({width:e,height:t,map:n},r){let i=r.top*e+r.left,o=i,s=(r.bottom-1)*e+r.left,l=i+(r.right-r.left-1);for(let t=r.top;t<r.bottom;t++){if(r.left>0&&n[o]==n[o-1]||r.right<e&&n[l]==n[l+1])return!0;o+=e,l+=e}for(let o=r.left;o<r.right;o++){if(r.top>0&&n[i]==n[i-e]||r.bottom<t&&n[s]==n[s+e])return!0;i++,s++}return!1}(i,r))return!1;if(t){let n,o,s=e.tr,a={},h=l.FK.empty;for(let e=r.top;e<r.bottom;e++)for(let t=r.left;t<r.right;t++){let l=i.map[e*i.width+t],c=r.table.nodeAt(l);if(!a[l]&&c)if(a[l]=!0,null==n)n=l,o=c;else{R(c)||(h=h.append(c.content));let e=s.mapping.map(l+r.tableStart);s.delete(e,e+c.nodeSize)}}if(null==n||null==o)return!0;if(s.setNodeMarkup(n+r.tableStart,null,{...v(o.attrs,o.attrs.colspan,r.right-r.left-o.attrs.colspan),rowspan:r.bottom-r.top}),h.size){let e=n+1+o.content.size,t=R(o)?n+1:e;s.replaceWith(t+r.tableStart,e+r.tableStart,h)}s.setSelection(new x(s.doc.resolve(n+r.tableStart))),t(s)}return!0}function I(e,t){var n;let r=u(e.schema);return(n=({node:e})=>r[e.type.spec.tableRole],(e,t)=>{var r;let i,o,s=e.selection;if(s instanceof x){if(s.$anchorCell.pos!=s.$headCell.pos)return!1;i=s.$anchorCell.nodeAfter,o=s.$anchorCell.pos}else{if(!(i=function(e){for(let t=e.depth;t>0;t--){let n=e.node(t).type.spec.tableRole;if("cell"===n||"header_cell"===n)return e.node(t)}return null}(s.$from)))return!1;o=null==(r=f(s.$from))?void 0:r.pos}if(null==i||null==o||1==i.attrs.colspan&&1==i.attrs.rowspan)return!1;if(t){let r,l=i.attrs,a=[],h=l.colwidth;l.rowspan>1&&(l={...l,rowspan:1}),l.colspan>1&&(l={...l,colspan:1});let c=A(e),d=e.tr;for(let e=0;e<c.right-c.left;e++)a.push(h?{...l,colwidth:h&&h[e]?[h[e]]:null}:l);for(let e=c.top;e<c.bottom;e++){let t=c.map.positionAt(e,c.left,c.table);e==c.top&&(t+=i.nodeSize);for(let o=c.left,s=0;o<c.right;o++,s++)(o!=c.left||e!=c.top)&&d.insert(r=d.mapping.map(t+c.tableStart,1),n({node:i,row:e,col:o}).createAndFill(a[s]))}d.setNodeMarkup(o,n({node:i,row:c.top,col:c.left}),a[0]),s instanceof x&&d.setSelection(new x(d.doc.resolve(s.$anchorCell.pos),r?d.doc.resolve(r):void 0)),t(d)}return!0})(e,t)}function L(e,t,n){let r=t.map.cellsInRect({left:0,top:0,right:"row"==e?t.map.width:1,bottom:"column"==e?t.map.height:1});for(let e=0;e<r.length;e++){let i=t.table.nodeAt(r[e]);if(i&&i.type!==n.header_cell)return!1}return!0}function _(e,t){if((t=t||{useDeprecatedLogic:!1}).useDeprecatedLogic)return function(t,n){if(!g(t))return!1;if(n){let r=u(t.schema),i=A(t),o=t.tr,s=i.map.cellsInRect("column"==e?{left:i.left,top:0,right:i.right,bottom:i.map.height}:"row"==e?{left:0,top:i.top,right:i.map.width,bottom:i.bottom}:i),l=s.map(e=>i.table.nodeAt(e));for(let e=0;e<s.length;e++)l[e].type==r.header_cell&&o.setNodeMarkup(i.tableStart+s[e],r.cell,l[e].attrs);if(0==o.steps.length)for(let e=0;e<s.length;e++)o.setNodeMarkup(i.tableStart+s[e],r.header_cell,l[e].attrs);n(o)}return!0};return function(t,n){if(!g(t))return!1;if(n){let r=u(t.schema),i=A(t),o=t.tr,s=L("row",i,r),l=L("column",i,r),a=+!!("column"===e?s:"row"===e&&l),h="column"==e?{left:0,top:a,right:1,bottom:i.map.height}:"row"==e?{left:a,top:0,right:i.map.width,bottom:1}:i,c="column"==e?l?r.cell:r.header_cell:"row"==e?s?r.cell:r.header_cell:r.cell;i.map.cellsInRect(h).forEach(e=>{let t=e+i.tableStart,n=o.doc.nodeAt(t);n&&o.setNodeMarkup(t,c,n.attrs)}),n(o)}return!0}}_("row",{useDeprecatedLogic:!0}),_("column",{useDeprecatedLogic:!0});var z=_("cell",{useDeprecatedLogic:!0});function $(e){return function(t,n){if(!g(t))return!1;let r=function(e,t){if(t<0){let t=e.nodeBefore;if(t)return e.pos-t.nodeSize;for(let t=e.index(-1)-1,n=e.before();t>=0;t--){let r=e.node(-1).child(t),i=r.lastChild;if(i)return n-1-i.nodeSize;n-=r.nodeSize}}else{if(e.index()<e.parent.childCount-1)return e.pos+e.nodeAfter.nodeSize;let t=e.node(-1);for(let n=e.indexAfter(-1),r=e.after();n<t.childCount;n++){let e=t.child(n);if(e.childCount)return r+1;r+=e.nodeSize}}return null}(m(t),e);if(null==r)return!1;if(n){let e=t.doc.resolve(r);n(t.tr.setSelection(s.U3.between(e,e.node(0).resolve(e.pos+e.nodeAfter.nodeSize))).scrollIntoView())}return!0}}function j(e,t){let n=e.selection;if(!(n instanceof x))return!1;if(t){let r=e.tr,i=u(e.schema).cell.createAndFill().content;n.forEachCell((e,t)=>{e.content.eq(i)||r.replace(r.mapping.map(t+1),r.mapping.map(t+e.nodeSize-1),new l.Ji(i,0,0))}),r.docChanged&&t(r)}return!0}function B(e,t){let n=e.createAndFill();return new c.dL(n).replace(0,n.content.size,t).doc}function H(e,t,n,r,i,o,s,l){if(0==s||s==t.height)return!1;let a=!1;for(let h=i;h<o;h++){let i=s*t.width+h,o=t.map[i];if(t.map[i-t.width]==o){a=!0;let i=n.nodeAt(o),{top:c,left:d}=t.findCell(o);e.setNodeMarkup(e.mapping.slice(l).map(o+r),null,{...i.attrs,rowspan:s-c}),e.insert(e.mapping.slice(l).map(t.positionAt(s,d,n)),i.type.createAndFill({...i.attrs,rowspan:c+i.attrs.rowspan-s})),h+=i.attrs.colspan-1}}return a}function D(e,t,n,r,i,o,s,l){if(0==s||s==t.width)return!1;let a=!1;for(let h=i;h<o;h++){let i=h*t.width+s,o=t.map[i];if(t.map[i-1]==o){a=!0;let i=n.nodeAt(o),c=t.colCount(o),d=e.mapping.slice(l).map(o+r);e.setNodeMarkup(d,null,k(i.attrs,s-c,i.attrs.colspan-(s-c))),e.insert(d+i.nodeSize,i.type.createAndFill(k(i.attrs,0,s-c))),h+=i.attrs.rowspan-1}}return a}function P(e,t,n,r,i){let o=n?e.doc.nodeAt(n-1):e.doc;if(!o)throw Error("No table found");let s=d.get(o),{top:a,left:h}=r,c=h+i.width,p=a+i.height,f=e.tr,g=0;function m(){if(!(o=n?f.doc.nodeAt(n-1):f.doc))throw Error("No table found");s=d.get(o),g=f.mapping.maps.length}(function(e,t,n,r,i,o,s){let a,h,c=u(e.doc.type.schema);if(i>t.width)for(let o=0,s=0;o<t.height;o++){let l,d=n.child(o);s+=d.nodeSize;let u=[];l=null==d.lastChild||d.lastChild.type==c.cell?a||(a=c.cell.createAndFill()):h||(h=c.header_cell.createAndFill());for(let e=t.width;e<i;e++)u.push(l);e.insert(e.mapping.slice(0).map(s-1+r),u)}if(o>t.height){let d=[];for(let e=0,r=(t.height-1)*t.width;e<Math.max(t.width,i);e++){let i=!(e>=t.width)&&n.nodeAt(t.map[r+e]).type==c.header_cell;d.push(i?h||(h=c.header_cell.createAndFill()):a||(a=c.cell.createAndFill()))}let u=c.row.create(null,l.FK.from(d)),p=[];for(let e=t.height;e<o;e++)p.push(u);e.insert(e.mapping.slice(s).map(r+n.nodeSize-2),p)}return!!(a||h)})(f,s,o,n,c,p,0)&&m(),H(f,s,o,n,h,c,a,g)&&m(),H(f,s,o,n,h,c,p,g)&&m(),D(f,s,o,n,a,p,h,g)&&m(),D(f,s,o,n,a,p,c,g)&&m();for(let e=a;e<p;e++){let t=s.positionAt(e,h,o),r=s.positionAt(e,c,o);f.replace(f.mapping.slice(g).map(t+n),f.mapping.slice(g).map(r+n),new l.Ji(i.rows[e-a],0,0))}m(),f.setSelection(new x(f.doc.resolve(n+s.positionAt(a,h,o)),f.doc.resolve(n+s.positionAt(p-1,c-1,o)))),t(f)}var F=(0,h.K)({ArrowLeft:K("horiz",-1),ArrowRight:K("horiz",1),ArrowUp:K("vert",-1),ArrowDown:K("vert",1),"Shift-ArrowLeft":W("horiz",-1),"Shift-ArrowRight":W("horiz",1),"Shift-ArrowUp":W("vert",-1),"Shift-ArrowDown":W("vert",1),Backspace:j,"Mod-Backspace":j,Delete:j,"Mod-Delete":j});function J(e,t,n){return!n.eq(e.selection)&&(t&&t(e.tr.setSelection(n).scrollIntoView()),!0)}function K(e,t){return(n,r,i)=>{if(!i)return!1;let o=n.selection;if(o instanceof x)return J(n,r,s.LN.near(o.$headCell,t));if("horiz"!=e&&!o.empty)return!1;let l=G(i,e,t);if(null==l)return!1;if("horiz"==e)return J(n,r,s.LN.near(n.doc.resolve(o.head+t),t));{let i,o=n.doc.resolve(l),a=w(o,e,t);return i=a?s.LN.near(a,1):t<0?s.LN.near(n.doc.resolve(o.before(-1)),-1):s.LN.near(n.doc.resolve(o.after(-1)),1),J(n,r,i)}}}function W(e,t){return(n,r,i)=>{let o;if(!i)return!1;let s=n.selection;if(s instanceof x)o=s;else{let r=G(i,e,t);if(null==r)return!1;o=new x(n.doc.resolve(r))}let l=w(o.$headCell,e,t);return!!l&&J(n,r,new x(o.$anchorCell,l))}}function U(e,t){let n=f(e.state.doc.resolve(t));return!!n&&(e.dispatch(e.state.tr.setSelection(new x(n))),!0)}function V(e,t,n){if(!g(e.state))return!1;let r=function(e){if(!e.size)return null;let{content:t,openStart:n,openEnd:r}=e;for(;1==t.childCount&&(n>0&&r>0||"table"==t.child(0).type.spec.tableRole);)n--,r--,t=t.child(0).content;let i=t.child(0),o=i.type.spec.tableRole,s=i.type.schema,a=[];if("row"==o)for(let e=0;e<t.childCount;e++){let i=t.child(e).content,o=e?0:Math.max(0,n-1),h=e<t.childCount-1?0:Math.max(0,r-1);(o||h)&&(i=B(u(s).row,new l.Ji(i,o,h)).content),a.push(i)}else{if("cell"!=o&&"header_cell"!=o)return null;a.push(n||r?B(u(s).row,new l.Ji(t,n,r)).content:t)}return function(e,t){let n=[];for(let e=0;e<t.length;e++){let r=t[e];for(let t=r.childCount-1;t>=0;t--){let{rowspan:i,colspan:o}=r.child(t).attrs;for(let t=e;t<e+i;t++)n[t]=(n[t]||0)+o}}let r=0;for(let e=0;e<n.length;e++)r=Math.max(r,n[e]);for(let i=0;i<n.length;i++)if(i>=t.length&&t.push(l.FK.empty),n[i]<r){let o=u(e).cell.createAndFill(),s=[];for(let e=n[i];e<r;e++)s.push(o);t[i]=t[i].append(l.FK.from(s))}return{height:t.length,width:r,rows:t}}(s,a)}(n),i=e.state.selection;if(i instanceof x){r||(r={width:1,height:1,rows:[l.FK.from(B(u(e.state.schema).cell,n))]});let t=i.$anchorCell.node(-1),o=i.$anchorCell.start(-1),s=d.get(t).rectBetween(i.$anchorCell.pos-o,i.$headCell.pos-o);return r=function({width:e,height:t,rows:n},r,i){if(e!=r){let t=[],i=[];for(let e=0;e<n.length;e++){let o=n[e],s=[];for(let n=t[e]||0,i=0;n<r;i++){let l=o.child(i%o.childCount);n+l.attrs.colspan>r&&(l=l.type.createChecked(k(l.attrs,l.attrs.colspan,n+l.attrs.colspan-r),l.content)),s.push(l),n+=l.attrs.colspan;for(let n=1;n<l.attrs.rowspan;n++)t[e+n]=(t[e+n]||0)+l.attrs.colspan}i.push(l.FK.from(s))}n=i,e=r}if(t!=i){let e=[];for(let r=0,o=0;r<i;r++,o++){let s=[],a=n[o%t];for(let e=0;e<a.childCount;e++){let t=a.child(e);r+t.attrs.rowspan>i&&(t=t.type.create({...t.attrs,rowspan:Math.max(1,i-t.attrs.rowspan)},t.content)),s.push(t)}e.push(l.FK.from(s))}n=e,t=i}return{width:e,height:t,rows:n}}(r,s.right-s.left,s.bottom-s.top),P(e.state,e.dispatch,o,s,r),!0}if(!r)return!1;{let t=m(e.state),n=t.start(-1);return P(e.state,e.dispatch,n,d.get(t.node(-1)).findCell(t.pos-n),r),!0}}function q(e,t){var n;let r;if(t.ctrlKey||t.metaKey)return;let i=X(e,t.target);if(t.shiftKey&&e.state.selection instanceof x)o(e.state.selection.$anchorCell,t),t.preventDefault();else if(t.shiftKey&&i&&null!=(r=f(e.state.selection.$anchor))&&(null==(n=Z(e,t))?void 0:n.pos)!=r.pos)o(r,t),t.preventDefault();else if(!i)return;function o(t,n){let r=Z(e,n),i=null==p.getState(e.state);if(!r||!b(t,r))if(!i)return;else r=t;let o=new x(t,r);if(i||!e.state.selection.eq(o)){let n=e.state.tr.setSelection(o);i&&n.setMeta(p,t.pos),e.dispatch(n)}}function s(){e.root.removeEventListener("mouseup",s),e.root.removeEventListener("dragstart",s),e.root.removeEventListener("mousemove",l),null!=p.getState(e.state)&&e.dispatch(e.state.tr.setMeta(p,-1))}function l(n){let r,l=p.getState(e.state);if(null!=l)r=e.state.doc.resolve(l);else if(X(e,n.target)!=i&&!(r=Z(e,t)))return s();r&&o(r,n)}e.root.addEventListener("mouseup",s),e.root.addEventListener("dragstart",s),e.root.addEventListener("mousemove",l)}function G(e,t,n){if(!(e.state.selection instanceof s.U3))return null;let{$head:r}=e.state.selection;for(let i=r.depth-1;i>=0;i--){let o=r.node(i);if((n<0?r.index(i):r.indexAfter(i))!=(n<0?0:o.childCount))break;if("cell"==o.type.spec.tableRole||"header_cell"==o.type.spec.tableRole){let o=r.before(i),s="vert"==t?n>0?"down":"up":n>0?"right":"left";return e.endOfTextblock(s)?o:null}}return null}function X(e,t){for(;t&&t!=e.dom;t=t.parentNode)if("TD"==t.nodeName||"TH"==t.nodeName)return t;return null}function Z(e,t){let n=e.posAtCoords({left:t.clientX,top:t.clientY});return n&&n?f(e.state.doc.resolve(n.pos)):null}var Y=class{constructor(e,t){this.node=e,this.defaultCellMinWidth=t,this.dom=document.createElement("div"),this.dom.className="tableWrapper",this.table=this.dom.appendChild(document.createElement("table")),this.table.style.setProperty("--default-cell-min-width",`${t}px`),this.colgroup=this.table.appendChild(document.createElement("colgroup")),Q(e,this.colgroup,this.table,t),this.contentDOM=this.table.appendChild(document.createElement("tbody"))}update(e){return e.type==this.node.type&&(this.node=e,Q(e,this.colgroup,this.table,this.defaultCellMinWidth),!0)}ignoreMutation(e){return"attributes"==e.type&&(e.target==this.table||this.colgroup.contains(e.target))}};function Q(e,t,n,r,i,o){var s;let l=0,a=!0,h=t.firstChild,c=e.firstChild;if(c){for(let e=0,n=0;e<c.childCount;e++){let{colspan:s,colwidth:d}=c.child(e).attrs;for(let e=0;e<s;e++,n++){let s=i==n?o:d&&d[e],c=s?s+"px":"";if(l+=s||r,s||(a=!1),h)h.style.width!=c&&(h.style.width=c),h=h.nextSibling;else{let e=document.createElement("col");e.style.width=c,t.appendChild(e)}}}for(;h;){let e=h.nextSibling;null==(s=h.parentNode)||s.removeChild(h),h=e}a?(n.style.width=l+"px",n.style.minWidth=""):(n.style.width="",n.style.minWidth=l+"px")}}var ee=new s.hs("tableColumnResizing"),et=class e{constructor(e,t){this.activeHandle=e,this.dragging=t}apply(t){let n=t.getMeta(ee);if(n&&null!=n.setHandle)return new e(n.setHandle,!1);if(n&&void 0!==n.setDragging)return new e(this.activeHandle,n.setDragging);if(this.activeHandle>-1&&t.docChanged){let n=t.mapping.map(this.activeHandle,-1);return y(t.doc.resolve(n))||(n=-1),new e(n,this.dragging)}return this}};function en(e,t,n,r){let i=e.posAtCoords({left:t.clientX+("right"==n?-r:r),top:t.clientY});if(!i)return -1;let{pos:o}=i,s=f(e.state.doc.resolve(o));if(!s)return -1;if("right"==n)return s.pos;let l=d.get(s.node(-1)),a=s.start(-1),h=l.map.indexOf(s.pos-a);return h%l.width==0?-1:a+l.map[h-1]}function er(e,t,n){let r=t.clientX-e.startX;return Math.max(n,e.startWidth+r)}function ei(e,t){e.dispatch(e.state.tr.setMeta(ee,{setHandle:t}))}function eo(e,t,n,r){let i=e.state.doc.resolve(t),o=i.node(-1),s=i.start(-1),l=d.get(o).colCount(i.pos-s)+i.nodeAfter.attrs.colspan-1,a=e.domAtPos(i.start(-1)).node;for(;a&&"TABLE"!=a.nodeName;)a=a.parentNode;a&&Q(o,a.firstChild,a,r,l,n)}function es(e,t){return t?["width",`${Math.max(t,e)}px`]:["min-width",`${e}px`]}function el(e,t,n,r,i,o){var s;let l=0,a=!0,h=t.firstChild,c=e.firstChild;if(null!==c)for(let e=0,n=0;e<c.childCount;e+=1){let{colspan:s,colwidth:d}=c.child(e).attrs;for(let e=0;e<s;e+=1,n+=1){let s=i===n?o:d&&d[e],c=s?`${s}px`:"";if(l+=s||r,s||(a=!1),h){if(h.style.width!==c){let[e,t]=es(r,s);h.style.setProperty(e,t)}h=h.nextSibling}else{let e=document.createElement("col"),[n,i]=es(r,s);e.style.setProperty(n,i),t.appendChild(e)}}}for(;h;){let e=h.nextSibling;null==(s=h.parentNode)||s.removeChild(h),h=e}a?(n.style.width=`${l}px`,n.style.minWidth=""):(n.style.width="",n.style.minWidth=`${l}px`)}class ea{constructor(e,t){this.node=e,this.cellMinWidth=t,this.dom=document.createElement("div"),this.dom.className="tableWrapper",this.table=this.dom.appendChild(document.createElement("table")),this.colgroup=this.table.appendChild(document.createElement("colgroup")),el(e,this.colgroup,this.table,t),this.contentDOM=this.table.appendChild(document.createElement("tbody"))}update(e){return e.type===this.node.type&&(this.node=e,el(e,this.colgroup,this.table,this.cellMinWidth),!0)}ignoreMutation(e){return"attributes"===e.type&&(e.target===this.table||this.colgroup.contains(e.target))}}function eh(e,t){return t?e.createChecked(null,t):e.createAndFill()}let ec=({editor:e})=>{let{selection:t}=e.state;if(!(t instanceof x))return!1;let n=0,r=(0,o.eL)(t.ranges[0].$from,e=>"table"===e.type.name);return null==r||r.node.descendants(e=>{if("table"===e.type.name)return!1;["tableCell","tableHeader"].includes(e.type.name)&&(n+=1)}),n===t.ranges.length&&(e.commands.deleteTable(),!0)},ed=o.bP.create({name:"table",addOptions:()=>({HTMLAttributes:{},resizable:!1,handleWidth:5,cellMinWidth:25,View:ea,lastColumnResizable:!0,allowTableNodeSelection:!1}),content:"tableRow+",tableRole:"table",isolating:!0,group:"block",parseHTML:()=>[{tag:"table"}],renderHTML({node:e,HTMLAttributes:t}){let{colgroup:n,tableWidth:r,tableMinWidth:i}=function(e,t,n,r){let i=0,o=!0,s=[],l=e.firstChild;if(!l)return{};for(let e=0,n=0;e<l.childCount;e+=1){let{colspan:r,colwidth:a}=l.child(e).attrs;for(let e=0;e<r;e+=1,n+=1){let r=void 0===n?void 0:a&&a[e];i+=r||t,r||(o=!1);let[l,h]=es(t,r);s.push(["col",{style:`${l}: ${h}`}])}}return{colgroup:["colgroup",{},...s],tableWidth:o?`${i}px`:"",tableMinWidth:o?"":`${i}px`}}(e,this.options.cellMinWidth);return["table",(0,o.KV)(this.options.HTMLAttributes,t,{style:r?`width: ${r}`:`min-width: ${i}`}),n,["tbody",0]]},addCommands:()=>({insertTable:({rows:e=3,cols:t=3,withHeaderRow:n=!0}={})=>({tr:r,dispatch:i,editor:o})=>{let l=function(e,t,n,r,i){let o=function(e){if(e.cached.tableNodeTypes)return e.cached.tableNodeTypes;let t={};return Object.keys(e.nodes).forEach(n=>{let r=e.nodes[n];r.spec.tableRole&&(t[r.spec.tableRole]=r)}),e.cached.tableNodeTypes=t,t}(e),s=[],l=[];for(let e=0;e<n;e+=1){let e=eh(o.cell,void 0);if(e&&l.push(e),r){let e=eh(o.header_cell,void 0);e&&s.push(e)}}let a=[];for(let e=0;e<t;e+=1)a.push(o.row.createChecked(null,r&&0===e?s:l));return o.table.createChecked(null,a)}(o.schema,e,t,n);if(i){let e=r.selection.from+1;r.replaceSelectionWith(l).scrollIntoView().setSelection(s.U3.near(r.doc.resolve(e)))}return!0},addColumnBefore:()=>({state:e,dispatch:t})=>(function(e,t){if(!g(e))return!1;if(t){let n=A(e);t(T(e.tr,n,n.left))}return!0})(e,t),addColumnAfter:()=>({state:e,dispatch:t})=>(function(e,t){if(!g(e))return!1;if(t){let n=A(e);t(T(e.tr,n,n.right))}return!0})(e,t),deleteColumn:()=>({state:e,dispatch:t})=>(function(e,t){if(!g(e))return!1;if(t){let n=A(e),r=e.tr;if(0==n.left&&n.right==n.map.width)return!1;for(let e=n.right-1;!function(e,{map:t,table:n,tableStart:r},i){let o=e.mapping.maps.length;for(let s=0;s<t.height;){let l=s*t.width+i,a=t.map[l],h=n.nodeAt(a),c=h.attrs;if(i>0&&t.map[l-1]==a||i<t.width-1&&t.map[l+1]==a)e.setNodeMarkup(e.mapping.slice(o).map(r+a),null,k(c,i-t.colCount(a)));else{let t=e.mapping.slice(o).map(r+a);e.delete(t,t+h.nodeSize)}s+=c.rowspan}}(r,n,e),e!=n.left;e--){let e=n.tableStart?r.doc.nodeAt(n.tableStart-1):r.doc;if(!e)throw RangeError("No table found");n.table=e,n.map=d.get(e)}t(r)}return!0})(e,t),addRowBefore:()=>({state:e,dispatch:t})=>(function(e,t){if(!g(e))return!1;if(t){let n=A(e);t(O(e.tr,n,n.top))}return!0})(e,t),addRowAfter:()=>({state:e,dispatch:t})=>(function(e,t){if(!g(e))return!1;if(t){let n=A(e);t(O(e.tr,n,n.bottom))}return!0})(e,t),deleteRow:()=>({state:e,dispatch:t})=>(function(e,t){if(!g(e))return!1;if(t){let n=A(e),r=e.tr;if(0==n.top&&n.bottom==n.map.height)return!1;for(let e=n.bottom-1;!function(e,{map:t,table:n,tableStart:r},i){let o=0;for(let e=0;e<i;e++)o+=n.child(e).nodeSize;let s=o+n.child(i).nodeSize,l=e.mapping.maps.length;e.delete(o+r,s+r);let a=new Set;for(let o=0,s=i*t.width;o<t.width;o++,s++){let h=t.map[s];if(!a.has(h)){if(a.add(h),i>0&&h==t.map[s-t.width]){let t=n.nodeAt(h).attrs;e.setNodeMarkup(e.mapping.slice(l).map(h+r),null,{...t,rowspan:t.rowspan-1}),o+=t.colspan-1}else if(i<t.height&&h==t.map[s+t.width]){let s=n.nodeAt(h),a=s.attrs,c=s.type.create({...a,rowspan:s.attrs.rowspan-1},s.content),d=t.positionAt(i+1,o,n);e.insert(e.mapping.slice(l).map(r+d),c),o+=a.colspan-1}}}}(r,n,e),e!=n.top;e--){let e=n.tableStart?r.doc.nodeAt(n.tableStart-1):r.doc;if(!e)throw RangeError("No table found");n.table=e,n.map=d.get(n.table)}t(r)}return!0})(e,t),deleteTable:()=>({state:e,dispatch:t})=>(function(e,t){let n=e.selection.$anchor;for(let r=n.depth;r>0;r--)if("table"==n.node(r).type.spec.tableRole)return t&&t(e.tr.delete(n.before(r),n.after(r)).scrollIntoView()),!0;return!1})(e,t),mergeCells:()=>({state:e,dispatch:t})=>N(e,t),splitCell:()=>({state:e,dispatch:t})=>I(e,t),toggleHeaderColumn:()=>({state:e,dispatch:t})=>_("column")(e,t),toggleHeaderRow:()=>({state:e,dispatch:t})=>_("row")(e,t),toggleHeaderCell:()=>({state:e,dispatch:t})=>z(e,t),mergeOrSplit:()=>({state:e,dispatch:t})=>!!N(e,t)||I(e,t),setCellAttribute:(e,t)=>({state:n,dispatch:r})=>(function(e,t){return function(n,r){if(!g(n))return!1;let i=m(n);if(i.nodeAfter.attrs[e]===t)return!1;if(r){let o=n.tr;n.selection instanceof x?n.selection.forEachCell((n,r)=>{n.attrs[e]!==t&&o.setNodeMarkup(r,null,{...n.attrs,[e]:t})}):o.setNodeMarkup(i.pos,null,{...i.nodeAfter.attrs,[e]:t}),r(o)}return!0}})(e,t)(n,r),goToNextCell:()=>({state:e,dispatch:t})=>$(1)(e,t),goToPreviousCell:()=>({state:e,dispatch:t})=>$(-1)(e,t),fixTables:()=>({state:e,dispatch:t})=>(t&&E(e),!0),setCellSelection:e=>({tr:t,dispatch:n})=>{if(n){let n=x.create(t.doc,e.anchorCell,e.headCell);t.setSelection(n)}return!0}}),addKeyboardShortcuts(){return{Tab:()=>!!this.editor.commands.goToNextCell()||!!this.editor.can().addRowAfter()&&this.editor.chain().addRowAfter().goToNextCell().run(),"Shift-Tab":()=>this.editor.commands.goToPreviousCell(),Backspace:ec,"Mod-Backspace":ec,Delete:ec,"Mod-Delete":ec}},addProseMirrorPlugins(){return[...this.options.resizable&&this.editor.isEditable?[function({handleWidth:e=5,cellMinWidth:t=25,defaultCellMinWidth:n=100,View:r=Y,lastColumnResizable:i=!0}={}){let o=new s.k_({key:ee,state:{init(e,t){var i,s;let l=null==(s=null==(i=o.spec)?void 0:i.props)?void 0:s.nodeViews,a=u(t.schema).table.name;return r&&l&&(l[a]=(e,t)=>new r(e,n,t)),new et(-1,!1)},apply:(e,t)=>t.apply(e)},props:{attributes:e=>{let t=ee.getState(e);return t&&t.activeHandle>-1?{class:"resize-cursor"}:{}},handleDOMEvents:{mousemove:(t,n)=>{!function(e,t,n,r){if(!e.editable)return;let i=ee.getState(e.state);if(i&&!i.dragging){let o=function(e){for(;e&&"TD"!=e.nodeName&&"TH"!=e.nodeName;)e=e.classList&&e.classList.contains("ProseMirror")?null:e.parentNode;return e}(t.target),s=-1;if(o){let{left:r,right:i}=o.getBoundingClientRect();t.clientX-r<=n?s=en(e,t,"left",n):i-t.clientX<=n&&(s=en(e,t,"right",n))}if(s!=i.activeHandle){if(!r&&-1!==s){let t=e.state.doc.resolve(s),n=t.node(-1),r=d.get(n),i=t.start(-1);if(r.colCount(t.pos-i)+t.nodeAfter.attrs.colspan-1==r.width-1)return}ei(e,s)}}}(t,n,e,i)},mouseleave:e=>{!function(e){if(!e.editable)return;let t=ee.getState(e.state);t&&t.activeHandle>-1&&!t.dragging&&ei(e,-1)}(e)},mousedown:(e,r)=>{!function(e,t,n,r){var i;if(!e.editable)return;let o=null!=(i=e.dom.ownerDocument.defaultView)?i:window,s=ee.getState(e.state);if(!s||-1==s.activeHandle||s.dragging)return;let l=e.state.doc.nodeAt(s.activeHandle),a=function(e,t,{colspan:n,colwidth:r}){let i=r&&r[r.length-1];if(i)return i;let o=e.domAtPos(t),s=o.node.childNodes[o.offset].offsetWidth,l=n;if(r)for(let e=0;e<n;e++)r[e]&&(s-=r[e],l--);return s/l}(e,s.activeHandle,l.attrs);function h(t){o.removeEventListener("mouseup",h),o.removeEventListener("mousemove",c);let r=ee.getState(e.state);(null==r?void 0:r.dragging)&&(function(e,t,n){let r=e.state.doc.resolve(t),i=r.node(-1),o=d.get(i),s=r.start(-1),l=o.colCount(r.pos-s)+r.nodeAfter.attrs.colspan-1,a=e.state.tr;for(let e=0;e<o.height;e++){let t=e*o.width+l;if(e&&o.map[t]==o.map[t-o.width])continue;let r=o.map[t],h=i.nodeAt(r).attrs,c=1==h.colspan?0:l-o.colCount(r);if(h.colwidth&&h.colwidth[c]==n)continue;let d=h.colwidth?h.colwidth.slice():Array(h.colspan).fill(0);d[c]=n,a.setNodeMarkup(s+r,null,{...h,colwidth:d})}a.docChanged&&e.dispatch(a)}(e,r.activeHandle,er(r.dragging,t,n)),e.dispatch(e.state.tr.setMeta(ee,{setDragging:null})))}function c(t){if(!t.which)return h(t);let i=ee.getState(e.state);if(i&&i.dragging){let o=er(i.dragging,t,n);eo(e,i.activeHandle,o,r)}}e.dispatch(e.state.tr.setMeta(ee,{setDragging:{startX:t.clientX,startWidth:a}})),eo(e,s.activeHandle,a,r),o.addEventListener("mouseup",h),o.addEventListener("mousemove",c),t.preventDefault()}(e,r,t,n)}},decorations:e=>{let t=ee.getState(e);if(t&&t.activeHandle>-1)return function(e,t){var n;let r=[],i=e.doc.resolve(t),o=i.node(-1);if(!o)return a.zF.empty;let s=d.get(o),l=i.start(-1),h=s.colCount(i.pos-l)+i.nodeAfter.attrs.colspan-1;for(let t=0;t<s.height;t++){let i=h+t*s.width;if((h==s.width-1||s.map[i]!=s.map[i+1])&&(0==t||s.map[i]!=s.map[i-s.width])){let t=s.map[i],h=l+t+o.nodeAt(t).nodeSize-1,c=document.createElement("div");c.className="column-resize-handle",(null==(n=ee.getState(e))?void 0:n.dragging)&&r.push(a.NZ.node(l+t,l+t+o.nodeAt(t).nodeSize,{class:"column-resize-dragging"})),r.push(a.NZ.widget(h,c))}}return a.zF.create(e.doc,r)}(e,t.activeHandle)},nodeViews:{}}});return o}({handleWidth:this.options.handleWidth,cellMinWidth:this.options.cellMinWidth,defaultCellMinWidth:this.options.cellMinWidth,View:this.options.View,lastColumnResizable:this.options.lastColumnResizable})]:[],function({allowTableNodeSelection:e=!1}={}){return new s.k_({key:p,state:{init:()=>null,apply(e,t){let n=e.getMeta(p);if(null!=n)return -1==n?null:n;if(null==t||!e.docChanged)return t;let{deleted:r,pos:i}=e.mapping.mapResult(t);return r?null:i}},props:{decorations:M,handleDOMEvents:{mousedown:q},createSelectionBetween:e=>null!=p.getState(e.state)?e.state.selection:null,handleTripleClick:U,handleKeyDown:F,handlePaste:V},appendTransaction:(t,n,r)=>(function(e,t,n){let r,i,o=(t||e).selection,l=(t||e).doc;if(o instanceof s.nh&&(i=o.node.type.spec.tableRole)){if("cell"==i||"header_cell"==i)r=x.create(l,o.from);else if("row"==i){let e=l.resolve(o.from+1);r=x.rowSelection(e,e)}else if(!n){let e=d.get(o.node),t=o.from+1,n=t+e.map[e.width*e.height-1];r=x.create(l,t+1,n)}}else o instanceof s.U3&&function({$from:e,$to:t}){if(e.pos==t.pos||e.pos<t.pos-6)return!1;let n=e.pos,r=t.pos,i=e.depth;for(;i>=0&&!(e.after(i+1)<e.end(i));i--,n++);for(let e=t.depth;e>=0&&!(t.before(e+1)>t.start(e));e--,r--);return n==r&&/row|table/.test(e.node(i).type.spec.tableRole)}(o)?r=s.U3.create(l,o.from):o instanceof s.U3&&function({$from:e,$to:t}){let n,r;for(let t=e.depth;t>0;t--){let r=e.node(t);if("cell"===r.type.spec.tableRole||"header_cell"===r.type.spec.tableRole){n=r;break}}for(let e=t.depth;e>0;e--){let n=t.node(e);if("cell"===n.type.spec.tableRole||"header_cell"===n.type.spec.tableRole){r=n;break}}return n!==r&&0===t.parentOffset}(o)&&(r=s.U3.create(l,o.$from.start(),o.$from.end()));return r&&(t||(t=e.tr)).setSelection(r),t})(r,E(r,n),e)})}({allowTableNodeSelection:this.options.allowTableNodeSelection})]},extendNodeSchema(e){let t={name:e.name,options:e.options,storage:e.storage};return{tableRole:(0,o.gk)((0,o.iI)(e,"tableRole",t))}}})},90290:(e,t,n)=>{"use strict";n.d(t,{$f:()=>N,G2:()=>k,I$:()=>C,Im:()=>L,Qv:()=>h,Sd:()=>b,Z1:()=>E,_G:()=>d,_e:()=>f,bh:()=>v,eB:()=>c,eT:()=>y,ec:()=>I,hy:()=>T,ic:()=>l,iz:()=>A,pC:()=>S,yY:()=>x,y_:()=>_});var r,i=n(808),o=n(10156),s=n(52571);let l=(e,t)=>!e.selection.empty&&(t&&t(e.tr.deleteSelection().scrollIntoView()),!0);function a(e,t){let{$cursor:n}=e.selection;return n&&(t?t.endOfTextblock("backward",e):!(n.parentOffset>0))?n:null}let h=(e,t,n)=>{let r=a(e,n);if(!r)return!1;let l=g(r);if(!l){let n=r.blockRange(),o=n&&(0,i.jP)(n);return null!=o&&(t&&t(e.tr.lift(n,o).scrollIntoView()),!0)}let h=l.nodeBefore;if(O(e,l,t,-1))return!0;if(0==r.parent.content.size&&(p(h,"end")||s.nh.isSelectable(h)))for(let n=r.depth;;n--){let a=(0,i.$L)(e.doc,r.before(n),r.after(n),o.Ji.empty);if(a&&a.slice.size<a.to-a.from){if(t){let n=e.tr.step(a);n.setSelection(p(h,"end")?s.LN.findFrom(n.doc.resolve(n.mapping.map(l.pos,-1)),-1):s.nh.create(n.doc,l.pos-h.nodeSize)),t(n.scrollIntoView())}return!0}if(1==n||r.node(n-1).childCount>1)break}return!!h.isAtom&&l.depth==r.depth-1&&(t&&t(e.tr.delete(l.pos-h.nodeSize,l.pos).scrollIntoView()),!0)},c=(e,t,n)=>{let r=a(e,n);if(!r)return!1;let i=g(r);return!!i&&u(e,i,t)},d=(e,t,n)=>{let r=m(e,n);if(!r)return!1;let i=w(r);return!!i&&u(e,i,t)};function u(e,t,n){let r=t.nodeBefore,l=t.pos-1;for(;!r.isTextblock;l--){if(r.type.spec.isolating)return!1;let e=r.lastChild;if(!e)return!1;r=e}let a=t.nodeAfter,h=t.pos+1;for(;!a.isTextblock;h++){if(a.type.spec.isolating)return!1;let e=a.firstChild;if(!e)return!1;a=e}let c=(0,i.$L)(e.doc,l,h,o.Ji.empty);if(!c||c.from!=l||c instanceof i.Ln&&c.slice.size>=h-l)return!1;if(n){let t=e.tr.step(c);t.setSelection(s.U3.create(t.doc,l)),n(t.scrollIntoView())}return!0}function p(e,t,n=!1){for(let r=e;r;r="start"==t?r.firstChild:r.lastChild){if(r.isTextblock)return!0;if(n&&1!=r.childCount)break}return!1}let f=(e,t,n)=>{let{$head:r,empty:i}=e.selection,o=r;if(!i)return!1;if(r.parent.isTextblock){if(n?!n.endOfTextblock("backward",e):r.parentOffset>0)return!1;o=g(r)}let l=o&&o.nodeBefore;return!!l&&!!s.nh.isSelectable(l)&&(t&&t(e.tr.setSelection(s.nh.create(e.doc,o.pos-l.nodeSize)).scrollIntoView()),!0)};function g(e){if(!e.parent.type.spec.isolating)for(let t=e.depth-1;t>=0;t--){if(e.index(t)>0)return e.doc.resolve(e.before(t+1));if(e.node(t).type.spec.isolating)break}return null}function m(e,t){let{$cursor:n}=e.selection;return n&&(t?t.endOfTextblock("forward",e):!(n.parentOffset<n.parent.content.size))?n:null}let y=(e,t,n)=>{let r=m(e,n);if(!r)return!1;let l=w(r);if(!l)return!1;let a=l.nodeAfter;if(O(e,l,t,1))return!0;if(0==r.parent.content.size&&(p(a,"start")||s.nh.isSelectable(a))){let n=(0,i.$L)(e.doc,r.before(),r.after(),o.Ji.empty);if(n&&n.slice.size<n.to-n.from){if(t){let r=e.tr.step(n);r.setSelection(p(a,"start")?s.LN.findFrom(r.doc.resolve(r.mapping.map(l.pos)),1):s.nh.create(r.doc,r.mapping.map(l.pos))),t(r.scrollIntoView())}return!0}}return!!a.isAtom&&l.depth==r.depth-1&&(t&&t(e.tr.delete(l.pos,l.pos+a.nodeSize).scrollIntoView()),!0)},b=(e,t,n)=>{let{$head:r,empty:i}=e.selection,o=r;if(!i)return!1;if(r.parent.isTextblock){if(n?!n.endOfTextblock("forward",e):r.parentOffset<r.parent.content.size)return!1;o=w(r)}let l=o&&o.nodeAfter;return!!l&&!!s.nh.isSelectable(l)&&(t&&t(e.tr.setSelection(s.nh.create(e.doc,o.pos)).scrollIntoView()),!0)};function w(e){if(!e.parent.type.spec.isolating)for(let t=e.depth-1;t>=0;t--){let n=e.node(t);if(e.index(t)+1<n.childCount)return e.doc.resolve(e.after(t+1));if(n.type.spec.isolating)break}return null}let k=(e,t)=>{let n=e.selection,r=n instanceof s.nh,o;if(r){if(n.node.isTextblock||!(0,i.n9)(e.doc,n.from))return!1;o=n.from}else if(null==(o=(0,i.N0)(e.doc,n.from,-1)))return!1;if(t){let n=e.tr.join(o);r&&n.setSelection(s.nh.create(n.doc,o-e.doc.resolve(o).nodeBefore.nodeSize)),t(n.scrollIntoView())}return!0},v=(e,t)=>{let n=e.selection,r;if(n instanceof s.nh){if(n.node.isTextblock||!(0,i.n9)(e.doc,n.to))return!1;r=n.to}else if(null==(r=(0,i.N0)(e.doc,n.to,1)))return!1;return t&&t(e.tr.join(r).scrollIntoView()),!0},x=(e,t)=>{let{$from:n,$to:r}=e.selection,o=n.blockRange(r),s=o&&(0,i.jP)(o);return null!=s&&(t&&t(e.tr.lift(o,s).scrollIntoView()),!0)},S=(e,t)=>{let{$head:n,$anchor:r}=e.selection;return!!n.parent.type.spec.code&&!!n.sameParent(r)&&(t&&t(e.tr.insertText("\n").scrollIntoView()),!0)};function M(e){for(let t=0;t<e.edgeCount;t++){let{type:n}=e.edge(t);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}let C=(e,t)=>{let{$head:n,$anchor:r}=e.selection;if(!n.parent.type.spec.code||!n.sameParent(r))return!1;let i=n.node(-1),o=n.indexAfter(-1),l=M(i.contentMatchAt(o));if(!l||!i.canReplaceWith(o,o,l))return!1;if(t){let r=n.after(),i=e.tr.replaceWith(r,r,l.createAndFill());i.setSelection(s.LN.near(i.doc.resolve(r),1)),t(i.scrollIntoView())}return!0},E=(e,t)=>{let n=e.selection,{$from:r,$to:i}=n;if(n instanceof s.i5||r.parent.inlineContent||i.parent.inlineContent)return!1;let o=M(i.parent.contentMatchAt(i.indexAfter()));if(!o||!o.isTextblock)return!1;if(t){let n=(!r.parentOffset&&i.index()<i.parent.childCount?r:i).pos,l=e.tr.insert(n,o.createAndFill());l.setSelection(s.U3.create(l.doc,n+1)),t(l.scrollIntoView())}return!0},A=(e,t)=>{let{$cursor:n}=e.selection;if(!n||n.parent.content.size)return!1;if(n.depth>1&&n.after()!=n.end(-1)){let r=n.before();if((0,i.zy)(e.doc,r))return t&&t(e.tr.split(r).scrollIntoView()),!0}let r=n.blockRange(),o=r&&(0,i.jP)(r);return null!=o&&(t&&t(e.tr.lift(r,o).scrollIntoView()),!0)},T=(e,t)=>{let{$from:n,to:r}=e.selection,i,o=n.sharedDepth(r);return 0!=o&&(i=n.before(o),t&&t(e.tr.setSelection(s.nh.create(e.doc,i))),!0)};function O(e,t,n,r){let l,a,h,c=t.nodeBefore,d=t.nodeAfter,u,f,g=c.type.spec.isolating||d.type.spec.isolating;if(!g&&(l=t.nodeBefore,a=t.nodeAfter,h=t.index(),l&&a&&l.type.compatibleContent(a.type)&&(!l.content.size&&t.parent.canReplace(h-1,h)?(n&&n(e.tr.delete(t.pos-l.nodeSize,t.pos).scrollIntoView()),!0):!!t.parent.canReplace(h,h+1)&&!!(a.isTextblock||(0,i.n9)(e.doc,t.pos))&&(n&&n(e.tr.join(t.pos).scrollIntoView()),!0))))return!0;let m=!g&&t.parent.canReplace(t.index(),t.index()+1);if(m&&(u=(f=c.contentMatchAt(c.childCount)).findWrapping(d.type))&&f.matchType(u[0]||d.type).validEnd){if(n){let r=t.pos+d.nodeSize,s=o.FK.empty;for(let e=u.length-1;e>=0;e--)s=o.FK.from(u[e].create(null,s));s=o.FK.from(c.copy(s));let l=e.tr.step(new i.Wg(t.pos-1,r,t.pos,r,new o.Ji(s,1,0),u.length,!0)),a=l.doc.resolve(r+2*u.length);a.nodeAfter&&a.nodeAfter.type==c.type&&(0,i.n9)(l.doc,a.pos)&&l.join(a.pos),n(l.scrollIntoView())}return!0}let y=d.type.spec.isolating||r>0&&g?null:s.LN.findFrom(t,1),b=y&&y.$from.blockRange(y.$to),w=b&&(0,i.jP)(b);if(null!=w&&w>=t.depth)return n&&n(e.tr.lift(b,w).scrollIntoView()),!0;if(m&&p(d,"start",!0)&&p(c,"end")){let r=c,s=[];for(;s.push(r),!r.isTextblock;)r=r.lastChild;let l=d,a=1;for(;!l.isTextblock;l=l.firstChild)a++;if(r.canReplace(r.childCount,r.childCount,l.content)){if(n){let r=o.FK.empty;for(let e=s.length-1;e>=0;e--)r=o.FK.from(s[e].copy(r));n(e.tr.step(new i.Wg(t.pos-s.length,t.pos+d.nodeSize,t.pos+a,t.pos+d.nodeSize-a,new o.Ji(r,s.length,0),0,!0)).scrollIntoView())}return!0}}return!1}function R(e){return function(t,n){let r=t.selection,i=e<0?r.$from:r.$to,o=i.depth;for(;i.node(o).isInline;){if(!o)return!1;o--}return!!i.node(o).isTextblock&&(n&&n(t.tr.setSelection(s.U3.create(t.doc,e<0?i.start(o):i.end(o)))),!0)}}let N=R(-1),I=R(1);function L(e,t=null){return function(n,r){let{$from:o,$to:s}=n.selection,l=o.blockRange(s),a=l&&(0,i.oM)(l,e,t);return!!a&&(r&&r(n.tr.wrap(l,a).scrollIntoView()),!0)}}function _(e,t=null){return function(n,r){let i=!1;for(let r=0;r<n.selection.ranges.length&&!i;r++){let{$from:{pos:o},$to:{pos:s}}=n.selection.ranges[r];n.doc.nodesBetween(o,s,(r,o)=>{if(i)return!1;if(!(!r.isTextblock||r.hasMarkup(e,t)))if(r.type==e)i=!0;else{let t=n.doc.resolve(o),r=t.index();i=t.parent.canReplaceWith(r,r+1,e)}})}if(!i)return!1;if(r){let i=n.tr;for(let r=0;r<n.selection.ranges.length;r++){let{$from:{pos:o},$to:{pos:s}}=n.selection.ranges[r];i.setBlockType(o,s,e,t)}r(i.scrollIntoView())}return!0}}function z(...e){return function(t,n,r){for(let i=0;i<e.length;i++)if(e[i](t,n,r))return!0;return!1}}let $=z(l,h,f),j=z(l,y,b),B={Enter:z(S,E,A,(e,t)=>{let{$from:n,$to:r}=e.selection;if(e.selection instanceof s.nh&&e.selection.node.isBlock)return!!n.parentOffset&&!!(0,i.zy)(e.doc,n.pos)&&(t&&t(e.tr.split(n.pos).scrollIntoView()),!0);if(!n.depth)return!1;let o=[],l,a,h=!1,c=!1;for(let e=n.depth;;e--){if(n.node(e).isBlock){let t;h=n.end(e)==n.pos+(n.depth-e),c=n.start(e)==n.pos-(n.depth-e),a=M(n.node(e-1).contentMatchAt(n.indexAfter(e-1)));o.unshift(t||(h&&a?{type:a}:null)),l=e;break}if(1==e)return!1;o.unshift(null)}let d=e.tr;(e.selection instanceof s.U3||e.selection instanceof s.i5)&&d.deleteSelection();let u=d.mapping.map(n.pos),p=(0,i.zy)(d.doc,u,o.length,o);if(p||(o[0]=a?{type:a}:null,p=(0,i.zy)(d.doc,u,o.length,o)),!p)return!1;if(d.split(u,o.length,o),!h&&c&&n.node(l).type!=a){let e=d.mapping.map(n.before(l)),t=d.doc.resolve(e);a&&n.node(l-1).canReplaceWith(t.index(),t.index()+1,a)&&d.setNodeMarkup(d.mapping.map(n.before(l)),a)}return t&&t(d.scrollIntoView()),!0}),"Mod-Enter":C,Backspace:$,"Mod-Backspace":$,"Shift-Backspace":$,Delete:j,"Mod-Delete":j,"Mod-a":(e,t)=>(t&&t(e.tr.setSelection(new s.i5(e.doc))),!0)},H={"Ctrl-h":B.Backspace,"Alt-Backspace":B["Mod-Backspace"],"Ctrl-d":B.Delete,"Ctrl-Alt-Backspace":B["Mod-Delete"],"Alt-Delete":B["Mod-Delete"],"Alt-d":B["Mod-Delete"],"Ctrl-a":N,"Ctrl-e":I};for(let e in B)H[e]=B[e];"undefined"!=typeof navigator?/Mac|iP(hone|[oa]d)/.test(navigator.platform):"undefined"!=typeof os&&os.platform&&os.platform()},92406:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("heading-3",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M17.5 10.5c1.7-1 3.5 0 3.5 1.5a2 2 0 0 1-2 2",key:"68ncm8"}],["path",{d:"M17 17.5c2 1.5 4 .3 4-1.5a2 2 0 0 0-2-2",key:"1ejuhz"}]])},93654:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("undo",[["path",{d:"M3 7v6h6",key:"1v2h90"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13",key:"1r6uu6"}]])},96770:(e,t,n)=>{"use strict";n.d(t,{K:()=>f,w:()=>p});for(var r={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},i={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},o="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),s="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),l=0;l<10;l++)r[48+l]=r[96+l]=String(l);for(var l=1;l<=24;l++)r[l+111]="F"+l;for(var l=65;l<=90;l++)r[l]=String.fromCharCode(l+32),i[l]=String.fromCharCode(l);for(var a in r)i.hasOwnProperty(a)||(i[a]=r[a]);var h=n(52571);let c="undefined"!=typeof navigator&&/Mac|iP(hone|[oa]d)/.test(navigator.platform),d="undefined"!=typeof navigator&&/Win/.test(navigator.platform);function u(e,t,n=!0){return t.altKey&&(e="Alt-"+e),t.ctrlKey&&(e="Ctrl-"+e),t.metaKey&&(e="Meta-"+e),n&&t.shiftKey&&(e="Shift-"+e),e}function p(e){return new h.k_({props:{handleKeyDown:f(e)}})}function f(e){let t=function(e){let t=Object.create(null);for(let n in e)t[function(e){let t,n,r,i,o=e.split(/-(?!$)/),s=o[o.length-1];"Space"==s&&(s=" ");for(let e=0;e<o.length-1;e++){let s=o[e];if(/^(cmd|meta|m)$/i.test(s))i=!0;else if(/^a(lt)?$/i.test(s))t=!0;else if(/^(c|ctrl|control)$/i.test(s))n=!0;else if(/^s(hift)?$/i.test(s))r=!0;else if(/^mod$/i.test(s))c?i=!0:n=!0;else throw Error("Unrecognized modifier name: "+s)}return t&&(s="Alt-"+s),n&&(s="Ctrl-"+s),i&&(s="Meta-"+s),r&&(s="Shift-"+s),s}(n)]=e[n];return t}(e);return function(e,n){var l;let a=("Esc"==(l=!(o&&n.metaKey&&n.shiftKey&&!n.ctrlKey&&!n.altKey||s&&n.shiftKey&&n.key&&1==n.key.length||"Unidentified"==n.key)&&n.key||(n.shiftKey?i:r)[n.keyCode]||n.key||"Unidentified")&&(l="Escape"),"Del"==l&&(l="Delete"),"Left"==l&&(l="ArrowLeft"),"Up"==l&&(l="ArrowUp"),"Right"==l&&(l="ArrowRight"),"Down"==l&&(l="ArrowDown"),l),h,c=t[u(a,n)];if(c&&c(e.state,e.dispatch,e))return!0;if(1==a.length&&" "!=a){if(n.shiftKey){let r=t[u(a,n,!1)];if(r&&r(e.state,e.dispatch,e))return!0}if((n.altKey||n.metaKey||n.ctrlKey)&&!(d&&n.ctrlKey&&n.altKey)&&(h=r[n.keyCode])&&h!=a){let r=t[u(h,n)];if(r&&r(e.state,e.dispatch,e))return!0}}return!1}}}}]);