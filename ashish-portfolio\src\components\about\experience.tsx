"use client";

import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Calendar, MapPin, ExternalLink, Briefcase } from "lucide-react";
import { VerticalTimeline, VerticalTimelineElement } from 'react-vertical-timeline-component';
import 'react-vertical-timeline-component/style.min.css';
import { Badge } from "@/components/ui/badge";
import { api, getPublishedExperiences, type Experience } from "@/lib/api";
import { useEffect, useState } from "react";
import Image from "next/image";

// Fallback experiences data (will be replaced by CMS data)
const fallbackExperiences = [
  {
    title: "Senior Full Stack Developer",
    company: "TechCorp Solutions",
    location: "Mumbai, India",
    period: "2022 - Present",
    type: "Full-time",
    description: "Leading development of scalable web applications using React, Next.js, and Node.js. Mentoring junior developers and implementing best practices for code quality and performance.",
    achievements: [
      "Increased application performance by 40% through optimization",
      "Led a team of 5 developers on multiple projects",
      "Implemented CI/CD pipelines reducing deployment time by 60%",
      "Architected microservices handling 1M+ requests daily"
    ],
    technologies: ["React", "Next.js", "TypeScript", "Node.js", "PostgreSQL", "AWS"],
    website: "https://techcorp.com"
  },
  {
    title: "Full Stack Developer",
    company: "StartupXYZ",
    location: "Remote",
    period: "2021 - 2022",
    type: "Full-time",
    description: "Developed and maintained multiple client projects using modern web technologies. Collaborated with design teams to create pixel-perfect user interfaces.",
    achievements: [
      "Built 10+ responsive web applications from scratch",
      "Reduced page load times by 50% through optimization",
      "Implemented real-time features using WebSocket",
      "Mentored 3 junior developers"
    ],
    technologies: ["React", "Vue.js", "Express.js", "MongoDB", "Firebase"],
    website: "https://startupxyz.com"
  },
  {
    title: "Frontend Developer",
    company: "Digital Agency Pro",
    location: "Mumbai, India",
    period: "2020 - 2021",
    type: "Full-time",
    description: "Focused on creating beautiful and functional user interfaces for various client projects. Worked closely with designers to bring mockups to life.",
    achievements: [
      "Delivered 20+ client projects on time and within budget",
      "Improved client satisfaction scores by 25%",
      "Implemented responsive designs for mobile-first approach",
      "Created reusable component library"
    ],
    technologies: ["HTML", "CSS", "JavaScript", "React", "Sass", "Figma"],
    website: "https://digitalagencypro.com"
  },
  {
    title: "Junior Web Developer",
    company: "WebDev Studio",
    location: "Mumbai, India",
    period: "2019 - 2020",
    type: "Full-time",
    description: "Started my professional journey learning modern web development practices and contributing to various client projects.",
    achievements: [
      "Completed 15+ small to medium-sized projects",
      "Learned modern JavaScript frameworks and tools",
      "Contributed to team's coding standards documentation",
      "Achieved 95% client satisfaction rating"
    ],
    technologies: ["HTML", "CSS", "JavaScript", "jQuery", "Bootstrap", "PHP"],
    website: "https://webdevstudio.com"
  }
];

export function Experience() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [experiences, setExperiences] = useState<Experience[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchExperiences = async () => {
      try {
        const data = await api.getExperiences();
        setExperiences(getPublishedExperiences(data));
      } catch (error) {
        console.error('Failed to fetch experiences:', error);
        // Use fallback data if API fails
        setExperiences(fallbackExperiences.map((exp, index) => ({
          id: `fallback-${index}`,
          title: exp.title,
          company: exp.company,
          companyLogo: undefined,
          location: exp.location,
          period: exp.period,
          type: exp.type,
          description: exp.description,
          achievements: exp.achievements,
          technologies: exp.technologies,
          website: exp.website,
          order: index,
          published: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })));
      } finally {
        setLoading(false);
      }
    };

    fetchExperiences();
  }, []);

  if (loading) {
    return (
      <section className="py-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-300 rounded w-64 mx-auto mb-4"></div>
              <div className="h-4 bg-gray-300 rounded w-96 mx-auto"></div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section ref={ref} className="py-20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
            <span className="gradient-text">Work Experience</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            My professional journey in web development, from junior developer to senior full-stack engineer.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={inView ? { opacity: 1 } : {}}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <VerticalTimeline>
            {experiences.map((experience) => {
              return (
                <VerticalTimelineElement
                  key={experience.id}
                  className="vertical-timeline-element--work"
                  contentStyle={{
                    background: 'hsl(var(--card))',
                    color: 'hsl(var(--card-foreground))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '12px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                  }}
                  contentArrowStyle={{
                    borderRight: '7px solid hsl(var(--border))',
                  }}
                  date={experience.period}
                  iconStyle={{
                    background: '#ffffff',
                    color: '#000',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    border: '3px solid hsl(var(--background))',
                    boxShadow: '0 0 0 2px hsl(var(--border))',
                    width: '60px',
                    height: '60px',
                  }}
                  icon={
                    experience.companyLogo ? (
                      <Image
                        src={experience.companyLogo}
                        alt={`${experience.company} logo`}
                        width={40}
                        height={40}
                        className="rounded-full object-contain"
                      />
                    ) : (
                      <Briefcase className="h-6 w-6" />
                    )
                  }
                >
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-xl font-bold">{experience.title}</h3>
                      <div className="flex items-center space-x-2 text-muted-foreground mt-1">
                        <span className="font-medium text-primary">{experience.company}</span>
                        {experience.website && (
                          <a
                            href={experience.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="hover:text-primary transition-colors"
                          >
                            <ExternalLink className="h-4 w-4" />
                          </a>
                        )}
                      </div>
                      <div className="flex flex-col sm:flex-row gap-2 text-sm text-muted-foreground mt-2">
                        <div className="flex items-center space-x-1">
                          <MapPin className="h-4 w-4 shrink-0 translate-y-[1px]" />
                          <span className="leading-none">{experience.location}</span>
                        </div>
                        <Badge variant="outline" className="w-fit">{experience.type}</Badge>
                      </div>
                    </div>

                    <p className="text-muted-foreground">{experience.description}</p>

                    <div>
                      <h4 className="font-semibold mb-2">Key Achievements:</h4>
                      <ul className="space-y-1">
                        {experience.achievements.map((achievement, achievementIndex) => (
                          <li key={achievementIndex} className="flex items-start space-x-2 text-sm text-muted-foreground">
                            <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0" />
                            <span>{achievement}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-2">Technologies:</h4>
                      <div className="flex flex-wrap gap-2">
                        {experience.technologies.map((tech) => (
                          <Badge key={tech} variant="secondary" className="text-xs">
                            {tech}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </VerticalTimelineElement>
              );
            })}
          </VerticalTimeline>
        </motion.div>
      </div>
    </section>
  );
}
