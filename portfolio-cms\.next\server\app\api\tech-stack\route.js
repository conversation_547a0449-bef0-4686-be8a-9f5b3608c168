(()=>{var e={};e.id=5501,e.ids=[5501],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var s=r(13581),o=r(16467),a=r(94747),i=r(85663);let n={adapter:(0,o.y)(a.z),providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=await a.z.user.findUnique({where:{email:e.email}});return t&&await i.Ay.compare(e.password,t.password)?{id:t.id,email:t.email,name:t.name,role:t.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},27746:(e,t,r)=>{"use strict";r.d(t,{JB:()=>n,gx:()=>i});var s=r(32190);let o=["http://localhost:3000","http://localhost:3001","http://localhost:3002","http://localhost:3003","http://127.0.0.1:3000","http://127.0.0.1:3001","http://127.0.0.1:3002","http://127.0.0.1:3003"];function a(e){return{"Access-Control-Allow-Origin":e&&o.includes(e)?e:"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-Requested-With","Access-Control-Allow-Credentials":"true","Access-Control-Max-Age":"86400"}}function i(e,t){return Object.entries(a(t)).forEach(([t,r])=>{e.headers.set(t,r)}),e}function n(e){return new s.NextResponse(null,{status:200,headers:a(e)})}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55521:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>w,serverHooks:()=>y,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>d,OPTIONS:()=>x,POST:()=>h});var o=r(96559),a=r(48088),i=r(37719),n=r(32190),c=r(19854),u=r(12909),l=r(94747),p=r(27746);async function d(){try{let e=await l.z.techStack.findMany({orderBy:[{category:"asc"},{order:"asc"}]});return(0,p.gx)(n.NextResponse.json(e))}catch(e){return console.error("Error fetching tech stack:",e),(0,p.gx)(n.NextResponse.json({error:"Failed to fetch tech stack"},{status:500}))}}async function h(e){try{let t=await (0,c.getServerSession)(u.N);if(!t?.user)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{name:r,logo:s,color:o,category:a,order:i,published:d}=await e.json(),h=await l.z.techStack.create({data:{name:r,logo:s,color:o,category:a,order:i||0,published:void 0===d||d}});return(0,p.gx)(n.NextResponse.json(h,{status:201}))}catch(e){return console.error("Error creating tech:",e),(0,p.gx)(n.NextResponse.json({error:"Failed to create tech"},{status:500}))}}async function x(){return(0,p.JB)()}let w=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/tech-stack/route",pathname:"/api/tech-stack",filename:"route",bundlePath:"app/api/tech-stack/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\tech-stack\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:m,serverHooks:y}=w;function f(){return(0,i.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:m})}},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},94747:(e,t,r)=>{"use strict";r.d(t,{z:()=>o});let s=require("@prisma/client"),o=globalThis.prisma??new s.PrismaClient},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,3542,2190],()=>r(55521));module.exports=s})();