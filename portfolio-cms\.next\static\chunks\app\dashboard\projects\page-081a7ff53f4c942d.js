(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7636],{13717:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},26126:(e,t,r)=>{"use strict";r.d(t,{E:()=>c});var s=r(95155);r(12115);var a=r(99708),n=r(74466),i=r(59434);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:r,asChild:n=!1,...c}=e,d=n?a.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(l({variant:r}),t),...c})}},33786:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},62525:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},83604:(e,t,r)=>{Promise.resolve().then(r.bind(r,98807))},84616:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85127:(e,t,r)=>{"use strict";r.d(t,{A0:()=>i,BF:()=>l,Hj:()=>c,XI:()=>n,nA:()=>o,nd:()=>d});var s=r(95155);r(12115);var a=r(59434);function n(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",t),...r})})}function i(e){let{className:t,...r}=e;return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",t),...r})}function l(e){let{className:t,...r}=e;return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",t),...r})}function c(e){let{className:t,...r}=e;return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...r})}function d(e){let{className:t,...r}=e;return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...r})}function o(e){let{className:t,...r}=e;return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...r})}},98807:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var s=r(95155),a=r(12115),n=r(35695),i=r(83930),l=r(30285),c=r(66695),d=r(85127),o=r(26126),h=r(84616),x=r(33786);let u=(0,r(19946).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]);var j=r(13717),p=r(62525);function v(){let e=(0,n.useRouter)(),[t,r]=(0,a.useState)([]),[v,f]=(0,a.useState)(!0);(0,a.useEffect)(()=>{g()},[]);let g=async()=>{try{let e=await fetch("/api/projects"),t=await e.json();r(t)}catch(e){console.error("Error fetching projects:",e)}finally{f(!1)}},b=async e=>{if(confirm("Are you sure you want to delete this project?"))try{(await fetch("/api/projects/".concat(e),{method:"DELETE"})).ok&&r(t.filter(t=>t.id!==e))}catch(e){console.error("Error deleting project:",e)}};return(0,s.jsx)(i.DashboardLayout,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Projects"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Manage your portfolio projects"})]}),(0,s.jsxs)(l.$,{onClick:()=>e.push("/dashboard/projects/new"),children:[(0,s.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Add Project"]})]}),(0,s.jsxs)(c.Zp,{children:[(0,s.jsxs)(c.aR,{children:[(0,s.jsx)(c.ZB,{children:"All Projects"}),(0,s.jsx)(c.BT,{children:"A list of all your portfolio projects"})]}),(0,s.jsx)(c.Wu,{children:v?(0,s.jsx)("div",{className:"text-center py-4",children:"Loading..."}):(0,s.jsxs)(d.XI,{children:[(0,s.jsx)(d.A0,{children:(0,s.jsxs)(d.Hj,{children:[(0,s.jsx)(d.nd,{children:"Title"}),(0,s.jsx)(d.nd,{children:"Category"}),(0,s.jsx)(d.nd,{children:"Technologies"}),(0,s.jsx)(d.nd,{children:"Status"}),(0,s.jsx)(d.nd,{children:"Links"}),(0,s.jsx)(d.nd,{children:"Actions"})]})}),(0,s.jsx)(d.BF,{children:t.map(t=>(0,s.jsxs)(d.Hj,{children:[(0,s.jsx)(d.nA,{children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:t.title}),(0,s.jsxs)("div",{className:"text-sm text-muted-foreground",children:[t.description.substring(0,60),"..."]})]})}),(0,s.jsx)(d.nA,{children:(0,s.jsx)(o.E,{variant:"outline",children:t.category})}),(0,s.jsx)(d.nA,{children:(0,s.jsxs)("div",{className:"flex flex-wrap gap-1",children:[t.technologies.slice(0,3).map(e=>(0,s.jsx)(o.E,{variant:"secondary",className:"text-xs",children:e},e)),t.technologies.length>3&&(0,s.jsxs)(o.E,{variant:"secondary",className:"text-xs",children:["+",t.technologies.length-3]})]})}),(0,s.jsx)(d.nA,{children:(0,s.jsxs)("div",{className:"flex gap-1",children:[t.featured&&(0,s.jsx)(o.E,{variant:"default",children:"Featured"}),(0,s.jsx)(o.E,{variant:t.published?"default":"secondary",children:t.published?"Published":"Draft"})]})}),(0,s.jsx)(d.nA,{children:(0,s.jsxs)("div",{className:"flex gap-2",children:[t.liveUrl&&(0,s.jsx)(l.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,s.jsx)("a",{href:t.liveUrl,target:"_blank",rel:"noopener noreferrer",children:(0,s.jsx)(x.A,{className:"h-4 w-4"})})}),t.githubUrl&&(0,s.jsx)(l.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,s.jsx)("a",{href:t.githubUrl,target:"_blank",rel:"noopener noreferrer",children:(0,s.jsx)(u,{className:"h-4 w-4"})})})]})}),(0,s.jsx)(d.nA,{children:(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>e.push("/dashboard/projects/".concat(t.id)),children:(0,s.jsx)(j.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>b(t.id),children:(0,s.jsx)(p.A,{className:"h-4 w-4"})})]})})]},t.id))})]})})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4854,2108,8858,9368,9008,8441,1684,7358],()=>t(83604)),_N_E=e.O()}]);