(()=>{var e={};e.id=5953,e.ids=[5953],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,t,r)=>{"use strict";r.d(t,{KE:()=>m,_C:()=>u,cn:()=>l,z9:()=>c});var s=r(49384),n=r(82348),i=r(85126),a=r.n(i),o=r(26728),d=r.n(o);function l(...e){return(0,n.QP)((0,s.$)(e))}function c(e){return a()(e,{lower:!0,strict:!0,remove:/[*+~.()'"!:@]/g})}function u(e){return Math.ceil(d()(e).minutes)}function m(e){try{let t=JSON.parse(e);if("doc"===t.type&&t.content)return function e(t){let r="";for(let s of t)"text"===s.type?r+=s.text||"":s.content&&(r+=e(s.content)),["paragraph","heading","listItem"].includes(s.type)&&(r+=" ");return r.trim()}(t.content)}catch{}return e.replace(/<[^>]*>/g," ").replace(/\s+/g," ").trim()}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16189:(e,t,r)=>{"use strict";var s=r(65773);r.o(s,"notFound")&&r.d(t,{notFound:function(){return s.notFound}}),r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(60687);r(43210);var n=r(8730),i=r(24224),a=r(4780);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:r,asChild:i=!1,...d}){let l=i?n.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,a.cn)(o({variant:t,size:r,className:e})),...d})}},33873:e=>{"use strict";e.exports=require("path")},40228:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>i,aR:()=>a});var s=r(60687);r(43210);var n=r(4780);function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function a({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}},46627:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},48730:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},49633:(e,t,r)=>{Promise.resolve().then(r.bind(r,83305))},56837:(e,t,r)=>{Promise.resolve().then(r.bind(r,89427))},60699:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},69989:(e,t,r)=>{Promise.resolve().then(r.bind(r,99901))},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},80415:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>i});var s=r(60687),n=r(82136);function i({children:e}){return(0,s.jsx)(n.SessionProvider,{children:e})}},80558:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var s=r(65239),n=r(48088),i=r(88170),a=r.n(i),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l={children:["",{children:["blog",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,89427)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\blog\\[slug]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\blog\\[slug]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/blog/[slug]/page",pathname:"/blog/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},83305:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\providers\\session-provider.tsx","AuthProvider")},86081:(e,t,r)=>{Promise.resolve().then(r.bind(r,80415))},89427:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\portfolio\\\\portfolio-cms\\\\src\\\\app\\\\blog\\\\[slug]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\blog\\[slug]\\page.tsx","default")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>l});var s=r(37413),n=r(22376),i=r.n(n),a=r(68726),o=r.n(a);r(61135);var d=r(83305);let l={title:"Portfolio CMS",description:"Content Management System for Portfolio Website"};function c({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${i().variable} ${o().variable} antialiased`,children:(0,s.jsx)(d.AuthProvider,{children:e})})})}},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>d});var s=r(60687);r(43210);var n=r(8730),i=r(24224),a=r(4780);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:r=!1,...i}){let d=r?n.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,a.cn)(o({variant:t}),e),...i})}},99901:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(60687),n=r(43210),i=r(16189),a=r(85814),o=r.n(a),d=r(29523),l=r(96834),c=r(44493),u=r(28559),m=r(63143),p=r(40228),h=r(48730),v=r(13861),x=r(30474);function g(){(0,i.useParams)().slug;let[e,t]=(0,n.useState)(null),[r,a]=(0,n.useState)(!0);return r?(0,s.jsx)("div",{className:"min-h-screen bg-background p-8",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,s.jsx)("div",{className:"text-center",children:"Loading blog post..."})})}):(e||(0,i.notFound)(),(0,s.jsx)("div",{className:"min-h-screen bg-background p-8",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,s.jsx)(o(),{href:"/blog",children:(0,s.jsxs)(d.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Back to Blog"]})}),(0,s.jsx)(o(),{href:`/dashboard/blog/${e.id}/edit`,children:(0,s.jsxs)(d.$,{size:"sm",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Edit Post"]})})]}),(0,s.jsx)(c.Zp,{children:(0,s.jsxs)(c.Wu,{className:"p-0",children:[e.image&&(0,s.jsxs)("div",{className:"relative h-64 md:h-80 overflow-hidden rounded-t-lg",children:[(0,s.jsx)(x.default,{src:e.image,alt:e.title,fill:!0,className:"object-cover"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,s.jsx)("div",{className:"absolute bottom-4 left-4 right-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)(l.E,{variant:"secondary",className:"bg-background/80 backdrop-blur-sm",children:e.category}),e.featured&&(0,s.jsx)(l.E,{variant:"default",children:"Featured"}),!e.published&&(0,s.jsx)(l.E,{variant:"destructive",children:"Draft"})]})})]}),(0,s.jsxs)("div",{className:"p-8",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h1",{className:"text-3xl md:text-4xl font-bold mb-4",children:e.title}),(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(p.A,{className:"mr-1 h-4 w-4"}),new Date(e.publishedAt||e.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(h.A,{className:"mr-1 h-4 w-4"}),e.readTime||5," min read"]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(v.A,{className:"mr-1 h-4 w-4"}),e.views," views"]}),(0,s.jsxs)("div",{children:["By ",e.author.name]})]}),(0,s.jsx)("p",{className:"text-lg text-muted-foreground mb-6",children:e.excerpt}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:e.tags.map((e,t)=>(0,s.jsx)(l.E,{variant:"outline",children:e},t))})]}),(0,s.jsx)("div",{className:"prose prose-gray dark:prose-invert max-w-none",dangerouslySetInnerHTML:{__html:e.content}})]})]})})]})}))}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,3310,1658,8580,4258,474],()=>r(80558));module.exports=s})();