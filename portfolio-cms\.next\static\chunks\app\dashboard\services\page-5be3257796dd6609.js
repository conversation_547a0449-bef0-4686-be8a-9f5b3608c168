(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9834],{13717:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},26126:(e,s,t)=>{"use strict";t.d(s,{E:()=>c});var r=t(95155);t(12115);var a=t(99708),i=t(74466),n=t(59434);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:s,variant:t,asChild:i=!1,...c}=e,d=i?a.DX:"span";return(0,r.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(l({variant:t}),s),...c})}},29513:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var r=t(95155),a=t(12115),i=t(83930),n=t(30285),l=t(66695),c=t(85127),d=t(26126),o=t(84616),h=t(19946);let x=(0,h.A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]),u=(0,h.A)("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);var v=t(13717),m=t(62525);function f(){let[e,s]=(0,a.useState)([]),[t,h]=(0,a.useState)(!0);(0,a.useEffect)(()=>{f()},[]);let f=async()=>{try{let e=await fetch("/api/services"),t=await e.json();s(t)}catch(e){console.error("Error fetching services:",e)}finally{h(!1)}},j=async t=>{if(confirm("Are you sure you want to delete this service?"))try{(await fetch("/api/services/".concat(t),{method:"DELETE"})).ok&&s(e.filter(e=>e.id!==t))}catch(e){console.error("Error deleting service:",e)}};return(0,r.jsx)(i.DashboardLayout,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Services"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage your service offerings"})]}),(0,r.jsxs)(n.$,{children:[(0,r.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Add Service"]})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsx)(l.ZB,{children:"All Services"}),(0,r.jsx)(l.BT,{children:"A list of all your service offerings"})]}),(0,r.jsx)(l.Wu,{children:t?(0,r.jsx)("div",{className:"text-center py-4",children:"Loading..."}):(0,r.jsxs)(c.XI,{children:[(0,r.jsx)(c.A0,{children:(0,r.jsxs)(c.Hj,{children:[(0,r.jsx)(c.nd,{children:"Service"}),(0,r.jsx)(c.nd,{children:"Features"}),(0,r.jsx)(c.nd,{children:"Order"}),(0,r.jsx)(c.nd,{children:"Status"}),(0,r.jsx)(c.nd,{children:"Actions"})]})}),(0,r.jsx)(c.BF,{children:e.map(e=>(0,r.jsxs)(c.Hj,{children:[(0,r.jsx)(c.nA,{children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"p-2 rounded-lg ".concat(e.bgColor),children:(0,r.jsx)("span",{className:"text-lg",children:e.icon})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:e.title}),(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:[e.description.substring(0,80),"..."]})]})]})}),(0,r.jsx)(c.nA,{children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.features.slice(0,2).map((e,s)=>(0,r.jsx)(d.E,{variant:"secondary",className:"text-xs",children:e},s)),e.features.length>2&&(0,r.jsxs)(d.E,{variant:"secondary",className:"text-xs",children:["+",e.features.length-2]})]})}),(0,r.jsx)(c.nA,{children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:e.order}),(0,r.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,r.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",children:(0,r.jsx)(x,{className:"h-3 w-3"})}),(0,r.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",children:(0,r.jsx)(u,{className:"h-3 w-3"})})]})]})}),(0,r.jsx)(c.nA,{children:(0,r.jsx)(d.E,{variant:e.published?"default":"secondary",children:e.published?"Published":"Draft"})}),(0,r.jsx)(c.nA,{children:(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,r.jsx)(v.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>j(e.id),children:(0,r.jsx)(m.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})]})]})})}},62525:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},84616:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85127:(e,s,t)=>{"use strict";t.d(s,{A0:()=>n,BF:()=>l,Hj:()=>c,XI:()=>i,nA:()=>o,nd:()=>d});var r=t(95155);t(12115);var a=t(59434);function i(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",s),...t})})}function n(e){let{className:s,...t}=e;return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",s),...t})}function l(e){let{className:s,...t}=e;return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",s),...t})}function c(e){let{className:s,...t}=e;return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",s),...t})}function d(e){let{className:s,...t}=e;return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...t})}function o(e){let{className:s,...t}=e;return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...t})}},88346:(e,s,t)=>{Promise.resolve().then(t.bind(t,29513))}},e=>{var s=s=>e(e.s=s);e.O(0,[4854,2108,8858,9368,9008,8441,1684,7358],()=>s(88346)),_N_E=e.O()}]);