(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8927],{6453:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var a=r(95155),s=r(12115),i=r(35695),l=r(83930),n=r(30285),o=r(66695),d=r(62523),c=r(85057),u=r(88539),h=r(31949),p=r(62154),m=r(35169),x=r(62525),v=r(84616),b=r(56671);function g(){let e=(0,i.useRouter)(),[t,r]=(0,s.useState)(!1),[g,f]=(0,s.useState)({title:"",company:"",companyLogo:"",location:"",period:"",type:"Full-time",description:"",achievements:[""],technologies:[""],website:"",order:0,published:!0}),y=(e,t)=>{f(r=>({...r,[e]:t}))},j=(e,t,r)=>{f(a=>({...a,[e]:a[e].map((e,a)=>a===t?r:e)}))},k=e=>{f(t=>({...t,[e]:[...t[e],""]}))},w=(e,t)=>{f(r=>({...r,[e]:r[e].filter((e,r)=>r!==t)}))},N=async t=>{t.preventDefault(),r(!0);try{let t={...g,achievements:g.achievements.filter(e=>""!==e.trim()),technologies:g.technologies.filter(e=>""!==e.trim())};if(!(await fetch("/api/experiences",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)})).ok)throw Error("Failed to create experience");b.oR.success("Experience created successfully"),e.push("/dashboard/experiences")}catch(e){console.error("Error creating experience:",e),b.oR.error("Failed to create experience")}finally{r(!1)}};return(0,a.jsx)(l.DashboardLayout,{children:(0,a.jsxs)("div",{className:"container mx-auto py-8",children:[(0,a.jsxs)("div",{className:"flex items-center mb-8",children:[(0,a.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>e.back(),className:"mr-4",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Add Experience"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Create a new work experience entry"})]})]}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsx)(o.ZB,{children:"Experience Details"})}),(0,a.jsx)(o.Wu,{children:(0,a.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"title",children:"Job Title *"}),(0,a.jsx)(d.p,{id:"title",value:g.title,onChange:e=>y("title",e.target.value),required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"company",children:"Company *"}),(0,a.jsx)(d.p,{id:"company",value:g.company,onChange:e=>y("company",e.target.value),required:!0})]}),(0,a.jsx)("div",{className:"md:col-span-2",children:(0,a.jsx)(p.B,{label:"Company Logo",value:g.companyLogo,onChange:e=>y("companyLogo",e),onRemove:()=>y("companyLogo","")})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"location",children:"Location *"}),(0,a.jsx)(d.p,{id:"location",value:g.location,onChange:e=>y("location",e.target.value),required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"period",children:"Period *"}),(0,a.jsx)(d.p,{id:"period",value:g.period,onChange:e=>y("period",e.target.value),placeholder:"e.g., 2022 - Present",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"type",children:"Employment Type"}),(0,a.jsx)(d.p,{id:"type",value:g.type,onChange:e=>y("type",e.target.value),placeholder:"e.g., Full-time, Part-time, Contract"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"website",children:"Company Website"}),(0,a.jsx)(d.p,{id:"website",value:g.website,onChange:e=>y("website",e.target.value),placeholder:"https://company.com"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"order",children:"Display Order"}),(0,a.jsx)(d.p,{id:"order",type:"number",value:g.order,onChange:e=>y("order",parseInt(e.target.value)||0)})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"description",children:"Description *"}),(0,a.jsx)(u.T,{id:"description",value:g.description,onChange:e=>y("description",e.target.value),rows:4,required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{children:"Key Achievements"}),g.achievements.map((e,t)=>(0,a.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,a.jsx)(d.p,{value:e,onChange:e=>j("achievements",t,e.target.value),placeholder:"Enter an achievement"}),(0,a.jsx)(n.$,{type:"button",variant:"outline",size:"sm",onClick:()=>w("achievements",t),children:(0,a.jsx)(x.A,{className:"h-4 w-4"})})]},t)),(0,a.jsxs)(n.$,{type:"button",variant:"outline",size:"sm",onClick:()=>k("achievements"),className:"mt-2",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Add Achievement"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{children:"Technologies"}),g.technologies.map((e,t)=>(0,a.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,a.jsx)(d.p,{value:e,onChange:e=>j("technologies",t,e.target.value),placeholder:"Enter a technology"}),(0,a.jsx)(n.$,{type:"button",variant:"outline",size:"sm",onClick:()=>w("technologies",t),children:(0,a.jsx)(x.A,{className:"h-4 w-4"})})]},t)),(0,a.jsxs)(n.$,{type:"button",variant:"outline",size:"sm",onClick:()=>k("technologies"),className:"mt-2",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Add Technology"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(h.d,{id:"published",checked:g.published,onCheckedChange:e=>y("published",e)}),(0,a.jsx)(c.J,{htmlFor:"published",children:"Published"})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(n.$,{type:"submit",disabled:t,children:t?"Creating...":"Create Experience"}),(0,a.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>e.back(),children:"Cancel"})]})]})})]})]})})}},27213:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},29869:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},31949:(e,t,r)=>{"use strict";r.d(t,{d:()=>w});var a=r(95155),s=r(12115),i=r(85185),l=r(6101),n=r(46081),o=r(5845),d=r(45503),c=r(11275),u=r(63655),h="Switch",[p,m]=(0,n.A)(h),[x,v]=p(h),b=s.forwardRef((e,t)=>{let{__scopeSwitch:r,name:n,checked:d,defaultChecked:c,required:p,disabled:m,value:v="on",onCheckedChange:b,form:g,...f}=e,[k,w]=s.useState(null),N=(0,l.s)(t,e=>w(e)),C=s.useRef(!1),A=!k||g||!!k.closest("form"),[E,F]=(0,o.i)({prop:d,defaultProp:null!=c&&c,onChange:b,caller:h});return(0,a.jsxs)(x,{scope:r,checked:E,disabled:m,children:[(0,a.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":E,"aria-required":p,"data-state":j(E),"data-disabled":m?"":void 0,disabled:m,value:v,...f,ref:N,onClick:(0,i.m)(e.onClick,e=>{F(e=>!e),A&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),A&&(0,a.jsx)(y,{control:k,bubbles:!C.current,name:n,value:v,checked:E,required:p,disabled:m,form:g,style:{transform:"translateX(-100%)"}})]})});b.displayName=h;var g="SwitchThumb",f=s.forwardRef((e,t)=>{let{__scopeSwitch:r,...s}=e,i=v(g,r);return(0,a.jsx)(u.sG.span,{"data-state":j(i.checked),"data-disabled":i.disabled?"":void 0,...s,ref:t})});f.displayName=g;var y=s.forwardRef((e,t)=>{let{__scopeSwitch:r,control:i,checked:n,bubbles:o=!0,...u}=e,h=s.useRef(null),p=(0,l.s)(h,t),m=(0,d.Z)(n),x=(0,c.X)(i);return s.useEffect(()=>{let e=h.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==n&&t){let r=new Event("click",{bubbles:o});t.call(e,n),e.dispatchEvent(r)}},[m,n,o]),(0,a.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...u,tabIndex:-1,ref:p,style:{...u.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function j(e){return e?"checked":"unchecked"}y.displayName="SwitchBubbleInput";var k=r(59434);let w=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(b,{className:(0,k.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",r),...s,ref:t,children:(0,a.jsx)(f,{className:(0,k.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});w.displayName=b.displayName},35169:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40968:(e,t,r)=>{"use strict";r.d(t,{b:()=>n});var a=r(12115),s=r(63655),i=r(95155),l=a.forwardRef((e,t)=>(0,i.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var n=l},45503:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var a=r(12115);function s(e){let t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},54416:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},62154:(e,t,r)=>{"use strict";r.d(t,{B:()=>p});var a=r(95155),s=r(12115),i=r(30285),l=r(62523),n=r(85057),o=r(54416),d=r(27213),c=r(29869),u=r(66766),h=r(56671);function p(e){let{value:t,onChange:r,onRemove:p,disabled:m,label:x="Upload Image",className:v=""}=e,[b,g]=(0,s.useState)(!1),f=(0,s.useRef)(null),y=async e=>{var t;let a=null==(t=e.target.files)?void 0:t[0];if(a){g(!0);try{let e=new FormData;e.append("file",a);let t=await fetch("/api/upload",{method:"POST",body:e});if(!t.ok)throw Error("Upload failed");let s=await t.json();r(s.url)}catch(e){console.error("Error uploading image:",e),h.oR.error("Failed to upload image. Please try again.")}finally{g(!1)}}};return(0,a.jsxs)("div",{className:"space-y-2 ".concat(v),children:[(0,a.jsx)(n.J,{children:x}),t?(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"relative w-full h-48 rounded-lg overflow-hidden border",children:(0,a.jsx)(u.default,{src:t,alt:"Uploaded image",fill:!0,className:"object-cover"})}),(0,a.jsx)(i.$,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2",onClick:p,disabled:m,children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})]}):(0,a.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(d.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>{var e;null==(e=f.current)||e.click()},disabled:m||b,children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4"}),b?"Uploading...":"Choose Image"]})}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"PNG, JPG, GIF up to 10MB"})]})}),(0,a.jsx)(l.p,{ref:f,type:"file",accept:"image/*",onChange:y,className:"hidden",disabled:m||b})]})}},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var a=r(95155);r(12115);var s=r(59434);function i(e){let{className:t,type:r,...i}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},62525:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},66696:(e,t,r)=>{Promise.resolve().then(r.bind(r,6453))},84616:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85057:(e,t,r)=>{"use strict";r.d(t,{J:()=>l});var a=r(95155);r(12115);var s=r(40968),i=r(59434);function l(e){let{className:t,...r}=e;return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},88539:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var a=r(95155);r(12115);var s=r(59434);function i(e){let{className:t,...r}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...r})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4854,2108,8858,9368,6671,6766,9008,8441,1684,7358],()=>t(66696)),_N_E=e.O()}]);