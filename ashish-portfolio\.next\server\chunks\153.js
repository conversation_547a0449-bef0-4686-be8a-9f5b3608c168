"use strict";exports.id=153,exports.ids=[153],exports.modules={12302:(e,a,t)=>{t.d(a,{ContactSection:()=>T});var s=t(60687),r=t(43210),i=t(26001),l=t(37472),n=t(27605),o=t(57335),c=t(9275),d=t(41550),m=t(48340),x=t(97992),h=t(48730),u=t(98876),p=t(62157),b=t(72575),g=t(11437),j=t(27900),v=t(29523),f=t(89667),y=t(4780);function N({className:e,...a}){return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,y.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...a})}var w=t(78148);function k({className:e,...a}){return(0,s.jsx)(w.b,{"data-slot":"label",className:(0,y.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...a})}var A=t(44493),P=t(52581);let S=c.Ik({name:c.Yj().min(2,"Name must be at least 2 characters"),email:c.Yj().email("Please enter a valid email address"),subject:c.Yj().min(5,"Subject must be at least 5 characters"),message:c.Yj().min(10,"Message must be at least 10 characters")}),C=[{icon:d.A,label:"Email",value:"<EMAIL>",href:"mailto:<EMAIL>"},{icon:m.A,label:"Phone",value:"+9779810580378",href:"tel:+9779810580378"},{icon:x.A,label:"Location",value:"Kathmandu, Nepal",href:"https://maps.google.com/?q=Kathmandu,Nepal"},{icon:h.A,label:"Timezone",value:"NPT (UTC+5:45)",href:null}],F=[{icon:u.A,label:"LinkedIn",href:"https://linkedin.com/in/ashishkamat0",color:"text-blue-600 hover:text-blue-700"},{icon:p.A,label:"GitHub",href:"https://github.com/ash-333",color:"text-gray-800 dark:text-gray-200 hover:text-gray-600 dark:hover:text-gray-400"},{icon:b.A,label:"Twitter",href:"https://twitter.com/ashishkamat7",color:"text-blue-500 hover:text-blue-600"},{icon:g.A,label:"Website",href:"https://ashishkamat.com/np",color:"text-green-600 hover:text-green-700"}];function T(){let[e,a]=(0,r.useState)(!1),[t,c]=(0,l.Wx)({triggerOnce:!0,threshold:.1}),{register:d,handleSubmit:m,reset:x,formState:{errors:h}}=(0,n.mN)({resolver:(0,o.u)(S)}),u=async e=>{a(!0);try{await new Promise(e=>setTimeout(e,2e3)),console.log("Form submitted:",e),P.oR.success("Message sent successfully! I'll get back to you soon."),x()}catch(e){P.oR.error("Failed to send message. Please try again.")}finally{a(!1)}};return(0,s.jsx)("section",{ref:t,className:"py-20 bg-muted/30",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)(i.P.div,{className:"text-center mb-16",initial:{opacity:0,y:50},animate:c?{opacity:1,y:0}:{},transition:{duration:.8},children:[(0,s.jsx)("h2",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6",children:(0,s.jsx)("span",{className:"gradient-text",children:"Let's Work Together"})}),(0,s.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"Have a project in mind? I'd love to hear about it. Let's discuss how we can bring your ideas to life."})]}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto",children:[(0,s.jsxs)(i.P.div,{className:"space-y-8",initial:{opacity:0,x:-50},animate:c?{opacity:1,x:0}:{},transition:{duration:.8,delay:.2},children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-2xl font-bold mb-6",children:"Get in Touch"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-8",children:"I'm always open to discussing new opportunities, creative projects, or potential collaborations. Feel free to reach out!"})]}),(0,s.jsx)("div",{className:"grid sm:grid-cols-2 gap-4",children:C.map((e,a)=>{let t=e.icon,r=(0,s.jsx)(A.Zp,{className:"hover-lift transition-all duration-300 border-border/50 hover:border-border",children:(0,s.jsx)(A.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center",children:(0,s.jsx)(t,{className:"h-5 w-5 text-primary"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:e.label}),(0,s.jsx)("div",{className:"font-medium",children:e.value})]})]})})});return(0,s.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:c?{opacity:1,y:0}:{},transition:{duration:.5,delay:.4+.1*a},children:e.href?(0,s.jsx)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",children:r}):r},e.label)})}),(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:c?{opacity:1,y:0}:{},transition:{duration:.8,delay:.6},children:[(0,s.jsx)("h4",{className:"font-semibold mb-4",children:"Connect with me"}),(0,s.jsx)("div",{className:"flex space-x-4",children:F.map((e,a)=>{let t=e.icon;return(0,s.jsx)(i.P.a,{href:e.href,target:"_blank",rel:"noopener noreferrer",className:`p-3 rounded-full bg-background border border-border hover:border-primary/50 transition-all duration-300 ${e.color}`,whileHover:{scale:1.1},whileTap:{scale:.9},initial:{opacity:0,scale:0},animate:c?{opacity:1,scale:1}:{},transition:{duration:.3,delay:.8+.1*a},children:(0,s.jsx)(t,{className:"h-5 w-5"})},e.label)})})]}),(0,s.jsxs)(i.P.div,{className:"bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg p-6 border border-border/50",initial:{opacity:0,y:20},animate:c?{opacity:1,y:0}:{},transition:{duration:.8,delay:.8},children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full animate-pulse"}),(0,s.jsx)("span",{className:"font-semibold text-green-700 dark:text-green-400",children:"Available for new projects"})]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"I'm currently accepting new client work and interesting project collaborations."})]})]}),(0,s.jsx)(i.P.div,{initial:{opacity:0,x:50},animate:c?{opacity:1,x:0}:{},transition:{duration:.8,delay:.4},children:(0,s.jsxs)(A.Zp,{className:"border-border/50",children:[(0,s.jsx)(A.aR,{children:(0,s.jsxs)(A.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(j.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"Send me a message"})]})}),(0,s.jsx)(A.Wu,{children:(0,s.jsxs)("form",{onSubmit:m(u),className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid sm:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(k,{htmlFor:"name",children:"Name *"}),(0,s.jsx)(f.p,{id:"name",placeholder:"Your name",...d("name"),className:h.name?"border-destructive":""}),h.name&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:h.name.message})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(k,{htmlFor:"email",children:"Email *"}),(0,s.jsx)(f.p,{id:"email",type:"email",placeholder:"<EMAIL>",...d("email"),className:h.email?"border-destructive":""}),h.email&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:h.email.message})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(k,{htmlFor:"subject",children:"Subject *"}),(0,s.jsx)(f.p,{id:"subject",placeholder:"Project inquiry, collaboration, etc.",...d("subject"),className:h.subject?"border-destructive":""}),h.subject&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:h.subject.message})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(k,{htmlFor:"message",children:"Message *"}),(0,s.jsx)(N,{id:"message",placeholder:"Tell me about your project, timeline, budget, and any specific requirements...",rows:6,...d("message"),className:h.message?"border-destructive":""}),h.message&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:h.message.message})]}),(0,s.jsx)(v.$,{type:"submit",className:"w-full group",disabled:e,children:e?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Sending..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(j.A,{className:"mr-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300"}),"Send Message"]})})]})})]})})]})]})})}},83802:(e,a,t)=>{t.d(a,{ContactSection:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call ContactSection() from the server but ContactSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\contact-section.tsx","ContactSection")}};