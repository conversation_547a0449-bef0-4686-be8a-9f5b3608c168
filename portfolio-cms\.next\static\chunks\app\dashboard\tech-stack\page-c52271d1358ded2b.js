(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2555],{13717:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},15452:(e,t,a)=>{"use strict";a.d(t,{UC:()=>ea,VY:()=>es,ZL:()=>ee,bL:()=>K,bm:()=>en,hE:()=>er,hJ:()=>et,l9:()=>Q});var r=a(12115),s=a(85185),n=a(6101),l=a(46081),o=a(61285),i=a(5845),d=a(19178),c=a(25519),u=a(34378),h=a(28905),p=a(63655),x=a(92293),g=a(93795),f=a(38168),m=a(99708),v=a(95155),b="Dialog",[j,y]=(0,l.A)(b),[w,N]=j(b),k=e=>{let{__scopeDialog:t,children:a,open:s,defaultOpen:n,onOpenChange:l,modal:d=!0}=e,c=r.useRef(null),u=r.useRef(null),[h,p]=(0,i.i)({prop:s,defaultProp:null!=n&&n,onChange:l,caller:b});return(0,v.jsx)(w,{scope:t,triggerRef:c,contentRef:u,contentId:(0,o.B)(),titleId:(0,o.B)(),descriptionId:(0,o.B)(),open:h,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:d,children:a})};k.displayName=b;var C="DialogTrigger",A=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,l=N(C,a),o=(0,n.s)(t,l.triggerRef);return(0,v.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":Z(l.open),...r,ref:o,onClick:(0,s.m)(e.onClick,l.onOpenToggle)})});A.displayName=C;var D="DialogPortal",[z,_]=j(D,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:a,children:s,container:n}=e,l=N(D,t);return(0,v.jsx)(z,{scope:t,forceMount:a,children:r.Children.map(s,e=>(0,v.jsx)(h.C,{present:a||l.open,children:(0,v.jsx)(u.Z,{asChild:!0,container:n,children:e})}))})};E.displayName=D;var R="DialogOverlay",F=r.forwardRef((e,t)=>{let a=_(R,e.__scopeDialog),{forceMount:r=a.forceMount,...s}=e,n=N(R,e.__scopeDialog);return n.modal?(0,v.jsx)(h.C,{present:r||n.open,children:(0,v.jsx)(O,{...s,ref:t})}):null});F.displayName=R;var I=(0,m.TL)("DialogOverlay.RemoveScroll"),O=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,s=N(R,a);return(0,v.jsx)(g.A,{as:I,allowPinchZoom:!0,shards:[s.contentRef],children:(0,v.jsx)(p.sG.div,{"data-state":Z(s.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),P="DialogContent",T=r.forwardRef((e,t)=>{let a=_(P,e.__scopeDialog),{forceMount:r=a.forceMount,...s}=e,n=N(P,e.__scopeDialog);return(0,v.jsx)(h.C,{present:r||n.open,children:n.modal?(0,v.jsx)(S,{...s,ref:t}):(0,v.jsx)(M,{...s,ref:t})})});T.displayName=P;var S=r.forwardRef((e,t)=>{let a=N(P,e.__scopeDialog),l=r.useRef(null),o=(0,n.s)(t,a.contentRef,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,f.Eq)(e)},[]),(0,v.jsx)(L,{...e,ref:o,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,s.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=a.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,s.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,a=0===t.button&&!0===t.ctrlKey;(2===t.button||a)&&e.preventDefault()}),onFocusOutside:(0,s.m)(e.onFocusOutside,e=>e.preventDefault())})}),M=r.forwardRef((e,t)=>{let a=N(P,e.__scopeDialog),s=r.useRef(!1),n=r.useRef(!1);return(0,v.jsx)(L,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,l;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(s.current||null==(l=a.triggerRef.current)||l.focus(),t.preventDefault()),s.current=!1,n.current=!1},onInteractOutside:t=>{var r,l;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(s.current=!0,"pointerdown"===t.detail.originalEvent.type&&(n.current=!0));let o=t.target;(null==(l=a.triggerRef.current)?void 0:l.contains(o))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),L=r.forwardRef((e,t)=>{let{__scopeDialog:a,trapFocus:s,onOpenAutoFocus:l,onCloseAutoFocus:o,...i}=e,u=N(P,a),h=r.useRef(null),p=(0,n.s)(t,h);return(0,x.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(c.n,{asChild:!0,loop:!0,trapped:s,onMountAutoFocus:l,onUnmountAutoFocus:o,children:(0,v.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":Z(u.open),...i,ref:p,onDismiss:()=>u.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(X,{titleId:u.titleId}),(0,v.jsx)(Y,{contentRef:h,descriptionId:u.descriptionId})]})]})}),B="DialogTitle",U=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,s=N(B,a);return(0,v.jsx)(p.sG.h2,{id:s.titleId,...r,ref:t})});U.displayName=B;var J="DialogDescription",q=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,s=N(J,a);return(0,v.jsx)(p.sG.p,{id:s.descriptionId,...r,ref:t})});q.displayName=J;var G="DialogClose",V=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,n=N(G,a);return(0,v.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,s.m)(e.onClick,()=>n.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}V.displayName=G;var $="DialogTitleWarning",[H,W]=(0,l.q)($,{contentName:P,titleName:B,docsSlug:"dialog"}),X=e=>{let{titleId:t}=e,a=W($),s="`".concat(a.contentName,"` requires a `").concat(a.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(a.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(a.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(s))},[s,t]),null},Y=e=>{let{contentRef:t,descriptionId:a}=e,s=W("DialogDescriptionWarning"),n="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(s.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");a&&r&&(document.getElementById(a)||console.warn(n))},[n,t,a]),null},K=k,Q=A,ee=E,et=F,ea=T,er=U,es=q,en=V},20195:(e,t,a)=>{Promise.resolve().then(a.bind(a,23129))},23129:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>y});var r=a(95155),s=a(12115),n=a(83930),l=a(30285),o=a(66695),i=a(85127),d=a(26126),c=a(84616),u=a(13717),h=a(62525),p=a(54165),x=a(62523),g=a(85057),f=a(59409),m=a(47262),v=a(62154),b=a(66766);let j=["frontend","backend","tools","database","cloud","mobile"];function y(){let[e,t]=(0,s.useState)([]),[a,y]=(0,s.useState)(!0),[w,N]=(0,s.useState)(!1),[k,C]=(0,s.useState)(null),[A,D]=(0,s.useState)({name:"",logo:"",color:"",category:"",order:0,published:!0});(0,s.useEffect)(()=>{z()},[]);let z=async()=>{try{let e=await fetch("/api/tech-stack"),a=await e.json();t(a)}catch(e){console.error("Error fetching tech stack:",e)}finally{y(!1)}},_=async e=>{e.preventDefault();try{let e=k?"/api/tech-stack/".concat(k.id):"/api/tech-stack",t=k?"PUT":"POST";(await fetch(e,{method:t,headers:{"Content-Type":"application/json"},body:JSON.stringify(A)})).ok&&(await z(),N(!1),F())}catch(e){console.error("Error saving tech:",e)}},E=e=>{C(e),D({name:e.name,logo:e.logo,color:e.color,category:e.category,order:e.order,published:e.published}),N(!0)},R=async a=>{if(confirm("Are you sure you want to delete this technology?"))try{(await fetch("/api/tech-stack/".concat(a),{method:"DELETE"})).ok&&t(e.filter(e=>e.id!==a))}catch(e){console.error("Error deleting tech:",e)}},F=()=>{C(null),D({name:"",logo:"",color:"",category:"",order:0,published:!0})},I=e.reduce((e,t)=>(e[t.category]||(e[t.category]=[]),e[t.category].push(t),e),{});return(0,r.jsx)(n.DashboardLayout,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Tech Stack"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage your technology skills and tools"})]}),(0,r.jsxs)(p.lG,{open:w,onOpenChange:N,children:[(0,r.jsx)(p.zM,{asChild:!0,children:(0,r.jsxs)(l.$,{onClick:F,children:[(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Add Technology"]})}),(0,r.jsxs)(p.Cf,{children:[(0,r.jsxs)(p.c7,{children:[(0,r.jsx)(p.L3,{children:k?"Edit Technology":"Add New Technology"}),(0,r.jsx)(p.rr,{children:k?"Update the technology information":"Add a new technology to your stack"})]}),(0,r.jsxs)("form",{onSubmit:_,className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(g.J,{htmlFor:"name",children:"Name *"}),(0,r.jsx)(x.p,{id:"name",value:A.name,onChange:e=>D(t=>({...t,name:e.target.value})),required:!0})]}),(0,r.jsx)("div",{children:(0,r.jsx)(v.B,{label:"Technology Logo *",value:A.logo,onChange:e=>D(t=>({...t,logo:e})),onRemove:()=>D(e=>({...e,logo:""}))})}),(0,r.jsxs)("div",{children:[(0,r.jsx)(g.J,{htmlFor:"color",children:"Color Class *"}),(0,r.jsx)(x.p,{id:"color",value:A.color,onChange:e=>D(t=>({...t,color:e.target.value})),placeholder:"text-blue-500",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(g.J,{htmlFor:"category",children:"Category *"}),(0,r.jsxs)(f.l6,{value:A.category,onValueChange:e=>D(t=>({...t,category:e})),children:[(0,r.jsx)(f.bq,{children:(0,r.jsx)(f.yv,{placeholder:"Select a category"})}),(0,r.jsx)(f.gC,{children:j.map(e=>(0,r.jsx)(f.eb,{value:e,children:e.charAt(0).toUpperCase()+e.slice(1)},e))})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(g.J,{htmlFor:"order",children:"Order"}),(0,r.jsx)(x.p,{id:"order",type:"number",min:"0",value:A.order,onChange:e=>D(t=>({...t,order:parseInt(e.target.value)||0}))})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(m.S,{id:"published",checked:A.published,onCheckedChange:e=>D(t=>({...t,published:e}))}),(0,r.jsx)(g.J,{htmlFor:"published",children:"Published"})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(l.$,{type:"submit",children:k?"Update":"Create"}),(0,r.jsx)(l.$,{type:"button",variant:"outline",onClick:()=>N(!1),children:"Cancel"})]})]})]})]})]}),a?(0,r.jsx)("div",{className:"text-center py-4",children:"Loading..."}):(0,r.jsx)("div",{className:"space-y-6",children:Object.entries(I).map(e=>{let[t,a]=e;return(0,r.jsxs)(o.Zp,{children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{className:"capitalize",children:t}),(0,r.jsxs)(o.BT,{children:[a.length," ",1===a.length?"technology":"technologies"]})]}),(0,r.jsx)(o.Wu,{children:(0,r.jsxs)(i.XI,{children:[(0,r.jsx)(i.A0,{children:(0,r.jsxs)(i.Hj,{children:[(0,r.jsx)(i.nd,{children:"Technology"}),(0,r.jsx)(i.nd,{children:"Order"}),(0,r.jsx)(i.nd,{children:"Status"}),(0,r.jsx)(i.nd,{children:"Actions"})]})}),(0,r.jsx)(i.BF,{children:a.map(e=>(0,r.jsxs)(i.Hj,{children:[(0,r.jsx)(i.nA,{children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 relative",children:(0,r.jsx)(b.default,{src:e.logo,alt:e.name,fill:!0,className:"object-contain"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:e.name}),(0,r.jsx)("div",{className:"text-sm ".concat(e.color),children:e.color})]})]})}),(0,r.jsx)(i.nA,{children:(0,r.jsx)("div",{className:"flex items-center gap-2",children:(0,r.jsx)("span",{className:"text-sm font-medium",children:e.order})})}),(0,r.jsx)(i.nA,{children:(0,r.jsx)(d.E,{variant:e.published?"default":"secondary",children:e.published?"Published":"Draft"})}),(0,r.jsx)(i.nA,{children:(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>E(e),children:(0,r.jsx)(u.A,{className:"h-4 w-4"})}),(0,r.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>R(e.id),children:(0,r.jsx)(h.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})]},t)})})]})})}},26126:(e,t,a)=>{"use strict";a.d(t,{E:()=>i});var r=a(95155);a(12115);var s=a(99708),n=a(74466),l=a(59434);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:a,asChild:n=!1,...i}=e,d=n?s.DX:"span";return(0,r.jsx)(d,{"data-slot":"badge",className:(0,l.cn)(o({variant:a}),t),...i})}},47262:(e,t,a)=>{"use strict";a.d(t,{S:()=>o});var r=a(95155);a(12115);var s=a(76981),n=a(5196),l=a(59434);function o(e){let{className:t,...a}=e;return(0,r.jsx)(s.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,r.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,r.jsx)(n.A,{className:"size-3.5"})})})}},54165:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>u,L3:()=>p,c7:()=>h,lG:()=>o,rr:()=>x,zM:()=>i});var r=a(95155);a(12115);var s=a(15452),n=a(54416),l=a(59434);function o(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"dialog",...t})}function i(e){let{...t}=e;return(0,r.jsx)(s.l9,{"data-slot":"dialog-trigger",...t})}function d(e){let{...t}=e;return(0,r.jsx)(s.ZL,{"data-slot":"dialog-portal",...t})}function c(e){let{className:t,...a}=e;return(0,r.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function u(e){let{className:t,children:a,showCloseButton:o=!0,...i}=e;return(0,r.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,r.jsx)(c,{}),(0,r.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...i,children:[a,o&&(0,r.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(n.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function h(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function p(e){let{className:t,...a}=e;return(0,r.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",t),...a})}function x(e){let{className:t,...a}=e;return(0,r.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",t),...a})}},59409:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>p,gC:()=>h,l6:()=>d,yv:()=>c});var r=a(95155);a(12115);var s=a(38715),n=a(66474),l=a(5196),o=a(47863),i=a(59434);function d(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,r.jsx)(s.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:l,...o}=e;return(0,r.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":a,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...o,children:[l,(0,r.jsx)(s.In,{asChild:!0,children:(0,r.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function h(e){let{className:t,children:a,position:n="popper",...l}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsxs)(s.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...l,children:[(0,r.jsx)(x,{}),(0,r.jsx)(s.LM,{className:(0,i.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,r.jsx)(g,{})]})})}function p(e){let{className:t,children:a,...n}=e;return(0,r.jsxs)(s.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{children:(0,r.jsx)(l.A,{className:"size-4"})})}),(0,r.jsx)(s.p4,{children:a})]})}function x(e){let{className:t,...a}=e;return(0,r.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(o.A,{className:"size-4"})})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(n.A,{className:"size-4"})})}},62154:(e,t,a)=>{"use strict";a.d(t,{B:()=>p});var r=a(95155),s=a(12115),n=a(30285),l=a(62523),o=a(85057),i=a(54416),d=a(27213),c=a(29869),u=a(66766),h=a(56671);function p(e){let{value:t,onChange:a,onRemove:p,disabled:x,label:g="Upload Image",className:f=""}=e,[m,v]=(0,s.useState)(!1),b=(0,s.useRef)(null),j=async e=>{var t;let r=null==(t=e.target.files)?void 0:t[0];if(r){v(!0);try{let e=new FormData;e.append("file",r);let t=await fetch("/api/upload",{method:"POST",body:e});if(!t.ok)throw Error("Upload failed");let s=await t.json();a(s.url)}catch(e){console.error("Error uploading image:",e),h.oR.error("Failed to upload image. Please try again.")}finally{v(!1)}}};return(0,r.jsxs)("div",{className:"space-y-2 ".concat(f),children:[(0,r.jsx)(o.J,{children:g}),t?(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"relative w-full h-48 rounded-lg overflow-hidden border",children:(0,r.jsx)(u.default,{src:t,alt:"Uploaded image",fill:!0,className:"object-cover"})}),(0,r.jsx)(n.$,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2",onClick:p,disabled:x,children:(0,r.jsx)(i.A,{className:"h-4 w-4"})})]}):(0,r.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(d.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsxs)(n.$,{type:"button",variant:"outline",onClick:()=>{var e;null==(e=b.current)||e.click()},disabled:x||m,children:[(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4"}),m?"Uploading...":"Choose Image"]})}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"PNG, JPG, GIF up to 10MB"})]})}),(0,r.jsx)(l.p,{ref:b,type:"file",accept:"image/*",onChange:j,className:"hidden",disabled:x||m})]})}},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var r=a(95155);a(12115);var s=a(59434);function n(e){let{className:t,type:a,...n}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},62525:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},84616:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85057:(e,t,a)=>{"use strict";a.d(t,{J:()=>l});var r=a(95155);a(12115);var s=a(40968),n=a(59434);function l(e){let{className:t,...a}=e;return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},85127:(e,t,a)=>{"use strict";a.d(t,{A0:()=>l,BF:()=>o,Hj:()=>i,XI:()=>n,nA:()=>c,nd:()=>d});var r=a(95155);a(12115);var s=a(59434);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm",t),...a})})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4854,2108,8858,9368,6671,6766,7481,9008,8441,1684,7358],()=>t(20195)),_N_E=e.O()}]);