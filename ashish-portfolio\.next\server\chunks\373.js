"use strict";exports.id=373,exports.ids=[373],exports.modules={363:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},2030:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,n){let r=t[0],i=n[0];if(Array.isArray(r)&&Array.isArray(i)){if(r[0]!==i[0]||r[2]!==i[2])return!0}else if(r!==i)return!0;if(t[4])return!n[4];if(n[4])return!0;let o=Object.values(t[1])[0],a=Object.values(n[1])[0];return!o||!a||e(o,a)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2255:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return i}});let r=n(19169);function i(e,t){if("string"!=typeof e)return!1;let{pathname:n}=(0,r.parsePath)(e);return n===t||n.startsWith(t+"/")}},2975:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]])},5144:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return u}});let r=n(51550),i=n(59656);var o=i._("_maxConcurrency"),a=i._("_runningCount"),s=i._("_queue"),l=i._("_processNext");class u{enqueue(e){let t,n,i=new Promise((e,r)=>{t=e,n=r}),o=async()=>{try{r._(this,a)[a]++;let n=await e();t(n)}catch(e){n(e)}finally{r._(this,a)[a]--,r._(this,l)[l]()}};return r._(this,s)[s].push({promiseFn:i,task:o}),r._(this,l)[l](),i}bump(e){let t=r._(this,s)[s].findIndex(t=>t.promiseFn===e);if(t>-1){let e=r._(this,s)[s].splice(t,1)[0];r._(this,s)[s].unshift(e),r._(this,l)[l](!0)}}constructor(e=5){Object.defineProperty(this,l,{value:c}),Object.defineProperty(this,o,{writable:!0,value:void 0}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,s,{writable:!0,value:void 0}),r._(this,o)[o]=e,r._(this,a)[a]=0,r._(this,s)[s]=[]}}function c(e){if(void 0===e&&(e=!1),(r._(this,a)[a]<r._(this,o)[o]||e)&&r._(this,s)[s].length>0){var t;null==(t=r._(this,s)[s].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5334:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DYNAMIC_STALETIME_MS:function(){return h},STATIC_STALETIME_MS:function(){return f},createSeededPrefetchCacheEntry:function(){return u},getOrCreatePrefetchCacheEntry:function(){return l},prunePrefetchCache:function(){return d}});let r=n(59008),i=n(59154),o=n(75076);function a(e,t,n){let r=e.pathname;return(t&&(r+=e.search),n)?""+n+"%"+r:r}function s(e,t,n){return a(e,t===i.PrefetchKind.FULL,n)}function l(e){let{url:t,nextUrl:n,tree:r,prefetchCache:o,kind:s,allowAliasing:l=!0}=e,u=function(e,t,n,r,o){for(let s of(void 0===t&&(t=i.PrefetchKind.TEMPORARY),[n,null])){let n=a(e,!0,s),l=a(e,!1,s),u=e.search?n:l,c=r.get(u);if(c&&o){if(c.url.pathname===e.pathname&&c.url.search!==e.search)return{...c,aliased:!0};return c}let d=r.get(l);if(o&&e.search&&t!==i.PrefetchKind.FULL&&d&&!d.key.includes("%"))return{...d,aliased:!0}}if(t!==i.PrefetchKind.FULL&&o){for(let t of r.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,s,n,o,l);return u?(u.status=p(u),u.kind!==i.PrefetchKind.FULL&&s===i.PrefetchKind.FULL&&u.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return c({tree:r,url:t,nextUrl:n,prefetchCache:o,kind:null!=s?s:i.PrefetchKind.TEMPORARY})}),s&&u.kind===i.PrefetchKind.TEMPORARY&&(u.kind=s),u):c({tree:r,url:t,nextUrl:n,prefetchCache:o,kind:s||i.PrefetchKind.TEMPORARY})}function u(e){let{nextUrl:t,tree:n,prefetchCache:r,url:o,data:a,kind:l}=e,u=a.couldBeIntercepted?s(o,l,t):s(o,l),c={treeAtTimeOfPrefetch:n,data:Promise.resolve(a),kind:l,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:-1,key:u,status:i.PrefetchCacheEntryStatus.fresh,url:o};return r.set(u,c),c}function c(e){let{url:t,kind:n,tree:a,nextUrl:l,prefetchCache:u}=e,c=s(t,n),d=o.prefetchQueue.enqueue(()=>(0,r.fetchServerResponse)(t,{flightRouterState:a,nextUrl:l,prefetchKind:n}).then(e=>{let n;if(e.couldBeIntercepted&&(n=function(e){let{url:t,nextUrl:n,prefetchCache:r,existingCacheKey:i}=e,o=r.get(i);if(!o)return;let a=s(t,o.kind,n);return r.set(a,{...o,key:a}),r.delete(i),a}({url:t,existingCacheKey:c,nextUrl:l,prefetchCache:u})),e.prerendered){let t=u.get(null!=n?n:c);t&&(t.kind=i.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),h={treeAtTimeOfPrefetch:a,data:d,kind:n,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:c,status:i.PrefetchCacheEntryStatus.fresh,url:t};return u.set(c,h),h}function d(e){for(let[t,n]of e)p(n)===i.PrefetchCacheEntryStatus.expired&&e.delete(t)}let h=1e3*Number("0"),f=1e3*Number("300");function p(e){let{kind:t,prefetchTime:n,lastUsedTime:r,staleTime:o}=e;return -1!==o?Date.now()<n+o?i.PrefetchCacheEntryStatus.fresh:i.PrefetchCacheEntryStatus.stale:Date.now()<(null!=r?r:n)+h?r?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.fresh:t===i.PrefetchKind.AUTO&&Date.now()<n+f?i.PrefetchCacheEntryStatus.stale:t===i.PrefetchKind.FULL&&Date.now()<n+f?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6361:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return i}});let r=n(96127);function i(e,t){if(e.startsWith(".")){let n=t.origin+t.pathname;return new URL((n.endsWith("/")?n:n+"/")+e)}return new URL((0,r.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7044:(e,t,n)=>{n.d(t,{B:()=>r});let r="undefined"!=typeof window},8730:(e,t,n)=>{n.d(t,{DX:()=>s,TL:()=>a});var r=n(43210),i=n(98599),o=n(60687);function a(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var a;let e,s,l=(a=n,(s=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(s=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),u=function(e,t){let n={...t};for(let r in t){let i=e[r],o=t[r];/^on[A-Z]/.test(r)?i&&o?n[r]=(...e)=>{let t=o(...e);return i(...e),t}:i&&(n[r]=i):"style"===r?n[r]={...i,...o}:"className"===r&&(n[r]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(u.ref=t?(0,i.t)(t,l):l),r.cloneElement(n,u)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:i,...a}=e,s=r.Children.toArray(i),l=s.find(u);if(l){let e=l.props.children,i=s.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...a,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,i):null})}return(0,o.jsx)(t,{...a,ref:n,children:i})});return n.displayName=`${e}.Slot`,n}var s=a("Slot"),l=Symbol("radix.slottable");function u(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},8830:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return r}}),n(59154),n(25232),n(29651),n(28627),n(78866),n(75076),n(97936),n(35429);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9707:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addSearchParamsToPageSegments:function(){return d},handleAliasedPrefetchEntry:function(){return c}});let r=n(83913),i=n(89752),o=n(86770),a=n(57391),s=n(33123),l=n(33898),u=n(59435);function c(e,t,n,c,h){let f,p=t.tree,m=t.cache,g=(0,a.createHrefFromUrl)(c);if("string"==typeof n)return!1;for(let t of n){if(!function e(t){if(!t)return!1;let n=t[2];if(t[3])return!0;for(let t in n)if(e(n[t]))return!0;return!1}(t.seedData))continue;let n=t.tree;n=d(n,Object.fromEntries(c.searchParams));let{seedData:a,isRootRender:u,pathToSegment:h}=t,y=["",...h];n=d(n,Object.fromEntries(c.searchParams));let v=(0,o.applyRouterStatePatchToTree)(y,p,n,g),b=(0,i.createEmptyCacheNode)();if(u&&a){let t=a[1];b.loading=a[3],b.rsc=t,function e(t,n,i,o,a){if(0!==Object.keys(o[1]).length)for(let l in o[1]){let u,c=o[1][l],d=c[0],h=(0,s.createRouterCacheKey)(d),f=null!==a&&void 0!==a[2][l]?a[2][l]:null;if(null!==f){let e=f[1],n=f[3];u={lazyData:null,rsc:d.includes(r.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:n,navigatedAt:t}}else u={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let p=n.parallelRoutes.get(l);p?p.set(h,u):n.parallelRoutes.set(l,new Map([[h,u]])),e(t,u,i,c,f)}}(e,b,m,n,a)}else b.rsc=m.rsc,b.prefetchRsc=m.prefetchRsc,b.loading=m.loading,b.parallelRoutes=new Map(m.parallelRoutes),(0,l.fillCacheWithNewSubTreeDataButOnlyLoading)(e,b,m,t);v&&(p=v,m=b,f=!0)}return!!f&&(h.patchedTree=p,h.cache=m,h.canonicalUrl=g,h.hashFragment=c.hash,(0,u.handleMutable)(t,h))}function d(e,t){let[n,i,...o]=e;if(n.includes(r.PAGE_SEGMENT_KEY))return[(0,r.addSearchParamsIfPageSegment)(n,t),i,...o];let a={};for(let[e,n]of Object.entries(i))a[e]=d(n,t);return[n,a,...o]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10022:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},11273:(e,t,n)=>{n.d(t,{A:()=>a,q:()=>o});var r=n(43210),i=n(60687);function o(e,t){let n=r.createContext(t),o=e=>{let{children:t,...o}=e,a=r.useMemo(()=>o,Object.values(o));return(0,i.jsx)(n.Provider,{value:a,children:t})};return o.displayName=e+"Provider",[o,function(i){let o=r.useContext(n);if(o)return o;if(void 0!==t)return t;throw Error(`\`${i}\` must be used within \`${e}\``)}]}function a(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let i=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return o.scopeName=e,[function(t,o){let a=r.createContext(o),s=n.length;n=[...n,o];let l=t=>{let{scope:n,children:o,...l}=t,u=n?.[e]?.[s]||a,c=r.useMemo(()=>l,Object.values(l));return(0,i.jsx)(u.Provider,{value:c,children:o})};return l.displayName=t+"Provider",[l,function(n,i){let l=i?.[e]?.[s]||a,u=r.useContext(l);if(u)return u;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e)[`__scope${r}`];return{...t,...i}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}(o,...t)]}},11860:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12157:(e,t,n)=>{n.d(t,{L:()=>r});let r=(0,n(43210).createContext)({})},12941:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},13166:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("coffee",[["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M14 2v2",key:"6buw04"}],["path",{d:"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1",key:"pwadti"}],["path",{d:"M6 2v2",key:"colzsn"}]])},13495:(e,t,n)=>{n.d(t,{c:()=>i});var r=n(43210);function i(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},14163:(e,t,n)=>{n.d(t,{hO:()=>l,sG:()=>s});var r=n(43210),i=n(51215),o=n(8730),a=n(60687),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,o.TL)(`Primitive.${t}`),i=r.forwardRef((e,r)=>{let{asChild:i,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i?n:t,{...o,ref:r})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{});function l(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},15124:(e,t,n)=>{n.d(t,{E:()=>i});var r=n(43210);let i=n(7044).B?r.useLayoutEffect:r.useEffect},16189:(e,t,n)=>{var r=n(65773);n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}})},18171:(e,t,n)=>{n.d(t,{s:()=>i});var r=n(74479);function i(e){return(0,r.G)(e)&&"offsetHeight"in e}},18468:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,n,o){let a=o.length<=2,[s,l]=o,u=(0,r.createRouterCacheKey)(l),c=n.parallelRoutes.get(s);if(!c)return;let d=t.parallelRoutes.get(s);if(d&&d!==c||(d=new Map(c),t.parallelRoutes.set(s,d)),a)return void d.delete(u);let h=c.get(u),f=d.get(u);f&&h&&(f===h&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes)},d.set(u,f)),e(f,h,(0,i.getNextFlightSegmentPath)(o)))}}});let r=n(33123),i=n(74007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19169:(e,t)=>{function n(e){let t=e.indexOf("#"),n=e.indexOf("?"),r=n>-1&&(t<0||n<t);return r||t>-1?{pathname:e.substring(0,r?n:t),query:r?e.substring(n,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return n}})},21134:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},21279:(e,t,n)=>{n.d(t,{t:()=>r});let r=(0,n(43210).createContext)(null)},22308:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,n){let[r,i,,a]=t;for(let s in r.includes(o.PAGE_SEGMENT_KEY)&&"refresh"!==a&&(t[2]=n,t[3]="refresh"),i)e(i[s],n)}},refreshInactiveParallelSegments:function(){return a}});let r=n(56928),i=n(59008),o=n(83913);async function a(e){let t=new Set;await s({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function s(e){let{navigatedAt:t,state:n,updatedTree:o,updatedCache:a,includeNextUrl:l,fetchedSegments:u,rootTree:c=o,canonicalUrl:d}=e,[,h,f,p]=o,m=[];if(f&&f!==d&&"refresh"===p&&!u.has(f)){u.add(f);let e=(0,i.fetchServerResponse)(new URL(f,location.origin),{flightRouterState:[c[0],c[1],c[2],"refetch"],nextUrl:l?n.nextUrl:null}).then(e=>{let{flightData:n}=e;if("string"!=typeof n)for(let e of n)(0,r.applyFlightData)(t,a,a,e)});m.push(e)}for(let e in h){let r=s({navigatedAt:t,state:n,updatedTree:h[e],updatedCache:a,includeNextUrl:l,fetchedSegments:u,rootTree:c,canonicalUrl:d});m.push(r)}await Promise.all(m)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},24224:(e,t,n)=>{n.d(t,{F:()=>a});var r=n(49384);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=r.$,a=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return o(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:a,defaultVariants:s}=t,l=Object.keys(a).map(e=>{let t=null==n?void 0:n[e],r=null==s?void 0:s[e];if(null===t)return null;let o=i(t)||i(r);return a[e][o]}),u=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return o(e,l,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...i}=t;return Object.entries(i).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...s,...u}[t]):({...s,...u})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},24642:(e,t)=>{function n(e){let t=parseInt(e.slice(0,2),16),n=t>>1&63,r=Array(6);for(let e=0;e<6;e++){let t=n>>5-e&1;r[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:r,hasRestArgs:1==(1&t)}}function r(e,t){let n=Array(e.length);for(let r=0;r<e.length;r++)(r<6&&t.usedArgs[r]||r>=6&&t.hasRestArgs)&&(n[r]=e[r]);return n}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{extractInfoFromServerReferenceId:function(){return n},omitUnusedArgs:function(){return r}})},25232:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{handleExternalUrl:function(){return b},navigateReducer:function(){return function e(t,n){let{url:x,isExternalUrl:P,navigateType:E,shouldScroll:T,allowAliasing:R}=n,S={},{hash:M}=x,A=(0,i.createHrefFromUrl)(x),k="push"===E;if((0,g.prunePrefetchCache)(t.prefetchCache),S.preserveCustomHistoryState=!1,S.pendingPush=k,P)return b(t,S,x.toString(),k);if(document.getElementById("__next-page-redirect"))return b(t,S,A,k);let _=(0,g.getOrCreatePrefetchCacheEntry)({url:x,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:R}),{treeAtTimeOfPrefetch:j,data:C}=_;return h.prefetchQueue.bump(C),C.then(h=>{let{flightData:g,canonicalUrl:P,postponed:E}=h,R=Date.now(),C=!1;if(_.lastUsedTime||(_.lastUsedTime=R,C=!0),_.aliased){let r=(0,v.handleAliasedPrefetchEntry)(R,t,g,x,S);return!1===r?e(t,{...n,allowAliasing:!1}):r}if("string"==typeof g)return b(t,S,g,k);let O=P?(0,i.createHrefFromUrl)(P):A;if(M&&t.canonicalUrl.split("#",1)[0]===O.split("#",1)[0])return S.onlyHashChange=!0,S.canonicalUrl=O,S.shouldScroll=T,S.hashFragment=M,S.scrollableSegments=[],(0,c.handleMutable)(t,S);let D=t.tree,L=t.cache,N=[];for(let e of g){let{pathToSegment:n,seedData:i,head:c,isHeadPartial:h,isRootRender:g}=e,v=e.tree,P=["",...n],T=(0,a.applyRouterStatePatchToTree)(P,D,v,A);if(null===T&&(T=(0,a.applyRouterStatePatchToTree)(P,j,v,A)),null!==T){if(i&&g&&E){let e=(0,m.startPPRNavigation)(R,L,D,v,i,c,h,!1,N);if(null!==e){if(null===e.route)return b(t,S,A,k);T=e.route;let n=e.node;null!==n&&(S.cache=n);let i=e.dynamicRequestTree;if(null!==i){let n=(0,r.fetchServerResponse)(x,{flightRouterState:i,nextUrl:t.nextUrl});(0,m.listenForDynamicRequest)(e,n)}}else T=v}else{if((0,l.isNavigatingToNewRootLayout)(D,T))return b(t,S,A,k);let r=(0,f.createEmptyCacheNode)(),i=!1;for(let t of(_.status!==u.PrefetchCacheEntryStatus.stale||C?i=(0,d.applyFlightData)(R,L,r,e,_):(i=function(e,t,n,r){let i=!1;for(let o of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),w(r).map(e=>[...n,...e])))(0,y.clearCacheNodeDataForSegmentPath)(e,t,o),i=!0;return i}(r,L,n,v),_.lastUsedTime=R),(0,s.shouldHardNavigate)(P,D)?(r.rsc=L.rsc,r.prefetchRsc=L.prefetchRsc,(0,o.invalidateCacheBelowFlightSegmentPath)(r,L,n),S.cache=r):i&&(S.cache=r,L=r),w(v))){let e=[...n,...t];e[e.length-1]!==p.DEFAULT_SEGMENT_KEY&&N.push(e)}}D=T}}return S.patchedTree=D,S.canonicalUrl=O,S.scrollableSegments=N,S.hashFragment=M,S.shouldScroll=T,(0,c.handleMutable)(t,S)},()=>t)}}});let r=n(59008),i=n(57391),o=n(18468),a=n(86770),s=n(65951),l=n(2030),u=n(59154),c=n(59435),d=n(56928),h=n(75076),f=n(89752),p=n(83913),m=n(65956),g=n(5334),y=n(97464),v=n(9707);function b(e,t,n,r){return t.mpaNavigation=!0,t.canonicalUrl=n,t.pendingPush=r,t.scrollableSegments=void 0,(0,c.handleMutable)(e,t)}function w(e){let t=[],[n,r]=e;if(0===Object.keys(r).length)return[[n]];for(let[e,i]of Object.entries(r))for(let r of w(i))""===n?t.push([e,...r]):t.push([n,e,...r]);return t}n(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25334:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},25942:(e,t,n)=>{function r(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return r}}),n(26736),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26001:(e,t,n)=>{let r;function i(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function o(e){let t=[{},{}];return e?.values.forEach((e,n)=>{t[0][n]=e.get(),t[1][n]=e.getVelocity()}),t}function a(e,t,n,r){if("function"==typeof t){let[i,a]=o(r);t=t(void 0!==n?n:e.custom,i,a)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,a]=o(r);t=t(void 0!==n?n:e.custom,i,a)}return t}function s(e,t,n){let r=e.getProps();return a(r,t,void 0!==n?n:r.custom,e)}function l(e,t){return e?.[t]??e?.default??e}n.d(t,{P:()=>oM});let u=e=>e,c={},d=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],h={value:null,addProjectionMetrics:null};function f(e,t){let n=!1,r=!0,i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,a=d.reduce((e,n)=>(e[n]=function(e,t){let n=new Set,r=new Set,i=!1,o=!1,a=new WeakSet,s={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){a.has(t)&&(c.schedule(t),e()),l++,t(s)}let c={schedule:(e,t=!1,o=!1)=>{let s=o&&i?n:r;return t&&a.add(e),s.has(e)||s.add(e),e},cancel:e=>{r.delete(e),a.delete(e)},process:e=>{if(s=e,i){o=!0;return}i=!0,[n,r]=[r,n],n.forEach(u),t&&h.value&&h.value.frameloop[t].push(l),l=0,n.clear(),i=!1,o&&(o=!1,c.process(e))}};return c}(o,t?n:void 0),e),{}),{setup:s,read:l,resolveKeyframes:u,preUpdate:f,update:p,preRender:m,render:g,postRender:y}=a,v=()=>{let o=c.useManualTiming?i.timestamp:performance.now();n=!1,c.useManualTiming||(i.delta=r?1e3/60:Math.max(Math.min(o-i.timestamp,40),1)),i.timestamp=o,i.isProcessing=!0,s.process(i),l.process(i),u.process(i),f.process(i),p.process(i),m.process(i),g.process(i),y.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(v))},b=()=>{n=!0,r=!0,i.isProcessing||e(v)};return{schedule:d.reduce((e,t)=>{let r=a[t];return e[t]=(e,t=!1,i=!1)=>(n||b(),r.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<d.length;t++)a[d[t]].cancel(e)},state:i,steps:a}}let{schedule:p,cancel:m,state:g,steps:y}=f("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],b=new Set(v),w=new Set(["width","height","top","left","right","bottom",...v]);function x(e,t){-1===e.indexOf(t)&&e.push(t)}function P(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}class E{constructor(){this.subscriptions=[]}add(e){return x(this.subscriptions,e),()=>P(this.subscriptions,e)}notify(e,t,n){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){let r=this.subscriptions[i];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function T(){r=void 0}let R={now:()=>(void 0===r&&R.set(g.isProcessing||c.useManualTiming?g.timestamp:performance.now()),r),set:e=>{r=e,queueMicrotask(T)}},S=e=>!isNaN(parseFloat(e)),M={current:void 0};class A{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let n=R.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=R.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=S(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new E);let n=this.events[e].add(t);return"change"===e?()=>{n(),p.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return M.current&&M.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=R.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),n?1e3/n*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function k(e,t){return new A(e,t)}let _=e=>Array.isArray(e),j=e=>!!(e&&e.getVelocity);function C(e,t){let n=e.getValue("willChange");if(j(n)&&n.add)return n.add(t);if(!n&&c.WillChange){let n=new c.WillChange("auto");e.addValue("willChange",n),n.add(t)}}let O=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),D="data-"+O("framerAppearId"),L=(e,t)=>n=>t(e(n)),N=(...e)=>e.reduce(L),I=(e,t,n)=>n>t?t:n<e?e:n,V=e=>1e3*e,U=e=>e/1e3,F={layout:0,mainThread:0,waapi:0},B=()=>{},z=()=>{},H=e=>t=>"string"==typeof t&&t.startsWith(e),W=H("--"),$=H("var(--"),K=e=>!!$(e)&&G.test(e.split("/*")[0].trim()),G=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Y={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},q={...Y,transform:e=>I(0,1,e)},X={...Y,default:1},Z=e=>Math.round(1e5*e)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ee=(e,t)=>n=>!!("string"==typeof n&&J.test(n)&&n.startsWith(e)||t&&null!=n&&Object.prototype.hasOwnProperty.call(n,t)),et=(e,t,n)=>r=>{if("string"!=typeof r)return r;let[i,o,a,s]=r.match(Q);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(a),alpha:void 0!==s?parseFloat(s):1}},en=e=>I(0,255,e),er={...Y,transform:e=>Math.round(en(e))},ei={test:ee("rgb","red"),parse:et("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+er.transform(e)+", "+er.transform(t)+", "+er.transform(n)+", "+Z(q.transform(r))+")"},eo={test:ee("#"),parse:function(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:ei.transform},ea=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),es=ea("deg"),el=ea("%"),eu=ea("px"),ec=ea("vh"),ed=ea("vw"),eh={...el,parse:e=>el.parse(e)/100,transform:e=>el.transform(100*e)},ef={test:ee("hsl","hue"),parse:et("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+el.transform(Z(t))+", "+el.transform(Z(n))+", "+Z(q.transform(r))+")"},ep={test:e=>ei.test(e)||eo.test(e)||ef.test(e),parse:e=>ei.test(e)?ei.parse(e):ef.test(e)?ef.parse(e):eo.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?ei.transform(e):ef.transform(e),getAnimatableNone:e=>{let t=ep.parse(e);return t.alpha=0,ep.transform(t)}},em=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eg="number",ey="color",ev=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eb(e){let t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[],o=0,a=t.replace(ev,e=>(ep.test(e)?(r.color.push(o),i.push(ey),n.push(ep.parse(e))):e.startsWith("var(")?(r.var.push(o),i.push("var"),n.push(e)):(r.number.push(o),i.push(eg),n.push(parseFloat(e))),++o,"${}")).split("${}");return{values:n,split:a,indexes:r,types:i}}function ew(e){return eb(e).values}function ex(e){let{split:t,types:n}=eb(e),r=t.length;return e=>{let i="";for(let o=0;o<r;o++)if(i+=t[o],void 0!==e[o]){let t=n[o];t===eg?i+=Z(e[o]):t===ey?i+=ep.transform(e[o]):i+=e[o]}return i}}let eP=e=>"number"==typeof e?0:ep.test(e)?ep.getAnimatableNone(e):e,eE={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(Q)?.length||0)+(e.match(em)?.length||0)>0},parse:ew,createTransformer:ex,getAnimatableNone:function(e){let t=ew(e);return ex(e)(t.map(eP))}};function eT(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+(t-e)*6*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function eR(e,t){return n=>n>0?t:e}let eS=(e,t,n)=>e+(t-e)*n,eM=(e,t,n)=>{let r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},eA=[eo,ei,ef],ek=e=>eA.find(t=>t.test(e));function e_(e){let t=ek(e);if(B(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let n=t.parse(e);return t===ef&&(n=function({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,n/=100;let i=0,o=0,a=0;if(t/=100){let r=n<.5?n*(1+t):n+t-n*t,s=2*n-r;i=eT(s,r,e+1/3),o=eT(s,r,e),a=eT(s,r,e-1/3)}else i=o=a=n;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*a),alpha:r}}(n)),n}let ej=(e,t)=>{let n=e_(e),r=e_(t);if(!n||!r)return eR(e,t);let i={...n};return e=>(i.red=eM(n.red,r.red,e),i.green=eM(n.green,r.green,e),i.blue=eM(n.blue,r.blue,e),i.alpha=eS(n.alpha,r.alpha,e),ei.transform(i))},eC=new Set(["none","hidden"]);function eO(e,t){return n=>eS(e,t,n)}function eD(e){return"number"==typeof e?eO:"string"==typeof e?K(e)?eR:ep.test(e)?ej:eI:Array.isArray(e)?eL:"object"==typeof e?ep.test(e)?ej:eN:eR}function eL(e,t){let n=[...e],r=n.length,i=e.map((e,n)=>eD(e)(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}}function eN(e,t){let n={...e,...t},r={};for(let i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=eD(e[i])(e[i],t[i]));return e=>{for(let t in r)n[t]=r[t](e);return n}}let eI=(e,t)=>{let n=eE.createTransformer(t),r=eb(e),i=eb(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?eC.has(e)&&!i.values.length||eC.has(t)&&!r.values.length?function(e,t){return eC.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}(e,t):N(eL(function(e,t){let n=[],r={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){let o=t.types[i],a=e.indexes[o][r[o]],s=e.values[a]??0;n[i]=s,r[o]++}return n}(r,i),i.values),n):(B(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eR(e,t))};function eV(e,t,n){return"number"==typeof e&&"number"==typeof t&&"number"==typeof n?eS(e,t,n):eD(e)(e,t)}let eU=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>p.update(t,e),stop:()=>m(t),now:()=>g.isProcessing?g.timestamp:R.now()}},eF=(e,t,n=10)=>{let r="",i=Math.max(Math.round(t/n),2);for(let t=0;t<i;t++)r+=Math.round(1e4*e(t/(i-1)))/1e4+", ";return`linear(${r.substring(0,r.length-2)})`};function eB(e){let t=0,n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}function ez(e,t,n){var r,i;let o=Math.max(t-5,0);return r=n-e(o),(i=t-o)?1e3/i*r:0}let eH={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function eW(e,t){return e*Math.sqrt(1-t*t)}let e$=["duration","bounce"],eK=["stiffness","damping","mass"];function eG(e,t){return t.some(t=>void 0!==e[t])}function eY(e=eH.visualDuration,t=eH.bounce){let n,r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:o}=r,a=r.keyframes[0],s=r.keyframes[r.keyframes.length-1],l={done:!1,value:a},{stiffness:u,damping:c,mass:d,duration:h,velocity:f,isResolvedFromDuration:p}=function(e){let t={velocity:eH.velocity,stiffness:eH.stiffness,damping:eH.damping,mass:eH.mass,isResolvedFromDuration:!1,...e};if(!eG(e,eK)&&eG(e,e$))if(e.visualDuration){let n=2*Math.PI/(1.2*e.visualDuration),r=n*n,i=2*I(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:eH.mass,stiffness:r,damping:i}}else{let n=function({duration:e=eH.duration,bounce:t=eH.bounce,velocity:n=eH.velocity,mass:r=eH.mass}){let i,o;B(e<=V(eH.maxDuration),"Spring duration must be 10 seconds or less");let a=1-t;a=I(eH.minDamping,eH.maxDamping,a),e=I(eH.minDuration,eH.maxDuration,U(e)),a<1?(i=t=>{let r=t*a,i=r*e;return .001-(r-n)/eW(t,a)*Math.exp(-i)},o=t=>{let r=t*a*e,o=Math.pow(a,2)*Math.pow(t,2)*e,s=Math.exp(-r),l=eW(Math.pow(t,2),a);return(r*n+n-o)*s*(-i(t)+.001>0?-1:1)/l}):(i=t=>-.001+Math.exp(-t*e)*((t-n)*e+1),o=t=>e*e*(n-t)*Math.exp(-t*e));let s=function(e,t,n){let r=n;for(let n=1;n<12;n++)r-=e(r)/t(r);return r}(i,o,5/e);if(e=V(e),isNaN(s))return{stiffness:eH.stiffness,damping:eH.damping,duration:e};{let t=Math.pow(s,2)*r;return{stiffness:t,damping:2*a*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...n,mass:eH.mass}).isResolvedFromDuration=!0}return t}({...r,velocity:-U(r.velocity||0)}),m=f||0,g=c/(2*Math.sqrt(u*d)),y=s-a,v=U(Math.sqrt(u/d)),b=5>Math.abs(y);if(i||(i=b?eH.restSpeed.granular:eH.restSpeed.default),o||(o=b?eH.restDelta.granular:eH.restDelta.default),g<1){let e=eW(v,g);n=t=>s-Math.exp(-g*v*t)*((m+g*v*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}else if(1===g)n=e=>s-Math.exp(-v*e)*(y+(m+v*y)*e);else{let e=v*Math.sqrt(g*g-1);n=t=>{let n=Math.exp(-g*v*t),r=Math.min(e*t,300);return s-n*((m+g*v*y)*Math.sinh(r)+e*y*Math.cosh(r))/e}}let w={calculatedDuration:p&&h||null,next:e=>{let t=n(e);if(p)l.done=e>=h;else{let r=0===e?m:0;g<1&&(r=0===e?V(m):ez(n,e,t));let a=Math.abs(s-t)<=o;l.done=Math.abs(r)<=i&&a}return l.value=l.done?s:t,l},toString:()=>{let e=Math.min(eB(w),2e4),t=eF(t=>w.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return w}function eq({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:a,min:s,max:l,restDelta:u=.5,restSpeed:c}){let d,h,f=e[0],p={done:!1,value:f},m=e=>void 0!==s&&e<s||void 0!==l&&e>l,g=e=>void 0===s?l:void 0===l||Math.abs(s-e)<Math.abs(l-e)?s:l,y=n*t,v=f+y,b=void 0===a?v:a(v);b!==v&&(y=b-f);let w=e=>-y*Math.exp(-e/r),x=e=>b+w(e),P=e=>{let t=w(e),n=x(e);p.done=Math.abs(t)<=u,p.value=p.done?b:n},E=e=>{m(p.value)&&(d=e,h=eY({keyframes:[p.value,g(p.value)],velocity:ez(x,e,p.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return E(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==d||(t=!0,P(e),E(e)),void 0!==d&&e>=d)?h.next(e-d):(t||P(e),p)}}}eY.applyToOptions=e=>{let t=function(e,t=100,n){let r=n({...e,keyframes:[0,t]}),i=Math.min(eB(r),2e4);return{type:"keyframes",ease:e=>r.next(i*e).value/t,duration:U(i)}}(e,100,eY);return e.ease=t.ease,e.duration=V(t.duration),e.type="keyframes",e};let eX=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function eZ(e,t,n,r){if(e===t&&n===r)return u;let i=t=>(function(e,t,n,r,i){let o,a,s=0;do(o=eX(a=t+(n-t)/2,r,i)-e)>0?n=a:t=a;while(Math.abs(o)>1e-7&&++s<12);return a})(t,0,1,e,n);return e=>0===e||1===e?e:eX(i(e),t,r)}let eQ=eZ(.42,0,1,1),eJ=eZ(0,0,.58,1),e0=eZ(.42,0,.58,1),e1=e=>Array.isArray(e)&&"number"!=typeof e[0],e2=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,e5=e=>t=>1-e(1-t),e3=eZ(.33,1.53,.69,.99),e4=e5(e3),e6=e2(e4),e9=e=>(e*=2)<1?.5*e4(e):.5*(2-Math.pow(2,-10*(e-1))),e7=e=>1-Math.sin(Math.acos(e)),e8=e5(e7),te=e2(e7),tt=e=>Array.isArray(e)&&"number"==typeof e[0],tn={linear:u,easeIn:eQ,easeInOut:e0,easeOut:eJ,circIn:e7,circInOut:te,circOut:e8,backIn:e4,backInOut:e6,backOut:e3,anticipate:e9},tr=e=>"string"==typeof e,ti=e=>{if(tt(e)){z(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,n,r,i]=e;return eZ(t,n,r,i)}return tr(e)?(z(void 0!==tn[e],`Invalid easing type '${e}'`),tn[e]):e},to=(e,t,n)=>{let r=t-e;return 0===r?1:(n-e)/r};function ta({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){var i;let o=e1(r)?r.map(ti):ti(r),a={done:!1,value:t[0]},s=function(e,t,{clamp:n=!0,ease:r,mixer:i}={}){let o=e.length;if(z(o===t.length,"Both input and output ranges must be the same length"),1===o)return()=>t[0];if(2===o&&t[0]===t[1])return()=>t[1];let a=e[0]===e[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());let s=function(e,t,n){let r=[],i=n||c.mix||eV,o=e.length-1;for(let n=0;n<o;n++){let o=i(e[n],e[n+1]);t&&(o=N(Array.isArray(t)?t[n]||u:t,o)),r.push(o)}return r}(t,r,i),l=s.length,d=n=>{if(a&&n<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(n<e[r+1]);r++);let i=to(e[r],e[r+1],n);return s[r](i)};return n?t=>d(I(e[0],e[o-1],t)):d}((i=n&&n.length===t.length?n:function(e){let t=[0];return!function(e,t){let n=e[e.length-1];for(let r=1;r<=t;r++){let i=to(0,t,r);e.push(eS(n,1,i))}}(t,e.length-1),t}(t),i.map(t=>t*e)),t,{ease:Array.isArray(o)?o:t.map(()=>o||e0).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(a.value=s(t),a.done=t>=e,a)}}let ts=e=>null!==e;function tl(e,{repeat:t,repeatType:n="loop"},r,i=1){let o=e.filter(ts),a=i<0||t&&"loop"!==n&&t%2==1?0:o.length-1;return a&&void 0!==r?r:o[a]}let tu={decay:eq,inertia:eq,tween:ta,keyframes:ta,spring:eY};function tc(e){"string"==typeof e.type&&(e.type=tu[e.type])}class td{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let th=e=>e/100;class tf extends td{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==R.now()&&this.tick(R.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},F.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;tc(e);let{type:t=ta,repeat:n=0,repeatDelay:r=0,repeatType:i,velocity:o=0}=e,{keyframes:a}=e,s=t||ta;s!==ta&&"number"!=typeof a[0]&&(this.mixKeyframes=N(th,eV(a[0],a[1])),a=[0,100]);let l=s({...e,keyframes:a});"mirror"===i&&(this.mirroredGenerator=s({...e,keyframes:[...a].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=eB(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(n+1)-r,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:n,totalDuration:r,mixKeyframes:i,mirroredGenerator:o,resolvedDuration:a,calculatedDuration:s}=this;if(null===this.startTime)return n.next(0);let{delay:l=0,keyframes:u,repeat:c,repeatType:d,repeatDelay:h,type:f,onUpdate:p,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-r/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let v=this.currentTime,b=n;if(c){let e=Math.min(this.currentTime,r)/a,t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,(t=Math.min(t,c+1))%2&&("reverse"===d?(n=1-n,h&&(n-=h/a)):"mirror"===d&&(b=o)),v=I(0,1,n)*a}let w=y?{done:!1,value:u[0]}:b.next(v);i&&(w.value=i(w.value));let{done:x}=w;y||null===s||(x=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let P=null===this.holdTime&&("finished"===this.state||"running"===this.state&&x);return P&&f!==eq&&(w.value=tl(u,this.options,m,this.speed)),p&&p(w.value),P&&this.finish(),w}then(e,t){return this.finished.then(e,t)}get duration(){return U(this.calculatedDuration)}get time(){return U(this.currentTime)}set time(e){e=V(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(R.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=U(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=eU,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=t??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(R.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,F.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}let tp=e=>180*e/Math.PI,tm=e=>ty(tp(Math.atan2(e[1],e[0]))),tg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:tm,rotateZ:tm,skewX:e=>tp(Math.atan(e[1])),skewY:e=>tp(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},ty=e=>((e%=360)<0&&(e+=360),e),tv=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),tb=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),tw={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:tv,scaleY:tb,scale:e=>(tv(e)+tb(e))/2,rotateX:e=>ty(tp(Math.atan2(e[6],e[5]))),rotateY:e=>ty(tp(Math.atan2(-e[2],e[0]))),rotateZ:tm,rotate:tm,skewX:e=>tp(Math.atan(e[4])),skewY:e=>tp(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function tx(e){return+!!e.includes("scale")}function tP(e,t){let n,r;if(!e||"none"===e)return tx(t);let i=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(i)n=tw,r=i;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=tg,r=t}if(!r)return tx(t);let o=n[t],a=r[1].split(",").map(tT);return"function"==typeof o?o(a):a[o]}let tE=(e,t)=>{let{transform:n="none"}=getComputedStyle(e);return tP(n,t)};function tT(e){return parseFloat(e.trim())}let tR=e=>e===Y||e===eu,tS=new Set(["x","y","z"]),tM=v.filter(e=>!tS.has(e)),tA={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>tP(t,"x"),y:(e,{transform:t})=>tP(t,"y")};tA.translateX=tA.x,tA.translateY=tA.y;let tk=new Set,t_=!1,tj=!1,tC=!1;function tO(){if(tj){let e=Array.from(tk).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),n=new Map;t.forEach(e=>{let t=function(e){let t=[];return tM.forEach(n=>{let r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(+!!n.startsWith("scale")))}),t}(e);t.length&&(n.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=n.get(e);t&&t.forEach(([t,n])=>{e.getValue(t)?.set(n)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tj=!1,t_=!1,tk.forEach(e=>e.complete(tC)),tk.clear()}function tD(){tk.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tj=!0)})}class tL{constructor(e,t,n,r,i,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=n,this.motionValue=r,this.element=i,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(tk.add(this),t_||(t_=!0,p.read(tD),p.resolveKeyframes(tO))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:n,motionValue:r}=this;if(null===e[0]){let i=r?.get(),o=e[e.length-1];if(void 0!==i)e[0]=i;else if(n&&t){let r=n.readValue(t,o);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=o),r&&void 0===i&&r.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),tk.delete(this)}cancel(){"scheduled"===this.state&&(tk.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tN=e=>e.startsWith("--");function tI(e){let t;return()=>(void 0===t&&(t=e()),t)}let tV=tI(()=>void 0!==window.ScrollTimeline),tU={},tF=function(e,t){let n=tI(e);return()=>tU[t]??n()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),tB=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,tz={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tB([0,.65,.55,1]),circOut:tB([.55,0,1,.45]),backIn:tB([.31,.01,.66,-.59]),backOut:tB([.33,1.53,.69,.99])};function tH(e){return"function"==typeof e&&"applyToOptions"in e}class tW extends td{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:n,keyframes:r,pseudoElement:i,allowFlatten:o=!1,finalKeyframe:a,onComplete:s}=e;this.isPseudoElement=!!i,this.allowFlatten=o,this.options=e,z("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return tH(e)&&tF()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,n,{delay:r=0,duration:i=300,repeat:o=0,repeatType:a="loop",ease:s="easeOut",times:l}={},u){let c={[t]:n};l&&(c.offset=l);let d=function e(t,n){if(t)return"function"==typeof t?tF()?eF(t,n):"ease-out":tt(t)?tB(t):Array.isArray(t)?t.map(t=>e(t,n)||tz.easeOut):tz[t]}(s,i);Array.isArray(d)&&(c.easing=d),h.value&&F.waapi++;let f={delay:r,duration:i,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:o+1,direction:"reverse"===a?"alternate":"normal"};u&&(f.pseudoElement=u);let p=e.animate(c,f);return h.value&&p.finished.finally(()=>{F.waapi--}),p}(t,n,r,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let e=tl(r,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,n){tN(t)?e.style.setProperty(t,n):e.style[t]=n}(t,n,e),this.animation.cancel()}s?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return U(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return U(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=V(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&tV())?(this.animation.timeline=e,u):t(this)}}let t$={anticipate:e9,backInOut:e6,circInOut:te};class tK extends tW{constructor(e){!function(e){"string"==typeof e.ease&&e.ease in t$&&(e.ease=t$[e.ease])}(e),tc(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:n,onComplete:r,element:i,...o}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let a=new tf({...o,autoplay:!1}),s=V(this.finishedTime??this.time);t.setWithVelocity(a.sample(s-10).value,a.sample(s).value,10),a.stop()}}let tG=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eE.test(e)||"0"===e)&&!e.startsWith("url("));var tY,tq,tX=n(18171);let tZ=new Set(["opacity","clipPath","filter","transform"]),tQ=tI(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tJ extends td{constructor({autoplay:e=!0,delay:t=0,type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:o="loop",keyframes:a,name:s,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=R.now();let d={autoplay:e,delay:t,type:n,repeat:r,repeatDelay:i,repeatType:o,name:s,motionValue:l,element:u,...c},h=u?.KeyframeResolver||tL;this.keyframeResolver=new h(a,(e,t,n)=>this.onKeyframesResolved(e,t,d,!n),s,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,n,r){this.keyframeResolver=void 0;let{name:i,type:o,velocity:a,delay:s,isHandoff:l,onUpdate:d}=n;this.resolvedAt=R.now(),!function(e,t,n,r){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let o=e[e.length-1],a=tG(i,t),s=tG(o,t);return B(a===s,`You are trying to animate ${t} from "${i}" to "${o}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${o} via the \`style\` property.`),!!a&&!!s&&(function(e){let t=e[0];if(1===e.length)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}(e)||("spring"===n||tH(n))&&r)}(e,i,o,a)&&((c.instantAnimations||!s)&&d?.(tl(e,n,t)),e[0]=e[e.length-1],n.duration=0,n.repeat=0);let h={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...n,keyframes:e},f=!l&&function(e){let{motionValue:t,name:n,repeatDelay:r,repeatType:i,damping:o,type:a}=e;if(!(0,tX.s)(t?.owner?.current))return!1;let{onUpdate:s,transformTemplate:l}=t.owner.getProps();return tQ()&&n&&tZ.has(n)&&("transform"!==n||!l)&&!s&&!r&&"mirror"!==i&&0!==o&&"inertia"!==a}(h)?new tK({...h,element:h.motionValue.owner.current}):new tf(h);f.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=f.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=f}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tC=!0,tD(),tO(),tC=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let t0=e=>null!==e,t1={type:"spring",stiffness:500,damping:25,restSpeed:10},t2=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),t5={type:"keyframes",duration:.8},t3={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},t4=(e,{keyframes:t})=>t.length>2?t5:b.has(e)?e.startsWith("scale")?t2(t[1]):t1:t3,t6=(e,t,n,r={},i,o)=>a=>{let s=l(r,e)||{},u=s.delay||r.delay||0,{elapsed:d=0}=r;d-=V(u);let h={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...s,delay:-d,onUpdate:e=>{t.set(e),s.onUpdate&&s.onUpdate(e)},onComplete:()=>{a(),s.onComplete&&s.onComplete()},name:e,motionValue:t,element:o?void 0:i};!function({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:a,repeatDelay:s,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(s)&&Object.assign(h,t4(e,h)),h.duration&&(h.duration=V(h.duration)),h.repeatDelay&&(h.repeatDelay=V(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let f=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0===h.delay&&(f=!0)),(c.instantAnimations||c.skipAnimations)&&(f=!0,h.duration=0,h.delay=0),h.allowFlatten=!s.type&&!s.ease,f&&!o&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:n="loop"},r){let i=e.filter(t0),o=t&&"loop"!==n&&t%2==1?0:i.length-1;return i[o]}(h.keyframes,s);if(void 0!==e)return void p.update(()=>{h.onUpdate(e),h.onComplete()})}return s.isSync?new tf(h):new tJ(h)};function t9(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:a,...u}=t;r&&(o=r);let c=[],d=i&&e.animationState&&e.animationState.getState()[i];for(let t in u){let r=e.getValue(t,e.latestValues[t]??null),i=u[t];if(void 0===i||d&&function({protectedKeys:e,needsAnimating:t},n){let r=e.hasOwnProperty(n)&&!0!==t[n];return t[n]=!1,r}(d,t))continue;let a={delay:n,...l(o||{},t)},s=r.get();if(void 0!==s&&!r.isAnimating&&!Array.isArray(i)&&i===s&&!a.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){let n=e.props[D];if(n){let e=window.MotionHandoffAnimation(n,t,p);null!==e&&(a.startTime=e,h=!0)}}C(e,t),r.start(t6(t,r,i,e.shouldReduceMotion&&w.has(t)?{type:!1}:a,e,h));let f=r.animation;f&&c.push(f)}return a&&Promise.all(c).then(()=>{p.update(()=>{a&&function(e,t){let{transitionEnd:n={},transition:r={},...i}=s(e,t)||{};for(let t in i={...i,...n}){var o;let n=_(o=i[t])?o[o.length-1]||0:o;e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,k(n))}}(e,a)})}),c}function t7(e,t,n={}){let r=s(e,t,"exit"===n.type?e.presenceContext?.custom:void 0),{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);let o=r?()=>Promise.all(t9(e,r,n)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:o=0,staggerChildren:a,staggerDirection:s}=i;return function(e,t,n=0,r=0,i=1,o){let a=[],s=(e.variantChildren.size-1)*r,l=1===i?(e=0)=>e*r:(e=0)=>s-e*r;return Array.from(e.variantChildren).sort(t8).forEach((e,r)=>{e.notify("AnimationStart",t),a.push(t7(e,t,{...o,delay:n+l(r)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,o+r,a,s,n)}:()=>Promise.resolve(),{when:l}=i;if(!l)return Promise.all([o(),a(n.delay)]);{let[e,t]="beforeChildren"===l?[o,a]:[a,o];return e().then(()=>t())}}function t8(e,t){return e.sortNodePosition(t)}function ne(e,t){if(!Array.isArray(t))return!1;let n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function nt(e){return"string"==typeof e||Array.isArray(e)}let nn=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],nr=["initial",...nn],ni=nr.length,no=[...nn].reverse(),na=nn.length;function ns(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function nl(){return{animate:ns(!0),whileInView:ns(),whileHover:ns(),whileTap:ns(),whileDrag:ns(),whileFocus:ns(),exit:ns()}}class nu{constructor(e){this.isMounted=!1,this.node=e}update(){}}class nc extends nu{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:n})=>(function(e,t,n={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>t7(e,t,n)));else if("string"==typeof t)r=t7(e,t,n);else{let i="function"==typeof t?s(e,t,n.custom):t;r=Promise.all(t9(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})})(e,t,n))),n=nl(),r=!0,o=t=>(n,r)=>{let i=s(e,r,"exit"===t?e.presenceContext?.custom:void 0);if(i){let{transition:e,transitionEnd:t,...r}=i;n={...n,...r,...t}}return n};function a(a){let{props:l}=e,u=function e(t){if(!t)return;if(!t.isControllingVariants){let n=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(n.initial=t.props.initial),n}let n={};for(let e=0;e<ni;e++){let r=nr[e],i=t.props[r];(nt(i)||!1===i)&&(n[r]=i)}return n}(e.parent)||{},c=[],d=new Set,h={},f=1/0;for(let t=0;t<na;t++){var p,m;let s=no[t],g=n[s],y=void 0!==l[s]?l[s]:u[s],v=nt(y),b=s===a?g.isActive:null;!1===b&&(f=t);let w=y===u[s]&&y!==l[s]&&v;if(w&&r&&e.manuallyAnimateOnMount&&(w=!1),g.protectedKeys={...h},!g.isActive&&null===b||!y&&!g.prevProp||i(y)||"boolean"==typeof y)continue;let x=(p=g.prevProp,"string"==typeof(m=y)?m!==p:!!Array.isArray(m)&&!ne(m,p)),P=x||s===a&&g.isActive&&!w&&v||t>f&&v,E=!1,T=Array.isArray(y)?y:[y],R=T.reduce(o(s),{});!1===b&&(R={});let{prevResolvedValues:S={}}=g,M={...S,...R},A=t=>{P=!0,d.has(t)&&(E=!0,d.delete(t)),g.needsAnimating[t]=!0;let n=e.getValue(t);n&&(n.liveStyle=!1)};for(let e in M){let t=R[e],n=S[e];if(h.hasOwnProperty(e))continue;let r=!1;(_(t)&&_(n)?ne(t,n):t===n)?void 0!==t&&d.has(e)?A(e):g.protectedKeys[e]=!0:null!=t?A(e):d.add(e)}g.prevProp=y,g.prevResolvedValues=R,g.isActive&&(h={...h,...R}),r&&e.blockInitialAnimation&&(P=!1);let k=!(w&&x)||E;P&&k&&c.push(...T.map(e=>({animation:e,options:{type:s}})))}if(d.size){let t={};if("boolean"!=typeof l.initial){let n=s(e,Array.isArray(l.initial)?l.initial[0]:l.initial);n&&n.transition&&(t.transition=n.transition)}d.forEach(n=>{let r=e.getBaseTarget(n),i=e.getValue(n);i&&(i.liveStyle=!0),t[n]=r??null}),c.push({animation:t})}let g=!!c.length;return r&&(!1===l.initial||l.initial===l.animate)&&!e.manuallyAnimateOnMount&&(g=!1),r=!1,g?t(c):Promise.resolve()}return{animateChanges:a,setActive:function(t,r){if(n[t].isActive===r)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,r)),n[t].isActive=r;let i=a(t);for(let e in n)n[e].protectedKeys={};return i},setAnimateFunction:function(n){t=n(e)},getState:()=>n,reset:()=>{n=nl(),r=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();i(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let nd=0;class nh extends nu{constructor(){super(...arguments),this.id=nd++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let nf={x:!1,y:!1};function np(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}let nm=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function ng(e){return{point:{x:e.pageX,y:e.pageY}}}let ny=e=>t=>nm(t)&&e(t,ng(t));function nv(e,t,n,r){return np(e,t,ny(n),r)}function nb({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function nw(e){return e.max-e.min}function nx(e,t,n,r=.5){e.origin=r,e.originPoint=eS(t.min,t.max,e.origin),e.scale=nw(n)/nw(t),e.translate=eS(n.min,n.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function nP(e,t,n,r){nx(e.x,t.x,n.x,r?r.originX:void 0),nx(e.y,t.y,n.y,r?r.originY:void 0)}function nE(e,t,n){e.min=n.min+t.min,e.max=e.min+nw(t)}function nT(e,t,n){e.min=t.min-n.min,e.max=e.min+nw(t)}function nR(e,t,n){nT(e.x,t.x,n.x),nT(e.y,t.y,n.y)}let nS=()=>({translate:0,scale:1,origin:0,originPoint:0}),nM=()=>({x:nS(),y:nS()}),nA=()=>({min:0,max:0}),nk=()=>({x:nA(),y:nA()});function n_(e){return[e("x"),e("y")]}function nj(e){return void 0===e||1===e}function nC({scale:e,scaleX:t,scaleY:n}){return!nj(e)||!nj(t)||!nj(n)}function nO(e){return nC(e)||nD(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function nD(e){var t,n;return(t=e.x)&&"0%"!==t||(n=e.y)&&"0%"!==n}function nL(e,t,n,r,i){return void 0!==i&&(e=r+i*(e-r)),r+n*(e-r)+t}function nN(e,t=0,n=1,r,i){e.min=nL(e.min,t,n,r,i),e.max=nL(e.max,t,n,r,i)}function nI(e,{x:t,y:n}){nN(e.x,t.translate,t.scale,t.originPoint),nN(e.y,n.translate,n.scale,n.originPoint)}function nV(e,t){e.min=e.min+t,e.max=e.max+t}function nU(e,t,n,r,i=.5){let o=eS(e.min,e.max,i);nN(e,t,n,o,r)}function nF(e,t){nU(e.x,t.x,t.scaleX,t.scale,t.originX),nU(e.y,t.y,t.scaleY,t.scale,t.originY)}function nB(e,t){return nb(function(e,t){if(!t)return e;let n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let nz=({current:e})=>e?e.ownerDocument.defaultView:null;function nH(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let nW=(e,t)=>Math.abs(e-t);class n${constructor(e,t,{transformPagePoint:n,contextWindow:r,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=nY(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){return Math.sqrt(nW(e.x,t.x)**2+nW(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!n)return;let{point:r}=e,{timestamp:i}=g;this.history.push({...r,timestamp:i});let{onStart:o,onMove:a}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=nK(t,this.transformPagePoint),p.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=nY("pointercancel"===e.type?this.lastMoveEventInfo:nK(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,o),r&&r(e,o)},!nm(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.contextWindow=r||window;let o=nK(ng(e),this.transformPagePoint),{point:a}=o,{timestamp:s}=g;this.history=[{...a,timestamp:s}];let{onSessionStart:l}=t;l&&l(e,nY(o,this.history)),this.removeListeners=N(nv(this.contextWindow,"pointermove",this.handlePointerMove),nv(this.contextWindow,"pointerup",this.handlePointerUp),nv(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),m(this.updatePoint)}}function nK(e,t){return t?{point:t(e.point)}:e}function nG(e,t){return{x:e.x-t.x,y:e.y-t.y}}function nY({point:e},t){return{point:e,delta:nG(e,nq(t)),offset:nG(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null,i=nq(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>V(.1)));)n--;if(!r)return{x:0,y:0};let o=U(i.timestamp-r.timestamp);if(0===o)return{x:0,y:0};let a={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,.1)}}function nq(e){return e[e.length-1]}function nX(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function nZ(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function nQ(e,t,n){return{min:nJ(e,t),max:nJ(e,n)}}function nJ(e,t){return"number"==typeof e?e:e[t]||0}let n0=new WeakMap;class n1{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=nk(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new n$(e,{onSessionStart:e=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(ng(e).point)},onStart:(e,t)=>{let{drag:n,dragPropagation:r,onDragStart:i}=this.getProps();if(n&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(nf[e])return null;else return nf[e]=!0,()=>{nf[e]=!1};return nf.x||nf.y?null:(nf.x=nf.y=!0,()=>{nf.x=nf.y=!1})}(n),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),n_(e=>{let t=this.getAxisMotionValue(e).get()||0;if(el.test(t)){let{projection:n}=this.visualElement;if(n&&n.layout){let r=n.layout.layoutBox[e];r&&(t=nw(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),i&&p.postRender(()=>i(e,t)),C(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:a}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}(a),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),o&&o(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>n_(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:nz(this.visualElement)})}stop(e,t){let n=this.isDragging;if(this.cancel(),!n)return;let{velocity:r}=t;this.startAnimation(r);let{onDragEnd:i}=this.getProps();i&&p.postRender(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){let{drag:r}=this.getProps();if(!n||!n2(e,r,this.currentDirection))return;let i=this.getAxisMotionValue(e),o=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(o=function(e,{min:t,max:n},r){return void 0!==t&&e<t?e=r?eS(t,e,r.min):Math.max(e,t):void 0!==n&&e>n&&(e=r?eS(n,e,r.max):Math.min(e,n)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;e&&nH(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(e,{top:t,left:n,bottom:r,right:i}){return{x:nX(e.x,n,i),y:nX(e.y,t,r)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:nQ(e,"left","right"),y:nQ(e,"top","bottom")}}(t),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&n_(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!nH(t))return!1;let r=t.current;z(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(e,t,n){let r=nB(e,n),{scroll:i}=t;return i&&(nV(r.x,i.offset.x),nV(r.y,i.offset.y)),r}(r,i.root,this.visualElement.getTransformPagePoint()),a=(e=i.layout.layoutBox,{x:nZ(e.x,o.x),y:nZ(e.y,o.y)});if(n){let e=n(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=nb(e))}return a}startAnimation(e){let{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),s=this.constraints||{};return Promise.all(n_(a=>{if(!n2(a,t,this.currentDirection))return;let l=s&&s[a]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:n?e[a]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(a,u)})).then(a)}startAxisValueAnimation(e,t){let n=this.getAxisMotionValue(e);return C(this.visualElement,e),n.start(t6(e,n,0,t,this.visualElement,!1))}stopAnimation(){n_(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){n_(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,n=this.visualElement.getProps();return n[t]||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){n_(t=>{let{drag:n}=this.getProps();if(!n2(t,n,this.currentDirection))return;let{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){let{min:n,max:o}=r.layout.layoutBox[t];i.set(e[t]-eS(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!nH(t)||!n||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};n_(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let n=t.get();r[e]=function(e,t){let n=.5,r=nw(e),i=nw(t);return i>r?n=to(t.min,t.max-r,e.min):r>i&&(n=to(e.min,e.max-i,t.min)),I(0,1,n)}({min:n,max:n},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),n_(t=>{if(!n2(t,e,null))return;let n=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];n.set(eS(i,o,r[t]))})}addListeners(){if(!this.visualElement.current)return;n0.set(this.visualElement,this);let e=nv(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();nH(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),p.read(t);let i=np(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(n_(t=>{let n=this.getAxisMotionValue(t);n&&(this.originPoint[t]+=e[t].translate,n.set(n.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),r(),o&&o()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:o,dragMomentum:a}}}function n2(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}class n5 extends nu{constructor(e){super(e),this.removeGroupControls=u,this.removeListeners=u,this.controls=new n1(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let n3=e=>(t,n)=>{e&&p.postRender(()=>e(t,n))};class n4 extends nu{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(e){this.session=new n$(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nz(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:n3(e),onStart:n3(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&p.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=nv(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var n6=n(60687);let{schedule:n9}=f(queueMicrotask,!1);var n7=n(43210),n8=n(86044),re=n(12157);let rt=(0,n7.createContext)({}),rn={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rr(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let ri={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!eu.test(e))return e;else e=parseFloat(e);let n=rr(e,t.target.x),r=rr(e,t.target.y);return`${n}% ${r}%`}},ro={};class ra extends n7.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;for(let e in rl)ro[e]=rl[e],W(e)&&(ro[e].isCSSVariable=!0);i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),rn.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,{projection:o}=n;return o&&(o.isPresent=i,r||e.layoutDependency!==t||void 0===t||e.isPresent!==i?o.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?o.promote():o.relegate()||p.postRender(()=>{let e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),n9.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function rs(e){let[t,n]=(0,n8.xQ)(),r=(0,n7.useContext)(re.L);return(0,n6.jsx)(ra,{...e,layoutGroup:r,switchLayoutGroup:(0,n7.useContext)(rt),isPresent:t,safeToRemove:n})}let rl={borderRadius:{...ri,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ri,borderTopRightRadius:ri,borderBottomLeftRadius:ri,borderBottomRightRadius:ri,boxShadow:{correct:(e,{treeScale:t,projectionDelta:n})=>{let r=eE.parse(e);if(r.length>5)return e;let i=eE.createTransformer(e),o=+("number"!=typeof r[0]),a=n.x.scale*t.x,s=n.y.scale*t.y;r[0+o]/=a,r[1+o]/=s;let l=eS(a,s,.5);return"number"==typeof r[2+o]&&(r[2+o]/=l),"number"==typeof r[3+o]&&(r[3+o]/=l),i(r)}}};var ru=n(74479);function rc(e){return(0,ru.G)(e)&&"ownerSVGElement"in e}let rd=(e,t)=>e.depth-t.depth;class rh{constructor(){this.children=[],this.isDirty=!1}add(e){x(this.children,e),this.isDirty=!0}remove(e){P(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(rd),this.isDirty=!1,this.children.forEach(e)}}function rf(e){return j(e)?e.get():e}let rp=["TopLeft","TopRight","BottomLeft","BottomRight"],rm=rp.length,rg=e=>"string"==typeof e?parseFloat(e):e,ry=e=>"number"==typeof e||eu.test(e);function rv(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let rb=rx(0,.5,e8),rw=rx(.5,.95,u);function rx(e,t,n){return r=>r<e?0:r>t?1:n(to(e,t,r))}function rP(e,t){e.min=t.min,e.max=t.max}function rE(e,t){rP(e.x,t.x),rP(e.y,t.y)}function rT(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function rR(e,t,n,r,i){return e-=t,e=r+1/n*(e-r),void 0!==i&&(e=r+1/i*(e-r)),e}function rS(e,t,[n,r,i],o,a){!function(e,t=0,n=1,r=.5,i,o=e,a=e){if(el.test(t)&&(t=parseFloat(t),t=eS(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let s=eS(o.min,o.max,r);e===o&&(s-=t),e.min=rR(e.min,t,n,s,i),e.max=rR(e.max,t,n,s,i)}(e,t[n],t[r],t[i],t.scale,o,a)}let rM=["x","scaleX","originX"],rA=["y","scaleY","originY"];function rk(e,t,n,r){rS(e.x,t,rM,n?n.x:void 0,r?r.x:void 0),rS(e.y,t,rA,n?n.y:void 0,r?r.y:void 0)}function r_(e){return 0===e.translate&&1===e.scale}function rj(e){return r_(e.x)&&r_(e.y)}function rC(e,t){return e.min===t.min&&e.max===t.max}function rO(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function rD(e,t){return rO(e.x,t.x)&&rO(e.y,t.y)}function rL(e){return nw(e.x)/nw(e.y)}function rN(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class rI{constructor(){this.members=[]}add(e){x(this.members,e),e.scheduleRender()}remove(e){if(P(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,n=this.members.findIndex(t=>e===t);if(0===n)return!1;for(let e=n;e>=0;e--){let n=this.members[e];if(!1!==n.isPresent){t=n;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rV={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},rU=["","X","Y","Z"],rF={visibility:"hidden"},rB=0;function rz(e,t,n,r){let{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function rH({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(e={},n=t?.()){this.id=rB++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,h.value&&(rV.nodes=rV.calculatedTargetDeltas=rV.calculatedProjections=0),this.nodes.forEach(rK),this.nodes.forEach(rJ),this.nodes.forEach(r0),this.nodes.forEach(rG),h.addProjectionMetrics&&h.addProjectionMetrics(rV)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new rh)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new E),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let n=this.eventHandlers.get(e);n&&n.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=rc(t)&&!(rc(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:n,layout:r,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||n)&&(this.isLayoutDirty=!0),e){let n,r=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){let n=R.now(),r=({timestamp:i})=>{let o=i-n;o>=250&&(m(r),e(o-t))};return p.setup(r,!0),()=>m(r)}(r,250),rn.hasAnimatedSinceResize&&(rn.hasAnimatedSinceResize=!1,this.nodes.forEach(rQ))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&i&&(n||r)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:n,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||i.getDefaultTransition()||r6,{onLayoutAnimationStart:a,onLayoutAnimationComplete:s}=i.getProps(),u=!this.targetLayout||!rD(this.targetLayout,r),c=!t&&n;if(this.options.layoutRoot||this.resumeFrom||c||t&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...l(o,"layout"),onPlay:a,onComplete:s};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,c)}else t||rQ(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),m(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(r1),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:n}=t.options;if(!n)return;let r=n.props[D];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:n}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",p,!(e||n))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rq);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(rX);this.isUpdating||this.nodes.forEach(rX),this.animationCommitId=this.animationId,this.isUpdating=!1,this.nodes.forEach(rZ),this.nodes.forEach(rW),this.nodes.forEach(r$),this.clearAllSnapshots();let e=R.now();g.delta=I(0,1e3/60,e-g.timestamp),g.timestamp=e,g.isProcessing=!0,y.update.process(g),y.preRender.process(g),y.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,n9.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rY),this.sharedNodes.forEach(r2)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,p.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){p.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||nw(this.snapshot.measuredBox.x)||nw(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=nk(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=r(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!rj(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,o=r!==this.prevTransformTemplateValue;e&&this.instance&&(t||nO(this.latestValues)||o)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let n=this.measurePageBox(),r=this.removeElementScroll(n);return e&&(r=this.removeTransform(r)),r8((t=r).x),r8(t.y),{animationId:this.root.animationId,measuredBox:n,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return nk();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(it))){let{scroll:e}=this.root;e&&(nV(t.x,e.offset.x),nV(t.y,e.offset.y))}return t}removeElementScroll(e){let t=nk();if(rE(t,e),this.scroll?.wasRoot)return t;for(let n=0;n<this.path.length;n++){let r=this.path[n],{scroll:i,options:o}=r;r!==this.root&&i&&o.layoutScroll&&(i.wasRoot&&rE(t,e),nV(t.x,i.offset.x),nV(t.y,i.offset.y))}return t}applyTransform(e,t=!1){let n=nk();rE(n,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&nF(n,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),nO(r.latestValues)&&nF(n,r.latestValues)}return nO(this.latestValues)&&nF(n,this.latestValues),n}removeTransform(e){let t=nk();rE(t,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];if(!n.instance||!nO(n.latestValues))continue;nC(n.latestValues)&&n.updateSnapshot();let r=nk();rE(r,n.measurePageBox()),rk(t,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,r)}return nO(this.latestValues)&&rk(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let n=!!this.resumingFrom||this!==t;if(!(e||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:i}=this.options;if(this.layout&&(r||i)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nk(),this.relativeTargetOrigin=nk(),nR(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),rE(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=nk(),this.targetWithTransforms=nk()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var o,a,s;this.forceRelativeParentToResolveTarget(),o=this.target,a=this.relativeTarget,s=this.relativeParent.target,nE(o.x,a.x,s.x),nE(o.y,a.y,s.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rE(this.target,this.layout.layoutBox),nI(this.target,this.targetDelta)):rE(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nk(),this.relativeTargetOrigin=nk(),nR(this.relativeTargetOrigin,this.target,e.target),rE(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}h.value&&rV.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||nC(this.parent.latestValues)||nD(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===g.timestamp&&(n=!1),n)return;let{layout:r,layoutId:i}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||i))return;rE(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(e,t,n,r=!1){let i,o,a=n.length;if(a){t.x=t.y=1;for(let s=0;s<a;s++){o=(i=n[s]).projectionDelta;let{visualElement:a}=i.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&nF(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,nI(e,o)),r&&nO(i.latestValues)&&nF(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=nk());let{target:s}=e;if(!s){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rT(this.prevProjectionDelta.x,this.projectionDelta.x),rT(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),nP(this.projectionDelta,this.layoutCorrected,s,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&rN(this.projectionDelta.x,this.prevProjectionDelta.x)&&rN(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",s)),h.value&&rV.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=nM(),this.projectionDelta=nM(),this.projectionDeltaWithTransform=nM()}setAnimationOrigin(e,t=!1){let n,r=this.snapshot,i=r?r.latestValues:{},o={...this.latestValues},a=nM();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let s=nk(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(r4));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(r5(a.x,e.x,r),r5(a.y,e.y,r),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,h,f,p,m,g;nR(s,this.layout.layoutBox,this.relativeParent.layout.layoutBox),f=this.relativeTarget,p=this.relativeTargetOrigin,m=s,g=r,r3(f.x,p.x,m.x,g),r3(f.y,p.y,m.y,g),n&&(u=this.relativeTarget,h=n,rC(u.x,h.x)&&rC(u.y,h.y))&&(this.isProjectionDirty=!1),n||(n=nk()),rE(n,this.relativeTarget)}l&&(this.animationValues=o,function(e,t,n,r,i,o){i?(e.opacity=eS(0,n.opacity??1,rb(r)),e.opacityExit=eS(t.opacity??1,0,rw(r))):o&&(e.opacity=eS(t.opacity??1,n.opacity??1,r));for(let i=0;i<rm;i++){let o=`border${rp[i]}Radius`,a=rv(t,o),s=rv(n,o);(void 0!==a||void 0!==s)&&(a||(a=0),s||(s=0),0===a||0===s||ry(a)===ry(s)?(e[o]=Math.max(eS(rg(a),rg(s),r),0),(el.test(s)||el.test(a))&&(e[o]+="%")):e[o]=s)}(t.rotate||n.rotate)&&(e.rotate=eS(t.rotate||0,n.rotate||0,r))}(o,i,this.latestValues,r,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(m(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=p.update(()=>{rn.hasAnimatedSinceResize=!0,F.layout++,this.motionValue||(this.motionValue=k(0)),this.currentAnimation=function(e,t,n){let r=j(e)?e:k(e);return r.start(t6("",r,t,n)),r.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{F.layout--},onComplete:()=>{F.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&ie(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||nk();let t=nw(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;let r=nw(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}rE(t,n),nF(t,i),nP(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new rI),this.sharedNodes.get(e).add(t);let n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:n}={}){let r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:n}=e;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(t=!0),!t)return;let r={};n.z&&rz("z",e,r,this.animationValues);for(let t=0;t<rU.length;t++)rz(`rotate${rU[t]}`,e,r,this.animationValues),rz(`skew${rU[t]}`,e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return rF;let t={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity="",t.pointerEvents=rf(e?.pointerEvents)||"",t.transform=n?n(this.latestValues,""):"none",t;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=rf(e?.pointerEvents)||""),this.hasProjected&&!nO(this.latestValues)&&(t.transform=n?n({},""):"none",this.hasProjected=!1),t}let i=r.animationValues||r.latestValues;this.applyTransformsToTarget(),t.transform=function(e,t,n){let r="",i=e.x.translate/t.x,o=e.y.translate/t.y,a=n?.z||0;if((i||o||a)&&(r=`translate3d(${i}px, ${o}px, ${a}px) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:o,skewX:a,skewY:s}=n;e&&(r=`perspective(${e}px) ${r}`),t&&(r+=`rotate(${t}deg) `),i&&(r+=`rotateX(${i}deg) `),o&&(r+=`rotateY(${o}deg) `),a&&(r+=`skewX(${a}deg) `),s&&(r+=`skewY(${s}deg) `)}let s=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==s||1!==l)&&(r+=`scale(${s}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,i),n&&(t.transform=n(i,t.transform));let{x:o,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*o.origin}% ${100*a.origin}% 0`,r.animationValues?t.opacity=r===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:t.opacity=r===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0,ro){if(void 0===i[e])continue;let{correct:n,applyTo:o,isCSSVariable:a}=ro[e],s="none"===t.transform?i[e]:n(i[e],r);if(o){let e=o.length;for(let n=0;n<e;n++)t[o[n]]=s}else a?this.options.visualElement.renderState.vars[e]=s:t[e]=s}return this.options.layoutId&&(t.pointerEvents=r===this?rf(e?.pointerEvents)||"":"none"),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(rq),this.root.sharedNodes.clear()}}}function rW(e){e.updateLayout()}function r$(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:n,measuredBox:r}=e.layout,{animationType:i}=e.options,o=t.source!==e.layout.source;"size"===i?n_(e=>{let r=o?t.measuredBox[e]:t.layoutBox[e],i=nw(r);r.min=n[e].min,r.max=r.min+i}):ie(i,t.layoutBox,n)&&n_(r=>{let i=o?t.measuredBox[r]:t.layoutBox[r],a=nw(n[r]);i.max=i.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+a)});let a=nM();nP(a,n,t.layoutBox);let s=nM();o?nP(s,e.applyTransform(r,!0),t.measuredBox):nP(s,n,t.layoutBox);let l=!rj(a),u=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:i,layout:o}=r;if(i&&o){let a=nk();nR(a,t.layoutBox,i.layoutBox);let s=nk();nR(s,n,o.layoutBox),rD(a,s)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=s,e.relativeTargetOrigin=a,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:t,delta:s,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function rK(e){h.value&&rV.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function rG(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function rY(e){e.clearSnapshot()}function rq(e){e.clearMeasurements()}function rX(e){e.isLayoutDirty=!1}function rZ(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function rQ(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function rJ(e){e.resolveTargetDelta()}function r0(e){e.calcProjection()}function r1(e){e.resetSkewAndRotation()}function r2(e){e.removeLeadSnapshot()}function r5(e,t,n){e.translate=eS(t.translate,0,n),e.scale=eS(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function r3(e,t,n,r){e.min=eS(t.min,n.min,r),e.max=eS(t.max,n.max,r)}function r4(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let r6={duration:.45,ease:[.4,0,.1,1]},r9=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),r7=r9("applewebkit/")&&!r9("chrome/")?Math.round:u;function r8(e){e.min=r7(e.min),e.max=r7(e.max)}function ie(e,t,n){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(rL(t)-rL(n)))}function it(e){return e!==e.root&&e.scroll?.wasRoot}let ir=rH({attachResizeListener:(e,t)=>np(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ii={current:void 0},io=rH({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ii.current){let e=new ir({});e.mount(window),e.setOptions({layoutScroll:!0}),ii.current=e}return ii.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function ia(e,t){let n=function(e,t,n){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,n=(void 0)??t.querySelectorAll(e);return n?Array.from(n):[]}return Array.from(e)}(e),r=new AbortController;return[n,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function is(e){return!("touch"===e.pointerType||nf.x||nf.y)}function il(e,t,n){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===n);let i=r["onHover"+n];i&&p.postRender(()=>i(t,ng(t)))}class iu extends nu{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=ia(e,n),a=e=>{if(!is(e))return;let{target:n}=e,r=t(n,e);if("function"!=typeof r||!n)return;let o=e=>{is(e)&&(r(e),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,i)};return r.forEach(e=>{e.addEventListener("pointerenter",a,i)}),o}(e,(e,t)=>(il(this.node,t,"Start"),e=>il(this.node,e,"End"))))}unmount(){}}class ic extends nu{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=N(np(this.node.current,"focus",()=>this.onFocus()),np(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let id=(e,t)=>!!t&&(e===t||id(e,t.parentElement)),ih=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),ip=new WeakSet;function im(e){return t=>{"Enter"===t.key&&e(t)}}function ig(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let iy=(e,t)=>{let n=e.currentTarget;if(!n)return;let r=im(()=>{if(ip.has(n))return;ig(n,"down");let e=im(()=>{ig(n,"up")});n.addEventListener("keyup",e,t),n.addEventListener("blur",()=>ig(n,"cancel"),t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function iv(e){return nm(e)&&!(nf.x||nf.y)}function ib(e,t,n){let{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===n);let i=r["onTap"+("End"===n?"":n)];i&&p.postRender(()=>i(t,ng(t)))}class iw extends nu{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=ia(e,n),a=e=>{let r=e.currentTarget;if(!iv(e))return;ip.add(r);let o=t(r,e),a=(e,t)=>{window.removeEventListener("pointerup",s),window.removeEventListener("pointercancel",l),ip.has(r)&&ip.delete(r),iv(e)&&"function"==typeof o&&o(e,{success:t})},s=e=>{a(e,r===window||r===document||n.useGlobalTarget||id(r,e.target))},l=e=>{a(e,!1)};window.addEventListener("pointerup",s,i),window.addEventListener("pointercancel",l,i)};return r.forEach(e=>{((n.useGlobalTarget?window:e).addEventListener("pointerdown",a,i),(0,tX.s)(e))&&(e.addEventListener("focus",e=>iy(e,i)),ih.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),o}(e,(e,t)=>(ib(this.node,t,"Start"),(e,{success:t})=>ib(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let ix=new WeakMap,iP=new WeakMap,iE=e=>{let t=ix.get(e.target);t&&t(e)},iT=e=>{e.forEach(iE)},iR={some:0,all:1};class iS extends nu{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:n,threshold:"number"==typeof r?r:iR[r]};return function(e,t,n){let r=function({root:e,...t}){let n=e||document;iP.has(n)||iP.set(n,{});let r=iP.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(iT,{root:e,...t})),r[i]}(t);return ix.set(e,n),r.observe(e),()=>{ix.delete(e),r.unobserve(e)}}(this.node.current,o,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),o=t?n:r;o&&o(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}(e,t))&&this.startObserver()}unmount(){}}let iM=(0,n7.createContext)({strict:!1});var iA=n(32582);let ik=(0,n7.createContext)({});function i_(e){return i(e.animate)||nr.some(t=>nt(e[t]))}function ij(e){return!!(i_(e)||e.variants)}function iC(e){return Array.isArray(e)?e.join(" "):e}var iO=n(7044);let iD={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},iL={};for(let e in iD)iL[e]={isEnabled:t=>iD[e].some(e=>!!t[e])};let iN=Symbol.for("motionComponentSymbol");var iI=n(21279),iV=n(15124);function iU(e,{layout:t,layoutId:n}){return b.has(e)||e.startsWith("origin")||(t||void 0!==n)&&(!!ro[e]||"opacity"===e)}let iF=(e,t)=>t&&"number"==typeof e?t.transform(e):e,iB={...Y,transform:Math.round},iz={borderWidth:eu,borderTopWidth:eu,borderRightWidth:eu,borderBottomWidth:eu,borderLeftWidth:eu,borderRadius:eu,radius:eu,borderTopLeftRadius:eu,borderTopRightRadius:eu,borderBottomRightRadius:eu,borderBottomLeftRadius:eu,width:eu,maxWidth:eu,height:eu,maxHeight:eu,top:eu,right:eu,bottom:eu,left:eu,padding:eu,paddingTop:eu,paddingRight:eu,paddingBottom:eu,paddingLeft:eu,margin:eu,marginTop:eu,marginRight:eu,marginBottom:eu,marginLeft:eu,backgroundPositionX:eu,backgroundPositionY:eu,rotate:es,rotateX:es,rotateY:es,rotateZ:es,scale:X,scaleX:X,scaleY:X,scaleZ:X,skew:es,skewX:es,skewY:es,distance:eu,translateX:eu,translateY:eu,translateZ:eu,x:eu,y:eu,z:eu,perspective:eu,transformPerspective:eu,opacity:q,originX:eh,originY:eh,originZ:eu,zIndex:iB,fillOpacity:q,strokeOpacity:q,numOctaves:iB},iH={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},iW=v.length;function i$(e,t,n){let{style:r,vars:i,transformOrigin:o}=e,a=!1,s=!1;for(let e in t){let n=t[e];if(b.has(e)){a=!0;continue}if(W(e)){i[e]=n;continue}{let t=iF(n,iz[e]);e.startsWith("origin")?(s=!0,o[e]=t):r[e]=t}}if(!t.transform&&(a||n?r.transform=function(e,t,n){let r="",i=!0;for(let o=0;o<iW;o++){let a=v[o],s=e[a];if(void 0===s)continue;let l=!0;if(!(l="number"==typeof s?s===+!!a.startsWith("scale"):0===parseFloat(s))||n){let e=iF(s,iz[a]);if(!l){i=!1;let t=iH[a]||a;r+=`${t}(${e}) `}n&&(t[a]=e)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}(t,e.transform,n):r.transform&&(r.transform="none")),s){let{originX:e="50%",originY:t="50%",originZ:n=0}=o;r.transformOrigin=`${e} ${t} ${n}`}}let iK=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function iG(e,t,n){for(let r in t)j(t[r])||iU(r,n)||(e[r]=t[r])}let iY={offset:"stroke-dashoffset",array:"stroke-dasharray"},iq={offset:"strokeDashoffset",array:"strokeDasharray"};function iX(e,{attrX:t,attrY:n,attrScale:r,pathLength:i,pathSpacing:o=1,pathOffset:a=0,...s},l,u,c){if(i$(e,s,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:d,style:h}=e;d.transform&&(h.transform=d.transform,delete d.transform),(h.transform||d.transformOrigin)&&(h.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),h.transform&&(h.transformBox=c?.transformBox??"fill-box",delete d.transformBox),void 0!==t&&(d.x=t),void 0!==n&&(d.y=n),void 0!==r&&(d.scale=r),void 0!==i&&function(e,t,n=1,r=0,i=!0){e.pathLength=1;let o=i?iY:iq;e[o.offset]=eu.transform(-r);let a=eu.transform(t),s=eu.transform(n);e[o.array]=`${a} ${s}`}(d,i,o,a,!1)}let iZ=()=>({...iK(),attrs:{}}),iQ=e=>"string"==typeof e&&"svg"===e.toLowerCase(),iJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function i0(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||iJ.has(e)}let i1=e=>!i0(e);try{!function(e){"function"==typeof e&&(i1=t=>t.startsWith("on")?!i0(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let i2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function i5(e){if("string"!=typeof e||e.includes("-"));else if(i2.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var i3=n(72789);let i4=e=>(t,n)=>{let r=(0,n7.useContext)(ik),o=(0,n7.useContext)(iI.t),s=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},n,r,o){return{latestValues:function(e,t,n,r){let o={},s=r(e,{});for(let e in s)o[e]=rf(s[e]);let{initial:l,animate:u}=e,c=i_(e),d=ij(e);t&&d&&!c&&!1!==e.inherit&&(void 0===l&&(l=t.initial),void 0===u&&(u=t.animate));let h=!!n&&!1===n.initial,f=(h=h||!1===l)?u:l;if(f&&"boolean"!=typeof f&&!i(f)){let t=Array.isArray(f)?f:[f];for(let n=0;n<t.length;n++){let r=a(e,t[n]);if(r){let{transitionEnd:e,transition:t,...n}=r;for(let e in n){let t=n[e];if(Array.isArray(t)){let e=h?t.length-1:0;t=t[e]}null!==t&&(o[e]=t)}for(let t in e)o[t]=e[t]}}}return o}(n,r,o,e),renderState:t()}})(e,t,r,o);return n?s():(0,i3.M)(s)};function i6(e,t,n){let{style:r}=e,i={};for(let o in r)(j(r[o])||t.style&&j(t.style[o])||iU(o,e)||n?.getValue(o)?.liveStyle!==void 0)&&(i[o]=r[o]);return i}let i9={useVisualState:i4({scrapeMotionValuesFromProps:i6,createRenderState:iK})};function i7(e,t,n){let r=i6(e,t,n);for(let n in e)(j(e[n])||j(t[n]))&&(r[-1!==v.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=e[n]);return r}let i8={useVisualState:i4({scrapeMotionValuesFromProps:i7,createRenderState:iZ})},oe=e=>t=>t.test(e),ot=[Y,eu,el,es,ed,ec,{test:e=>"auto"===e,parse:e=>e}],on=e=>ot.find(oe(e)),or=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),oi=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,oo=e=>/^0[^.\s]+$/u.test(e),oa=new Set(["brightness","contrast","saturate","opacity"]);function os(e){let[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=n.match(Q)||[];if(!r)return e;let i=n.replace(r,""),o=+!!oa.has(t);return r!==n&&(o*=100),t+"("+o+i+")"}let ol=/\b([a-z-]*)\(.*?\)/gu,ou={...eE,getAnimatableNone:e=>{let t=e.match(ol);return t?t.map(os).join(" "):e}},oc={...iz,color:ep,backgroundColor:ep,outlineColor:ep,fill:ep,stroke:ep,borderColor:ep,borderTopColor:ep,borderRightColor:ep,borderBottomColor:ep,borderLeftColor:ep,filter:ou,WebkitFilter:ou},od=e=>oc[e];function oh(e,t){let n=od(e);return n!==ou&&(n=eE),n.getAnimatableNone?n.getAnimatableNone(t):void 0}let of=new Set(["auto","none","0"]);class op extends tL{constructor(e,t,n,r,i){super(e,t,n,r,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:n}=this;if(!t||!t.current)return;super.readKeyframes();for(let n=0;n<e.length;n++){let r=e[n];if("string"==typeof r&&K(r=r.trim())){let i=function e(t,n,r=1){z(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,o]=function(e){let t=oi.exec(e);if(!t)return[,];let[,n,r,i]=t;return[`--${n??r}`,i]}(t);if(!i)return;let a=window.getComputedStyle(n).getPropertyValue(i);if(a){let e=a.trim();return or(e)?parseFloat(e):e}return K(o)?e(o,n,r+1):o}(r,t.current);void 0!==i&&(e[n]=i),n===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!w.has(n)||2!==e.length)return;let[r,i]=e,o=on(r),a=on(i);if(o!==a)if(tR(o)&&tR(a))for(let t=0;t<e.length;t++){let n=e[t];"string"==typeof n&&(e[t]=parseFloat(n))}else tA[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,n=[];for(let t=0;t<e.length;t++){var r;(null===e[t]||("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||oo(r)))&&n.push(t)}n.length&&function(e,t,n){let r,i=0;for(;i<e.length&&!r;){let t=e[i];"string"==typeof t&&!of.has(t)&&eb(t).values.length&&(r=e[i]),i++}if(r&&n)for(let i of t)e[i]=oh(n,r)}(e,n,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:n}=this;if(!e||!e.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tA[n](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(n,r).jump(r,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:n}=this;if(!e||!e.current)return;let r=e.getValue(t);r&&r.jump(this.measuredOrigin,!1);let i=n.length-1,o=n[i];n[i]=tA[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,n])=>{e.getValue(t).set(n)}),this.resolveNoneKeyframes()}}let om=[...ot,ep,eE],og=e=>om.find(oe(e)),oy={current:null},ov={current:!1},ob=new WeakMap,ow=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ox{scrapeMotionValuesFromProps(e,t,n){return{}}constructor({parent:e,props:t,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:i,visualState:o},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tL,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=R.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,p.render(this.render,!1,!0))};let{latestValues:s,renderState:l}=o;this.latestValues=s,this.baseTarget={...s},this.initialValues=t.initial?{...s}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=a,this.blockInitialAnimation=!!i,this.isControllingVariants=i_(t),this.isVariantNode=ij(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in c){let t=c[e];void 0!==s[e]&&j(t)&&t.set(s[e],!1)}}mount(e){this.current=e,ob.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),ov.current||function(){if(ov.current=!0,iO.B)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>oy.current=e.matches;e.addListener(t),t()}else oy.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||oy.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),m(this.notifyUpdate),m(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let n;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=b.has(e);r&&this.onBindTransform&&this.onBindTransform();let i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&p.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(n=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),o(),n&&n(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in iL){let t=iL[e];if(!t)continue;let{isEnabled:n,Feature:r}=t;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):nk()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<ow.length;t++){let n=ow[t];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let r=e["on"+n];r&&(this.propEventSubscriptions[n]=this.on(n,r))}this.prevMotionValues=function(e,t,n){for(let r in t){let i=t[r],o=n[r];if(j(i))e.addValue(r,i);else if(j(o))e.addValue(r,k(i,{owner:e}));else if(o!==i)if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(r);e.addValue(r,k(void 0!==t?t:i,{owner:e}))}}for(let r in n)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let n=this.values.get(e);t!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=k(null===t?void 0:t,{owner:this}),this.addValue(e,n)),n}readValue(e,t){let n=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=n&&("string"==typeof n&&(or(n)||oo(n))?n=parseFloat(n):!og(n)&&eE.test(t)&&(n=oh(e,t)),this.setBaseTarget(e,j(n)?n.get():n)),j(n)?n.get():n}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let r=a(this.props,n,this.presenceContext?.custom);r&&(t=r[e])}if(n&&void 0!==t)return t;let r=this.getBaseTargetFromProps(this.props,e);return void 0===r||j(r)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:r}on(e,t){return this.events[e]||(this.events[e]=new E),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class oP extends ox{constructor(){super(...arguments),this.KeyframeResolver=op}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:n}){delete t[e],delete n[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;j(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function oE(e,{style:t,vars:n},r,i){for(let o in Object.assign(e.style,t,i&&i.getProjectionStyles(r)),n)e.style.setProperty(o,n[o])}class oT extends oP{constructor(){super(...arguments),this.type="html",this.renderInstance=oE}readValueFromInstance(e,t){if(b.has(t))return this.projection?.isProjecting?tx(t):tE(e,t);{let n=window.getComputedStyle(e),r=(W(t)?n.getPropertyValue(t):n[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return nB(e,t)}build(e,t,n){i$(e,t,n.transformTemplate)}scrapeMotionValuesFromProps(e,t,n){return i6(e,t,n)}}let oR=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class oS extends oP{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=nk}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(b.has(t)){let e=od(t);return e&&e.default||0}return t=oR.has(t)?t:O(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,n){return i7(e,t,n)}build(e,t,n){iX(e,t,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(e,t,n,r){for(let n in oE(e,t,void 0,r),t.attrs)e.setAttribute(oR.has(n)?n:O(n),t.attrs[n])}mount(e){this.isSVGTag=iQ(e.tagName),super.mount(e)}}let oM=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(n,r)=>"create"===r?e:(t.has(r)||t.set(r,e(r)),t.get(r))})}((tY={animation:{Feature:nc},exit:{Feature:nh},inView:{Feature:iS},tap:{Feature:iw},focus:{Feature:ic},hover:{Feature:iu},pan:{Feature:n4},drag:{Feature:n5,ProjectionNode:io,MeasureLayout:rs},layout:{ProjectionNode:io,MeasureLayout:rs}},tq=(e,t)=>i5(e)?new oS(t):new oT(t,{allowProjection:e!==n7.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){function o(e,o){var a,s,l;let u,c={...(0,n7.useContext)(iA.Q),...e,layoutId:function({layoutId:e}){let t=(0,n7.useContext)(re.L).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:d}=c,h=function(e){let{initial:t,animate:n}=function(e,t){if(i_(e)){let{initial:t,animate:n}=e;return{initial:!1===t||nt(t)?t:void 0,animate:nt(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,n7.useContext)(ik));return(0,n7.useMemo)(()=>({initial:t,animate:n}),[iC(t),iC(n)])}(e),f=r(e,d);if(!d&&iO.B){s=0,l=0,(0,n7.useContext)(iM).strict;let e=function(e){let{drag:t,layout:n}=iL;if(!t&&!n)return{};let r={...t,...n};return{MeasureLayout:t?.isEnabled(e)||n?.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(c);u=e.MeasureLayout,h.visualElement=function(e,t,n,r,i){let{visualElement:o}=(0,n7.useContext)(ik),a=(0,n7.useContext)(iM),s=(0,n7.useContext)(iI.t),l=(0,n7.useContext)(iA.Q).reducedMotion,u=(0,n7.useRef)(null);r=r||a.renderer,!u.current&&r&&(u.current=r(e,{visualState:t,parent:o,props:n,presenceContext:s,blockInitialAnimation:!!s&&!1===s.initial,reducedMotionConfig:l}));let c=u.current,d=(0,n7.useContext)(rt);c&&!c.projection&&i&&("html"===c.type||"svg"===c.type)&&function(e,t,n,r){let{layoutId:i,layout:o,drag:a,dragConstraints:s,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!a||s&&nH(s),visualElement:e,animationType:"string"==typeof o?o:"both",initialPromotionConfig:r,crossfade:c,layoutScroll:l,layoutRoot:u})}(u.current,n,i,d);let h=(0,n7.useRef)(!1);(0,n7.useInsertionEffect)(()=>{c&&h.current&&c.update(n,s)});let f=n[D],p=(0,n7.useRef)(!!f&&!window.MotionHandoffIsComplete?.(f)&&window.MotionHasOptimisedAnimation?.(f));return(0,iV.E)(()=>{c&&(h.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),n9.render(c.render),p.current&&c.animationState&&c.animationState.animateChanges())}),(0,n7.useEffect)(()=>{c&&(!p.current&&c.animationState&&c.animationState.animateChanges(),p.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(f)}),p.current=!1))}),c}(i,f,c,t,e.ProjectionNode)}return(0,n6.jsxs)(ik.Provider,{value:h,children:[u&&h.visualElement?(0,n6.jsx)(u,{visualElement:h.visualElement,...c}):null,n(i,e,(a=h.visualElement,(0,n7.useCallback)(e=>{e&&f.onMount&&f.onMount(e),a&&(e?a.mount(e):a.unmount()),o&&("function"==typeof o?o(e):nH(o)&&(o.current=e))},[a])),f,d,h.visualElement)]})}e&&function(e){for(let t in e)iL[t]={...iL[t],...e[t]}}(e),o.displayName=`motion.${"string"==typeof i?i:`create(${i.displayName??i.name??""})`}`;let a=(0,n7.forwardRef)(o);return a[iN]=i,a}({...i5(e)?i8:i9,preloadedFeatures:tY,useRender:function(e=!1){return(t,n,r,{latestValues:i},o)=>{let a=(i5(t)?function(e,t,n,r){let i=(0,n7.useMemo)(()=>{let n=iZ();return iX(n,t,iQ(r),e.transformTemplate,e.style),{...n.attrs,style:{...n.style}}},[t]);if(e.style){let t={};iG(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let n={},r=function(e,t){let n=e.style||{},r={};return iG(r,n,e),Object.assign(r,function({transformTemplate:e},t){return(0,n7.useMemo)(()=>{let n=iK();return i$(n,t,e),Object.assign({},n.vars,n.style)},[t])}(e,t)),r}(e,t);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n})(n,i,o,t),s=function(e,t,n){let r={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(i1(i)||!0===n&&i0(i)||!t&&!i0(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(n,"string"==typeof t,e),l=t!==n7.Fragment?{...s,...a,ref:r}:{},{children:u}=n,c=(0,n7.useMemo)(()=>j(u)?u.get():u,[u]);return(0,n7.createElement)(t,{...l,children:c})}}(t),createVisualElement:tq,Component:e})}))},26736:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return i}});let r=n(2255);function i(e){return(0,r.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28627:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return o}});let r=n(57391),i=n(70642);function o(e,t){var n;let{url:o,tree:a}=t,s=(0,r.createHrefFromUrl)(o),l=a||e.tree,u=e.cache;return{canonicalUrl:s,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:u,prefetchCache:e.prefetchCache,tree:l,nextUrl:null!=(n=(0,i.extractPathFromFlightRouterState)(l))?n:o.pathname}}n(65956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},29651:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return c}});let r=n(57391),i=n(86770),o=n(2030),a=n(25232),s=n(56928),l=n(59435),u=n(89752);function c(e,t){let{serverResponse:{flightData:n,canonicalUrl:c},navigatedAt:d}=t,h={};if(h.preserveCustomHistoryState=!1,"string"==typeof n)return(0,a.handleExternalUrl)(e,h,n,e.pushRef.pendingPush);let f=e.tree,p=e.cache;for(let t of n){let{segmentPath:n,tree:l}=t,m=(0,i.applyRouterStatePatchToTree)(["",...n],f,l,e.canonicalUrl);if(null===m)return e;if((0,o.isNavigatingToNewRootLayout)(f,m))return(0,a.handleExternalUrl)(e,h,e.canonicalUrl,e.pushRef.pendingPush);let g=c?(0,r.createHrefFromUrl)(c):void 0;g&&(h.canonicalUrl=g);let y=(0,u.createEmptyCacheNode)();(0,s.applyFlightData)(d,p,y,t),h.patchedTree=m,h.cache=y,p=y,f=m}return(0,l.handleMutable)(e,h)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},30195:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return o},formatWithValidation:function(){return s},urlObjectKeys:function(){return a}});let r=n(40740)._(n(76715)),i=/https?|ftp|gopher|file/;function o(e){let{auth:t,hostname:n}=e,o=e.protocol||"",a=e.pathname||"",s=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:n&&(u=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(r.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return o&&!o.endsWith(":")&&(o+=":"),e.slashes||(!o||i.test(o))&&!1!==u?(u="//"+(u||""),a&&"/"!==a[0]&&(a="/"+a)):u||(u=""),s&&"#"!==s[0]&&(s="#"+s),c&&"?"!==c[0]&&(c="?"+c),""+o+u+(a=a.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+s}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return o(e)}},32192:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},32582:(e,t,n)=>{n.d(t,{Q:()=>r});let r=(0,n(43210).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},32708:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},33898:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{fillCacheWithNewSubTreeData:function(){return l},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return u}});let r=n(34400),i=n(41500),o=n(33123),a=n(83913);function s(e,t,n,s,l,u){let{segmentPath:c,seedData:d,tree:h,head:f}=s,p=t,m=n;for(let t=0;t<c.length;t+=2){let n=c[t],s=c[t+1],g=t===c.length-2,y=(0,o.createRouterCacheKey)(s),v=m.parallelRoutes.get(n);if(!v)continue;let b=p.parallelRoutes.get(n);b&&b!==v||(b=new Map(v),p.parallelRoutes.set(n,b));let w=v.get(y),x=b.get(y);if(g){if(d&&(!x||!x.lazyData||x===w)){let t=d[0],n=d[1],o=d[3];x={lazyData:null,rsc:u||t!==a.PAGE_SEGMENT_KEY?n:null,prefetchRsc:null,head:null,prefetchHead:null,loading:o,parallelRoutes:u&&w?new Map(w.parallelRoutes):new Map,navigatedAt:e},w&&u&&(0,r.invalidateCacheByRouterState)(x,w,h),u&&(0,i.fillLazyItemsTillLeafWithHead)(e,x,w,h,d,f,l),b.set(y,x)}continue}x&&w&&(x===w&&(x={lazyData:x.lazyData,rsc:x.rsc,prefetchRsc:x.prefetchRsc,head:x.head,prefetchHead:x.prefetchHead,parallelRoutes:new Map(x.parallelRoutes),loading:x.loading},b.set(y,x)),p=x,m=w)}}function l(e,t,n,r,i){s(e,t,n,r,i,!0)}function u(e,t,n,r,i){s(e,t,n,r,i,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34400:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return i}});let r=n(33123);function i(e,t,n){for(let i in n[1]){let o=n[1][i][0],a=(0,r.createRouterCacheKey)(o),s=t.parallelRoutes.get(i);if(s){let t=new Map(s);t.delete(a),e.parallelRoutes.set(i,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},35416:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return r.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return o},getBotType:function(){return l},isBot:function(){return s}});let r=n(95796),i=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,o=r.HTML_LIMITED_BOT_UA_RE.source;function a(e){return r.HTML_LIMITED_BOT_UA_RE.test(e)}function s(e){return i.test(e)||a(e)}function l(e){return i.test(e)?"dom":a(e)?"html":void 0}},35429:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return k}});let r=n(11264),i=n(11448),o=n(91563),a=n(59154),s=n(6361),l=n(57391),u=n(25232),c=n(86770),d=n(2030),h=n(59435),f=n(41500),p=n(89752),m=n(68214),g=n(96493),y=n(22308),v=n(74007),b=n(36875),w=n(97860),x=n(5334),P=n(25942),E=n(26736),T=n(24642);n(50593);let{createFromFetch:R,createTemporaryReferenceSet:S,encodeReply:M}=n(19357);async function A(e,t,n){let a,l,{actionId:u,actionArgs:c}=n,d=S(),h=(0,T.extractInfoFromServerReferenceId)(u),f="use-cache"===h.type?(0,T.omitUnusedArgs)(c,h):c,p=await M(f,{temporaryReferences:d}),m=await fetch("",{method:"POST",headers:{Accept:o.RSC_CONTENT_TYPE_HEADER,[o.ACTION_HEADER]:u,[o.NEXT_ROUTER_STATE_TREE_HEADER]:encodeURIComponent(JSON.stringify(e.tree)),...{},...t?{[o.NEXT_URL]:t}:{}},body:p}),g=m.headers.get("x-action-redirect"),[y,b]=(null==g?void 0:g.split(";"))||[];switch(b){case"push":a=w.RedirectType.push;break;case"replace":a=w.RedirectType.replace;break;default:a=void 0}let x=!!m.headers.get(o.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(m.headers.get("x-action-revalidated")||"[[],0,0]");l={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){l={paths:[],tag:!1,cookie:!1}}let P=y?(0,s.assignLocation)(y,new URL(e.canonicalUrl,window.location.href)):void 0,E=m.headers.get("content-type");if(null==E?void 0:E.startsWith(o.RSC_CONTENT_TYPE_HEADER)){let e=await R(Promise.resolve(m),{callServer:r.callServer,findSourceMapURL:i.findSourceMapURL,temporaryReferences:d});return y?{actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:P,redirectType:a,revalidatedParts:l,isPrerender:x}:{actionResult:e.a,actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:P,redirectType:a,revalidatedParts:l,isPrerender:x}}if(m.status>=400)throw Object.defineProperty(Error("text/plain"===E?await m.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:P,redirectType:a,revalidatedParts:l,isPrerender:x}}function k(e,t){let{resolve:n,reject:r}=t,i={},o=e.tree;i.preserveCustomHistoryState=!1;let s=e.nextUrl&&(0,m.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null,v=Date.now();return A(e,s,t).then(async m=>{let T,{actionResult:R,actionFlightData:S,redirectLocation:M,redirectType:A,isPrerender:k,revalidatedParts:_}=m;if(M&&(A===w.RedirectType.replace?(e.pushRef.pendingPush=!1,i.pendingPush=!1):(e.pushRef.pendingPush=!0,i.pendingPush=!0),i.canonicalUrl=T=(0,l.createHrefFromUrl)(M,!1)),!S)return(n(R),M)?(0,u.handleExternalUrl)(e,i,M.href,e.pushRef.pendingPush):e;if("string"==typeof S)return n(R),(0,u.handleExternalUrl)(e,i,S,e.pushRef.pendingPush);let j=_.paths.length>0||_.tag||_.cookie;for(let r of S){let{tree:a,seedData:l,head:h,isRootRender:m}=r;if(!m)return console.log("SERVER ACTION APPLY FAILED"),n(R),e;let b=(0,c.applyRouterStatePatchToTree)([""],o,a,T||e.canonicalUrl);if(null===b)return n(R),(0,g.handleSegmentMismatch)(e,t,a);if((0,d.isNavigatingToNewRootLayout)(o,b))return n(R),(0,u.handleExternalUrl)(e,i,T||e.canonicalUrl,e.pushRef.pendingPush);if(null!==l){let t=l[1],n=(0,p.createEmptyCacheNode)();n.rsc=t,n.prefetchRsc=null,n.loading=l[3],(0,f.fillLazyItemsTillLeafWithHead)(v,n,void 0,a,l,h,void 0),i.cache=n,i.prefetchCache=new Map,j&&await (0,y.refreshInactiveParallelSegments)({navigatedAt:v,state:e,updatedTree:b,updatedCache:n,includeNextUrl:!!s,canonicalUrl:i.canonicalUrl||e.canonicalUrl})}i.patchedTree=b,o=b}return M&&T?(j||((0,x.createSeededPrefetchCacheEntry)({url:M,data:{flightData:S,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:k?a.PrefetchKind.FULL:a.PrefetchKind.AUTO}),i.prefetchCache=e.prefetchCache),r((0,b.getRedirectError)((0,E.hasBasePath)(T)?(0,P.removeBasePath)(T):T,A||w.RedirectType.push))):n(R),(0,h.handleMutable)(e,i)},t=>(r(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},37472:(e,t,n)=>{n.d(t,{Wx:()=>c});var r=n(43210),i=Object.defineProperty,o=(e,t,n)=>t in e?i(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,a=new Map,s=new WeakMap,l=0,u=void 0;function c({threshold:e,delay:t,trackVisibility:n,rootMargin:i,root:o,triggerOnce:d,skip:h,initialInView:f,fallbackInView:p,onChange:m}={}){var g;let[y,v]=r.useState(null),b=r.useRef(m),[w,x]=r.useState({inView:!!f,entry:void 0});b.current=m,r.useEffect(()=>{let r;if(!h&&y)return r=function(e,t,n={},r=u){if(void 0===window.IntersectionObserver&&void 0!==r){let i=e.getBoundingClientRect();return t(r,{isIntersecting:r,target:e,intersectionRatio:"number"==typeof n.threshold?n.threshold:0,time:0,boundingClientRect:i,intersectionRect:i,rootBounds:i}),()=>{}}let{id:i,observer:o,elements:c}=function(e){let t=Object.keys(e).sort().filter(t=>void 0!==e[t]).map(t=>{var n;return`${t}_${"root"===t?!(n=e.root)?"0":(s.has(n)||(l+=1,s.set(n,l.toString())),s.get(n)):e[t]}`}).toString(),n=a.get(t);if(!n){let r,i=new Map,o=new IntersectionObserver(t=>{t.forEach(t=>{var n;let o=t.isIntersecting&&r.some(e=>t.intersectionRatio>=e);e.trackVisibility&&void 0===t.isVisible&&(t.isVisible=o),null==(n=i.get(t.target))||n.forEach(e=>{e(o,t)})})},e);r=o.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),n={id:t,observer:o,elements:i},a.set(t,n)}return n}(n),d=c.get(e)||[];return c.has(e)||c.set(e,d),d.push(t),o.observe(e),function(){d.splice(d.indexOf(t),1),0===d.length&&(c.delete(e),o.unobserve(e)),0===c.size&&(o.disconnect(),a.delete(i))}}(y,(e,t)=>{x({inView:e,entry:t}),b.current&&b.current(e,t),t.isIntersecting&&d&&r&&(r(),r=void 0)},{root:o,rootMargin:i,threshold:e,trackVisibility:n,delay:t},p),()=>{r&&r()}},[Array.isArray(e)?e.toString():e,y,o,i,d,h,n,p,t]);let P=null==(g=w.entry)?void 0:g.target,E=r.useRef(void 0);y||!P||d||h||E.current===P||(E.current=P,x({inView:!!f,entry:void 0}));let T=[v,w.inView,w.entry];return T.ref=T[0],T.inView=T[1],T.entry=T[2],T}r.Component},41500:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,n,o,a,s,l,u){if(0===Object.keys(a[1]).length){n.head=l;return}for(let c in a[1]){let d,h=a[1][c],f=h[0],p=(0,r.createRouterCacheKey)(f),m=null!==s&&void 0!==s[2][c]?s[2][c]:null;if(o){let r=o.parallelRoutes.get(c);if(r){let o,a=(null==u?void 0:u.kind)==="auto"&&u.status===i.PrefetchCacheEntryStatus.reusable,s=new Map(r),d=s.get(p);o=null!==m?{lazyData:null,rsc:m[1],prefetchRsc:null,head:null,prefetchHead:null,loading:m[3],parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),navigatedAt:t}:a&&d?{lazyData:d.lazyData,rsc:d.rsc,prefetchRsc:d.prefetchRsc,head:d.head,prefetchHead:d.prefetchHead,parallelRoutes:new Map(d.parallelRoutes),loading:d.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==d?void 0:d.parallelRoutes),loading:null,navigatedAt:t},s.set(p,o),e(t,o,d,h,m||null,l,u),n.parallelRoutes.set(c,s);continue}}if(null!==m){let e=m[1],n=m[3];d={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:n,navigatedAt:t}}else d={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let g=n.parallelRoutes.get(c);g?g.set(p,d):n.parallelRoutes.set(c,new Map([[p,d]])),e(t,d,void 0,h,m,l,u)}}}});let r=n(33123),i=n(59154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},41550:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},44397:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return i}});let r=n(33123);function i(e,t){return function e(t,n,i){if(0===Object.keys(n).length)return[t,i];let o=Object.keys(n).filter(e=>"children"!==e);for(let a of("children"in n&&o.unshift("children"),o)){let[o,s]=n[a],l=t.parallelRoutes.get(a);if(!l)continue;let u=(0,r.createRouterCacheKey)(o),c=l.get(u);if(!c)continue;let d=e(c,s,i+"/"+u);if(d)return d}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},49384:(e,t,n)=>{n.d(t,{$:()=>r});function r(){for(var e,t,n=0,r="",i=arguments.length;n<i;n++)(e=arguments[n])&&(t=function e(t){var n,r,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t)if(Array.isArray(t)){var o=t.length;for(n=0;n<o;n++)t[n]&&(r=e(t[n]))&&(i&&(i+=" "),i+=r)}else for(r in t)t[r]&&(i&&(i+=" "),i+=r);return i}(e))&&(r&&(r+=" "),r+=t);return r}},50593:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{NavigationResultTag:function(){return d},PrefetchPriority:function(){return h},cancelPrefetchTask:function(){return l},createCacheKey:function(){return c},getCurrentCacheVersion:function(){return a},navigate:function(){return i},prefetch:function(){return r},reschedulePrefetchTask:function(){return u},revalidateEntireCache:function(){return o},schedulePrefetchTask:function(){return s}});let n=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},r=n,i=n,o=n,a=n,s=n,l=n,u=n,c=n;var d=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),h=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51550:(e,t,n)=>{function r(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}n.r(t),n.d(t,{_:()=>r})},53038:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return i}});let r=n(43210);function i(e,t){let n=(0,r.useRef)(null),i=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=i.current;t&&(i.current=null,t())}else e&&(n.current=o(e,r)),t&&(i.current=o(t,r))},[e,t])}function o(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},54026:(e,t,n)=>{n.d(t,{bm:()=>ta,UC:()=>ti,hJ:()=>tr,ZL:()=>tn,bL:()=>tt,hE:()=>to});var r,i,o,a=n(43210),s=n.t(a,2);function l(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var u=n(98599),c=n(11273),d=n(66156),h=s[" useId ".trim().toString()]||(()=>void 0),f=0;function p(e){let[t,n]=a.useState(h());return(0,d.N)(()=>{e||n(e=>e??String(f++))},[e]),e||(t?`radix-${t}`:"")}var m=s[" useInsertionEffect ".trim().toString()]||d.N,g=(Symbol("RADIX:SYNC_STATE"),n(14163)),y=n(13495),v=n(60687),b="dismissableLayer.update",w=a.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),x=a.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:c,onDismiss:d,...h}=e,f=a.useContext(w),[p,m]=a.useState(null),x=p?.ownerDocument??globalThis?.document,[,T]=a.useState({}),R=(0,u.s)(t,e=>m(e)),S=Array.from(f.layers),[M]=[...f.layersWithOutsidePointerEventsDisabled].slice(-1),A=S.indexOf(M),k=p?S.indexOf(p):-1,_=f.layersWithOutsidePointerEventsDisabled.size>0,j=k>=A,C=function(e,t=globalThis?.document){let n=(0,y.c)(e),r=a.useRef(!1),i=a.useRef(()=>{});return a.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){E("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=r,t.addEventListener("click",i.current,{once:!0})):r()}else t.removeEventListener("click",i.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...f.branches].some(e=>e.contains(t));j&&!n&&(o?.(e),c?.(e),e.defaultPrevented||d?.())},x),O=function(e,t=globalThis?.document){let n=(0,y.c)(e),r=a.useRef(!1);return a.useEffect(()=>{let e=e=>{e.target&&!r.current&&E("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...f.branches].some(e=>e.contains(t))&&(s?.(e),c?.(e),e.defaultPrevented||d?.())},x);return!function(e,t=globalThis?.document){let n=(0,y.c)(e);a.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{k===f.layers.size-1&&(r?.(e),!e.defaultPrevented&&d&&(e.preventDefault(),d()))},x),a.useEffect(()=>{if(p)return n&&(0===f.layersWithOutsidePointerEventsDisabled.size&&(i=x.body.style.pointerEvents,x.body.style.pointerEvents="none"),f.layersWithOutsidePointerEventsDisabled.add(p)),f.layers.add(p),P(),()=>{n&&1===f.layersWithOutsidePointerEventsDisabled.size&&(x.body.style.pointerEvents=i)}},[p,x,n,f]),a.useEffect(()=>()=>{p&&(f.layers.delete(p),f.layersWithOutsidePointerEventsDisabled.delete(p),P())},[p,f]),a.useEffect(()=>{let e=()=>T({});return document.addEventListener(b,e),()=>document.removeEventListener(b,e)},[]),(0,v.jsx)(g.sG.div,{...h,ref:R,style:{pointerEvents:_?j?"auto":"none":void 0,...e.style},onFocusCapture:l(e.onFocusCapture,O.onFocusCapture),onBlurCapture:l(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:l(e.onPointerDownCapture,C.onPointerDownCapture)})});function P(){let e=new CustomEvent(b);document.dispatchEvent(e)}function E(e,t,n,{discrete:r}){let i=n.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),r?(0,g.hO)(i,o):i.dispatchEvent(o)}x.displayName="DismissableLayer",a.forwardRef((e,t)=>{let n=a.useContext(w),r=a.useRef(null),i=(0,u.s)(t,r);return a.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,v.jsx)(g.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var T="focusScope.autoFocusOnMount",R="focusScope.autoFocusOnUnmount",S={bubbles:!1,cancelable:!0},M=a.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:i,onUnmountAutoFocus:o,...s}=e,[l,c]=a.useState(null),d=(0,y.c)(i),h=(0,y.c)(o),f=a.useRef(null),p=(0,u.s)(t,e=>c(e)),m=a.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;a.useEffect(()=>{if(r){let e=function(e){if(m.paused||!l)return;let t=e.target;l.contains(t)?f.current=t:_(f.current,{select:!0})},t=function(e){if(m.paused||!l)return;let t=e.relatedTarget;null!==t&&(l.contains(t)||_(f.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&_(l)});return l&&n.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,l,m.paused]),a.useEffect(()=>{if(l){j.add(m);let e=document.activeElement;if(!l.contains(e)){let t=new CustomEvent(T,S);l.addEventListener(T,d),l.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(_(r,{select:t}),document.activeElement!==n)return}(A(l).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&_(l))}return()=>{l.removeEventListener(T,d),setTimeout(()=>{let t=new CustomEvent(R,S);l.addEventListener(R,h),l.dispatchEvent(t),t.defaultPrevented||_(e??document.body,{select:!0}),l.removeEventListener(R,h),j.remove(m)},0)}}},[l,d,h,m]);let b=a.useCallback(e=>{if(!n&&!r||m.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,i=document.activeElement;if(t&&i){let t=e.currentTarget,[r,o]=function(e){let t=A(e);return[k(t,e),k(t.reverse(),e)]}(t);r&&o?e.shiftKey||i!==o?e.shiftKey&&i===r&&(e.preventDefault(),n&&_(o,{select:!0})):(e.preventDefault(),n&&_(r,{select:!0})):i===t&&e.preventDefault()}},[n,r,m.paused]);return(0,v.jsx)(g.sG.div,{tabIndex:-1,...s,ref:p,onKeyDown:b})});function A(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function k(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function _(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}M.displayName="FocusScope";var j=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=C(e,t)).unshift(t)},remove(t){e=C(e,t),e[0]?.resume()}}}();function C(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var O=n(51215),D=a.forwardRef((e,t)=>{let{container:n,...r}=e,[i,o]=a.useState(!1);(0,d.N)(()=>o(!0),[]);let s=n||i&&globalThis?.document?.body;return s?O.createPortal((0,v.jsx)(g.sG.div,{...r,ref:t}),s):null});D.displayName="Portal";var L=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,i]=a.useState(),o=a.useRef(null),s=a.useRef(e),l=a.useRef("none"),[u,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},a.useReducer((e,t)=>n[e][t]??e,t));return a.useEffect(()=>{let e=N(o.current);l.current="mounted"===u?e:"none"},[u]),(0,d.N)(()=>{let t=o.current,n=s.current;if(n!==e){let r=l.current,i=N(t);e?c("MOUNT"):"none"===i||t?.display==="none"?c("UNMOUNT"):n&&r!==i?c("ANIMATION_OUT"):c("UNMOUNT"),s.current=e}},[e,c]),(0,d.N)(()=>{if(r){let e,t=r.ownerDocument.defaultView??window,n=n=>{let i=N(o.current).includes(n.animationName);if(n.target===r&&i&&(c("ANIMATION_END"),!s.current)){let n=r.style.animationFillMode;r.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=n)})}},i=e=>{e.target===r&&(l.current=N(o.current))};return r.addEventListener("animationstart",i),r.addEventListener("animationcancel",n),r.addEventListener("animationend",n),()=>{t.clearTimeout(e),r.removeEventListener("animationstart",i),r.removeEventListener("animationcancel",n),r.removeEventListener("animationend",n)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:a.useCallback(e=>{o.current=e?getComputedStyle(e):null,i(e)},[])}}(t),i="function"==typeof n?n({present:r.isPresent}):a.Children.only(n),o=(0,u.s)(r.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||r.isPresent?a.cloneElement(i,{ref:o}):null};function N(e){return e?.animationName||"none"}L.displayName="Presence";var I=0;function V(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var U=function(){return(U=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function F(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)0>t.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n}Object.create;Object.create;var B=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),z="width-before-scroll-bar";function H(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var W="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,$=new WeakMap;function K(e){return e}var G=function(e){void 0===e&&(e={});var t,n,r,i,o=(t=null,void 0===n&&(n=K),r=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,i);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){i=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var o=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(o)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return o.options=U({async:!0,ssr:!1},e),o}(),Y=function(){},q=a.forwardRef(function(e,t){var n,r,i,o,s=a.useRef(null),l=a.useState({onScrollCapture:Y,onWheelCapture:Y,onTouchMoveCapture:Y}),u=l[0],c=l[1],d=e.forwardProps,h=e.children,f=e.className,p=e.removeScrollBar,m=e.enabled,g=e.shards,y=e.sideCar,v=e.noRelative,b=e.noIsolation,w=e.inert,x=e.allowPinchZoom,P=e.as,E=e.gapMode,T=F(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=(n=[s,t],r=function(e){return n.forEach(function(t){return H(t,e)})},(i=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return i.value},set current(value){var e=i.value;e!==value&&(i.value=value,i.callback(value,e))}}}})[0]).callback=r,o=i.facade,W(function(){var e=$.get(o);if(e){var t=new Set(e),r=new Set(n),i=o.current;t.forEach(function(e){r.has(e)||H(e,null)}),r.forEach(function(e){t.has(e)||H(e,i)})}$.set(o,n)},[n]),o),S=U(U({},T),u);return a.createElement(a.Fragment,null,m&&a.createElement(y,{sideCar:G,removeScrollBar:p,shards:g,noRelative:v,noIsolation:b,inert:w,setCallbacks:c,allowPinchZoom:!!x,lockRef:s,gapMode:E}),d?a.cloneElement(a.Children.only(h),U(U({},S),{ref:R})):a.createElement(void 0===P?"div":P,U({},S,{className:f,ref:R}),h))});q.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},q.classNames={fullWidth:z,zeroRight:B};var X=function(e){var t=e.sideCar,n=F(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,U({},n))};X.isSideCarExport=!0;var Z=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Q=function(){var e=Z();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},J=function(){var e=Q();return function(t){return e(t.styles,t.dynamic),null}},ee={left:0,top:0,right:0,gap:0},et=function(e){return parseInt(e||"",10)||0},en=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],i=t["padding"===e?"paddingRight":"marginRight"];return[et(n),et(r),et(i)]},er=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return ee;var t=en(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},ei=J(),eo="data-scroll-locked",ea=function(e,t,n,r){var i=e.left,o=e.top,a=e.right,s=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(s,"px ").concat(r,";\n  }\n  body[").concat(eo,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(i,"px;\n    padding-top: ").concat(o,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(s,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(s,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(B," {\n    right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(z," {\n    margin-right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(B," .").concat(B," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(z," .").concat(z," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(eo,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(s,"px;\n  }\n")},es=function(){var e=parseInt(document.body.getAttribute(eo)||"0",10);return isFinite(e)?e:0},el=function(){a.useEffect(function(){return document.body.setAttribute(eo,(es()+1).toString()),function(){var e=es()-1;e<=0?document.body.removeAttribute(eo):document.body.setAttribute(eo,e.toString())}},[])},eu=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,i=void 0===r?"margin":r;el();var o=a.useMemo(function(){return er(i)},[i]);return a.createElement(ei,{styles:ea(o,!t,i,n?"":"!important")})},ec=!1;if("undefined"!=typeof window)try{var ed=Object.defineProperty({},"passive",{get:function(){return ec=!0,!0}});window.addEventListener("test",ed,ed),window.removeEventListener("test",ed,ed)}catch(e){ec=!1}var eh=!!ec&&{passive:!1},ef=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},ep=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),em(e,r)){var i=eg(e,r);if(i[1]>i[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},em=function(e,t){return"v"===e?ef(t,"overflowY"):ef(t,"overflowX")},eg=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},ey=function(e,t,n,r,i){var o,a=(o=window.getComputedStyle(t).direction,"h"===e&&"rtl"===o?-1:1),s=a*r,l=n.target,u=t.contains(l),c=!1,d=s>0,h=0,f=0;do{if(!l)break;var p=eg(e,l),m=p[0],g=p[1]-p[2]-a*m;(m||g)&&em(e,l)&&(h+=g,f+=m);var y=l.parentNode;l=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!u&&l!==document.body||u&&(t.contains(l)||t===l));return d&&(i&&1>Math.abs(h)||!i&&s>h)?c=!0:!d&&(i&&1>Math.abs(f)||!i&&-s>f)&&(c=!0),c},ev=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},eb=function(e){return[e.deltaX,e.deltaY]},ew=function(e){return e&&"current"in e?e.current:e},ex=0,eP=[];let eE=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),i=a.useState(ex++)[0],o=a.useState(J)[0],s=a.useRef(e);a.useEffect(function(){s.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(i));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(ew),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(i))}),function(){document.body.classList.remove("block-interactivity-".concat(i)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(i))})}}},[e.inert,e.lockRef.current,e.shards]);var l=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!s.current.allowPinchZoom;var i,o=ev(e),a=n.current,l="deltaX"in e?e.deltaX:a[0]-o[0],u="deltaY"in e?e.deltaY:a[1]-o[1],c=e.target,d=Math.abs(l)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var h=ep(d,c);if(!h)return!0;if(h?i=d:(i="v"===d?"h":"v",h=ep(d,c)),!h)return!1;if(!r.current&&"changedTouches"in e&&(l||u)&&(r.current=i),!i)return!0;var f=r.current||i;return ey(f,t,e,"h"===f?l:u,!0)},[]),u=a.useCallback(function(e){if(eP.length&&eP[eP.length-1]===o){var n="deltaY"in e?eb(e):ev(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var i=(s.current.shards||[]).map(ew).filter(Boolean).filter(function(t){return t.contains(e.target)});(i.length>0?l(e,i[0]):!s.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=a.useCallback(function(e,n,r,i){var o={name:e,delta:n,target:r,should:i,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(o),setTimeout(function(){t.current=t.current.filter(function(e){return e!==o})},1)},[]),d=a.useCallback(function(e){n.current=ev(e),r.current=void 0},[]),h=a.useCallback(function(t){c(t.type,eb(t),t.target,l(t,e.lockRef.current))},[]),f=a.useCallback(function(t){c(t.type,ev(t),t.target,l(t,e.lockRef.current))},[]);a.useEffect(function(){return eP.push(o),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:f}),document.addEventListener("wheel",u,eh),document.addEventListener("touchmove",u,eh),document.addEventListener("touchstart",d,eh),function(){eP=eP.filter(function(e){return e!==o}),document.removeEventListener("wheel",u,eh),document.removeEventListener("touchmove",u,eh),document.removeEventListener("touchstart",d,eh)}},[]);var p=e.removeScrollBar,m=e.inert;return a.createElement(a.Fragment,null,m?a.createElement(o,{styles:"\n  .block-interactivity-".concat(i," {pointer-events: none;}\n  .allow-interactivity-").concat(i," {pointer-events: all;}\n")}):null,p?a.createElement(eu,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},G.useMedium(r),X);var eT=a.forwardRef(function(e,t){return a.createElement(q,U({},e,{ref:t,sideCar:eE}))});eT.classNames=q.classNames;var eR=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},eS=new WeakMap,eM=new WeakMap,eA={},ek=0,e_=function(e){return e&&(e.host||e_(e.parentNode))},ej=function(e,t,n,r){var i=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=e_(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});eA[n]||(eA[n]=new WeakMap);var o=eA[n],a=[],s=new Set,l=new Set(i),u=function(e){!e||s.has(e)||(s.add(e),u(e.parentNode))};i.forEach(u);var c=function(e){!e||l.has(e)||Array.prototype.forEach.call(e.children,function(e){if(s.has(e))c(e);else try{var t=e.getAttribute(r),i=null!==t&&"false"!==t,l=(eS.get(e)||0)+1,u=(o.get(e)||0)+1;eS.set(e,l),o.set(e,u),a.push(e),1===l&&i&&eM.set(e,!0),1===u&&e.setAttribute(n,"true"),i||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return c(t),s.clear(),ek++,function(){a.forEach(function(e){var t=eS.get(e)-1,i=o.get(e)-1;eS.set(e,t),o.set(e,i),t||(eM.has(e)||e.removeAttribute(r),eM.delete(e)),i||e.removeAttribute(n)}),--ek||(eS=new WeakMap,eS=new WeakMap,eM=new WeakMap,eA={})}},eC=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),i=t||eR(e);return i?(r.push.apply(r,Array.from(i.querySelectorAll("[aria-live], script"))),ej(r,i,n,"aria-hidden")):function(){return null}},eO=n(8730),eD="Dialog",[eL,eN]=(0,c.A)(eD),[eI,eV]=eL(eD),eU=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:i,onOpenChange:o,modal:s=!0}=e,l=a.useRef(null),u=a.useRef(null),[c,d]=function({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,o,s]=function({defaultProp:e,onChange:t}){let[n,r]=a.useState(e),i=a.useRef(n),o=a.useRef(t);return m(()=>{o.current=t},[t]),a.useEffect(()=>{i.current!==n&&(o.current?.(n),i.current=n)},[n,i]),[n,r,o]}({defaultProp:t,onChange:n}),l=void 0!==e,u=l?e:i;{let t=a.useRef(void 0!==e);a.useEffect(()=>{let e=t.current;if(e!==l){let t=l?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,r])}return[u,a.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&s.current?.(n)}else o(t)},[l,e,o,s])]}({prop:r,defaultProp:i??!1,onChange:o,caller:eD});return(0,v.jsx)(eI,{scope:t,triggerRef:l,contentRef:u,contentId:p(),titleId:p(),descriptionId:p(),open:c,onOpenChange:d,onOpenToggle:a.useCallback(()=>d(e=>!e),[d]),modal:s,children:n})};eU.displayName=eD;var eF="DialogTrigger";a.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=eV(eF,n),o=(0,u.s)(t,i.triggerRef);return(0,v.jsx)(g.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":e4(i.open),...r,ref:o,onClick:l(e.onClick,i.onOpenToggle)})}).displayName=eF;var eB="DialogPortal",[ez,eH]=eL(eB,{forceMount:void 0}),eW=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:i}=e,o=eV(eB,t);return(0,v.jsx)(ez,{scope:t,forceMount:n,children:a.Children.map(r,e=>(0,v.jsx)(L,{present:n||o.open,children:(0,v.jsx)(D,{asChild:!0,container:i,children:e})}))})};eW.displayName=eB;var e$="DialogOverlay",eK=a.forwardRef((e,t)=>{let n=eH(e$,e.__scopeDialog),{forceMount:r=n.forceMount,...i}=e,o=eV(e$,e.__scopeDialog);return o.modal?(0,v.jsx)(L,{present:r||o.open,children:(0,v.jsx)(eY,{...i,ref:t})}):null});eK.displayName=e$;var eG=(0,eO.TL)("DialogOverlay.RemoveScroll"),eY=a.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=eV(e$,n);return(0,v.jsx)(eT,{as:eG,allowPinchZoom:!0,shards:[i.contentRef],children:(0,v.jsx)(g.sG.div,{"data-state":e4(i.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),eq="DialogContent",eX=a.forwardRef((e,t)=>{let n=eH(eq,e.__scopeDialog),{forceMount:r=n.forceMount,...i}=e,o=eV(eq,e.__scopeDialog);return(0,v.jsx)(L,{present:r||o.open,children:o.modal?(0,v.jsx)(eZ,{...i,ref:t}):(0,v.jsx)(eQ,{...i,ref:t})})});eX.displayName=eq;var eZ=a.forwardRef((e,t)=>{let n=eV(eq,e.__scopeDialog),r=a.useRef(null),i=(0,u.s)(t,n.contentRef,r);return a.useEffect(()=>{let e=r.current;if(e)return eC(e)},[]),(0,v.jsx)(eJ,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:l(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:l(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:l(e.onFocusOutside,e=>e.preventDefault())})}),eQ=a.forwardRef((e,t)=>{let n=eV(eq,e.__scopeDialog),r=a.useRef(!1),i=a.useRef(!1);return(0,v.jsx)(eJ,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(r.current||n.triggerRef.current?.focus(),t.preventDefault()),r.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let o=t.target;n.triggerRef.current?.contains(o)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),eJ=a.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:i,onCloseAutoFocus:o,...s}=e,l=eV(eq,n),c=a.useRef(null),d=(0,u.s)(t,c);return a.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??V()),document.body.insertAdjacentElement("beforeend",e[1]??V()),I++,()=>{1===I&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),I--}},[]),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(M,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:i,onUnmountAutoFocus:o,children:(0,v.jsx)(x,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":e4(l.open),...s,ref:d,onDismiss:()=>l.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(e8,{titleId:l.titleId}),(0,v.jsx)(te,{contentRef:c,descriptionId:l.descriptionId})]})]})}),e0="DialogTitle",e1=a.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=eV(e0,n);return(0,v.jsx)(g.sG.h2,{id:i.titleId,...r,ref:t})});e1.displayName=e0;var e2="DialogDescription";a.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=eV(e2,n);return(0,v.jsx)(g.sG.p,{id:i.descriptionId,...r,ref:t})}).displayName=e2;var e5="DialogClose",e3=a.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=eV(e5,n);return(0,v.jsx)(g.sG.button,{type:"button",...r,ref:t,onClick:l(e.onClick,()=>i.onOpenChange(!1))})});function e4(e){return e?"open":"closed"}e3.displayName=e5;var e6="DialogTitleWarning",[e9,e7]=(0,c.q)(e6,{contentName:eq,titleName:e0,docsSlug:"dialog"}),e8=({titleId:e})=>{let t=e7(e6),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return a.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},te=({contentRef:e,descriptionId:t})=>{let n=e7("DialogDescriptionWarning"),r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return a.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},tt=eU,tn=eW,tr=eK,ti=eX,to=e1,ta=e3},54674:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return o}});let r=n(84949),i=n(19169),o=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:n,hash:o}=(0,i.parsePath)(e);return""+(0,r.removeTrailingSlash)(t)+n+o};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56928:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return o}});let r=n(41500),i=n(33898);function o(e,t,n,o,a){let{tree:s,seedData:l,head:u,isRootRender:c}=o;if(null===l)return!1;if(c){let i=l[1];n.loading=l[3],n.rsc=i,n.prefetchRsc=null,(0,r.fillLazyItemsTillLeafWithHead)(e,n,t,s,l,u,a)}else n.rsc=t.rsc,n.prefetchRsc=t.prefetchRsc,n.parallelRoutes=new Map(t.parallelRoutes),n.loading=t.loading,(0,i.fillCacheWithNewSubTreeData)(e,n,t,o,a);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},57800:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},58869:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},59435:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return o}});let r=n(70642);function i(e){return void 0!==e}function o(e,t){var n,o;let a=null==(n=t.shouldScroll)||n,s=e.nextUrl;if(i(t.patchedTree)){let n=(0,r.computeChangedPath)(e.tree,t.patchedTree);n?s=n:s||(s=e.canonicalUrl)}return{canonicalUrl:i(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:i(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:i(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:i(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!a&&(!!i(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:a?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:a?null!=(o=null==t?void 0:t.scrollableSegments)?o:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:i(t.patchedTree)?t.patchedTree:e.tree,nextUrl:s}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59656:(e,t,n)=>{n.r(t),n.d(t,{_:()=>i});var r=0;function i(e){return"__private_"+r+++"_"+e}},61794:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return o}});let r=n(79289),i=n(26736);function o(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,i.hasBasePath)(n.pathname)}catch(e){return!1}}},62157:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},62688:(e,t,n)=>{n.d(t,{A:()=>d});var r=n(43210);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),a=e=>{let t=o(e);return t.charAt(0).toUpperCase()+t.slice(1)},s=(...e)=>e.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:i,className:o="",children:a,iconNode:c,...d},h)=>(0,r.createElement)("svg",{ref:h,...u,width:t,height:t,stroke:e,strokeWidth:i?24*Number(n)/Number(t):n,className:s("lucide",o),...!a&&!l(d)&&{"aria-hidden":"true"},...d},[...c.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(a)?a:[a]])),d=(e,t)=>{let n=(0,r.forwardRef)(({className:n,...o},l)=>(0,r.createElement)(c,{ref:l,iconNode:t,className:s(`lucide-${i(a(e))}`,`lucide-${e}`,n),...o}));return n.displayName=a(e),n}},63690:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createMutableActionQueue:function(){return p},dispatchNavigateAction:function(){return y},dispatchTraverseAction:function(){return v},getCurrentAppRouterState:function(){return m},publicAppRouterInstance:function(){return b}});let r=n(59154),i=n(8830),o=n(43210),a=n(91992);n(50593);let s=n(19129),l=n(96127),u=n(89752),c=n(75076),d=n(73406);function h(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?f({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:r.ACTION_REFRESH,origin:window.location.origin},t)))}async function f(e){let{actionQueue:t,action:n,setState:r}=e,i=t.state;t.pending=n;let o=n.payload,s=t.action(i,o);function l(e){n.discarded||(t.state=e,h(t,r),n.resolve(e))}(0,a.isThenable)(s)?s.then(l,e=>{h(t,r),n.reject(e)}):l(s)}function p(e,t){let n={state:e,dispatch:(e,t)=>(function(e,t,n){let i={resolve:n,reject:()=>{}};if(t.type!==r.ACTION_RESTORE){let e=new Promise((e,t)=>{i={resolve:e,reject:t}});(0,o.startTransition)(()=>{n(e)})}let a={payload:t,next:null,resolve:i.resolve,reject:i.reject};null===e.pending?(e.last=a,f({actionQueue:e,action:a,setState:n})):t.type===r.ACTION_NAVIGATE||t.type===r.ACTION_RESTORE?(e.pending.discarded=!0,a.next=e.pending.next,e.pending.payload.type===r.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),f({actionQueue:e,action:a,setState:n})):(null!==e.last&&(e.last.next=a),e.last=a)})(n,e,t),action:async(e,t)=>(0,i.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};return n}function m(){return null}function g(){return null}function y(e,t,n,i){let o=new URL((0,l.addBasePath)(e),location.href);(0,d.setLinkForCurrentNavigation)(i);(0,s.dispatchAppRouterAction)({type:r.ACTION_NAVIGATE,url:o,isExternalUrl:(0,u.isExternalURL)(o),locationSearch:location.search,shouldScroll:n,navigateType:t,allowAliasing:!0})}function v(e,t){(0,s.dispatchAppRouterAction)({type:r.ACTION_RESTORE,url:new URL(e),tree:t})}let b={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let n=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),i=(0,u.createPrefetchURL)(e);if(null!==i){var o;(0,c.prefetchReducer)(n.state,{type:r.ACTION_PREFETCH,url:i,kind:null!=(o=null==t?void 0:t.kind)?o:r.PrefetchKind.FULL})}},replace:(e,t)=>{(0,o.startTransition)(()=>{var n;y(e,"replace",null==(n=null==t?void 0:t.scroll)||n,null)})},push:(e,t)=>{(0,o.startTransition)(()=>{var n;y(e,"push",null==(n=null==t?void 0:t.scroll)||n,null)})},refresh:()=>{(0,o.startTransition)(()=>{(0,s.dispatchAppRouterAction)({type:r.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65951:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,n){let[o,a]=n,[s,l]=t;return(0,i.matchSegment)(s,o)?!(t.length<=2)&&e((0,r.getNextFlightSegmentPath)(t),a[l]):!!Array.isArray(s)}}});let r=n(74007),i=n(14077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65956:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{abortTask:function(){return p},listenForDynamicRequest:function(){return f},startPPRNavigation:function(){return u},updateCacheNodeOnPopstateRestoration:function(){return function e(t,n){let r=n[1],i=t.parallelRoutes,a=new Map(i);for(let t in r){let n=r[t],s=n[0],l=(0,o.createRouterCacheKey)(s),u=i.get(t);if(void 0!==u){let r=u.get(l);if(void 0!==r){let i=e(r,n),o=new Map(u);o.set(l,i),a.set(t,o)}}}let s=t.rsc,l=y(s)&&"pending"===s.status;return{lazyData:null,rsc:s,head:t.head,prefetchHead:l?t.prefetchHead:[null,null],prefetchRsc:l?t.prefetchRsc:null,loading:t.loading,parallelRoutes:a,navigatedAt:t.navigatedAt}}}});let r=n(83913),i=n(14077),o=n(33123),a=n(2030),s=n(5334),l={route:null,node:null,dynamicRequestTree:null,children:null};function u(e,t,n,a,s,u,h,f,p){return function e(t,n,a,s,u,h,f,p,m,g,y){let v=a[1],b=s[1],w=null!==h?h[2]:null;u||!0===s[4]&&(u=!0);let x=n.parallelRoutes,P=new Map(x),E={},T=null,R=!1,S={};for(let n in b){let a,s=b[n],d=v[n],h=x.get(n),M=null!==w?w[n]:null,A=s[0],k=g.concat([n,A]),_=(0,o.createRouterCacheKey)(A),j=void 0!==d?d[0]:void 0,C=void 0!==h?h.get(_):void 0;if(null!==(a=A===r.DEFAULT_SEGMENT_KEY?void 0!==d?{route:d,node:null,dynamicRequestTree:null,children:null}:c(t,d,s,C,u,void 0!==M?M:null,f,p,k,y):m&&0===Object.keys(s[1]).length?c(t,d,s,C,u,void 0!==M?M:null,f,p,k,y):void 0!==d&&void 0!==j&&(0,i.matchSegment)(A,j)&&void 0!==C&&void 0!==d?e(t,C,d,s,u,M,f,p,m,k,y):c(t,d,s,C,u,void 0!==M?M:null,f,p,k,y))){if(null===a.route)return l;null===T&&(T=new Map),T.set(n,a);let e=a.node;if(null!==e){let t=new Map(h);t.set(_,e),P.set(n,t)}let t=a.route;E[n]=t;let r=a.dynamicRequestTree;null!==r?(R=!0,S[n]=r):S[n]=t}else E[n]=s,S[n]=s}if(null===T)return null;let M={lazyData:null,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,loading:n.loading,parallelRoutes:P,navigatedAt:t};return{route:d(s,E),node:M,dynamicRequestTree:R?d(s,S):null,children:T}}(e,t,n,a,!1,s,u,h,f,[],p)}function c(e,t,n,r,i,u,c,f,p,m){return!i&&(void 0===t||(0,a.isNavigatingToNewRootLayout)(t,n))?l:function e(t,n,r,i,a,l,u,c){let f,p,m,g,y=n[1],v=0===Object.keys(y).length;if(void 0!==r&&r.navigatedAt+s.DYNAMIC_STALETIME_MS>t)f=r.rsc,p=r.loading,m=r.head,g=r.navigatedAt;else if(null===i)return h(t,n,null,a,l,u,c);else if(f=i[1],p=i[3],m=v?a:null,g=t,i[4]||l&&v)return h(t,n,i,a,l,u,c);let b=null!==i?i[2]:null,w=new Map,x=void 0!==r?r.parallelRoutes:null,P=new Map(x),E={},T=!1;if(v)c.push(u);else for(let n in y){let r=y[n],i=null!==b?b[n]:null,s=null!==x?x.get(n):void 0,d=r[0],h=u.concat([n,d]),f=(0,o.createRouterCacheKey)(d),p=e(t,r,void 0!==s?s.get(f):void 0,i,a,l,h,c);w.set(n,p);let m=p.dynamicRequestTree;null!==m?(T=!0,E[n]=m):E[n]=r;let g=p.node;if(null!==g){let e=new Map;e.set(f,g),P.set(n,e)}}return{route:n,node:{lazyData:null,rsc:f,prefetchRsc:null,head:m,prefetchHead:null,loading:p,parallelRoutes:P,navigatedAt:g},dynamicRequestTree:T?d(n,E):null,children:w}}(e,n,r,u,c,f,p,m)}function d(e,t){let n=[e[0],t];return 2 in e&&(n[2]=e[2]),3 in e&&(n[3]=e[3]),4 in e&&(n[4]=e[4]),n}function h(e,t,n,r,i,a,s){let l=d(t,t[1]);return l[3]="refetch",{route:t,node:function e(t,n,r,i,a,s,l){let u=n[1],c=null!==r?r[2]:null,d=new Map;for(let n in u){let r=u[n],h=null!==c?c[n]:null,f=r[0],p=s.concat([n,f]),m=(0,o.createRouterCacheKey)(f),g=e(t,r,void 0===h?null:h,i,a,p,l),y=new Map;y.set(m,g),d.set(n,y)}let h=0===d.size;h&&l.push(s);let f=null!==r?r[1]:null,p=null!==r?r[3]:null;return{lazyData:null,parallelRoutes:d,prefetchRsc:void 0!==f?f:null,prefetchHead:h?i:[null,null],loading:void 0!==p?p:null,rsc:v(),head:h?v():null,navigatedAt:t}}(e,t,n,r,i,a,s),dynamicRequestTree:l,children:null}}function f(e,t){t.then(t=>{let{flightData:n}=t;if("string"!=typeof n){for(let t of n){let{segmentPath:n,tree:r,seedData:a,head:s}=t;a&&function(e,t,n,r,a){let s=e;for(let e=0;e<t.length;e+=2){let n=t[e],r=t[e+1],o=s.children;if(null!==o){let e=o.get(n);if(void 0!==e){let t=e.route[0];if((0,i.matchSegment)(r,t)){s=e;continue}}}return}!function e(t,n,r,a){if(null===t.dynamicRequestTree)return;let s=t.children,l=t.node;if(null===s){null!==l&&(function e(t,n,r,a,s){let l=n[1],u=r[1],c=a[2],d=t.parallelRoutes;for(let t in l){let n=l[t],r=u[t],a=c[t],h=d.get(t),f=n[0],p=(0,o.createRouterCacheKey)(f),g=void 0!==h?h.get(p):void 0;void 0!==g&&(void 0!==r&&(0,i.matchSegment)(f,r[0])&&null!=a?e(g,n,r,a,s):m(n,g,null))}let h=t.rsc,f=a[1];null===h?t.rsc=f:y(h)&&h.resolve(f);let p=t.head;y(p)&&p.resolve(s)}(l,t.route,n,r,a),t.dynamicRequestTree=null);return}let u=n[1],c=r[2];for(let t in n){let n=u[t],r=c[t],o=s.get(t);if(void 0!==o){let t=o.route[0];if((0,i.matchSegment)(n[0],t)&&null!=r)return e(o,n,r,a)}}}(s,n,r,a)}(e,n,r,a,s)}p(e,null)}},t=>{p(e,t)})}function p(e,t){let n=e.node;if(null===n)return;let r=e.children;if(null===r)m(e.route,n,t);else for(let e of r.values())p(e,t);e.dynamicRequestTree=null}function m(e,t,n){let r=e[1],i=t.parallelRoutes;for(let e in r){let t=r[e],a=i.get(e);if(void 0===a)continue;let s=t[0],l=(0,o.createRouterCacheKey)(s),u=a.get(l);void 0!==u&&m(t,u,n)}let a=t.rsc;y(a)&&(null===n?a.resolve(null):a.reject(n));let s=t.head;y(s)&&s.resolve(null)}let g=Symbol();function y(e){return e&&e.tag===g}function v(){let e,t,n=new Promise((n,r)=>{e=n,t=r});return n.status="pending",n.resolve=t=>{"pending"===n.status&&(n.status="fulfilled",n.value=t,e(t))},n.reject=e=>{"pending"===n.status&&(n.status="rejected",n.reason=e,t(e))},n.tag=g,n}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},66156:(e,t,n)=>{n.d(t,{N:()=>i});var r=n(43210),i=globalThis?.document?r.useLayoutEffect:()=>{}},67760:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},70642:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{computeChangedPath:function(){return c},extractPathFromFlightRouterState:function(){return u},getSelectedParams:function(){return function e(t,n){for(let r of(void 0===n&&(n={}),Object.values(t[1]))){let t=r[0],o=Array.isArray(t),a=o?t[1]:t;!a||a.startsWith(i.PAGE_SEGMENT_KEY)||(o&&("c"===t[2]||"oc"===t[2])?n[t[0]]=t[1].split("/"):o&&(n[t[0]]=t[1]),n=e(r,n))}return n}}});let r=n(72859),i=n(83913),o=n(14077),a=e=>"/"===e[0]?e.slice(1):e,s=e=>"string"==typeof e?"children"===e?"":e:e[1];function l(e){return e.reduce((e,t)=>""===(t=a(t))||(0,i.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function u(e){var t;let n=Array.isArray(e[0])?e[0][1]:e[0];if(n===i.DEFAULT_SEGMENT_KEY||r.INTERCEPTION_ROUTE_MARKERS.some(e=>n.startsWith(e)))return;if(n.startsWith(i.PAGE_SEGMENT_KEY))return"";let o=[s(n)],a=null!=(t=e[1])?t:{},c=a.children?u(a.children):void 0;if(void 0!==c)o.push(c);else for(let[e,t]of Object.entries(a)){if("children"===e)continue;let n=u(t);void 0!==n&&o.push(n)}return l(o)}function c(e,t){let n=function e(t,n){let[i,a]=t,[l,c]=n,d=s(i),h=s(l);if(r.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)||h.startsWith(e)))return"";if(!(0,o.matchSegment)(i,l)){var f;return null!=(f=u(n))?f:""}for(let t in a)if(c[t]){let n=e(a[t],c[t]);if(null!==n)return s(l)+"/"+n}return null}(e,t);return null==n||"/"===n?n:l(n.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},72575:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},72789:(e,t,n)=>{n.d(t,{M:()=>i});var r=n(43210);function i(e){let t=(0,r.useRef)(null);return null===t.current&&(t.current=e()),t.current}},73406:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{IDLE_LINK_STATUS:function(){return u},PENDING_LINK_STATUS:function(){return l},mountFormInstance:function(){return v},mountLinkInstance:function(){return y},onLinkVisibilityChanged:function(){return w},onNavigationIntent:function(){return x},pingVisibleLinks:function(){return E},setLinkForCurrentNavigation:function(){return c},unmountLinkForCurrentNavigation:function(){return d},unmountPrefetchableInstance:function(){return b}}),n(63690);let r=n(89752),i=n(59154),o=n(50593),a=n(43210),s=null,l={pending:!0},u={pending:!1};function c(e){(0,a.startTransition)(()=>{null==s||s.setOptimisticLinkStatus(u),null==e||e.setOptimisticLinkStatus(l),s=e})}function d(e){s===e&&(s=null)}let h="function"==typeof WeakMap?new WeakMap:new Map,f=new Set,p="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;w(t.target,e)}},{rootMargin:"200px"}):null;function m(e,t){void 0!==h.get(e)&&b(e),h.set(e,t),null!==p&&p.observe(e)}function g(e){try{return(0,r.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function y(e,t,n,r,i,o){if(i){let i=g(t);if(null!==i){let t={router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:i.href,setOptimisticLinkStatus:o};return m(e,t),t}}return{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:o}}function v(e,t,n,r){let i=g(t);null!==i&&m(e,{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:i.href,setOptimisticLinkStatus:null})}function b(e){let t=h.get(e);if(void 0!==t){h.delete(e),f.delete(t);let n=t.prefetchTask;null!==n&&(0,o.cancelPrefetchTask)(n)}null!==p&&p.unobserve(e)}function w(e,t){let n=h.get(e);void 0!==n&&(n.isVisible=t,t?f.add(n):f.delete(n),P(n))}function x(e,t){let n=h.get(e);void 0!==n&&void 0!==n&&(n.wasHoveredOrTouched=!0,P(n))}function P(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,o.cancelPrefetchTask)(t);return}}function E(e,t){let n=(0,o.getCurrentCacheVersion)();for(let r of f){let a=r.prefetchTask;if(null!==a&&r.cacheVersion===n&&a.key.nextUrl===e&&a.treeAtTimeOfPrefetch===t)continue;null!==a&&(0,o.cancelPrefetchTask)(a);let s=(0,o.createCacheKey)(r.prefetchHref,e),l=r.wasHoveredOrTouched?o.PrefetchPriority.Intent:o.PrefetchPriority.Default;r.prefetchTask=(0,o.schedulePrefetchTask)(s,t,r.kind===i.PrefetchKind.FULL,l),r.cacheVersion=(0,o.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74479:(e,t,n)=>{n.d(t,{G:()=>r});function r(e){return"object"==typeof e&&null!==e}},75076:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{prefetchQueue:function(){return o},prefetchReducer:function(){return a}});let r=n(5144),i=n(5334),o=new r.PromiseQueue(5),a=function(e,t){(0,i.prunePrefetchCache)(e.prefetchCache);let{url:n}=t;return(0,i.getOrCreatePrefetchCacheEntry)({url:n,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},76715:(e,t)=>{function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[n,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(n,r(e));else t.set(n,r(i));return t}function o(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return o},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return i}})},77022:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return a}});let r=n(43210),i=n(51215),o="next-route-announcer";function a(e){let{tree:t}=e,[n,a]=(0,r.useState)(null);(0,r.useEffect)(()=>(a(function(){var e;let t=document.getElementsByName(o)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(o);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(o)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[s,l]=(0,r.useState)(""),u=(0,r.useRef)(void 0);return(0,r.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==u.current&&u.current!==e&&l(e),u.current=e},[t]),n?(0,i.createPortal)(s,n):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},78866:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return p}});let r=n(59008),i=n(57391),o=n(86770),a=n(2030),s=n(25232),l=n(59435),u=n(41500),c=n(89752),d=n(96493),h=n(68214),f=n(22308);function p(e,t){let{origin:n}=t,p={},m=e.canonicalUrl,g=e.tree;p.preserveCustomHistoryState=!1;let y=(0,c.createEmptyCacheNode)(),v=(0,h.hasInterceptionRouteInCurrentTree)(e.tree);y.lazyData=(0,r.fetchServerResponse)(new URL(m,n),{flightRouterState:[g[0],g[1],g[2],"refetch"],nextUrl:v?e.nextUrl:null});let b=Date.now();return y.lazyData.then(async n=>{let{flightData:r,canonicalUrl:c}=n;if("string"==typeof r)return(0,s.handleExternalUrl)(e,p,r,e.pushRef.pendingPush);for(let n of(y.lazyData=null,r)){let{tree:r,seedData:l,head:h,isRootRender:w}=n;if(!w)return console.log("REFRESH FAILED"),e;let x=(0,o.applyRouterStatePatchToTree)([""],g,r,e.canonicalUrl);if(null===x)return(0,d.handleSegmentMismatch)(e,t,r);if((0,a.isNavigatingToNewRootLayout)(g,x))return(0,s.handleExternalUrl)(e,p,m,e.pushRef.pendingPush);let P=c?(0,i.createHrefFromUrl)(c):void 0;if(c&&(p.canonicalUrl=P),null!==l){let e=l[1],t=l[3];y.rsc=e,y.prefetchRsc=null,y.loading=t,(0,u.fillLazyItemsTillLeafWithHead)(b,y,void 0,r,l,h,void 0),p.prefetchCache=new Map}await (0,f.refreshInactiveParallelSegments)({navigatedAt:b,state:e,updatedTree:x,updatedCache:y,includeNextUrl:v,canonicalUrl:p.canonicalUrl||e.canonicalUrl}),p.cache=y,p.patchedTree=x,g=x}return(0,l.handleMutable)(e,p)},()=>e)}n(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79289:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return h},ST:function(){return f},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return s},isAbsoluteUrl:function(){return o},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,i=Array(r),o=0;o<r;o++)i[o]=arguments[o];return n||(n=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>i.test(e);function a(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function s(){let{href:e}=window.location,t=a();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&u(n))return r;if(!r)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let h="undefined"!=typeof performance,f=h&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},80375:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]])},82348:(e,t,n)=>{n.d(t,{QP:()=>eu});let r=e=>{let t=s(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:e=>{let n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),i(n,t)||a(e)},getConflictingClassGroupIds:(e,t)=>{let i=n[e]||[];return t&&r[e]?[...i,...r[e]]:i}}},i=(e,t)=>{if(0===e.length)return t.classGroupId;let n=e[0],r=t.nextPart.get(n),o=r?i(e.slice(1),r):void 0;if(o)return o;if(0===t.validators.length)return;let a=e.join("-");return t.validators.find(({validator:e})=>e(a))?.classGroupId},o=/^\[(.+)\]$/,a=e=>{if(o.test(e)){let t=o.exec(e)[1],n=t?.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},s=e=>{let{theme:t,classGroups:n}=e,r={nextPart:new Map,validators:[]};for(let e in n)l(n[e],r,e,t);return r},l=(e,t,n,r)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=n;return}if("function"==typeof e)return c(e)?void l(e(r),t,n,r):void t.validators.push({validator:e,classGroupId:n});Object.entries(e).forEach(([e,i])=>{l(i,u(t,e),n,r)})})},u=(e,t)=>{let n=e;return t.split("-").forEach(e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)}),n},c=e=>e.isThemeGetter,d=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,n=new Map,r=new Map,i=(i,o)=>{n.set(i,o),++t>e&&(t=0,r=n,n=new Map)};return{get(e){let t=n.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(i(e,t),t):void 0},set(e,t){n.has(e)?n.set(e,t):i(e,t)}}},h=e=>{let{prefix:t,experimentalParseClassName:n}=e,r=e=>{let t,n=[],r=0,i=0,o=0;for(let a=0;a<e.length;a++){let s=e[a];if(0===r&&0===i){if(":"===s){n.push(e.slice(o,a)),o=a+1;continue}if("/"===s){t=a;continue}}"["===s?r++:"]"===s?r--:"("===s?i++:")"===s&&i--}let a=0===n.length?e:e.substring(o),s=f(a);return{modifiers:n,hasImportantModifier:s!==a,baseClassName:s,maybePostfixModifierPosition:t&&t>o?t-o:void 0}};if(t){let e=t+":",n=r;r=t=>t.startsWith(e)?n(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(n){let e=r;r=t=>n({className:t,parseClassName:e})}return r},f=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,p=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let n=[],r=[];return e.forEach(e=>{"["===e[0]||t[e]?(n.push(...r.sort(),e),r=[]):r.push(e)}),n.push(...r.sort()),n}},m=e=>({cache:d(e.cacheSize),parseClassName:h(e),sortModifiers:p(e),...r(e)}),g=/\s+/,y=(e,t)=>{let{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:i,sortModifiers:o}=t,a=[],s=e.trim().split(g),l="";for(let e=s.length-1;e>=0;e-=1){let t=s[e],{isExternal:u,modifiers:c,hasImportantModifier:d,baseClassName:h,maybePostfixModifierPosition:f}=n(t);if(u){l=t+(l.length>0?" "+l:l);continue}let p=!!f,m=r(p?h.substring(0,f):h);if(!m){if(!p||!(m=r(h))){l=t+(l.length>0?" "+l:l);continue}p=!1}let g=o(c).join(":"),y=d?g+"!":g,v=y+m;if(a.includes(v))continue;a.push(v);let b=i(m,p);for(let e=0;e<b.length;++e){let t=b[e];a.push(y+t)}l=t+(l.length>0?" "+l:l)}return l};function v(){let e,t,n=0,r="";for(;n<arguments.length;)(e=arguments[n++])&&(t=b(e))&&(r&&(r+=" "),r+=t);return r}let b=e=>{let t;if("string"==typeof e)return e;let n="";for(let r=0;r<e.length;r++)e[r]&&(t=b(e[r]))&&(n&&(n+=" "),n+=t);return n},w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},x=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,P=/^\((?:(\w[\w-]*):)?(.+)\)$/i,E=/^\d+\/\d+$/,T=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,R=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,S=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,M=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,A=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,k=e=>E.test(e),_=e=>!!e&&!Number.isNaN(Number(e)),j=e=>!!e&&Number.isInteger(Number(e)),C=e=>e.endsWith("%")&&_(e.slice(0,-1)),O=e=>T.test(e),D=()=>!0,L=e=>R.test(e)&&!S.test(e),N=()=>!1,I=e=>M.test(e),V=e=>A.test(e),U=e=>!B(e)&&!G(e),F=e=>ee(e,ei,N),B=e=>x.test(e),z=e=>ee(e,eo,L),H=e=>ee(e,ea,_),W=e=>ee(e,en,N),$=e=>ee(e,er,V),K=e=>ee(e,el,I),G=e=>P.test(e),Y=e=>et(e,eo),q=e=>et(e,es),X=e=>et(e,en),Z=e=>et(e,ei),Q=e=>et(e,er),J=e=>et(e,el,!0),ee=(e,t,n)=>{let r=x.exec(e);return!!r&&(r[1]?t(r[1]):n(r[2]))},et=(e,t,n=!1)=>{let r=P.exec(e);return!!r&&(r[1]?t(r[1]):n)},en=e=>"position"===e||"percentage"===e,er=e=>"image"===e||"url"===e,ei=e=>"length"===e||"size"===e||"bg-size"===e,eo=e=>"length"===e,ea=e=>"number"===e,es=e=>"family-name"===e,el=e=>"shadow"===e;Symbol.toStringTag;let eu=function(e,...t){let n,r,i,o=function(s){return r=(n=m(t.reduce((e,t)=>t(e),e()))).cache.get,i=n.cache.set,o=a,a(s)};function a(e){let t=r(e);if(t)return t;let o=y(e,n);return i(e,o),o}return function(){return o(v.apply(null,arguments))}}(()=>{let e=w("color"),t=w("font"),n=w("text"),r=w("font-weight"),i=w("tracking"),o=w("leading"),a=w("breakpoint"),s=w("container"),l=w("spacing"),u=w("radius"),c=w("shadow"),d=w("inset-shadow"),h=w("text-shadow"),f=w("drop-shadow"),p=w("blur"),m=w("perspective"),g=w("aspect"),y=w("ease"),v=w("animate"),b=()=>["auto","avoid","all","avoid-page","page","left","right","column"],x=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],P=()=>[...x(),G,B],E=()=>["auto","hidden","clip","visible","scroll"],T=()=>["auto","contain","none"],R=()=>[G,B,l],S=()=>[k,"full","auto",...R()],M=()=>[j,"none","subgrid",G,B],A=()=>["auto",{span:["full",j,G,B]},j,G,B],L=()=>[j,"auto",G,B],N=()=>["auto","min","max","fr",G,B],I=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],V=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...R()],et=()=>[k,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...R()],en=()=>[e,G,B],er=()=>[...x(),X,W,{position:[G,B]}],ei=()=>["no-repeat",{repeat:["","x","y","space","round"]}],eo=()=>["auto","cover","contain",Z,F,{size:[G,B]}],ea=()=>[C,Y,z],es=()=>["","none","full",u,G,B],el=()=>["",_,Y,z],eu=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ed=()=>[_,C,X,W],eh=()=>["","none",p,G,B],ef=()=>["none",_,G,B],ep=()=>["none",_,G,B],em=()=>[_,G,B],eg=()=>[k,"full",...R()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[O],breakpoint:[O],color:[D],container:[O],"drop-shadow":[O],ease:["in","out","in-out"],font:[U],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[O],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[O],shadow:[O],spacing:["px",_],text:[O],"text-shadow":[O],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",k,B,G,g]}],container:["container"],columns:[{columns:[_,B,G,s]}],"break-after":[{"break-after":b()}],"break-before":[{"break-before":b()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:P()}],overflow:[{overflow:E()}],"overflow-x":[{"overflow-x":E()}],"overflow-y":[{"overflow-y":E()}],overscroll:[{overscroll:T()}],"overscroll-x":[{"overscroll-x":T()}],"overscroll-y":[{"overscroll-y":T()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:S()}],"inset-x":[{"inset-x":S()}],"inset-y":[{"inset-y":S()}],start:[{start:S()}],end:[{end:S()}],top:[{top:S()}],right:[{right:S()}],bottom:[{bottom:S()}],left:[{left:S()}],visibility:["visible","invisible","collapse"],z:[{z:[j,"auto",G,B]}],basis:[{basis:[k,"full","auto",s,...R()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[_,k,"auto","initial","none",B]}],grow:[{grow:["",_,G,B]}],shrink:[{shrink:["",_,G,B]}],order:[{order:[j,"first","last","none",G,B]}],"grid-cols":[{"grid-cols":M()}],"col-start-end":[{col:A()}],"col-start":[{"col-start":L()}],"col-end":[{"col-end":L()}],"grid-rows":[{"grid-rows":M()}],"row-start-end":[{row:A()}],"row-start":[{"row-start":L()}],"row-end":[{"row-end":L()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":N()}],"auto-rows":[{"auto-rows":N()}],gap:[{gap:R()}],"gap-x":[{"gap-x":R()}],"gap-y":[{"gap-y":R()}],"justify-content":[{justify:[...I(),"normal"]}],"justify-items":[{"justify-items":[...V(),"normal"]}],"justify-self":[{"justify-self":["auto",...V()]}],"align-content":[{content:["normal",...I()]}],"align-items":[{items:[...V(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...V(),{baseline:["","last"]}]}],"place-content":[{"place-content":I()}],"place-items":[{"place-items":[...V(),"baseline"]}],"place-self":[{"place-self":["auto",...V()]}],p:[{p:R()}],px:[{px:R()}],py:[{py:R()}],ps:[{ps:R()}],pe:[{pe:R()}],pt:[{pt:R()}],pr:[{pr:R()}],pb:[{pb:R()}],pl:[{pl:R()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":R()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":R()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[s,"screen",...et()]}],"min-w":[{"min-w":[s,"screen","none",...et()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[a]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",n,Y,z]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,G,H]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",C,B]}],"font-family":[{font:[q,B,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[i,G,B]}],"line-clamp":[{"line-clamp":[_,"none",G,H]}],leading:[{leading:[o,...R()]}],"list-image":[{"list-image":["none",G,B]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",G,B]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:en()}],"text-color":[{text:en()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...eu(),"wavy"]}],"text-decoration-thickness":[{decoration:[_,"from-font","auto",G,z]}],"text-decoration-color":[{decoration:en()}],"underline-offset":[{"underline-offset":[_,"auto",G,B]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:R()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",G,B]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",G,B]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:er()}],"bg-repeat":[{bg:ei()}],"bg-size":[{bg:eo()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},j,G,B],radial:["",G,B],conic:[j,G,B]},Q,$]}],"bg-color":[{bg:en()}],"gradient-from-pos":[{from:ea()}],"gradient-via-pos":[{via:ea()}],"gradient-to-pos":[{to:ea()}],"gradient-from":[{from:en()}],"gradient-via":[{via:en()}],"gradient-to":[{to:en()}],rounded:[{rounded:es()}],"rounded-s":[{"rounded-s":es()}],"rounded-e":[{"rounded-e":es()}],"rounded-t":[{"rounded-t":es()}],"rounded-r":[{"rounded-r":es()}],"rounded-b":[{"rounded-b":es()}],"rounded-l":[{"rounded-l":es()}],"rounded-ss":[{"rounded-ss":es()}],"rounded-se":[{"rounded-se":es()}],"rounded-ee":[{"rounded-ee":es()}],"rounded-es":[{"rounded-es":es()}],"rounded-tl":[{"rounded-tl":es()}],"rounded-tr":[{"rounded-tr":es()}],"rounded-br":[{"rounded-br":es()}],"rounded-bl":[{"rounded-bl":es()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...eu(),"hidden","none"]}],"divide-style":[{divide:[...eu(),"hidden","none"]}],"border-color":[{border:en()}],"border-color-x":[{"border-x":en()}],"border-color-y":[{"border-y":en()}],"border-color-s":[{"border-s":en()}],"border-color-e":[{"border-e":en()}],"border-color-t":[{"border-t":en()}],"border-color-r":[{"border-r":en()}],"border-color-b":[{"border-b":en()}],"border-color-l":[{"border-l":en()}],"divide-color":[{divide:en()}],"outline-style":[{outline:[...eu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[_,G,B]}],"outline-w":[{outline:["",_,Y,z]}],"outline-color":[{outline:en()}],shadow:[{shadow:["","none",c,J,K]}],"shadow-color":[{shadow:en()}],"inset-shadow":[{"inset-shadow":["none",d,J,K]}],"inset-shadow-color":[{"inset-shadow":en()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:en()}],"ring-offset-w":[{"ring-offset":[_,z]}],"ring-offset-color":[{"ring-offset":en()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":en()}],"text-shadow":[{"text-shadow":["none",h,J,K]}],"text-shadow-color":[{"text-shadow":en()}],opacity:[{opacity:[_,G,B]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[_]}],"mask-image-linear-from-pos":[{"mask-linear-from":ed()}],"mask-image-linear-to-pos":[{"mask-linear-to":ed()}],"mask-image-linear-from-color":[{"mask-linear-from":en()}],"mask-image-linear-to-color":[{"mask-linear-to":en()}],"mask-image-t-from-pos":[{"mask-t-from":ed()}],"mask-image-t-to-pos":[{"mask-t-to":ed()}],"mask-image-t-from-color":[{"mask-t-from":en()}],"mask-image-t-to-color":[{"mask-t-to":en()}],"mask-image-r-from-pos":[{"mask-r-from":ed()}],"mask-image-r-to-pos":[{"mask-r-to":ed()}],"mask-image-r-from-color":[{"mask-r-from":en()}],"mask-image-r-to-color":[{"mask-r-to":en()}],"mask-image-b-from-pos":[{"mask-b-from":ed()}],"mask-image-b-to-pos":[{"mask-b-to":ed()}],"mask-image-b-from-color":[{"mask-b-from":en()}],"mask-image-b-to-color":[{"mask-b-to":en()}],"mask-image-l-from-pos":[{"mask-l-from":ed()}],"mask-image-l-to-pos":[{"mask-l-to":ed()}],"mask-image-l-from-color":[{"mask-l-from":en()}],"mask-image-l-to-color":[{"mask-l-to":en()}],"mask-image-x-from-pos":[{"mask-x-from":ed()}],"mask-image-x-to-pos":[{"mask-x-to":ed()}],"mask-image-x-from-color":[{"mask-x-from":en()}],"mask-image-x-to-color":[{"mask-x-to":en()}],"mask-image-y-from-pos":[{"mask-y-from":ed()}],"mask-image-y-to-pos":[{"mask-y-to":ed()}],"mask-image-y-from-color":[{"mask-y-from":en()}],"mask-image-y-to-color":[{"mask-y-to":en()}],"mask-image-radial":[{"mask-radial":[G,B]}],"mask-image-radial-from-pos":[{"mask-radial-from":ed()}],"mask-image-radial-to-pos":[{"mask-radial-to":ed()}],"mask-image-radial-from-color":[{"mask-radial-from":en()}],"mask-image-radial-to-color":[{"mask-radial-to":en()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":x()}],"mask-image-conic-pos":[{"mask-conic":[_]}],"mask-image-conic-from-pos":[{"mask-conic-from":ed()}],"mask-image-conic-to-pos":[{"mask-conic-to":ed()}],"mask-image-conic-from-color":[{"mask-conic-from":en()}],"mask-image-conic-to-color":[{"mask-conic-to":en()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:er()}],"mask-repeat":[{mask:ei()}],"mask-size":[{mask:eo()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",G,B]}],filter:[{filter:["","none",G,B]}],blur:[{blur:eh()}],brightness:[{brightness:[_,G,B]}],contrast:[{contrast:[_,G,B]}],"drop-shadow":[{"drop-shadow":["","none",f,J,K]}],"drop-shadow-color":[{"drop-shadow":en()}],grayscale:[{grayscale:["",_,G,B]}],"hue-rotate":[{"hue-rotate":[_,G,B]}],invert:[{invert:["",_,G,B]}],saturate:[{saturate:[_,G,B]}],sepia:[{sepia:["",_,G,B]}],"backdrop-filter":[{"backdrop-filter":["","none",G,B]}],"backdrop-blur":[{"backdrop-blur":eh()}],"backdrop-brightness":[{"backdrop-brightness":[_,G,B]}],"backdrop-contrast":[{"backdrop-contrast":[_,G,B]}],"backdrop-grayscale":[{"backdrop-grayscale":["",_,G,B]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[_,G,B]}],"backdrop-invert":[{"backdrop-invert":["",_,G,B]}],"backdrop-opacity":[{"backdrop-opacity":[_,G,B]}],"backdrop-saturate":[{"backdrop-saturate":[_,G,B]}],"backdrop-sepia":[{"backdrop-sepia":["",_,G,B]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":R()}],"border-spacing-x":[{"border-spacing-x":R()}],"border-spacing-y":[{"border-spacing-y":R()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",G,B]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[_,"initial",G,B]}],ease:[{ease:["linear","initial",y,G,B]}],delay:[{delay:[_,G,B]}],animate:[{animate:["none",v,G,B]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[m,G,B]}],"perspective-origin":[{"perspective-origin":P()}],rotate:[{rotate:ef()}],"rotate-x":[{"rotate-x":ef()}],"rotate-y":[{"rotate-y":ef()}],"rotate-z":[{"rotate-z":ef()}],scale:[{scale:ep()}],"scale-x":[{"scale-x":ep()}],"scale-y":[{"scale-y":ep()}],"scale-z":[{"scale-z":ep()}],"scale-3d":["scale-3d"],skew:[{skew:em()}],"skew-x":[{"skew-x":em()}],"skew-y":[{"skew-y":em()}],transform:[{transform:[G,B,"","none","gpu","cpu"]}],"transform-origin":[{origin:P()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:en()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:en()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",G,B]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":R()}],"scroll-mx":[{"scroll-mx":R()}],"scroll-my":[{"scroll-my":R()}],"scroll-ms":[{"scroll-ms":R()}],"scroll-me":[{"scroll-me":R()}],"scroll-mt":[{"scroll-mt":R()}],"scroll-mr":[{"scroll-mr":R()}],"scroll-mb":[{"scroll-mb":R()}],"scroll-ml":[{"scroll-ml":R()}],"scroll-p":[{"scroll-p":R()}],"scroll-px":[{"scroll-px":R()}],"scroll-py":[{"scroll-py":R()}],"scroll-ps":[{"scroll-ps":R()}],"scroll-pe":[{"scroll-pe":R()}],"scroll-pt":[{"scroll-pt":R()}],"scroll-pr":[{"scroll-pr":R()}],"scroll-pb":[{"scroll-pb":R()}],"scroll-pl":[{"scroll-pl":R()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",G,B]}],fill:[{fill:["none",...en()]}],"stroke-w":[{stroke:[_,Y,z,H]}],stroke:[{stroke:["none",...en()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},84949:(e,t)=>{function n(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return n}})},85814:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return g},useLinkStatus:function(){return v}});let r=n(40740),i=n(60687),o=r._(n(43210)),a=n(30195),s=n(22142),l=n(59154),u=n(53038),c=n(79289),d=n(96127);n(50148);let h=n(73406),f=n(61794),p=n(63690);function m(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}function g(e){let t,n,r,[a,g]=(0,o.useOptimistic)(h.IDLE_LINK_STATUS),v=(0,o.useRef)(null),{href:b,as:w,children:x,prefetch:P=null,passHref:E,replace:T,shallow:R,scroll:S,onClick:M,onMouseEnter:A,onTouchStart:k,legacyBehavior:_=!1,onNavigate:j,ref:C,unstable_dynamicOnHover:O,...D}=e;t=x,_&&("string"==typeof t||"number"==typeof t)&&(t=(0,i.jsx)("a",{children:t}));let L=o.default.useContext(s.AppRouterContext),N=!1!==P,I=null===P?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:V,as:U}=o.default.useMemo(()=>{let e=m(b);return{href:e,as:w?m(w):e}},[b,w]);_&&(n=o.default.Children.only(t));let F=_?n&&"object"==typeof n&&n.ref:C,B=o.default.useCallback(e=>(null!==L&&(v.current=(0,h.mountLinkInstance)(e,V,L,I,N,g)),()=>{v.current&&((0,h.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,h.unmountPrefetchableInstance)(e)}),[N,V,L,I,g]),z={ref:(0,u.useMergedRef)(B,F),onClick(e){_||"function"!=typeof M||M(e),_&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),L&&(e.defaultPrevented||function(e,t,n,r,i,a,s){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,f.isLocalURL)(t)){i&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),o.default.startTransition(()=>{if(s){let e=!1;if(s({preventDefault:()=>{e=!0}}),e)return}(0,p.dispatchNavigateAction)(n||t,i?"replace":"push",null==a||a,r.current)})}}(e,V,U,v,T,S,j))},onMouseEnter(e){_||"function"!=typeof A||A(e),_&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),L&&N&&(0,h.onNavigationIntent)(e.currentTarget,!0===O)},onTouchStart:function(e){_||"function"!=typeof k||k(e),_&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),L&&N&&(0,h.onNavigationIntent)(e.currentTarget,!0===O)}};return(0,c.isAbsoluteUrl)(U)?z.href=U:_&&!E&&("a"!==n.type||"href"in n.props)||(z.href=(0,d.addBasePath)(U)),r=_?o.default.cloneElement(n,z):(0,i.jsx)("a",{...D,...z,children:t}),(0,i.jsx)(y.Provider,{value:a,children:r})}n(32708);let y=(0,o.createContext)(h.IDLE_LINK_STATUS),v=()=>(0,o.useContext)(y);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86044:(e,t,n)=>{n.d(t,{xQ:()=>o});var r=n(43210),i=n(21279);function o(e=!0){let t=(0,r.useContext)(i.t);if(null===t)return[!0,null];let{isPresent:n,onExitComplete:a,register:s}=t,l=(0,r.useId)();(0,r.useEffect)(()=>{if(e)return s(l)},[e]);let u=(0,r.useCallback)(()=>e&&a&&a(l),[l,a,e]);return!n&&a?[!1,u]:[!0]}},86770:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,n,r,l){let u,[c,d,h,f,p]=n;if(1===t.length){let e=s(n,r);return(0,a.addRefreshMarkerToActiveParallelSegments)(e,l),e}let[m,g]=t;if(!(0,o.matchSegment)(m,c))return null;if(2===t.length)u=s(d[g],r);else if(null===(u=e((0,i.getNextFlightSegmentPath)(t),d[g],r,l)))return null;let y=[t[0],{...d,[g]:u},h,f];return p&&(y[4]=!0),(0,a.addRefreshMarkerToActiveParallelSegments)(y,l),y}}});let r=n(83913),i=n(74007),o=n(14077),a=n(22308);function s(e,t){let[n,i]=e,[a,l]=t;if(a===r.DEFAULT_SEGMENT_KEY&&n!==r.DEFAULT_SEGMENT_KEY)return e;if((0,o.matchSegment)(n,a)){let t={};for(let e in i)void 0!==l[e]?t[e]=s(i[e],l[e]):t[e]=i[e];for(let e in l)t[e]||(t[e]=l[e]);let r=[n,t];return e[2]&&(r[2]=e[2]),e[3]&&(r[3]=e[3]),e[4]&&(r[4]=e[4]),r}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88920:(e,t,n)=>{n.d(t,{N:()=>v});var r=n(60687),i=n(43210),o=n(12157),a=n(72789),s=n(15124),l=n(21279),u=n(18171),c=n(32582);class d extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=(0,u.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function h({children:e,isPresent:t,anchorX:n,root:o}){let a=(0,i.useId)(),s=(0,i.useRef)(null),l=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:u}=(0,i.useContext)(c.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:r,top:i,left:c,right:d}=l.current;if(t||!s.current||!e||!r)return;let h="left"===n?`left: ${c}`:`right: ${d}`;s.current.dataset.motionPopId=a;let f=document.createElement("style");u&&(f.nonce=u);let p=o??document.head;return p.appendChild(f),f.sheet&&f.sheet.insertRule(`
          [data-motion-pop-id="${a}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${r}px !important;
            ${h}px !important;
            top: ${i}px !important;
          }
        `),()=>{p.removeChild(f),p.contains(f)&&p.removeChild(f)}},[t]),(0,r.jsx)(d,{isPresent:t,childRef:s,sizeRef:l,children:i.cloneElement(e,{ref:s})})}let f=({children:e,initial:t,isPresent:n,onExitComplete:o,custom:s,presenceAffectsLayout:u,mode:c,anchorX:d,root:f})=>{let m=(0,a.M)(p),g=(0,i.useId)(),y=!0,v=(0,i.useMemo)(()=>(y=!1,{id:g,initial:t,isPresent:n,custom:s,onExitComplete:e=>{for(let t of(m.set(e,!0),m.values()))if(!t)return;o&&o()},register:e=>(m.set(e,!1),()=>m.delete(e))}),[n,m,o]);return u&&y&&(v={...v}),(0,i.useMemo)(()=>{m.forEach((e,t)=>m.set(t,!1))},[n]),i.useEffect(()=>{n||m.size||!o||o()},[n]),"popLayout"===c&&(e=(0,r.jsx)(h,{isPresent:n,anchorX:d,root:f,children:e})),(0,r.jsx)(l.t.Provider,{value:v,children:e})};function p(){return new Map}var m=n(86044);let g=e=>e.key||"";function y(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let v=({children:e,custom:t,initial:n=!0,onExitComplete:l,presenceAffectsLayout:u=!0,mode:c="sync",propagate:d=!1,anchorX:h="left",root:p})=>{let[v,b]=(0,m.xQ)(d),w=(0,i.useMemo)(()=>y(e),[e]),x=d&&!v?[]:w.map(g),P=(0,i.useRef)(!0),E=(0,i.useRef)(w),T=(0,a.M)(()=>new Map),[R,S]=(0,i.useState)(w),[M,A]=(0,i.useState)(w);(0,s.E)(()=>{P.current=!1,E.current=w;for(let e=0;e<M.length;e++){let t=g(M[e]);x.includes(t)?T.delete(t):!0!==T.get(t)&&T.set(t,!1)}},[M,x.length,x.join("-")]);let k=[];if(w!==R){let e=[...w];for(let t=0;t<M.length;t++){let n=M[t],r=g(n);x.includes(r)||(e.splice(t,0,n),k.push(n))}return"wait"===c&&k.length&&(e=k),A(y(e)),S(w),null}let{forceRender:_}=(0,i.useContext)(o.L);return(0,r.jsx)(r.Fragment,{children:M.map(e=>{let i=g(e),o=(!d||!!v)&&(w===M||x.includes(i));return(0,r.jsx)(f,{isPresent:o,initial:(!P.current||!!n)&&void 0,custom:t,presenceAffectsLayout:u,mode:c,root:p,onExitComplete:o?void 0:()=>{if(!T.has(i))return;T.set(i,!0);let e=!0;T.forEach(t=>{t||(e=!1)}),e&&(_?.(),A(E.current),d&&b?.(),l&&l())},anchorX:h,children:e},i)})})}},89752:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createEmptyCacheNode:function(){return k},createPrefetchURL:function(){return M},default:function(){return O},isExternalURL:function(){return S}});let r=n(40740),i=n(60687),o=r._(n(43210)),a=n(22142),s=n(59154),l=n(57391),u=n(10449),c=n(19129),d=r._(n(35656)),h=n(35416),f=n(96127),p=n(77022),m=n(67086),g=n(44397),y=n(89330),v=n(25942),b=n(26736),w=n(70642),x=n(12776),P=n(63690),E=n(36875),T=n(97860);n(73406);let R={};function S(e){return e.origin!==window.location.origin}function M(e){let t;if((0,h.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,f.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return S(t)?null:t}function A(e){let{appRouterState:t}=e;return(0,o.useInsertionEffect)(()=>{let{tree:e,pushRef:n,canonicalUrl:r}=t,i={...n.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};n.pendingPush&&(0,l.createHrefFromUrl)(new URL(window.location.href))!==r?(n.pendingPush=!1,window.history.pushState(i,"",r)):window.history.replaceState(i,"",r)},[t]),(0,o.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function k(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function _(e){null==e&&(e={});let t=window.history.state,n=null==t?void 0:t.__NA;n&&(e.__NA=n);let r=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return r&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=r),e}function j(e){let{headCacheNode:t}=e,n=null!==t?t.head:null,r=null!==t?t.prefetchHead:null,i=null!==r?r:n;return(0,o.useDeferredValue)(n,i)}function C(e){let t,{actionQueue:n,assetPrefix:r,globalError:l}=e,h=(0,c.useActionQueue)(n),{canonicalUrl:f}=h,{searchParams:x,pathname:S}=(0,o.useMemo)(()=>{let e=new URL(f,"http://n");return{searchParams:e.searchParams,pathname:(0,b.hasBasePath)(e.pathname)?(0,v.removeBasePath)(e.pathname):e.pathname}},[f]);(0,o.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(R.pendingMpaPath=void 0,(0,c.dispatchAppRouterAction)({type:s.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,o.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,T.isRedirectError)(t)){e.preventDefault();let n=(0,E.getURLFromRedirectError)(t);(0,E.getRedirectTypeFromError)(t)===T.RedirectType.push?P.publicAppRouterInstance.push(n,{}):P.publicAppRouterInstance.replace(n,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:M}=h;if(M.mpaNavigation){if(R.pendingMpaPath!==f){let e=window.location;M.pendingPush?e.assign(f):e.replace(f),R.pendingMpaPath=f}(0,o.use)(y.unresolvedThenable)}(0,o.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),n=e=>{var t;let n=window.location.href,r=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,o.startTransition)(()=>{(0,c.dispatchAppRouterAction)({type:s.ACTION_RESTORE,url:new URL(null!=e?e:n,n),tree:r})})};window.history.pushState=function(t,r,i){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=_(t),i&&n(i)),e(t,r,i)},window.history.replaceState=function(e,r,i){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=_(e),i&&n(i)),t(e,r,i)};let r=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,o.startTransition)(()=>{(0,P.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",r),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",r)}},[]);let{cache:k,tree:C,nextUrl:O,focusAndScrollRef:D}=h,L=(0,o.useMemo)(()=>(0,g.findHeadInCache)(k,C[1]),[k,C]),I=(0,o.useMemo)(()=>(0,w.getSelectedParams)(C),[C]),V=(0,o.useMemo)(()=>({parentTree:C,parentCacheNode:k,parentSegmentPath:null,url:f}),[C,k,f]),U=(0,o.useMemo)(()=>({tree:C,focusAndScrollRef:D,nextUrl:O}),[C,D,O]);if(null!==L){let[e,n]=L;t=(0,i.jsx)(j,{headCacheNode:e},n)}else t=null;let F=(0,i.jsxs)(m.RedirectBoundary,{children:[t,k.rsc,(0,i.jsx)(p.AppRouterAnnouncer,{tree:C})]});return F=(0,i.jsx)(d.ErrorBoundary,{errorComponent:l[0],errorStyles:l[1],children:F}),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(A,{appRouterState:h}),(0,i.jsx)(N,{}),(0,i.jsx)(u.PathParamsContext.Provider,{value:I,children:(0,i.jsx)(u.PathnameContext.Provider,{value:S,children:(0,i.jsx)(u.SearchParamsContext.Provider,{value:x,children:(0,i.jsx)(a.GlobalLayoutRouterContext.Provider,{value:U,children:(0,i.jsx)(a.AppRouterContext.Provider,{value:P.publicAppRouterInstance,children:(0,i.jsx)(a.LayoutRouterContext.Provider,{value:V,children:F})})})})})})]})}function O(e){let{actionQueue:t,globalErrorComponentAndStyles:[n,r],assetPrefix:o}=e;return(0,x.useNavFailureHandler)(),(0,i.jsx)(d.ErrorBoundary,{errorComponent:d.default,children:(0,i.jsx)(C,{actionQueue:t,assetPrefix:o,globalError:[n,r]})})}let D=new Set,L=new Set;function N(){let[,e]=o.default.useState(0),t=D.size;return(0,o.useEffect)(()=>{let n=()=>e(e=>e+1);return L.add(n),t!==D.size&&n(),()=>{L.delete(n)}},[t,e]),[...D].map((e,t)=>(0,i.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=D.size;return D.add(e),D.size!==t&&L.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95796:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return n}});let n=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},96127:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return o}});let r=n(98834),i=n(54674);function o(e,t){return(0,i.normalizePathTrailingSlash)((0,r.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96493:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return i}});let r=n(25232);function i(e,t,n){return(0,r.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97464:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,n,o){let a=o.length<=2,[s,l]=o,u=(0,i.createRouterCacheKey)(l),c=n.parallelRoutes.get(s),d=t.parallelRoutes.get(s);d&&d!==c||(d=new Map(c),t.parallelRoutes.set(s,d));let h=null==c?void 0:c.get(u),f=d.get(u);if(a){f&&f.lazyData&&f!==h||d.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!f||!h){f||d.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return f===h&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes),loading:f.loading},d.set(u,f)),e(f,h,(0,r.getNextFlightSegmentPath)(o))}}});let r=n(74007),i=n(33123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97936:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return r}}),n(59008),n(57391),n(86770),n(2030),n(25232),n(59435),n(56928),n(89752),n(96493),n(68214);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},98599:(e,t,n)=>{n.d(t,{s:()=>a,t:()=>o});var r=n(43210);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}function a(...e){return r.useCallback(o(...e),e)}},98834:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return i}});let r=n(19169);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:n,query:i,hash:o}=(0,r.parsePath)(e);return""+t+n+i+o}},98876:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},99270:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};