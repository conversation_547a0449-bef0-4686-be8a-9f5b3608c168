(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4631],{26126:(e,s,t)=>{"use strict";t.d(s,{E:()=>d});var a=t(95155);t(12115);var i=t(99708),r=t(74466),n=t(59434);let l=(0,r.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:s,variant:t,asChild:r=!1,...d}=e,c=r?i.DX:"span";return(0,a.jsx)(c,{"data-slot":"badge",className:(0,n.cn)(l({variant:t}),s),...d})}},29869:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},40968:(e,s,t)=>{"use strict";t.d(s,{b:()=>l});var a=t(12115),i=t(63655),r=t(95155),n=a.forwardRef((e,s)=>(0,r.jsx)(i.sG.label,{...e,ref:s,onMouseDown:s=>{var t;s.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var l=n},43007:(e,s,t)=>{Promise.resolve().then(t.bind(t,80685))},62523:(e,s,t)=>{"use strict";t.d(s,{p:()=>r});var a=t(95155);t(12115);var i=t(59434);function r(e){let{className:s,type:t,...r}=e;return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...r})}},80685:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var a=t(95155),i=t(12115),r=t(12108),n=t(83930),l=t(30285),d=t(66695),c=t(62523),o=t(85057),u=t(26126),x=t(19946);let m=(0,x.A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);var v=t(29869),h=t(72713);let g=(0,x.A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]),p=(0,x.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),f=(0,x.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var j=t(71007);let b=(0,x.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var y=t(49509);function N(){var e,s,t;let{data:x}=(0,r.useSession)(),[N,w]=(0,i.useState)(!1),k=[{title:"Database Configuration",description:"PostgreSQL database connection",icon:m,status:"connected",items:[{label:"Database URL",value:"postgresql://***@neon.tech/***",masked:!0},{label:"Connection Pool",value:"Active",status:"success"},{label:"Last Migration",value:"2025-07-01 07:42:52",status:"success"}]},{title:"Cloudinary Integration",description:"Image upload and management service",icon:v.A,status:"configured",items:[{label:"Cloud Name",value:y.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME||"Not configured"},{label:"Upload Preset",value:"portfolio",status:"success"},{label:"Storage Used",value:"0 MB / 25 GB",status:"success"}]},{title:"Google Analytics",description:"Website analytics and tracking",icon:h.A,status:"pending",items:[{label:"Property ID",value:"Not configured",status:"warning"},{label:"Tracking Status",value:"Inactive",status:"warning"},{label:"Data Collection",value:"Disabled",status:"warning"}]},{title:"Authentication",description:"User authentication and security",icon:g,status:"active",items:[{label:"NextAuth Secret",value:"Configured",status:"success"},{label:"Session Strategy",value:"JWT",status:"success"},{label:"Active Sessions",value:"1",status:"success"}]}],A=e=>{switch(e){case"success":case"connected":case"active":case"configured":return(0,a.jsx)(p,{className:"h-4 w-4 text-green-500"});case"warning":case"pending":return(0,a.jsx)(f,{className:"h-4 w-4 text-yellow-500"});default:return(0,a.jsx)(f,{className:"h-4 w-4 text-red-500"})}},C=e=>{switch(e){case"connected":case"active":case"configured":return(0,a.jsx)(u.E,{variant:"default",className:"bg-green-500",children:"Connected"});case"pending":return(0,a.jsx)(u.E,{variant:"secondary",children:"Pending Setup"});default:return(0,a.jsx)(u.E,{variant:"destructive",children:"Disconnected"})}};return(0,a.jsx)(n.DashboardLayout,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Settings"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage your CMS configuration and integrations"})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(j.A,{className:"h-5 w-5"}),"User Information"]}),(0,a.jsx)(d.BT,{children:"Your account details and preferences"})]}),(0,a.jsxs)(d.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{children:"Name"}),(0,a.jsx)(c.p,{value:(null==x||null==(e=x.user)?void 0:e.name)||"Admin User",disabled:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{children:"Email"}),(0,a.jsx)(c.p,{value:(null==x||null==(s=x.user)?void 0:s.email)||"<EMAIL>",disabled:!0})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{children:"Role"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)(u.E,{variant:"default",children:(null==x||null==(t=x.user)?void 0:t.role)||"ADMIN"})})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold tracking-tight",children:"System Configuration"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Overview of your CMS integrations and services"})]}),(0,a.jsx)("div",{className:"grid gap-6 md:grid-cols-2",children:k.map(e=>(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(e.icon,{className:"h-5 w-5"}),e.title]}),C(e.status)]}),(0,a.jsx)(d.BT,{children:e.description})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)("div",{className:"space-y-3",children:e.items.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[e.status&&A(e.status),(0,a.jsx)("span",{className:"text-sm font-medium",children:e.label})]}),(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"masked"in e&&e.masked?"••••••••••••":e.value})]},s))})})]},e.title))})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(b,{className:"h-5 w-5"}),"Environment Configuration"]}),(0,a.jsx)(d.BT,{children:"Required environment variables for CMS functionality"})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"DATABASE_URL"}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"PostgreSQL connection string"})]}),(0,a.jsx)(p,{className:"h-5 w-5 text-green-500"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"NEXTAUTH_SECRET"}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Authentication secret key"})]}),(0,a.jsx)(p,{className:"h-5 w-5 text-green-500"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"CLOUDINARY_*"}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Image upload configuration"})]}),(0,a.jsx)(f,{className:"h-5 w-5 text-yellow-500"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"GOOGLE_ANALYTICS_*"}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Analytics integration"})]}),(0,a.jsx)(f,{className:"h-5 w-5 text-yellow-500"})]})]})})})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsx)(d.ZB,{children:"System Actions"}),(0,a.jsx)(d.BT,{children:"Maintenance and administrative actions"})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(l.$,{variant:"outline",disabled:N,children:"Backup Database"}),(0,a.jsx)(l.$,{variant:"outline",disabled:N,children:"Clear Cache"}),(0,a.jsx)(l.$,{variant:"outline",disabled:N,children:"Export Data"})]})})]})]})})}},85057:(e,s,t)=>{"use strict";t.d(s,{J:()=>n});var a=t(95155);t(12115);var i=t(40968),r=t(59434);function n(e){let{className:s,...t}=e;return(0,a.jsx)(i.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...t})}}},e=>{var s=s=>e(e.s=s);e.O(0,[4854,2108,8858,9368,9008,8441,1684,7358],()=>s(43007)),_N_E=e.O()}]);