"use strict";exports.id=5089,exports.ids=[5089],exports.modules={15079:(e,t,s)=>{s.d(t,{bq:()=>u,eb:()=>x,gC:()=>h,l6:()=>d,yv:()=>c});var r=s(60687);s(43210);var a=s(97822),i=s(78272),n=s(13964),o=s(3589),l=s(4780);function d({...e}){return(0,r.jsx)(a.bL,{"data-slot":"select",...e})}function c({...e}){return(0,r.jsx)(a.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:s,...n}){return(0,r.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[s,(0,r.jsx)(a.In,{asChild:!0,children:(0,r.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function h({className:e,children:t,position:s="popper",...i}){return(0,r.jsx)(a.ZL,{children:(0,r.jsxs)(a.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...i,children:[(0,r.jsx)(p,{}),(0,r.jsx)(a.LM,{className:(0,l.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,r.jsx)(g,{})]})})}function x({className:e,children:t,...s}){return(0,r.jsxs)(a.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(n.A,{className:"size-4"})})}),(0,r.jsx)(a.p4,{children:t})]})}function p({className:e,...t}){return(0,r.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(o.A,{className:"size-4"})})}function g({className:e,...t}){return(0,r.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(i.A,{className:"size-4"})})}},19312:(e,t,s)=>{s.d(t,{B:()=>x});var r=s(60687),a=s(43210),i=s(29523),n=s(89667),o=s(80013),l=s(11860),d=s(9005),c=s(16023),u=s(30474),h=s(52581);function x({value:e,onChange:t,onRemove:s,disabled:x,label:p="Upload Image",className:g=""}){let[m,v]=(0,a.useState)(!1),f=(0,a.useRef)(null),b=async e=>{let s=e.target.files?.[0];if(s){v(!0);try{let e=new FormData;e.append("file",s);let r=await fetch("/api/upload",{method:"POST",body:e});if(!r.ok)throw Error("Upload failed");let a=await r.json();t(a.url)}catch(e){console.error("Error uploading image:",e),h.oR.error("Failed to upload image. Please try again.")}finally{v(!1)}}};return(0,r.jsxs)("div",{className:`space-y-2 ${g}`,children:[(0,r.jsx)(o.J,{children:p}),e?(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"relative w-full h-48 rounded-lg overflow-hidden border",children:(0,r.jsx)(u.default,{src:e,alt:"Uploaded image",fill:!0,className:"object-cover"})}),(0,r.jsx)(i.$,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2",onClick:s,disabled:x,children:(0,r.jsx)(l.A,{className:"h-4 w-4"})})]}):(0,r.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(d.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>{f.current?.click()},disabled:x||m,children:[(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4"}),m?"Uploading...":"Choose Image"]})}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"PNG, JPG, GIF up to 10MB"})]})}),(0,r.jsx)(n.p,{ref:f,type:"file",accept:"image/*",onChange:b,className:"hidden",disabled:x||m})]})}},34729:(e,t,s)=>{s.d(t,{T:()=>i});var r=s(60687);s(43210);var a=s(4780);function i({className:e,...t}){return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},56896:(e,t,s)=>{s.d(t,{S:()=>o});var r=s(60687);s(43210);var a=s(40211),i=s(13964),n=s(4780);function o({className:e,...t}){return(0,r.jsx)(a.bL,{"data-slot":"checkbox",className:(0,n.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,r.jsx)(a.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,r.jsx)(i.A,{className:"size-3.5"})})})}},65089:(e,t,s)=>{s.d(t,{BlogForm:()=>Y});var r=s(60687),a=s(43210),i=s(16189),n=s(29523),o=s(89667),l=s(80013),d=s(34729),c=s(56896),u=s(15079),h=s(44493),x=s(19312),p=s(96834),g=s(7203),m=s(39710),v=s(79801),f=s(2188),b=s(36293),j=s(55023),y=s(98926),w=s(21500),N=s(84963),k=s(65557),C=s(94157),A=s(84082),z=s(27235),T=s(62369),S=s(4780);function $({className:e,orientation:t="horizontal",decorative:s=!0,...a}){return(0,r.jsx)(T.b,{"data-slot":"separator",decorative:s,orientation:t,className:(0,S.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...a})}var F=s(27777),R=s(11922),B=s(75687),L=s(80375),P=s(45984),J=s(69169),U=s(21782),E=s(25366),q=s(14290),Z=s(98916),H=s(47342),I=s(9005),M=s(82164),W=s(27063),_=s(54388),D=s(31110);function O({content:e,onChange:t,className:s}){let i=(0,z.$)(),o=(0,g.hG)({extensions:[m.A.configure({codeBlock:!1}),v.Ay.configure({HTMLAttributes:{class:"max-w-full h-auto rounded-lg"}}),f.Ay.configure({openOnClick:!1,HTMLAttributes:{class:"text-primary underline hover:text-primary/80"}}),b.A,j.A,y.Ay.configure({multicolor:!0}),w.A.configure({lowlight:i,HTMLAttributes:{class:"bg-muted p-4 rounded-lg font-mono text-sm"}}),N.Ay.configure({resizable:!0}),k.A,A.h,C.A],content:e||"",onUpdate:({editor:e})=>{t(e.getHTML())},editorProps:{attributes:{class:(0,S.cn)("prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[300px] p-4","prose-headings:font-bold prose-headings:text-foreground","prose-p:text-muted-foreground prose-p:leading-relaxed","prose-a:text-primary prose-a:no-underline hover:prose-a:underline","prose-strong:text-foreground prose-strong:font-semibold","prose-code:bg-muted prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:text-sm","prose-pre:bg-muted prose-pre:border prose-pre:border-border","prose-blockquote:border-l-4 prose-blockquote:border-primary prose-blockquote:pl-4 prose-blockquote:italic","prose-ul:list-disc prose-ol:list-decimal","prose-li:text-muted-foreground","prose-table:border-collapse prose-table:border prose-table:border-border","prose-th:border prose-th:border-border prose-th:bg-muted prose-th:p-2 prose-th:font-semibold","prose-td:border prose-td:border-border prose-td:p-2",s)}}}),l=(0,a.useCallback)(()=>{let e=window.prompt("Enter image URL:");e&&o&&o.chain().focus().setImage({src:e}).run()},[o]),d=(0,a.useCallback)(()=>{if(!o)return;let e=o.getAttributes("link").href,t=window.prompt("Enter URL:",e);if(null!==t){if(""===t)return void o.chain().focus().extendMarkRange("link").unsetLink().run();o.chain().focus().extendMarkRange("link").setLink({href:t}).run()}},[o]),c=(0,a.useCallback)(()=>{o&&o.chain().focus().insertTable({rows:3,cols:3,withHeaderRow:!0}).run()},[o]);return o?(0,r.jsxs)("div",{className:"border border-border rounded-lg overflow-hidden",children:[(0,r.jsx)("div",{className:"border-b border-border bg-muted/50 p-2",children:(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-1",children:[(0,r.jsx)(n.$,{variant:o.isActive("bold")?"default":"ghost",size:"sm",onClick:()=>o.chain().focus().toggleBold().run(),children:(0,r.jsx)(F.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:o.isActive("italic")?"default":"ghost",size:"sm",onClick:()=>o.chain().focus().toggleItalic().run(),children:(0,r.jsx)(R.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:o.isActive("strike")?"default":"ghost",size:"sm",onClick:()=>o.chain().focus().toggleStrike().run(),children:(0,r.jsx)(B.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:o.isActive("code")?"default":"ghost",size:"sm",onClick:()=>o.chain().focus().toggleCode().run(),children:(0,r.jsx)(L.A,{className:"h-4 w-4"})}),(0,r.jsx)($,{orientation:"vertical",className:"h-6"}),(0,r.jsx)(n.$,{variant:o.isActive("heading",{level:1})?"default":"ghost",size:"sm",onClick:()=>o.chain().focus().toggleHeading({level:1}).run(),children:(0,r.jsx)(P.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:o.isActive("heading",{level:2})?"default":"ghost",size:"sm",onClick:()=>o.chain().focus().toggleHeading({level:2}).run(),children:(0,r.jsx)(J.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:o.isActive("heading",{level:3})?"default":"ghost",size:"sm",onClick:()=>o.chain().focus().toggleHeading({level:3}).run(),children:(0,r.jsx)(U.A,{className:"h-4 w-4"})}),(0,r.jsx)($,{orientation:"vertical",className:"h-6"}),(0,r.jsx)(n.$,{variant:o.isActive("bulletList")?"default":"ghost",size:"sm",onClick:()=>o.chain().focus().toggleBulletList().run(),children:(0,r.jsx)(E.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:o.isActive("orderedList")?"default":"ghost",size:"sm",onClick:()=>o.chain().focus().toggleOrderedList().run(),children:(0,r.jsx)(q.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:o.isActive("blockquote")?"default":"ghost",size:"sm",onClick:()=>o.chain().focus().toggleBlockquote().run(),children:(0,r.jsx)(Z.A,{className:"h-4 w-4"})}),(0,r.jsx)($,{orientation:"vertical",className:"h-6"}),(0,r.jsx)(n.$,{variant:"ghost",size:"sm",onClick:d,children:(0,r.jsx)(H.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"ghost",size:"sm",onClick:l,children:(0,r.jsx)(I.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"ghost",size:"sm",onClick:c,children:(0,r.jsx)(M.A,{className:"h-4 w-4"})}),(0,r.jsx)($,{orientation:"vertical",className:"h-6"}),(0,r.jsx)(n.$,{variant:o.isActive("highlight")?"default":"ghost",size:"sm",onClick:()=>o.chain().focus().toggleHighlight().run(),children:(0,r.jsx)(W.A,{className:"h-4 w-4"})}),(0,r.jsx)($,{orientation:"vertical",className:"h-6"}),(0,r.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>o.chain().focus().undo().run(),disabled:!o.can().undo(),children:(0,r.jsx)(_.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>o.chain().focus().redo().run(),disabled:!o.can().redo(),children:(0,r.jsx)(D.A,{className:"h-4 w-4"})})]})}),(0,r.jsx)(g.$Z,{editor:o,className:"min-h-[300px] max-h-[600px] overflow-y-auto"})]}):null}var G=s(15574),V=s(48730),K=s(11860),X=s(52581);let Q=["Web Development","React","Next.js","TypeScript","JavaScript","CSS","Node.js","Database","DevOps","Tutorial","Tips & Tricks","Career"];function Y({postId:e,onSuccess:t}){let s=(0,i.useRouter)(),[g,m]=(0,a.useState)(!1),[v,f]=(0,a.useState)(""),[b,j]=(0,a.useState)(!0),[y,w]=(0,a.useState)(!0),[N,k]=(0,a.useState)({title:"",slug:"",excerpt:"",content:"",image:"",category:"",tags:[],published:!1,featured:!1,readTime:5}),C=async r=>{r.preventDefault(),m(!0);try{let r=e?`/api/blog/${e}`:"/api/blog";if((await fetch(r,{method:e?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(N)})).ok)t?.(),e||s.push("/dashboard/blog");else throw Error("Failed to save post")}catch(e){console.error("Error saving post:",e),X.oR.error("Failed to save post. Please try again.")}finally{m(!1)}},A=()=>{v.trim()&&!N.tags.includes(v.trim())&&(k(e=>({...e,tags:[...e.tags,v.trim()]})),f(""))},z=e=>{k(t=>({...t,tags:t.tags.filter(t=>t!==e)}))};return(0,r.jsxs)("form",{onSubmit:C,className:"space-y-6",children:[(0,r.jsxs)(h.Zp,{children:[(0,r.jsxs)(h.aR,{children:[(0,r.jsx)(h.ZB,{children:"Basic Information"}),(0,r.jsx)(h.BT,{children:"Enter the basic details about your blog post"})]}),(0,r.jsxs)(h.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(l.J,{htmlFor:"title",children:"Post Title *"}),(0,r.jsx)(o.p,{id:"title",value:N.title,onChange:e=>k(t=>({...t,title:e.target.value})),required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)(l.J,{htmlFor:"slug",children:"URL Slug *"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.S,{id:"auto-slug",checked:b,onCheckedChange:e=>j(e)}),(0,r.jsxs)(l.J,{htmlFor:"auto-slug",className:"text-sm text-muted-foreground",children:[(0,r.jsx)(G.A,{className:"inline h-3 w-3 mr-1"}),"Auto-generate"]})]})]}),(0,r.jsx)(o.p,{id:"slug",value:N.slug,onChange:e=>{k(t=>({...t,slug:e.target.value})),j(!1)},placeholder:"url-friendly-slug",required:!0}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"The URL slug will be used in the blog post URL"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(l.J,{htmlFor:"excerpt",children:"Excerpt *"}),(0,r.jsx)(d.T,{id:"excerpt",value:N.excerpt,onChange:e=>k(t=>({...t,excerpt:e.target.value})),rows:3,placeholder:"A brief summary of your post...",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(l.J,{htmlFor:"category",children:"Category *"}),(0,r.jsxs)(u.l6,{value:N.category,onValueChange:e=>k(t=>({...t,category:e})),children:[(0,r.jsx)(u.bq,{children:(0,r.jsx)(u.yv,{placeholder:"Select a category"})}),(0,r.jsx)(u.gC,{children:Q.map(e=>(0,r.jsx)(u.eb,{value:e,children:e},e))})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)(l.J,{htmlFor:"readTime",children:"Reading Time (minutes)"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.S,{id:"auto-read-time",checked:y,onCheckedChange:e=>w(e)}),(0,r.jsxs)(l.J,{htmlFor:"auto-read-time",className:"text-sm text-muted-foreground",children:[(0,r.jsx)(V.A,{className:"inline h-3 w-3 mr-1"}),"Auto-calculate"]})]})]}),(0,r.jsx)(o.p,{id:"readTime",type:"number",min:"1",max:"60",value:N.readTime,onChange:e=>{k(t=>({...t,readTime:parseInt(e.target.value)||5})),w(!1)},placeholder:"5"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Estimated reading time based on content length"})]})]})]}),(0,r.jsxs)(h.Zp,{children:[(0,r.jsxs)(h.aR,{children:[(0,r.jsx)(h.ZB,{children:"Featured Image"}),(0,r.jsx)(h.BT,{children:"Upload a featured image for your blog post"})]}),(0,r.jsx)(h.Wu,{children:(0,r.jsx)(x.B,{value:N.image,onChange:e=>k(t=>({...t,image:e})),onRemove:()=>k(e=>({...e,image:""})),disabled:g})})]}),(0,r.jsxs)(h.Zp,{children:[(0,r.jsxs)(h.aR,{children:[(0,r.jsx)(h.ZB,{children:"Content"}),(0,r.jsx)(h.BT,{children:"Write your blog post content using the rich text editor"})]}),(0,r.jsx)(h.Wu,{children:(0,r.jsxs)("div",{children:[(0,r.jsx)(l.J,{htmlFor:"content",children:"Post Content *"}),(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsx)(O,{content:N.content,onChange:e=>k(t=>({...t,content:e})),placeholder:"Start writing your blog post..."})})]})})]}),(0,r.jsxs)(h.Zp,{children:[(0,r.jsxs)(h.aR,{children:[(0,r.jsx)(h.ZB,{children:"Tags"}),(0,r.jsx)(h.BT,{children:"Add tags to help categorize your post"})]}),(0,r.jsxs)(h.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(o.p,{placeholder:"Enter tag (e.g., react, tutorial)",value:v,onChange:e=>f(e.target.value),onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),A())}}),(0,r.jsx)(n.$,{type:"button",onClick:A,children:"Add"})]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:N.tags.map(e=>(0,r.jsxs)(p.E,{variant:"secondary",className:"flex items-center gap-1",children:[e,(0,r.jsx)(K.A,{className:"h-3 w-3 cursor-pointer",onClick:()=>z(e)})]},e))})]})]}),(0,r.jsxs)(h.Zp,{children:[(0,r.jsxs)(h.aR,{children:[(0,r.jsx)(h.ZB,{children:"Settings"}),(0,r.jsx)(h.BT,{children:"Configure post visibility and display options"})]}),(0,r.jsxs)(h.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.S,{id:"featured",checked:N.featured,onCheckedChange:e=>k(t=>({...t,featured:e}))}),(0,r.jsx)(l.J,{htmlFor:"featured",children:"Featured Post"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.S,{id:"published",checked:N.published,onCheckedChange:e=>k(t=>({...t,published:e}))}),(0,r.jsx)(l.J,{htmlFor:"published",children:"Published"})]})]})]}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(n.$,{type:"submit",disabled:g,children:g?"Saving...":e?"Update Post":"Create Post"}),(0,r.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>s.back(),children:"Cancel"})]})]})}},80013:(e,t,s)=>{s.d(t,{J:()=>n});var r=s(60687);s(43210);var a=s(78148),i=s(4780);function n({className:e,...t}){return(0,r.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},89667:(e,t,s)=>{s.d(t,{p:()=>i});var r=s(60687);s(43210);var a=s(4780);function i({className:e,type:t,...s}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},96834:(e,t,s)=>{s.d(t,{E:()=>l});var r=s(60687);s(43210);var a=s(8730),i=s(24224),n=s(4780);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:s=!1,...i}){let l=s?a.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(o({variant:t}),e),...i})}}};