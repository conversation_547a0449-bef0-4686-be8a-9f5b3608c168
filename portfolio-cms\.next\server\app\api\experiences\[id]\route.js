(()=>{var e={};e.id=8441,e.ids=[8441],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>a});var s=t(13581),i=t(16467),o=t(94747),n=t(85663);let a={adapter:(0,i.y)(o.z),providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await o.z.user.findUnique({where:{email:e.email}});return r&&await n.Ay.compare(e.password,r.password)?{id:r.id,email:r.email,name:r.name,role:r.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},27746:(e,r,t)=>{"use strict";t.d(r,{JB:()=>a,gx:()=>n});var s=t(32190);let i=["http://localhost:3000","http://localhost:3001","http://localhost:3002","http://localhost:3003","http://127.0.0.1:3000","http://127.0.0.1:3001","http://127.0.0.1:3002","http://127.0.0.1:3003"];function o(e){return{"Access-Control-Allow-Origin":e&&i.includes(e)?e:"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-Requested-With","Access-Control-Allow-Credentials":"true","Access-Control-Max-Age":"86400"}}function n(e,r){return Object.entries(o(r)).forEach(([r,t])=>{e.headers.set(r,t)}),e}function a(e){return new s.NextResponse(null,{status:200,headers:o(e)})}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55721:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>w,serverHooks:()=>f,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{DELETE:()=>h,GET:()=>d,PATCH:()=>x});var i=t(96559),o=t(48088),n=t(37719),a=t(32190),u=t(19854),p=t(12909),c=t(94747),l=t(27746);async function d(e,{params:r}){try{let{id:e}=await r,t=await c.z.experience.findUnique({where:{id:e}});if(!t)return(0,l.gx)(a.NextResponse.json({error:"Experience not found"},{status:404}));return(0,l.gx)(a.NextResponse.json(t))}catch(e){return console.error("Error fetching experience:",e),(0,l.gx)(a.NextResponse.json({error:"Failed to fetch experience"},{status:500}))}}async function x(e,{params:r}){try{let t=await (0,u.getServerSession)(p.N);if(!t?.user)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{id:s}=await r,i=await e.json(),o=await c.z.experience.update({where:{id:s},data:i});return(0,l.gx)(a.NextResponse.json(o))}catch(e){return console.error("Error updating experience:",e),(0,l.gx)(a.NextResponse.json({error:"Failed to update experience"},{status:500}))}}async function h(e,{params:r}){try{let e=await (0,u.getServerSession)(p.N);if(!e?.user)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{id:t}=await r;return await c.z.experience.delete({where:{id:t}}),(0,l.gx)(a.NextResponse.json({success:!0}))}catch(e){return console.error("Error deleting experience:",e),(0,l.gx)(a.NextResponse.json({error:"Failed to delete experience"},{status:500}))}}let w=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/experiences/[id]/route",pathname:"/api/experiences/[id]",filename:"route",bundlePath:"app/api/experiences/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\experiences\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:m,serverHooks:f}=w;function y(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:m})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},94747:(e,r,t)=>{"use strict";t.d(r,{z:()=>i});let s=require("@prisma/client"),i=globalThis.prisma??new s.PrismaClient},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,3542,2190],()=>t(55721));module.exports=s})();