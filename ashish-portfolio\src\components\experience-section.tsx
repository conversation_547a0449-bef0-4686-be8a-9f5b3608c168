"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Calendar, MapPin, ExternalLink, Briefcase, ArrowRight } from "lucide-react";
import { VerticalTimeline, VerticalTimelineElement } from 'react-vertical-timeline-component';
import 'react-vertical-timeline-component/style.min.css';
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { api, getPublishedExperiences, type Experience } from "@/lib/api";
import Image from "next/image";
import Link from "next/link";

// Fallback experiences data
const fallbackExperiences = [
  {
    title: "Senior Full Stack Developer",
    company: "TechCorp Solutions",
    location: "Mumbai, India",
    period: "2022 - Present",
    type: "Full-time",
    description: "Leading development of scalable web applications using React, Next.js, and Node.js. Mentoring junior developers and implementing best practices.",
    achievements: [
      "Increased application performance by 40%",
      "Led a team of 5 developers",
      "Implemented CI/CD pipelines"
    ],
    technologies: ["React", "Next.js", "TypeScript", "Node.js", "PostgreSQL", "AWS"],
  },
  {
    title: "Full Stack Developer",
    company: "StartupXYZ",
    location: "Remote",
    period: "2021 - 2022",
    type: "Full-time",
    description: "Developed and maintained multiple client projects using modern web technologies. Collaborated with design teams to create pixel-perfect interfaces.",
    achievements: [
      "Built 10+ responsive web applications",
      "Reduced page load times by 50%",
      "Implemented real-time features"
    ],
    technologies: ["React", "Vue.js", "Express.js", "MongoDB", "Firebase"],
  },
  {
    title: "Frontend Developer",
    company: "Digital Agency Pro",
    location: "Mumbai, India",
    period: "2020 - 2021",
    type: "Full-time",
    description: "Focused on creating beautiful and functional user interfaces for various client projects. Worked closely with designers to bring mockups to life.",
    achievements: [
      "Delivered 20+ client projects on time",
      "Improved client satisfaction by 25%",
      "Created reusable component library"
    ],
    technologies: ["HTML", "CSS", "JavaScript", "React", "Sass", "Figma"],
  }
];

export function ExperienceSection() {
  const [experiences, setExperiences] = useState<Experience[]>([]);
  const [loading, setLoading] = useState(true);
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  useEffect(() => {
    const fetchExperiences = async () => {
      try {
        const data = await api.getExperiences();
        console.log('Fetched experiences data:', data); // Debug log
        const publishedExperiences = getPublishedExperiences(data);
        console.log('Published experiences:', publishedExperiences); // Debug log
        // Show only the first 3 experiences for the home page
        const limitedExperiences = publishedExperiences.slice(0, 3);
        console.log('Limited experiences for home page:', limitedExperiences); // Debug log
        setExperiences(limitedExperiences);
      } catch (error) {
        console.error('Failed to fetch experiences:', error);
        // Use fallback data if API fails
        setExperiences(fallbackExperiences.map((exp, index) => ({
          id: `fallback-${index}`,
          title: exp.title,
          company: exp.company,
          companyLogo: undefined,
          location: exp.location,
          period: exp.period,
          type: exp.type,
          description: exp.description,
          achievements: exp.achievements,
          technologies: exp.technologies,
          website: undefined,
          order: index,
          published: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })));
      } finally {
        setLoading(false);
      }
    };

    fetchExperiences();
  }, []);

  if (loading) {
    return (
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="animate-pulse space-y-8">
            <div className="text-center">
              <div className="h-12 bg-gray-300 rounded w-64 mx-auto mb-4"></div>
              <div className="h-6 bg-gray-300 rounded w-96 mx-auto"></div>
            </div>
            <div className="max-w-4xl mx-auto space-y-8">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-start space-x-4">
                  <div className="w-16 h-16 bg-gray-300 rounded-full flex-shrink-0"></div>
                  <div className="flex-1 space-y-3">
                    <div className="h-6 bg-gray-300 rounded w-3/4"></div>
                    <div className="h-4 bg-gray-300 rounded w-1/2"></div>
                    <div className="h-20 bg-gray-300 rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section ref={ref} className="py-20 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
            <span className="gradient-text">Work Experience</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            My professional journey in web development, building scalable applications and leading development teams.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={inView ? { opacity: 1 } : {}}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <VerticalTimeline>
            {experiences.map((experience) => {
              return (
                <VerticalTimelineElement
                  key={experience.id}
                  className="vertical-timeline-element--work"
                  contentStyle={{
                    background: 'hsl(var(--card))',
                    color: 'hsl(var(--card-foreground))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '12px',
                    boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
                  }}
                  contentArrowStyle={{
                    borderRight: '7px solid hsl(var(--border))',
                  }}
                  date={experience.period}
                  iconStyle={{
                    background: '#ffffff',
                    color: '#000',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    border: '3px solid hsl(var(--background))',
                    boxShadow: '0 0 0 2px hsl(var(--border))',
                    width: '60px',
                    height: '60px',
                  }}
                  icon={
                    experience.companyLogo ? (
                      <Image
                        src={experience.companyLogo}
                        alt={`${experience.company} logo`}
                        width={40}
                        height={40}
                        className="rounded-full object-contain"
                        onError={(e) => {
                          console.error('Image failed to load:', experience.companyLogo, e);
                        }}
                        onLoad={() => {
                          console.log('Image loaded successfully:', experience.companyLogo);
                        }}
                      />
                    ) : (
                      <Briefcase className="h-6 w-6" />
                    )
                  }
                >
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-xl font-bold">{experience.title}</h3>
                      <h4 className="text-lg font-semibold text-primary">{experience.company}</h4>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-2">
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-1" />
                          {experience.location}
                        </div>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          {experience.period}
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {experience.type}
                        </Badge>
                      </div>
                    </div>

                    <p className="text-muted-foreground">{experience.description}</p>

                    <div>
                      <h4 className="font-semibold mb-2">Key Achievements:</h4>
                      <ul className="space-y-1">
                        {experience.achievements.slice(0, 3).map((achievement, achievementIndex) => (
                          <li key={achievementIndex} className="flex items-start space-x-2 text-sm text-muted-foreground">
                            <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0" />
                            <span>{achievement}</span>
                          </li>
                        ))}
                        {experience.achievements.length > 3 && (
                          <li className="text-sm text-muted-foreground ml-3.5">
                            +{experience.achievements.length - 3} more achievements
                          </li>
                        )}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-2">Technologies:</h4>
                      <div className="flex flex-wrap gap-2">
                        {experience.technologies.slice(0, 6).map((tech) => (
                          <Badge key={tech} variant="secondary" className="text-xs">
                            {tech}
                          </Badge>
                        ))}
                        {experience.technologies.length > 6 && (
                          <Badge variant="outline" className="text-xs">
                            +{experience.technologies.length - 6} more
                          </Badge>
                        )}
                      </div>
                    </div>

                    {experience.website && (
                      <div className="pt-2">
                        <a
                          href={experience.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-sm text-primary hover:underline"
                        >
                          <ExternalLink className="h-4 w-4 mr-1" />
                          Visit Company Website
                        </a>
                      </div>
                    )}
                  </div>
                </VerticalTimelineElement>
              );
            })}
          </VerticalTimeline>
        </motion.div>

        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <Link href="/about#experience">
            <Button variant="outline" size="lg" className="group">
              View Full Experience
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </motion.div>
      </div>
    </section>
  );
}
