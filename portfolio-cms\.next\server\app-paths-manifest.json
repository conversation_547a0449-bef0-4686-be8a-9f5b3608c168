{"/_not-found/page": "app/_not-found/page.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/blog/route": "app/api/blog/route.js", "/api/certifications/[id]/route": "app/api/certifications/[id]/route.js", "/api/blog/[id]/route": "app/api/blog/[id]/route.js", "/api/certifications/route": "app/api/certifications/route.js", "/api/education/route": "app/api/education/route.js", "/api/experiences/[id]/route": "app/api/experiences/[id]/route.js", "/api/education/[id]/route": "app/api/education/[id]/route.js", "/api/projects/[id]/route": "app/api/projects/[id]/route.js", "/api/experiences/route": "app/api/experiences/route.js", "/api/tech-stack/[id]/route": "app/api/tech-stack/[id]/route.js", "/api/services/[id]/route": "app/api/services/[id]/route.js", "/api/projects/route": "app/api/projects/route.js", "/api/services/route": "app/api/services/route.js", "/api/testimonials/route": "app/api/testimonials/route.js", "/api/tech-stack/route": "app/api/tech-stack/route.js", "/api/upload/route": "app/api/upload/route.js", "/api/testimonials/[id]/route": "app/api/testimonials/[id]/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/dashboard/blog/page": "app/dashboard/blog/page.js", "/dashboard/analytics/page": "app/dashboard/analytics/page.js", "/blog/page": "app/blog/page.js", "/blog/[slug]/page": "app/blog/[slug]/page.js", "/dashboard/certifications/add/page": "app/dashboard/certifications/add/page.js", "/dashboard/certifications/page": "app/dashboard/certifications/page.js", "/dashboard/certifications/[id]/page": "app/dashboard/certifications/[id]/page.js", "/dashboard/education/[id]/page": "app/dashboard/education/[id]/page.js", "/auth/signin/page": "app/auth/signin/page.js", "/dashboard/education/page": "app/dashboard/education/page.js", "/dashboard/experiences/[id]/edit/page": "app/dashboard/experiences/[id]/edit/page.js", "/dashboard/experiences/add/page": "app/dashboard/experiences/add/page.js", "/dashboard/blog/[id]/page": "app/dashboard/blog/[id]/page.js", "/dashboard/blog/new/page": "app/dashboard/blog/new/page.js", "/dashboard/education/add/page": "app/dashboard/education/add/page.js", "/dashboard/experiences/page": "app/dashboard/experiences/page.js", "/dashboard/projects/[id]/page": "app/dashboard/projects/[id]/page.js", "/dashboard/page": "app/dashboard/page.js", "/dashboard/projects/new/page": "app/dashboard/projects/new/page.js", "/dashboard/projects/page": "app/dashboard/projects/page.js", "/dashboard/settings/page": "app/dashboard/settings/page.js", "/page": "app/page.js", "/dashboard/services/page": "app/dashboard/services/page.js", "/dashboard/tech-stack/page": "app/dashboard/tech-stack/page.js", "/dashboard/testimonials/page": "app/dashboard/testimonials/page.js"}