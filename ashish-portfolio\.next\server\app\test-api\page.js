(()=>{var e={};e.id=990,e.ids=[990],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7156:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(60687),o=r(43210);function i(){let[e,t]=(0,o.useState)(null),[r,i]=(0,o.useState)(!0),[n,a]=(0,o.useState)(null);return r?(0,s.jsx)("div",{className:"p-8",children:"Loading..."}):n?(0,s.jsxs)("div",{className:"p-8 text-red-500",children:["Error: ",n]}):(0,s.jsxs)("div",{className:"p-8",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"API Test Results"}),(0,s.jsx)("pre",{className:"bg-gray-100 p-4 rounded overflow-auto",children:JSON.stringify(e,null,2)})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15983:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>h,pages:()=>p,routeModule:()=>m,tree:()=>d});var s=r(65239),o=r(48088),i=r(88170),n=r.n(i),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["test-api",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,32022)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\test-api\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,p=["C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\test-api\\page.tsx"],h={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/test-api/page",pathname:"/test-api",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32022:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\portfolio\\\\ashish-portfolio\\\\src\\\\app\\\\test-api\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\test-api\\page.tsx","default")},32546:(e,t,r)=>{Promise.resolve().then(r.bind(r,7156))},33873:e=>{"use strict";e.exports=require("path")},34711:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},38648:(e,t,r)=>{Promise.resolve().then(r.bind(r,83701)),Promise.resolve().then(r.bind(r,48482))},48376:(e,t,r)=>{Promise.resolve().then(r.bind(r,96871)),Promise.resolve().then(r.bind(r,64616))},48482:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\sonner.tsx","Toaster")},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64616:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>n});var s=r(60687),o=r(10218),i=r(52581);let n=({...e})=>{let{theme:t="system"}=(0,o.D)();return(0,s.jsx)(i.l$,{theme:t,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(31658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},83701:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\theme-provider.tsx","ThemeProvider")},87855:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},92378:(e,t,r)=>{Promise.resolve().then(r.bind(r,32022))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h,metadata:()=>p});var s=r(37413),o=r(22376),i=r.n(o),n=r(68726),a=r.n(n);r(61135);var l=r(48482),d=r(83701);let p={title:"Ashish Kamat - Full Stack Developer & UI/UX Designer",description:"Passionate full-stack developer and UI/UX designer creating innovative digital experiences. Specializing in React, Next.js, TypeScript, and modern web technologies.",keywords:["Ashish Kamat","Full Stack Developer","UI/UX Designer","React","Next.js","TypeScript","Web Development","Backend Development","Portfolio"],authors:[{name:"Ashish Kamat"}],creator:"Ashish Kamat",openGraph:{type:"website",locale:"en_US",url:"https://ashishkamat.com.np",title:"Ashish Kamat - Full Stack Developer & UI/UX Designer",description:"Passionate full-stack developer and UI/UX designer creating innovative digital experiences.",siteName:"Ashish Kamat Portfolio"},twitter:{card:"summary_large_image",title:"Ashish Kamat - Full Stack Developer & UI/UX Designer",description:"Passionate full-stack developer and UI/UX designer creating innovative digital experiences.",creator:"@ashishkamat"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function h({children:e}){return(0,s.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,s.jsx)("body",{className:`${i().variable} ${a().variable} antialiased`,children:(0,s.jsxs)(d.ThemeProvider,{attribute:"class",defaultTheme:"dark",enableSystem:!0,disableTransitionOnChange:!0,children:[e,(0,s.jsx)(l.Toaster,{})]})})})}},96871:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>i});var s=r(60687);r(43210);var o=r(10218);function i({children:e,...t}){return(0,s.jsx)(o.N,{...t,children:e})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,760,658],()=>r(15983));module.exports=s})();