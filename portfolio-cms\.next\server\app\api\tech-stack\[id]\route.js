(()=>{var e={};e.id=2549,e.ids=[2549],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>n});var s=t(13581),a=t(16467),i=t(94747),o=t(85663);let n={adapter:(0,a.y)(i.z),providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await i.z.user.findUnique({where:{email:e.email}});return r&&await o.Ay.compare(e.password,r.password)?{id:r.id,email:r.email,name:r.name,role:r.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},27864:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>x,serverHooks:()=>f,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{DELETE:()=>h,GET:()=>l,PUT:()=>d});var a=t(96559),i=t(48088),o=t(37719),n=t(32190),u=t(19854),c=t(12909),p=t(94747);async function l(e,{params:r}){try{let{id:e}=await r,t=await p.z.techStack.findUnique({where:{id:e}});if(!t)return n.NextResponse.json({error:"Tech not found"},{status:404});return n.NextResponse.json(t)}catch(e){return console.error("Error fetching tech:",e),n.NextResponse.json({error:"Failed to fetch tech"},{status:500})}}async function d(e,{params:r}){try{let t=await (0,u.getServerSession)(c.N);if(!t?.user)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{id:s}=await r,{name:a,logo:i,color:o,category:l,order:d,published:h}=await e.json(),x=await p.z.techStack.update({where:{id:s},data:{name:a,logo:i,color:o,category:l,order:d,published:h}});return n.NextResponse.json(x)}catch(e){return console.error("Error updating tech:",e),n.NextResponse.json({error:"Failed to update tech"},{status:500})}}async function h(e,{params:r}){try{let e=await (0,u.getServerSession)(c.N);if(!e?.user)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{id:t}=await r;return await p.z.techStack.delete({where:{id:t}}),n.NextResponse.json({message:"Tech deleted successfully"})}catch(e){return console.error("Error deleting tech:",e),n.NextResponse.json({error:"Failed to delete tech"},{status:500})}}let x=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/tech-stack/[id]/route",pathname:"/api/tech-stack/[id]",filename:"route",bundlePath:"app/api/tech-stack/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\tech-stack\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:m,serverHooks:f}=x;function y(){return(0,o.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:m})}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},94747:(e,r,t)=>{"use strict";t.d(r,{z:()=>a});let s=require("@prisma/client"),a=globalThis.prisma??new s.PrismaClient},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,3542,2190],()=>t(27864));module.exports=s})();