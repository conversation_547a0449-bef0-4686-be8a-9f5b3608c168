(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[220],{2882:(e,t,i)=>{"use strict";i.d(t,{Experience:()=>u});var a=i(5155),s=i(6408),r=i(3096),o=i(7576),n=i(3786),l=i(4516),c=i(2992);i(7917);var d=i(6126),m=i(5731),p=i(2115),h=i(6766);let x=[{title:"Senior Full Stack Developer",company:"TechCorp Solutions",location:"Mumbai, India",period:"2022 - Present",type:"Full-time",description:"Leading development of scalable web applications using React, Next.js, and Node.js. Mentoring junior developers and implementing best practices for code quality and performance.",achievements:["Increased application performance by 40% through optimization","Led a team of 5 developers on multiple projects","Implemented CI/CD pipelines reducing deployment time by 60%","Architected microservices handling 1M+ requests daily"],technologies:["React","Next.js","TypeScript","Node.js","PostgreSQL","AWS"],website:"https://techcorp.com"},{title:"Full Stack Developer",company:"StartupXYZ",location:"Remote",period:"2021 - 2022",type:"Full-time",description:"Developed and maintained multiple client projects using modern web technologies. Collaborated with design teams to create pixel-perfect user interfaces.",achievements:["Built 10+ responsive web applications from scratch","Reduced page load times by 50% through optimization","Implemented real-time features using WebSocket","Mentored 3 junior developers"],technologies:["React","Vue.js","Express.js","MongoDB","Firebase"],website:"https://startupxyz.com"},{title:"Frontend Developer",company:"Digital Agency Pro",location:"Mumbai, India",period:"2020 - 2021",type:"Full-time",description:"Focused on creating beautiful and functional user interfaces for various client projects. Worked closely with designers to bring mockups to life.",achievements:["Delivered 20+ client projects on time and within budget","Improved client satisfaction scores by 25%","Implemented responsive designs for mobile-first approach","Created reusable component library"],technologies:["HTML","CSS","JavaScript","React","Sass","Figma"],website:"https://digitalagencypro.com"},{title:"Junior Web Developer",company:"WebDev Studio",location:"Mumbai, India",period:"2019 - 2020",type:"Full-time",description:"Started my professional journey learning modern web development practices and contributing to various client projects.",achievements:["Completed 15+ small to medium-sized projects","Learned modern JavaScript frameworks and tools","Contributed to team's coding standards documentation","Achieved 95% client satisfaction rating"],technologies:["HTML","CSS","JavaScript","jQuery","Bootstrap","PHP"],website:"https://webdevstudio.com"}];function u(){let[e,t]=(0,r.Wx)({triggerOnce:!0,threshold:.1}),[i,u]=(0,p.useState)([]),[g,b]=(0,p.useState)(!0);return((0,p.useEffect)(()=>{(async()=>{try{let e=await m.FH.getExperiences();u((0,m.O6)(e))}catch(e){console.error("Failed to fetch experiences:",e),u(x.map((e,t)=>({id:"fallback-".concat(t),title:e.title,company:e.company,companyLogo:void 0,location:e.location,period:e.period,type:e.type,description:e.description,achievements:e.achievements,technologies:e.technologies,website:e.website,order:t,published:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()})))}finally{b(!1)}})()},[]),g)?(0,a.jsx)("section",{className:"py-20",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-300 rounded w-64 mx-auto mb-4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-300 rounded w-96 mx-auto"})]})})})}):(0,a.jsx)("section",{ref:e,className:"py-20",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(s.P.div,{className:"text-center mb-16",initial:{opacity:0,y:50},animate:t?{opacity:1,y:0}:{},transition:{duration:.8},children:[(0,a.jsx)("h2",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6",children:(0,a.jsx)("span",{className:"gradient-text",children:"Work Experience"})}),(0,a.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"My professional journey in web development, from junior developer to senior full-stack engineer."})]}),(0,a.jsx)(s.P.div,{initial:{opacity:0},animate:t?{opacity:1}:{},transition:{duration:.8,delay:.2},children:(0,a.jsx)(c.VerticalTimeline,{children:i.map(e=>(0,a.jsx)(c.VerticalTimelineElement,{className:"vertical-timeline-element--work",contentStyle:{background:"hsl(var(--card))",color:"hsl(var(--card-foreground))",border:"1px solid hsl(var(--border))",borderRadius:"12px",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"},contentArrowStyle:{borderRight:"7px solid hsl(var(--border))"},date:e.period,iconStyle:{background:"#ffffff",color:"#000",display:"flex",alignItems:"center",justifyContent:"center",border:"3px solid hsl(var(--background))",boxShadow:"0 0 0 2px hsl(var(--border))",width:"60px",height:"60px"},icon:e.companyLogo?(0,a.jsx)(h.default,{src:e.companyLogo,alt:"".concat(e.company," logo"),width:40,height:40,className:"rounded-full object-contain"}):(0,a.jsx)(o.A,{className:"h-6 w-6"}),children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-muted-foreground mt-1",children:[(0,a.jsx)("span",{className:"font-medium text-primary",children:e.company}),e.website&&(0,a.jsx)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"hover:text-primary transition-colors",children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 text-sm text-muted-foreground mt-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(l.A,{className:"h-4 w-4 shrink-0 translate-y-[1px]"}),(0,a.jsx)("span",{className:"leading-none",children:e.location})]}),(0,a.jsx)(d.E,{variant:"outline",className:"w-fit",children:e.type})]})]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:e.description}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold mb-2",children:"Key Achievements:"}),(0,a.jsx)("ul",{className:"space-y-1",children:e.achievements.map((e,t)=>(0,a.jsxs)("li",{className:"flex items-start space-x-2 text-sm text-muted-foreground",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0"}),(0,a.jsx)("span",{children:e})]},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold mb-2",children:"Technologies:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:e.technologies.map(e=>(0,a.jsx)(d.E,{variant:"secondary",className:"text-xs",children:e},e))})]})]})},e.id))})})]})})}},4516:(e,t,i)=>{"use strict";i.d(t,{A:()=>a});let a=(0,i(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},5040:(e,t,i)=>{"use strict";i.d(t,{A:()=>a});let a=(0,i(9946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5702:(e,t,i)=>{"use strict";i.d(t,{AboutHero:()=>p});var a=i(5155),s=i(6766),r=i(6408),o=i(1788),n=i(7312),l=i(4516),c=i(9074),d=i(285),m=i(6695);function p(){return(0,a.jsx)("section",{className:"py-20 bg-muted/30",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[(0,a.jsxs)(r.P.div,{className:"space-y-8",initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8},children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(r.P.h1,{className:"text-4xl sm:text-5xl lg:text-6xl font-bold",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:["About ",(0,a.jsx)("span",{className:"gradient-text-blue",children:"Me"})]}),(0,a.jsx)(r.P.p,{className:"text-xl text-muted-foreground leading-relaxed",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:"I'm a passionate full-stack developer and UI/UX designer with over 3 years of experience creating innovative digital solutions that make a real impact."})]}),(0,a.jsxs)(r.P.div,{className:"prose prose-lg dark:prose-invert max-w-none",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:[(0,a.jsx)("p",{children:"My journey in tech started with a curiosity about how things work behind the scenes. What began as tinkering with HTML and CSS has evolved into a deep passion for creating seamless, user-centered digital experiences."}),(0,a.jsx)("p",{children:"I specialize in modern web technologies like React, Next.js, and TypeScript, with a strong focus on performance, accessibility, and user experience. I believe that great software should not only function flawlessly but also delight users at every interaction."}),(0,a.jsx)("p",{children:"When I'm not coding, you'll find me exploring new technologies, contributing to open-source projects, or sharing knowledge with the developer community through blog posts and mentoring."})]}),(0,a.jsxs)(r.P.div,{className:"flex flex-col sm:flex-row gap-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:[(0,a.jsxs)(d.$,{size:"lg",className:"group",children:[(0,a.jsx)(o.A,{className:"mr-2 h-4 w-4 group-hover:animate-bounce"}),"Download Resume"]}),(0,a.jsxs)(d.$,{variant:"outline",size:"lg",children:[(0,a.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"Let's Chat"]})]})]}),(0,a.jsxs)(r.P.div,{className:"relative",initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.2},children:[(0,a.jsxs)("div",{className:"relative w-80 h-80 mx-auto lg:w-96 lg:h-96 mb-8",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl animate-pulse-glow"}),(0,a.jsx)("div",{className:"absolute inset-2 bg-background rounded-2xl overflow-hidden",children:(0,a.jsx)(s.default,{src:"/ashish-profile.svg",alt:"Ashish Kamat",fill:!0,className:"object-cover",priority:!0})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},children:(0,a.jsx)(m.Zp,{className:"text-center hover-lift",children:(0,a.jsxs)(m.Wu,{className:"p-4",children:[(0,a.jsx)(l.A,{className:"h-6 w-6 text-primary mx-auto mb-2"}),(0,a.jsx)("div",{className:"font-semibold",children:"Location"}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Kathmandu, Nepal"})]})})}),(0,a.jsx)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7},children:(0,a.jsx)(m.Zp,{className:"text-center hover-lift",children:(0,a.jsxs)(m.Wu,{className:"p-4",children:[(0,a.jsx)(c.A,{className:"h-6 w-6 text-primary mx-auto mb-2"}),(0,a.jsx)("div",{className:"font-semibold",children:"Experience"}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"3+ Years"})]})})})]})]})]})})})}},5731:(e,t,i)=>{"use strict";i.d(t,{$_:()=>l,FH:()=>s,LE:()=>d,O6:()=>m,OA:()=>n,Wq:()=>c,a0:()=>h,h1:()=>p,tx:()=>o});let a="http://localhost:3001",s={getProjects:async()=>{let e=await fetch("".concat(a,"/api/projects"));if(!e.ok)throw Error("Failed to fetch projects");return e.json()},getProject:async e=>{let t=await fetch("".concat(a,"/api/projects/").concat(e));if(!t.ok)throw Error("Failed to fetch project");return t.json()},getBlogPosts:async()=>{let e=await fetch("".concat(a,"/api/blog"));if(!e.ok)throw Error("Failed to fetch blog posts");return e.json()},getBlogPost:async e=>{let t=await fetch("".concat(a,"/api/blog/").concat(e));if(!t.ok)throw Error("Failed to fetch blog post");return t.json()},getServices:async()=>{let e=await fetch("".concat(a,"/api/services"));if(!e.ok)throw Error("Failed to fetch services");return e.json()},getTechStack:async()=>{let e=await fetch("".concat(a,"/api/tech-stack"));if(!e.ok)throw Error("Failed to fetch tech stack");return e.json()},getTestimonials:async()=>{let e=await fetch("".concat(a,"/api/testimonials"));if(!e.ok)throw Error("Failed to fetch testimonials");return e.json()},getExperiences:async()=>{let e=await fetch("".concat(a,"/api/experiences"));if(!e.ok)throw Error("Failed to fetch experiences");return e.json()},getEducation:async()=>{let e=await fetch("".concat(a,"/api/education"));if(!e.ok)throw Error("Failed to fetch education");return e.json()},getCertifications:async()=>{let e=await fetch("".concat(a,"/api/certifications"));if(!e.ok)throw Error("Failed to fetch certifications");return e.json()}},r=e=>e.filter(e=>e.published).sort((e,t)=>e.order-t.order),o=e=>e.filter(e=>e.published&&e.featured).sort((e,t)=>e.order-t.order),n=(e,t)=>"All"===t?r(e):e.filter(e=>e.published&&e.category===t).sort((e,t)=>e.order-t.order),l=e=>e.filter(e=>e.published).sort((e,t)=>e.order-t.order),c=e=>e.filter(e=>e.published).reduce((e,t)=>(e[t.category]||(e[t.category]=[]),e[t.category].push(t),e),{}),d=e=>e.filter(e=>e.published).sort((e,t)=>e.order-t.order),m=e=>e.filter(e=>e.published).sort((e,t)=>e.order-t.order),p=e=>e.filter(e=>e.published).sort((e,t)=>e.order-t.order),h=e=>e.filter(e=>e.published).sort((e,t)=>e.order-t.order)},5780:(e,t,i)=>{Promise.resolve().then(i.bind(i,5702)),Promise.resolve().then(i.bind(i,9204)),Promise.resolve().then(i.bind(i,2882)),Promise.resolve().then(i.bind(i,9364)),Promise.resolve().then(i.bind(i,3317)),Promise.resolve().then(i.bind(i,8211))},9204:(e,t,i)=>{"use strict";i.d(t,{Education:()=>f});var a=i(5155),s=i(6408),r=i(3096),o=i(9946);let n=(0,o.A)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]),l=(0,o.A)("school",[["path",{d:"M14 22v-4a2 2 0 1 0-4 0v4",key:"hhkicm"}],["path",{d:"m18 10 3.447 1.724a1 1 0 0 1 .553.894V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-7.382a1 1 0 0 1 .553-.894L6 10",key:"1xqip1"}],["path",{d:"M18 5v17",key:"1sw6gf"}],["path",{d:"m4 6 7.106-3.553a2 2 0 0 1 1.788 0L20 6",key:"9d2mlk"}],["path",{d:"M6 5v17",key:"1xfsm0"}],["circle",{cx:"12",cy:"9",r:"2",key:"1092wv"}]]),c=(0,o.A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]),d=(0,o.A)("trophy",[["path",{d:"M10 14.66v1.626a2 2 0 0 1-.976 1.696A5 5 0 0 0 7 21.978",key:"1n3hpd"}],["path",{d:"M14 14.66v1.626a2 2 0 0 0 .976 1.696A5 5 0 0 1 17 21.978",key:"rfe1zi"}],["path",{d:"M18 9h1.5a1 1 0 0 0 0-5H18",key:"7xy6bh"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M6 9a6 6 0 0 0 12 0V3a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1z",key:"1mhfuq"}],["path",{d:"M6 9H4.5a1 1 0 0 1 0-5H6",key:"tex48p"}]]);var m=i(5040),p=i(2992);i(7917);var h=i(6695),x=i(6126),u=i(5731),g=i(2115);let b=[{degree:"Bachelor of Engineering in Computer Science",institution:"Chandigarh University",location:"Chandigarh, India",period:"2020 - 2024",grade:"First Class with Distinction (8.5/10 CGPA)",description:"Focused on software engineering, data structures, algorithms, and web technologies. Completed final year project on e-commerce platform development.",highlights:["Dean's List for 3 consecutive semesters","Led university coding club with 200+ members","Won inter-college hackathon for innovative web solution","Published research paper on web performance optimization"],icon:n,iconBg:"#3b82f6",date:"2016 - 2020"},{degree:"Higher Secondary Certificate (Science)",institution:"St. Xavier's College",location:"Mumbai, India",period:"2014 - 2016",grade:"92.5%",description:"Specialized in Mathematics, Physics, and Chemistry with additional focus on computer science fundamentals.",highlights:["School topper in Computer Science","Represented school in state-level science exhibition","Active member of robotics club"],icon:l,iconBg:"#10b981",date:"2014 - 2016"}],y=[{title:"AWS Certified Solutions Architect",issuer:"Amazon Web Services",date:"2023",credentialId:"AWS-CSA-2023-001",emoji:"☁️",icon:c,iconBg:"#f59e0b",description:"Comprehensive certification covering AWS architecture best practices, security, and scalability."},{title:"React Developer Certification",issuer:"Meta (Facebook)",date:"2022",credentialId:"META-REACT-2022-456",emoji:"⚛️",icon:d,iconBg:"#3b82f6",description:"Advanced React development patterns, hooks, and modern React ecosystem."},{title:"Google Analytics Certified",issuer:"Google",date:"2022",credentialId:"GOOGLE-GA-2022-789",emoji:"\uD83D\uDCCA",icon:c,iconBg:"#10b981",description:"Digital analytics, data interpretation, and conversion optimization strategies."},{title:"MongoDB Developer Certification",issuer:"MongoDB University",date:"2021",credentialId:"MONGO-DEV-2021-123",emoji:"\uD83C\uDF43",icon:d,iconBg:"#8b5cf6",description:"NoSQL database design, aggregation pipelines, and performance optimization."}],v=["Advanced React Patterns - Kent C. Dodds","TypeScript Masterclass - Marius Schulz","Node.js Design Patterns - Mario Casciaro","System Design Interview - Alex Xu","AWS Solutions Architecture - A Cloud Guru","UI/UX Design Fundamentals - Google UX"];function f(){let[e,t]=(0,r.Wx)({triggerOnce:!0,threshold:.1}),[i,o]=(0,g.useState)([]),[l,d]=(0,g.useState)([]),[f,j]=(0,g.useState)(!0);return((0,g.useEffect)(()=>{(async()=>{try{let[e,t]=await Promise.all([u.FH.getEducation(),u.FH.getCertifications()]);o((0,u.h1)(e)),d((0,u.a0)(t))}catch(e){console.error("Failed to fetch education data:",e),o(b.map((e,t)=>({id:"fallback-edu-".concat(t),degree:e.degree,institution:e.institution,location:e.location,period:e.period,grade:e.grade,description:e.description,highlights:e.highlights,order:t,published:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}))),d(y.map((e,t)=>({id:"fallback-cert-".concat(t),title:e.title,issuer:e.issuer,date:e.date,credentialId:e.credentialId,emoji:e.emoji,description:e.description,order:t,published:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()})))}finally{j(!1)}})()},[]),f)?(0,a.jsx)("section",{className:"py-20",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-300 rounded w-64 mx-auto mb-4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-300 rounded w-96 mx-auto"})]})})})}):(0,a.jsx)("section",{ref:e,className:"py-20",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(s.P.div,{className:"text-center mb-16",initial:{opacity:0,y:50},animate:t?{opacity:1,y:0}:{},transition:{duration:.8},children:[(0,a.jsx)("h2",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6",children:(0,a.jsx)("span",{className:"gradient-text",children:"Education & Learning"})}),(0,a.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"My educational background and continuous learning journey in technology and software development."})]}),(0,a.jsxs)("div",{className:"mb-16",children:[(0,a.jsxs)(s.P.h3,{className:"text-2xl font-bold mb-8 flex items-center",initial:{opacity:0,x:-20},animate:t?{opacity:1,x:0}:{},transition:{duration:.6,delay:.2},children:[(0,a.jsx)(n,{className:"mr-3 h-6 w-6 text-primary"}),"Formal Education"]}),(0,a.jsx)(s.P.div,{initial:{opacity:0},animate:t?{opacity:1}:{},transition:{duration:.8,delay:.3},children:(0,a.jsx)(p.VerticalTimeline,{children:i.map(e=>(0,a.jsx)(p.VerticalTimelineElement,{className:"vertical-timeline-element--education",contentStyle:{background:"hsl(var(--card))",color:"hsl(var(--card-foreground))",border:"1px solid hsl(var(--border))",borderRadius:"12px",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"},contentArrowStyle:{borderRight:"7px solid hsl(var(--border))"},date:e.period,iconStyle:{background:"#3b82f6",color:"#fff",display:"flex",alignItems:"center",justifyContent:"center",border:"3px solid hsl(var(--background))",boxShadow:"0 0 0 2px hsl(var(--border))",width:"60px",height:"60px"},icon:(0,a.jsx)(n,{className:"h-7 w-7"}),children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold",children:e.degree}),(0,a.jsx)("div",{className:"text-primary font-medium",children:e.institution}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:e.location}),(0,a.jsx)(x.E,{variant:"secondary",className:"mt-2",children:e.grade})]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:e.description}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold mb-2",children:"Key Highlights:"}),(0,a.jsx)("ul",{className:"space-y-1",children:e.highlights.map((e,t)=>(0,a.jsxs)("li",{className:"flex items-start space-x-2 text-sm text-muted-foreground",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0"}),(0,a.jsx)("span",{children:e})]},t))})]})]})},e.id))})})]}),(0,a.jsxs)("div",{className:"mb-16",children:[(0,a.jsxs)(s.P.h3,{className:"text-2xl font-bold mb-8 flex items-center",initial:{opacity:0,x:-20},animate:t?{opacity:1,x:0}:{},transition:{duration:.6,delay:.4},children:[(0,a.jsx)(c,{className:"mr-3 h-6 w-6 text-primary"}),"Certifications"]}),(0,a.jsx)(s.P.div,{initial:{opacity:0},animate:t?{opacity:1}:{},transition:{duration:.8,delay:.5},children:(0,a.jsx)(p.VerticalTimeline,{children:l.map(e=>(0,a.jsx)(p.VerticalTimelineElement,{className:"vertical-timeline-element--certification",contentStyle:{background:"hsl(var(--card))",color:"hsl(var(--card-foreground))",border:"1px solid hsl(var(--border))",borderRadius:"12px",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"},contentArrowStyle:{borderRight:"7px solid hsl(var(--border))"},date:e.date,iconStyle:{background:"#f59e0b",color:"#fff",display:"flex",alignItems:"center",justifyContent:"center",border:"3px solid hsl(var(--background))",boxShadow:"0 0 0 2px hsl(var(--border))",width:"60px",height:"60px"},icon:(0,a.jsx)(c,{className:"h-6 w-6"}),children:(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"text-2xl",children:e.emoji}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"font-bold text-lg",children:e.title}),(0,a.jsx)("p",{className:"text-primary font-medium",children:e.issuer}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.description}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-2",children:["Credential ID: ",e.credentialId]})]})]})})},e.id))})})]}),(0,a.jsxs)(s.P.div,{initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{},transition:{duration:.6,delay:.6},children:[(0,a.jsxs)("h3",{className:"text-2xl font-bold mb-8 flex items-center",children:[(0,a.jsx)(m.A,{className:"mr-3 h-6 w-6 text-primary"}),"Continuous Learning"]}),(0,a.jsxs)(h.Zp,{className:"border-border/50",children:[(0,a.jsx)(h.aR,{children:(0,a.jsx)(h.ZB,{children:"Recent Courses & Training"})}),(0,a.jsxs)(h.Wu,{children:[(0,a.jsx)("div",{className:"grid md:grid-cols-2 gap-4",children:v.map((e,i)=>(0,a.jsxs)(s.P.div,{className:"flex items-center space-x-3 p-3 rounded-lg bg-muted/50 hover:bg-muted transition-colors duration-200",initial:{opacity:0,x:-20},animate:t?{opacity:1,x:0}:{},transition:{duration:.4,delay:.7+.05*i},children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full bg-primary flex-shrink-0"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:e})]},i))}),(0,a.jsx)("div",{className:"mt-6 p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg border border-border/50",children:(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[(0,a.jsx)("strong",{children:"Learning Philosophy:"})," I believe in continuous learning and staying updated with the latest technologies. I dedicate at least 5 hours per week to learning new skills and exploring emerging trends in web development and software engineering."]})})]})]})]})]})})}},9364:(e,t,i)=>{"use strict";i.d(t,{Skills:()=>l});var a=i(5155),s=i(6408),r=i(3096),o=i(6695);let n=[{title:"Frontend Development",skills:[{name:"React/Next.js",level:95,color:"bg-blue-500"},{name:"TypeScript",level:90,color:"bg-blue-600"},{name:"Tailwind CSS",level:92,color:"bg-cyan-500"},{name:"JavaScript (ES6+)",level:88,color:"bg-yellow-500"},{name:"HTML5 & CSS3",level:95,color:"bg-orange-500"},{name:"Framer Motion",level:85,color:"bg-purple-500"}]},{title:"Backend Development",skills:[{name:"Node.js",level:88,color:"bg-green-600"},{name:"Express.js",level:85,color:"bg-gray-600"},{name:"PostgreSQL",level:82,color:"bg-blue-800"},{name:"MongoDB",level:80,color:"bg-green-500"},{name:"Prisma ORM",level:85,color:"bg-indigo-600"},{name:"REST APIs",level:90,color:"bg-teal-500"}]},{title:"Tools & Technologies",skills:[{name:"Git & GitHub",level:92,color:"bg-gray-800"},{name:"Docker",level:75,color:"bg-blue-500"},{name:"AWS",level:70,color:"bg-orange-500"},{name:"Vercel",level:88,color:"bg-black"},{name:"Figma",level:85,color:"bg-purple-500"},{name:"VS Code",level:95,color:"bg-blue-600"}]},{title:"Soft Skills",skills:[{name:"Problem Solving",level:92,color:"bg-emerald-500"},{name:"Team Leadership",level:85,color:"bg-rose-500"},{name:"Communication",level:88,color:"bg-amber-500"},{name:"Project Management",level:80,color:"bg-violet-500"},{name:"Mentoring",level:82,color:"bg-pink-500"},{name:"Adaptability",level:90,color:"bg-sky-500"}]}];function l(){let[e,t]=(0,r.Wx)({triggerOnce:!0,threshold:.1});return(0,a.jsx)("section",{ref:e,className:"py-20 bg-muted/30",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(s.P.div,{className:"text-center mb-16",initial:{opacity:0,y:50},animate:t?{opacity:1,y:0}:{},transition:{duration:.8},children:[(0,a.jsx)("h2",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6",children:(0,a.jsx)("span",{className:"gradient-text",children:"Skills & Expertise"})}),(0,a.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"A comprehensive overview of my technical skills and proficiency levels across different technologies and domains."})]}),(0,a.jsx)("div",{className:"grid md:grid-cols-2 gap-8",children:n.map((e,i)=>(0,a.jsx)(s.P.div,{initial:{opacity:0,y:50},animate:t?{opacity:1,y:0}:{},transition:{duration:.8,delay:.1*i},children:(0,a.jsxs)(o.Zp,{className:"h-full hover-lift border-border/50 hover:border-border transition-all duration-300",children:[(0,a.jsx)(o.aR,{children:(0,a.jsx)(o.ZB,{className:"text-xl font-bold",children:e.title})}),(0,a.jsx)(o.Wu,{children:(0,a.jsx)("div",{className:"space-y-6",children:e.skills.map((e,r)=>(0,a.jsxs)(s.P.div,{className:"space-y-2",initial:{opacity:0,x:-20},animate:t?{opacity:1,x:0}:{},transition:{duration:.5,delay:.1*i+.05*r},children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"font-medium text-sm",children:e.name}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.level,"%"]})]}),(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)("div",{className:"w-full bg-muted rounded-full h-2",children:(0,a.jsx)(s.P.div,{className:"h-2 rounded-full ".concat(e.color),initial:{width:0},animate:t?{width:"".concat(e.level,"%")}:{width:0},transition:{duration:1,delay:.1*i+.05*r+.2,ease:"easeOut"}})})})]},e.name))})})]})},e.title))}),(0,a.jsx)(s.P.div,{className:"mt-16 text-center",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{},transition:{duration:.8,delay:.6},children:(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl p-8 border border-border/50",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold mb-4",children:"Continuous Learning"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-6 max-w-2xl mx-auto",children:"I'm passionate about staying up-to-date with the latest technologies and best practices. Currently exploring AI/ML integration in web applications and advanced React patterns."}),(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-4",children:["AI/ML","Web3","GraphQL","Microservices","DevOps","Mobile Development"].map(e=>(0,a.jsx)("span",{className:"px-3 py-1 bg-background border border-border rounded-full text-sm font-medium",children:e},e))})]})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[244,27,766,190,269,441,684,358],()=>t(5780)),_N_E=e.O()}]);