"use strict";exports.id=8732,exports.ids=[8732],exports.modules={3589:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},9005:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},11860:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},13964:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},16023:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},40211:(e,t,r)=>{r.d(t,{C1:()=>C,bL:()=>b});var n=r(43210),l=r(98599),o=r(11273),a=r(70569),i=r(65551),s=r(83721),d=r(18853),c=r(46059),u=r(14163),p=r(60687),f="Checkbox",[h,v]=(0,o.A)(f),[m,x]=h(f);function w(e){let{__scopeCheckbox:t,checked:r,children:l,defaultChecked:o,disabled:a,form:s,name:d,onCheckedChange:c,required:u,value:h="on",internal_do_not_use_render:v}=e,[x,w]=(0,i.i)({prop:r,defaultProp:o??!1,onChange:c,caller:f}),[g,y]=n.useState(null),[b,S]=n.useState(null),C=n.useRef(!1),k=!g||!!s||!!g.closest("form"),j={checked:x,disabled:a,setChecked:w,control:g,setControl:y,name:d,form:s,value:h,hasConsumerStoppedPropagationRef:C,required:u,defaultChecked:!R(o)&&o,isFormControl:k,bubbleInput:b,setBubbleInput:S};return(0,p.jsx)(m,{scope:t,...j,children:"function"==typeof v?v(j):l})}var g="CheckboxTrigger",y=n.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...o},i)=>{let{control:s,value:d,disabled:c,checked:f,required:h,setControl:v,setChecked:m,hasConsumerStoppedPropagationRef:w,isFormControl:y,bubbleInput:b}=x(g,e),S=(0,l.s)(i,v),C=n.useRef(f);return n.useEffect(()=>{let e=s?.form;if(e){let t=()=>m(C.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[s,m]),(0,p.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":R(f)?"mixed":f,"aria-required":h,"data-state":T(f),"data-disabled":c?"":void 0,disabled:c,value:d,...o,ref:S,onKeyDown:(0,a.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,a.m)(r,e=>{m(e=>!!R(e)||!e),b&&y&&(w.current=e.isPropagationStopped(),w.current||e.stopPropagation())})})});y.displayName=g;var b=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:l,defaultChecked:o,required:a,disabled:i,value:s,onCheckedChange:d,form:c,...u}=e;return(0,p.jsx)(w,{__scopeCheckbox:r,checked:l,defaultChecked:o,disabled:i,required:a,onCheckedChange:d,name:n,form:c,value:s,internal_do_not_use_render:({isFormControl:e})=>(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(y,{...u,ref:t,__scopeCheckbox:r}),e&&(0,p.jsx)(j,{__scopeCheckbox:r})]})})});b.displayName=f;var S="CheckboxIndicator",C=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...l}=e,o=x(S,r);return(0,p.jsx)(c.C,{present:n||R(o.checked)||!0===o.checked,children:(0,p.jsx)(u.sG.span,{"data-state":T(o.checked),"data-disabled":o.disabled?"":void 0,...l,ref:t,style:{pointerEvents:"none",...e.style}})})});C.displayName=S;var k="CheckboxBubbleInput",j=n.forwardRef(({__scopeCheckbox:e,...t},r)=>{let{control:o,hasConsumerStoppedPropagationRef:a,checked:i,defaultChecked:c,required:f,disabled:h,name:v,value:m,form:w,bubbleInput:g,setBubbleInput:y}=x(k,e),b=(0,l.s)(r,y),S=(0,s.Z)(i),C=(0,d.X)(o);n.useEffect(()=>{if(!g)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!a.current;if(S!==i&&e){let r=new Event("click",{bubbles:t});g.indeterminate=R(i),e.call(g,!R(i)&&i),g.dispatchEvent(r)}},[g,S,i,a]);let j=n.useRef(!R(i)&&i);return(0,p.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:c??j.current,required:f,disabled:h,name:v,value:m,form:w,...t,tabIndex:-1,ref:b,style:{...t.style,...C,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function R(e){return"indeterminate"===e}function T(e){return R(e)?"indeterminate":e?"checked":"unchecked"}j.displayName=k},78148:(e,t,r)=>{r.d(t,{b:()=>i});var n=r(43210),l=r(14163),o=r(60687),a=n.forwardRef((e,t)=>(0,o.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var i=a},78272:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},83721:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(43210);function l(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},97822:(e,t,r)=>{r.d(t,{UC:()=>eM,In:()=>eP,q7:()=>eL,VF:()=>eH,p4:()=>eA,ZL:()=>eI,bL:()=>eT,wn:()=>e_,PP:()=>eB,l9:()=>eE,WT:()=>eN,LM:()=>eD});var n=r(43210),l=r(51215);function o(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(70569),i=r(9510),s=r(98599),d=r(11273),c=r(43),u=r(31355),p=r(1359),f=r(32547),h=r(96963),v=r(55509),m=r(25028),x=r(14163),w=r(8730),g=r(13495),y=r(65551),b=r(66156),S=r(83721),C=r(60687),k=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});n.forwardRef((e,t)=>(0,C.jsx)(x.sG.span,{...e,ref:t,style:{...k,...e.style}})).displayName="VisuallyHidden";var j=r(63376),R=r(42247),T=[" ","Enter","ArrowUp","ArrowDown"],E=[" ","Enter"],N="Select",[P,I,M]=(0,i.N)(N),[D,L]=(0,d.A)(N,[M,v.Bk]),A=(0,v.Bk)(),[H,B]=D(N),[_,G]=D(N),V=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:d,dir:u,name:p,autoComplete:f,disabled:m,required:x,form:w}=e,g=A(t),[b,S]=n.useState(null),[k,j]=n.useState(null),[R,T]=n.useState(!1),E=(0,c.jH)(u),[I,M]=(0,y.i)({prop:l,defaultProp:o??!1,onChange:a,caller:N}),[D,L]=(0,y.i)({prop:i,defaultProp:s,onChange:d,caller:N}),B=n.useRef(null),G=!b||w||!!b.closest("form"),[V,O]=n.useState(new Set),F=Array.from(V).map(e=>e.props.value).join(";");return(0,C.jsx)(v.bL,{...g,children:(0,C.jsxs)(H,{required:x,scope:t,trigger:b,onTriggerChange:S,valueNode:k,onValueNodeChange:j,valueNodeHasChildren:R,onValueNodeHasChildrenChange:T,contentId:(0,h.B)(),value:D,onValueChange:L,open:I,onOpenChange:M,dir:E,triggerPointerDownPosRef:B,disabled:m,children:[(0,C.jsx)(P.Provider,{scope:t,children:(0,C.jsx)(_,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{O(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{O(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),G?(0,C.jsxs)(eC,{"aria-hidden":!0,required:x,tabIndex:-1,name:p,autoComplete:f,value:D,onChange:e=>L(e.target.value),disabled:m,form:w,children:[void 0===D?(0,C.jsx)("option",{value:""}):null,Array.from(V)]},F):null]})})};V.displayName=N;var O="SelectTrigger",F=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=A(r),d=B(O,r),c=d.disabled||l,u=(0,s.s)(t,d.onTriggerChange),p=I(r),f=n.useRef("touch"),[h,m,w]=ej(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===d.value),n=eR(t,e,r);void 0!==n&&d.onValueChange(n.value)}),g=e=>{c||(d.onOpenChange(!0),w()),e&&(d.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,C.jsx)(v.Mz,{asChild:!0,...i,children:(0,C.jsx)(x.sG.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":ek(d.value)?"":void 0,...o,ref:u,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&g(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&T.includes(e.key)&&(g(),e.preventDefault())})})})});F.displayName=O;var K="SelectValue",W=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,d=B(K,r),{onValueNodeHasChildrenChange:c}=d,u=void 0!==o,p=(0,s.s)(t,d.onValueNodeChange);return(0,b.N)(()=>{c(u)},[c,u]),(0,C.jsx)(x.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:ek(d.value)?(0,C.jsx)(C.Fragment,{children:a}):o})});W.displayName=K;var U=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,C.jsx)(x.sG.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});U.displayName="SelectIcon";var q=e=>(0,C.jsx)(m.Z,{asChild:!0,...e});q.displayName="SelectPortal";var z="SelectContent",Z=n.forwardRef((e,t)=>{let r=B(z,e.__scopeSelect),[o,a]=n.useState();return((0,b.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,C.jsx)(Q,{...e,ref:t}):o?l.createPortal((0,C.jsx)(X,{scope:e.__scopeSelect,children:(0,C.jsx)(P.Slot,{scope:e.__scopeSelect,children:(0,C.jsx)("div",{children:e.children})})}),o):null});Z.displayName=z;var[X,Y]=D(z),J=(0,w.TL)("SelectContent.RemoveScroll"),Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:d,side:c,sideOffset:h,align:v,alignOffset:m,arrowPadding:x,collisionBoundary:w,collisionPadding:g,sticky:y,hideWhenDetached:b,avoidCollisions:S,...k}=e,T=B(z,r),[E,N]=n.useState(null),[P,M]=n.useState(null),D=(0,s.s)(t,e=>N(e)),[L,A]=n.useState(null),[H,_]=n.useState(null),G=I(r),[V,O]=n.useState(!1),F=n.useRef(!1);n.useEffect(()=>{if(E)return(0,j.Eq)(E)},[E]),(0,p.Oh)();let K=n.useCallback(e=>{let[t,...r]=G().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(r?.scrollIntoView({block:"nearest"}),r===t&&P&&(P.scrollTop=0),r===n&&P&&(P.scrollTop=P.scrollHeight),r?.focus(),document.activeElement!==l))return},[G,P]),W=n.useCallback(()=>K([L,E]),[K,L,E]);n.useEffect(()=>{V&&W()},[V,W]);let{onOpenChange:U,triggerPointerDownPosRef:q}=T;n.useEffect(()=>{if(E){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(q.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(q.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():E.contains(r.target)||U(!1),document.removeEventListener("pointermove",t),q.current=null};return null!==q.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[E,U,q]),n.useEffect(()=>{let e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);let[Z,Y]=ej(e=>{let t=G().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eR(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),Q=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==T.value&&T.value===t||n)&&(A(e),n&&(F.current=!0))},[T.value]),et=n.useCallback(()=>E?.focus(),[E]),er=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==T.value&&T.value===t||n)&&_(e)},[T.value]),en="popper"===l?ee:$,el=en===ee?{side:c,sideOffset:h,align:v,alignOffset:m,arrowPadding:x,collisionBoundary:w,collisionPadding:g,sticky:y,hideWhenDetached:b,avoidCollisions:S}:{};return(0,C.jsx)(X,{scope:r,content:E,viewport:P,onViewportChange:M,itemRefCallback:Q,selectedItem:L,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:W,selectedItemText:H,position:l,isPositioned:V,searchRef:Z,children:(0,C.jsx)(R.A,{as:J,allowPinchZoom:!0,children:(0,C.jsx)(f.n,{asChild:!0,trapped:T.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{T.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,C.jsx)(u.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>T.onOpenChange(!1),children:(0,C.jsx)(en,{role:"listbox",id:T.contentId,"data-state":T.open?"open":"closed",dir:T.dir,onContextMenu:e=>e.preventDefault(),...k,...el,onPlaced:()=>O(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...k.style},onKeyDown:(0,a.m)(k.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=G().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>K(t)),e.preventDefault()}})})})})})})});Q.displayName="SelectContentImpl";var $=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=B(z,r),d=Y(z,r),[c,u]=n.useState(null),[p,f]=n.useState(null),h=(0,s.s)(t,e=>f(e)),v=I(r),m=n.useRef(!1),w=n.useRef(!0),{viewport:g,selectedItem:y,selectedItemText:S,focusSelectedItem:k}=d,j=n.useCallback(()=>{if(i.trigger&&i.valueNode&&c&&p&&g&&y&&S){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=S.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,s=e.width+i,d=Math.max(s,t.width),u=o(a,[10,Math.max(10,window.innerWidth-10-d)]);c.style.minWidth=s+"px",c.style.left=u+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,s=e.width+i,d=Math.max(s,t.width),u=o(a,[10,Math.max(10,window.innerWidth-10-d)]);c.style.minWidth=s+"px",c.style.right=u+"px"}let a=v(),s=window.innerHeight-20,d=g.scrollHeight,u=window.getComputedStyle(p),f=parseInt(u.borderTopWidth,10),h=parseInt(u.paddingTop,10),x=parseInt(u.borderBottomWidth,10),w=f+h+d+parseInt(u.paddingBottom,10)+x,b=Math.min(5*y.offsetHeight,w),C=window.getComputedStyle(g),k=parseInt(C.paddingTop,10),j=parseInt(C.paddingBottom,10),R=e.top+e.height/2-10,T=y.offsetHeight/2,E=f+h+(y.offsetTop+T);if(E<=R){let e=a.length>0&&y===a[a.length-1].ref.current;c.style.bottom="0px";let t=Math.max(s-R,T+(e?j:0)+(p.clientHeight-g.offsetTop-g.offsetHeight)+x);c.style.height=E+t+"px"}else{let e=a.length>0&&y===a[0].ref.current;c.style.top="0px";let t=Math.max(R,f+g.offsetTop+(e?k:0)+T);c.style.height=t+(w-E)+"px",g.scrollTop=E-R+g.offsetTop}c.style.margin="10px 0",c.style.minHeight=b+"px",c.style.maxHeight=s+"px",l?.(),requestAnimationFrame(()=>m.current=!0)}},[v,i.trigger,i.valueNode,c,p,g,y,S,i.dir,l]);(0,b.N)(()=>j(),[j]);let[R,T]=n.useState();(0,b.N)(()=>{p&&T(window.getComputedStyle(p).zIndex)},[p]);let E=n.useCallback(e=>{e&&!0===w.current&&(j(),k?.(),w.current=!1)},[j,k]);return(0,C.jsx)(et,{scope:r,contentWrapper:c,shouldExpandOnScrollRef:m,onScrollButtonChange:E,children:(0,C.jsx)("div",{ref:u,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,C.jsx)(x.sG.div,{...a,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});$.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=A(r);return(0,C.jsx)(v.UC,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=D(z,{}),en="SelectViewport",el=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=Y(en,r),d=er(en,r),c=(0,s.s)(t,i.onViewportChange),u=n.useRef(0);return(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,C.jsx)(P.Slot,{scope:r,children:(0,C.jsx)(x.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=d;if(n?.current&&r){let e=Math.abs(u.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}u.current=t.scrollTop})})})]})});el.displayName=en;var eo="SelectGroup",[ea,ei]=D(eo);n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,h.B)();return(0,C.jsx)(ea,{scope:r,id:l,children:(0,C.jsx)(x.sG.div,{role:"group","aria-labelledby":l,...n,ref:t})})}).displayName=eo;var es="SelectLabel";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=ei(es,r);return(0,C.jsx)(x.sG.div,{id:l.id,...n,ref:t})}).displayName=es;var ed="SelectItem",[ec,eu]=D(ed),ep=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...d}=e,c=B(ed,r),u=Y(ed,r),p=c.value===l,[f,v]=n.useState(i??""),[m,w]=n.useState(!1),g=(0,s.s)(t,e=>u.itemRefCallback?.(e,l,o)),y=(0,h.B)(),b=n.useRef("touch"),S=()=>{o||(c.onValueChange(l),c.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,C.jsx)(ec,{scope:r,value:l,disabled:o,textId:y,isSelected:p,onItemTextChange:n.useCallback(e=>{v(t=>t||(e?.textContent??"").trim())},[]),children:(0,C.jsx)(P.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,C.jsx)(x.sG.div,{role:"option","aria-labelledby":y,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...d,ref:g,onFocus:(0,a.m)(d.onFocus,()=>w(!0)),onBlur:(0,a.m)(d.onBlur,()=>w(!1)),onClick:(0,a.m)(d.onClick,()=>{"mouse"!==b.current&&S()}),onPointerUp:(0,a.m)(d.onPointerUp,()=>{"mouse"===b.current&&S()}),onPointerDown:(0,a.m)(d.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.m)(d.onPointerMove,e=>{b.current=e.pointerType,o?u.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(d.onPointerLeave,e=>{e.currentTarget===document.activeElement&&u.onItemLeave?.()}),onKeyDown:(0,a.m)(d.onKeyDown,e=>{(u.searchRef?.current===""||" "!==e.key)&&(E.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});ep.displayName=ed;var ef="SelectItemText",eh=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,d=B(ef,r),c=Y(ef,r),u=eu(ef,r),p=G(ef,r),[f,h]=n.useState(null),v=(0,s.s)(t,e=>h(e),u.onItemTextChange,e=>c.itemTextRefCallback?.(e,u.value,u.disabled)),m=f?.textContent,w=n.useMemo(()=>(0,C.jsx)("option",{value:u.value,disabled:u.disabled,children:m},u.value),[u.disabled,u.value,m]),{onNativeOptionAdd:g,onNativeOptionRemove:y}=p;return(0,b.N)(()=>(g(w),()=>y(w)),[g,y,w]),(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)(x.sG.span,{id:u.textId,...i,ref:v}),u.isSelected&&d.valueNode&&!d.valueNodeHasChildren?l.createPortal(i.children,d.valueNode):null]})});eh.displayName=ef;var ev="SelectItemIndicator",em=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return eu(ev,r).isSelected?(0,C.jsx)(x.sG.span,{"aria-hidden":!0,...n,ref:t}):null});em.displayName=ev;var ex="SelectScrollUpButton",ew=n.forwardRef((e,t)=>{let r=Y(ex,e.__scopeSelect),l=er(ex,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,C.jsx)(eb,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ew.displayName=ex;var eg="SelectScrollDownButton",ey=n.forwardRef((e,t)=>{let r=Y(eg,e.__scopeSelect),l=er(eg,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,C.jsx)(eb,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ey.displayName=eg;var eb=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=Y("SelectScrollButton",r),s=n.useRef(null),d=I(r),c=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>c(),[c]),(0,b.N)(()=>{let e=d().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[d]),(0,C.jsx)(x.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{i.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{c()})})});n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,C.jsx)(x.sG.div,{"aria-hidden":!0,...n,ref:t})}).displayName="SelectSeparator";var eS="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=A(r),o=B(eS,r),a=Y(eS,r);return o.open&&"popper"===a.position?(0,C.jsx)(v.i3,{...l,...n,ref:t}):null}).displayName=eS;var eC=n.forwardRef(({__scopeSelect:e,value:t,...r},l)=>{let o=n.useRef(null),a=(0,s.s)(l,o),i=(0,S.Z)(t);return n.useEffect(()=>{let e=o.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==t&&r){let n=new Event("change",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[i,t]),(0,C.jsx)(x.sG.select,{...r,style:{...k,...r.style},ref:a,defaultValue:t})});function ek(e){return""===e||void 0===e}function ej(e){let t=(0,g.c)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function eR(e,t,r){var n,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(n=e,l=Math.max(a,0),n.map((e,t)=>n[(l+t)%n.length]));1===o.length&&(i=i.filter(e=>e!==r));let s=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}eC.displayName="SelectBubbleInput";var eT=V,eE=F,eN=W,eP=U,eI=q,eM=Z,eD=el,eL=ep,eA=eh,eH=em,eB=ew,e_=ey}};