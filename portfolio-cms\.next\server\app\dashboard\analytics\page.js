(()=>{var e={};e.id=754,e.ids=[754],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4080:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\portfolio\\\\portfolio-cms\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\analytics\\page.tsx","default")},8492:(e,s,t)=>{Promise.resolve().then(t.bind(t,37469))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21644:(e,s,t)=>{Promise.resolve().then(t.bind(t,4080))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37469:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var r=t(60687),a=t(43210),i=t(62280),n=t(44493),o=t(96834),d=t(13861),l=t(62688);let c=(0,l.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);var x=t(53411);let m=(0,l.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);var p=t(48730);let u=(0,l.A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]]);function h(){let[e,s]=(0,a.useState)(null),[t,l]=(0,a.useState)(!0);return(0,r.jsx)(i.DashboardLayout,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Analytics"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Portfolio website performance and visitor insights"})]}),t?(0,r.jsx)("div",{className:"text-center py-8",children:"Loading analytics..."}):e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Page Views"}),(0,r.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.pageViews.toLocaleString()}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[(0,r.jsx)(c,{className:"inline h-3 w-3 mr-1"}),"+12.5% from last month"]})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Sessions"}),(0,r.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.sessions.toLocaleString()}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[(0,r.jsx)(c,{className:"inline h-3 w-3 mr-1"}),"+8.2% from last month"]})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Users"}),(0,r.jsx)(m,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.users.toLocaleString()}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[(0,r.jsx)(c,{className:"inline h-3 w-3 mr-1"}),"+15.3% from last month"]})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Avg. Duration"}),(0,r.jsx)(p.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:(e=>{let s=Math.floor(e/60);return`${s}:${(e%60).toString().padStart(2,"0")}`})(e.avgDuration)}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[(0,r.jsx)(u,{className:"inline h-3 w-3 mr-1"}),"-2.1% from last month"]})]})]})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Top Pages"}),(0,r.jsx)(n.BT,{children:"Most visited pages on your portfolio"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"space-y-3",children:e.topPages.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(o.E,{variant:"outline",className:"w-6 h-6 p-0 flex items-center justify-center text-xs",children:s+1}),(0,r.jsx)("span",{className:"font-medium",children:e.page})]}),(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.views.toLocaleString()," views"]})]},e.page))})})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Traffic Sources"}),(0,r.jsx)(n.BT,{children:"Where your visitors are coming from"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"space-y-3",children:e.topSources.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(o.E,{variant:"outline",className:"w-6 h-6 p-0 flex items-center justify-center text-xs",children:s+1}),(0,r.jsx)("span",{className:"font-medium",children:e.source})]}),(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.sessions.toLocaleString()," sessions"]})]},e.source))})})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Additional Metrics"}),(0,r.jsx)(n.BT,{children:"More insights about your portfolio performance"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[e.bounceRate,"%"]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Bounce Rate"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:(e.pageViews/e.sessions).toFixed(1)}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Pages per Session"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[(e.users/e.sessions*100).toFixed(1),"%"]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"New Visitors"})]})]})})]})]}):(0,r.jsx)("div",{className:"text-center py-8",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"No analytics data available"})})]})})}},48730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},91002:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>l});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(s,d);let l={children:["",{children:["dashboard",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4080)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\analytics\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\analytics\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/analytics/page",pathname:"/dashboard/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},96834:(e,s,t)=>{"use strict";t.d(s,{E:()=>d});var r=t(60687);t(43210);var a=t(8730),i=t(24224),n=t(4780);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:s,asChild:t=!1,...i}){let d=t?a.DX:"span";return(0,r.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(o({variant:s}),e),...i})}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,3310,1658,8580,4258,3868,6929],()=>t(91002));module.exports=r})();