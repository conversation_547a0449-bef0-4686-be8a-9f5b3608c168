(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[550],{35:(e,t)=>{"use strict";var r={H:null,A:null};function n(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var r=2;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var a=Array.isArray,i=Symbol.for("react.transitional.element"),o=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),u=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),f=Symbol.iterator,g=Object.prototype.hasOwnProperty,y=Object.assign;function m(e,t,r,n,a,o){return{$$typeof:i,type:e,key:t,ref:void 0!==(r=o.ref)?r:null,props:o}}function w(e){return"object"==typeof e&&null!==e&&e.$$typeof===i}var b=/\/+/g;function v(e,t){var r,n;return"object"==typeof e&&null!==e&&null!=e.key?(r=""+e.key,n={"=":"=0",":":"=2"},"$"+r.replace(/[=:]/g,function(e){return n[e]})):t.toString(36)}function _(){}function E(e,t,r){if(null==e)return e;var s=[],c=0;return!function e(t,r,s,c,l){var u,d,p,g=typeof t;("undefined"===g||"boolean"===g)&&(t=null);var y=!1;if(null===t)y=!0;else switch(g){case"bigint":case"string":case"number":y=!0;break;case"object":switch(t.$$typeof){case i:case o:y=!0;break;case h:return e((y=t._init)(t._payload),r,s,c,l)}}if(y)return l=l(t),y=""===c?"."+v(t,0):c,a(l)?(s="",null!=y&&(s=y.replace(b,"$&/")+"/"),e(l,r,s,"",function(e){return e})):null!=l&&(w(l)&&(u=l,d=s+(null==l.key||t&&t.key===l.key?"":(""+l.key).replace(b,"$&/")+"/")+y,l=m(u.type,d,void 0,void 0,void 0,u.props)),r.push(l)),1;y=0;var E=""===c?".":c+":";if(a(t))for(var S=0;S<t.length;S++)g=E+v(c=t[S],S),y+=e(c,r,s,g,l);else if("function"==typeof(S=null===(p=t)||"object"!=typeof p?null:"function"==typeof(p=f&&p[f]||p["@@iterator"])?p:null))for(t=S.call(t),S=0;!(c=t.next()).done;)g=E+v(c=c.value,S++),y+=e(c,r,s,g,l);else if("object"===g){if("function"==typeof t.then)return e(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(_,_):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(t),r,s,c,l);throw Error(n(31,"[object Object]"===(r=String(t))?"object with keys {"+Object.keys(t).join(", ")+"}":r))}return y}(e,s,"","",function(e){return t.call(r,e,c++)}),s}function S(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){(0===e._status||-1===e._status)&&(e._status=1,e._result=t)},function(t){(0===e._status||-1===e._status)&&(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}function A(){return new WeakMap}function x(){return{s:0,v:void 0,o:null,p:null}}t.Children={map:E,forEach:function(e,t,r){E(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return E(e,function(){t++}),t},toArray:function(e){return E(e,function(e){return e})||[]},only:function(e){if(!w(e))throw Error(n(143));return e}},t.Fragment=s,t.Profiler=l,t.StrictMode=c,t.Suspense=d,t.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,t.cache=function(e){return function(){var t=r.A;if(!t)return e.apply(null,arguments);var n=t.getCacheForType(A);void 0===(t=n.get(e))&&(t=x(),n.set(e,t)),n=0;for(var a=arguments.length;n<a;n++){var i=arguments[n];if("function"==typeof i||"object"==typeof i&&null!==i){var o=t.o;null===o&&(t.o=o=new WeakMap),void 0===(t=o.get(i))&&(t=x(),o.set(i,t))}else null===(o=t.p)&&(t.p=o=new Map),void 0===(t=o.get(i))&&(t=x(),o.set(i,t))}if(1===t.s)return t.v;if(2===t.s)throw t.v;try{var s=e.apply(null,arguments);return(n=t).s=1,n.v=s}catch(e){throw(s=t).s=2,s.v=e,e}}},t.captureOwnerStack=function(){return null},t.cloneElement=function(e,t,r){if(null==e)throw Error(n(267,e));var a=y({},e.props),i=e.key,o=void 0;if(null!=t)for(s in void 0!==t.ref&&(o=void 0),void 0!==t.key&&(i=""+t.key),t)g.call(t,s)&&"key"!==s&&"__self"!==s&&"__source"!==s&&("ref"!==s||void 0!==t.ref)&&(a[s]=t[s]);var s=arguments.length-2;if(1===s)a.children=r;else if(1<s){for(var c=Array(s),l=0;l<s;l++)c[l]=arguments[l+2];a.children=c}return m(e.type,i,void 0,void 0,o,a)},t.createElement=function(e,t,r){var n,a={},i=null;if(null!=t)for(n in void 0!==t.key&&(i=""+t.key),t)g.call(t,n)&&"key"!==n&&"__self"!==n&&"__source"!==n&&(a[n]=t[n]);var o=arguments.length-2;if(1===o)a.children=r;else if(1<o){for(var s=Array(o),c=0;c<o;c++)s[c]=arguments[c+2];a.children=s}if(e&&e.defaultProps)for(n in o=e.defaultProps)void 0===a[n]&&(a[n]=o[n]);return m(e,i,void 0,void 0,null,a)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=w,t.lazy=function(e){return{$$typeof:h,_payload:{_status:-1,_result:e},_init:S}},t.memo=function(e,t){return{$$typeof:p,type:e,compare:void 0===t?null:t}},t.use=function(e){return r.H.use(e)},t.useCallback=function(e,t){return r.H.useCallback(e,t)},t.useDebugValue=function(){},t.useId=function(){return r.H.useId()},t.useMemo=function(e,t){return r.H.useMemo(e,t)},t.version="19.2.0-canary-3fbfb9ba-20250409"},58:(e,t,r)=>{"use strict";r.d(t,{xl:()=>o});let n=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class a{disable(){throw n}getStore(){}run(){throw n}exit(){throw n}enterWith(){throw n}static bind(e){return e}}let i="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function o(){return i?new i:new a}},115:(e,t,r)=>{"use strict";r.d(t,{XN:()=>a,FP:()=>n});let n=(0,r(58).xl)();function a(e){let t=n.getStore();switch(!t&&function(e){throw Object.defineProperty(Error(`\`${e}\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`),"__NEXT_ERROR_CODE",{value:"E251",enumerable:!1,configurable:!0})}(e),t.type){case"request":default:return t;case"prerender":case"prerender-ppr":case"prerender-legacy":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside a prerender. This is a bug in Next.js.`),"__NEXT_ERROR_CODE",{value:"E401",enumerable:!1,configurable:!0});case"cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside "use cache". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E37",enumerable:!1,configurable:!0});case"unstable-cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E69",enumerable:!1,configurable:!0})}}},131:(e,t)=>{"use strict";function r(e,t,r){n(e,t),t.set(e,r)}function n(e,t){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object")}function a(e,t){return e.get(o(e,t))}function i(e,t,r){return e.set(o(e,t),r),r}function o(e,t,r){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:r;throw TypeError("Private element is not present on this object")}Object.defineProperty(t,"__esModule",{value:!0}),t.SessionStore=void 0,t.defaultCookies=function(e){let t=e?"__Secure-":"";return{sessionToken:{name:`${t}next-auth.session-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},callbackUrl:{name:`${t}next-auth.callback-url`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},csrfToken:{name:`${e?"__Host-":""}next-auth.csrf-token`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}},pkceCodeVerifier:{name:`${t}next-auth.pkce.code_verifier`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},state:{name:`${t}next-auth.state`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e,maxAge:900}},nonce:{name:`${t}next-auth.nonce`,options:{httpOnly:!0,sameSite:"lax",path:"/",secure:e}}}};var s=new WeakMap,c=new WeakMap,l=new WeakMap,u=new WeakSet;class d{constructor(e,t,o){!function(e,t){n(e,t),t.add(e)}(this,u),r(this,s,{}),r(this,c,void 0),r(this,l,void 0),i(l,this,o),i(c,this,e);let{cookies:d}=t,{name:p}=e;if("function"==typeof(null==d?void 0:d.getAll))for(let{name:e,value:t}of d.getAll())e.startsWith(p)&&(a(s,this)[e]=t);else if(d instanceof Map)for(let e of d.keys())e.startsWith(p)&&(a(s,this)[e]=d.get(e));else for(let e in d)e.startsWith(p)&&(a(s,this)[e]=d[e])}get value(){return Object.keys(a(s,this)).sort((e,t)=>{var r,n;return parseInt(null!=(r=e.split(".").pop())?r:"0")-parseInt(null!=(n=t.split(".").pop())?n:"0")}).map(e=>a(s,this)[e]).join("")}chunk(e,t){let r=o(u,this,h).call(this);for(let n of o(u,this,p).call(this,{name:a(c,this).name,value:e,options:{...a(c,this).options,...t}}))r[n.name]=n;return Object.values(r)}clean(){return Object.values(o(u,this,h).call(this))}}function p(e){let t=Math.ceil(e.value.length/3933);if(1===t)return a(s,this)[e.name]=e.value,[e];let r=[];for(let n=0;n<t;n++){let t=`${e.name}.${n}`,i=e.value.substr(3933*n,3933);r.push({...e,name:t,value:i}),a(s,this)[t]=i}return a(l,this).debug("CHUNKING_SESSION_COOKIE",{message:"Session cookie exceeds allowed 4096 bytes.",emptyCookieSize:163,valueSize:e.value.length,chunks:r.map(e=>e.value.length+163)}),r}function h(){let e={};for(let r in a(s,this)){var t;null==(t=a(s,this))||delete t[r],e[r]={name:r,value:"",options:{...a(c,this).options,maxAge:0}}}return e}t.SessionStore=d},201:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getTestReqInfo:function(){return o},withRequest:function(){return i}});let n=new(r(521)).AsyncLocalStorage;function a(e,t){let r=t.header(e,"next-test-proxy-port");if(!r)return;let n=t.url(e);return{url:n,proxyPort:Number(r),testData:t.header(e,"next-test-data")||""}}function i(e,t,r){let i=a(e,t);return i?n.run(i,r):r()}function o(e,t){let r=n.getStore();return r||(e&&t?a(e,t):void 0)}},211:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var n=r(648),a=r(241),i=r(600),o=r(725);let s=Symbol("internal request");class c extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);(0,a.qU)(r),e instanceof Request?super(e,t):super(r,t);let i=new n.X(r,{headers:(0,a.Cu)(this.headers),nextConfig:t.nextConfig});this[s]={cookies:new o.tm(this.headers),nextUrl:i,url:i.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[s].cookies}get nextUrl(){return this[s].nextUrl}get page(){throw new i.Yq}get ua(){throw new i.l_}get url(){return this[s].url}}},241:(e,t,r)=>{"use strict";r.d(t,{Cu:()=>o,RD:()=>i,p$:()=>a,qU:()=>s,wN:()=>c});var n=r(430);function a(e){let t=new Headers;for(let[r,n]of Object.entries(e))for(let e of Array.isArray(n)?n:[n])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}function i(e){var t,r,n,a,i,o=[],s=0;function c(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,i=!1;c();)if(","===(r=e.charAt(s))){for(n=s,s+=1,c(),a=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(i=!0,s=a,o.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!i||s>=e.length)&&o.push(e.substring(t,e.length))}return o}function o(e){let t={},r=[];if(e)for(let[n,a]of e.entries())"set-cookie"===n.toLowerCase()?(r.push(...i(a)),t[n]=1===r.length?r[0]:r):t[n]=a;return t}function s(e){try{return String(new URL(String(e)))}catch(t){throw Object.defineProperty(Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}function c(e){for(let t of[n.AA,n.h])if(e!==t&&e.startsWith(t))return e.substring(t.length);return null}},280:(e,t,r)=>{var n;(()=>{var a={226:function(a,i){!function(o,s){"use strict";var c="function",l="undefined",u="object",d="string",p="major",h="model",f="name",g="type",y="vendor",m="version",w="architecture",b="console",v="mobile",_="tablet",E="smarttv",S="wearable",A="embedded",x="Amazon",C="Apple",P="ASUS",R="BlackBerry",O="Browser",T="Chrome",k="Firefox",N="Google",I="Huawei",H="Microsoft",M="Motorola",D="Opera",j="Samsung",U="Sharp",W="Sony",L="Xiaomi",K="Zebra",J="Facebook",$="Chromium OS",B="Mac OS",V=function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},q=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},G=function(e,t){return typeof e===d&&-1!==z(t).indexOf(z(e))},z=function(e){return e.toLowerCase()},F=function(e,t){if(typeof e===d)return e=e.replace(/^\s\s*/,""),typeof t===l?e:e.substring(0,350)},X=function(e,t){for(var r,n,a,i,o,l,d=0;d<t.length&&!o;){var p=t[d],h=t[d+1];for(r=n=0;r<p.length&&!o&&p[r];)if(o=p[r++].exec(e))for(a=0;a<h.length;a++)l=o[++n],typeof(i=h[a])===u&&i.length>0?2===i.length?typeof i[1]==c?this[i[0]]=i[1].call(this,l):this[i[0]]=i[1]:3===i.length?typeof i[1]!==c||i[1].exec&&i[1].test?this[i[0]]=l?l.replace(i[1],i[2]):void 0:this[i[0]]=l?i[1].call(this,l,i[2]):void 0:4===i.length&&(this[i[0]]=l?i[3].call(this,l.replace(i[1],i[2])):s):this[i]=l||s;d+=2}},Y=function(e,t){for(var r in t)if(typeof t[r]===u&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(G(t[r][n],e))return"?"===r?s:r}else if(G(t[r],e))return"?"===r?s:r;return e},Z={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Q={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[m,[f,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[m,[f,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[f,m],[/opios[\/ ]+([\w\.]+)/i],[m,[f,D+" Mini"]],[/\bopr\/([\w\.]+)/i],[m,[f,D]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[f,m],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[m,[f,"UC"+O]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[m,[f,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[m,[f,"WeChat"]],[/konqueror\/([\w\.]+)/i],[m,[f,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[m,[f,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[m,[f,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[f,/(.+)/,"$1 Secure "+O],m],[/\bfocus\/([\w\.]+)/i],[m,[f,k+" Focus"]],[/\bopt\/([\w\.]+)/i],[m,[f,D+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[m,[f,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[m,[f,"Dolphin"]],[/coast\/([\w\.]+)/i],[m,[f,D+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[m,[f,"MIUI "+O]],[/fxios\/([-\w\.]+)/i],[m,[f,k]],[/\bqihu|(qi?ho?o?|360)browser/i],[[f,"360 "+O]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[f,/(.+)/,"$1 "+O],m],[/(comodo_dragon)\/([\w\.]+)/i],[[f,/_/g," "],m],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[f,m],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[f],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[f,J],m],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[f,m],[/\bgsa\/([\w\.]+) .*safari\//i],[m,[f,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[m,[f,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[m,[f,T+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[f,T+" WebView"],m],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[m,[f,"Android "+O]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[f,m],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[m,[f,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[m,f],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[f,[m,Y,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[f,m],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[f,"Netscape"],m],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[m,[f,k+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[f,m],[/(cobalt)\/([\w\.]+)/i],[f,[m,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[w,"amd64"]],[/(ia32(?=;))/i],[[w,z]],[/((?:i[346]|x)86)[;\)]/i],[[w,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[w,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[w,"armhf"]],[/windows (ce|mobile); ppc;/i],[[w,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[w,/ower/,"",z]],[/(sun4\w)[;\)]/i],[[w,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[w,z]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[h,[y,j],[g,_]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[h,[y,j],[g,v]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[h,[y,C],[g,v]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[h,[y,C],[g,_]],[/(macintosh);/i],[h,[y,C]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[h,[y,U],[g,v]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[h,[y,I],[g,_]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[h,[y,I],[g,v]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[h,/_/g," "],[y,L],[g,v]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[h,/_/g," "],[y,L],[g,_]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[h,[y,"OPPO"],[g,v]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[h,[y,"Vivo"],[g,v]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[h,[y,"Realme"],[g,v]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[h,[y,M],[g,v]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[h,[y,M],[g,_]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[h,[y,"LG"],[g,_]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[h,[y,"LG"],[g,v]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[h,[y,"Lenovo"],[g,_]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[h,/_/g," "],[y,"Nokia"],[g,v]],[/(pixel c)\b/i],[h,[y,N],[g,_]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[h,[y,N],[g,v]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[h,[y,W],[g,v]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[h,"Xperia Tablet"],[y,W],[g,_]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[h,[y,"OnePlus"],[g,v]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[h,[y,x],[g,_]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[h,/(.+)/g,"Fire Phone $1"],[y,x],[g,v]],[/(playbook);[-\w\),; ]+(rim)/i],[h,y,[g,_]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[h,[y,R],[g,v]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[h,[y,P],[g,_]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[h,[y,P],[g,v]],[/(nexus 9)/i],[h,[y,"HTC"],[g,_]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[y,[h,/_/g," "],[g,v]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[h,[y,"Acer"],[g,_]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[h,[y,"Meizu"],[g,v]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[y,h,[g,v]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[y,h,[g,_]],[/(surface duo)/i],[h,[y,H],[g,_]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[h,[y,"Fairphone"],[g,v]],[/(u304aa)/i],[h,[y,"AT&T"],[g,v]],[/\bsie-(\w*)/i],[h,[y,"Siemens"],[g,v]],[/\b(rct\w+) b/i],[h,[y,"RCA"],[g,_]],[/\b(venue[\d ]{2,7}) b/i],[h,[y,"Dell"],[g,_]],[/\b(q(?:mv|ta)\w+) b/i],[h,[y,"Verizon"],[g,_]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[h,[y,"Barnes & Noble"],[g,_]],[/\b(tm\d{3}\w+) b/i],[h,[y,"NuVision"],[g,_]],[/\b(k88) b/i],[h,[y,"ZTE"],[g,_]],[/\b(nx\d{3}j) b/i],[h,[y,"ZTE"],[g,v]],[/\b(gen\d{3}) b.+49h/i],[h,[y,"Swiss"],[g,v]],[/\b(zur\d{3}) b/i],[h,[y,"Swiss"],[g,_]],[/\b((zeki)?tb.*\b) b/i],[h,[y,"Zeki"],[g,_]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[y,"Dragon Touch"],h,[g,_]],[/\b(ns-?\w{0,9}) b/i],[h,[y,"Insignia"],[g,_]],[/\b((nxa|next)-?\w{0,9}) b/i],[h,[y,"NextBook"],[g,_]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[y,"Voice"],h,[g,v]],[/\b(lvtel\-)?(v1[12]) b/i],[[y,"LvTel"],h,[g,v]],[/\b(ph-1) /i],[h,[y,"Essential"],[g,v]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[h,[y,"Envizen"],[g,_]],[/\b(trio[-\w\. ]+) b/i],[h,[y,"MachSpeed"],[g,_]],[/\btu_(1491) b/i],[h,[y,"Rotor"],[g,_]],[/(shield[\w ]+) b/i],[h,[y,"Nvidia"],[g,_]],[/(sprint) (\w+)/i],[y,h,[g,v]],[/(kin\.[onetw]{3})/i],[[h,/\./g," "],[y,H],[g,v]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[h,[y,K],[g,_]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[h,[y,K],[g,v]],[/smart-tv.+(samsung)/i],[y,[g,E]],[/hbbtv.+maple;(\d+)/i],[[h,/^/,"SmartTV"],[y,j],[g,E]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[y,"LG"],[g,E]],[/(apple) ?tv/i],[y,[h,C+" TV"],[g,E]],[/crkey/i],[[h,T+"cast"],[y,N],[g,E]],[/droid.+aft(\w)( bui|\))/i],[h,[y,x],[g,E]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[h,[y,U],[g,E]],[/(bravia[\w ]+)( bui|\))/i],[h,[y,W],[g,E]],[/(mitv-\w{5}) bui/i],[h,[y,L],[g,E]],[/Hbbtv.*(technisat) (.*);/i],[y,h,[g,E]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[y,F],[h,F],[g,E]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[g,E]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[y,h,[g,b]],[/droid.+; (shield) bui/i],[h,[y,"Nvidia"],[g,b]],[/(playstation [345portablevi]+)/i],[h,[y,W],[g,b]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[h,[y,H],[g,b]],[/((pebble))app/i],[y,h,[g,S]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[h,[y,C],[g,S]],[/droid.+; (glass) \d/i],[h,[y,N],[g,S]],[/droid.+; (wt63?0{2,3})\)/i],[h,[y,K],[g,S]],[/(quest( 2| pro)?)/i],[h,[y,J],[g,S]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[y,[g,A]],[/(aeobc)\b/i],[h,[y,x],[g,A]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[h,[g,v]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[h,[g,_]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[g,_]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[g,v]],[/(android[-\w\. ]{0,9});.+buil/i],[h,[y,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[m,[f,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[m,[f,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[f,m],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[m,f]],os:[[/microsoft (windows) (vista|xp)/i],[f,m],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[f,[m,Y,Z]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[f,"Windows"],[m,Y,Z]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[m,/_/g,"."],[f,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[f,B],[m,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[m,f],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[f,m],[/\(bb(10);/i],[m,[f,R]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[m,[f,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[m,[f,k+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[m,[f,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[m,[f,"watchOS"]],[/crkey\/([\d\.]+)/i],[m,[f,T+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[f,$],m],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[f,m],[/(sunos) ?([\w\.\d]*)/i],[[f,"Solaris"],m],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[f,m]]},ee=function(e,t){if(typeof e===u&&(t=e,e=s),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof o!==l&&o.navigator?o.navigator:s,n=e||(r&&r.userAgent?r.userAgent:""),a=r&&r.userAgentData?r.userAgentData:s,i=t?V(Q,t):Q,b=r&&r.userAgent==n;return this.getBrowser=function(){var e,t={};return t[f]=s,t[m]=s,X.call(t,n,i.browser),t[p]=typeof(e=t[m])===d?e.replace(/[^\d\.]/g,"").split(".")[0]:s,b&&r&&r.brave&&typeof r.brave.isBrave==c&&(t[f]="Brave"),t},this.getCPU=function(){var e={};return e[w]=s,X.call(e,n,i.cpu),e},this.getDevice=function(){var e={};return e[y]=s,e[h]=s,e[g]=s,X.call(e,n,i.device),b&&!e[g]&&a&&a.mobile&&(e[g]=v),b&&"Macintosh"==e[h]&&r&&typeof r.standalone!==l&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[h]="iPad",e[g]=_),e},this.getEngine=function(){var e={};return e[f]=s,e[m]=s,X.call(e,n,i.engine),e},this.getOS=function(){var e={};return e[f]=s,e[m]=s,X.call(e,n,i.os),b&&!e[f]&&a&&"Unknown"!=a.platform&&(e[f]=a.platform.replace(/chrome os/i,$).replace(/macos/i,B)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=typeof e===d&&e.length>350?F(e,350):e,this},this.setUA(n),this};ee.VERSION="1.0.35",ee.BROWSER=q([f,m,p]),ee.CPU=q([w]),ee.DEVICE=q([h,y,g,b,v,E,_,S,A]),ee.ENGINE=ee.OS=q([f,m]),typeof i!==l?(a.exports&&(i=a.exports=ee),i.UAParser=ee):r.amdO?void 0===(n=(function(){return ee}).call(t,r,t,e))||(e.exports=n):typeof o!==l&&(o.UAParser=ee);var et=typeof o!==l&&(o.jQuery||o.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},i={};function o(e){var t=i[e];if(void 0!==t)return t.exports;var r=i[e]={exports:{}},n=!0;try{a[e].call(r.exports,r,r.exports,o),n=!1}finally{n&&delete i[e]}return r.exports}o.ab="//",e.exports=o(226)})()},356:e=>{"use strict";e.exports=require("node:buffer")},427:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(620).xl)()},430:(e,t,r)=>{"use strict";r.d(t,{AA:()=>n,gW:()=>s,h:()=>a,kz:()=>i,r4:()=>o});let n="nxtP",a="nxtI",i="x-prerender-revalidate",o="x-prerender-revalidate-if-generated",s="_N_T_",c={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};({...c,GROUP:{builtinReact:[c.reactServerComponents,c.actionBrowser],serverOnly:[c.reactServerComponents,c.actionBrowser,c.instrument,c.middleware],neutralTarget:[c.apiNode,c.apiEdge],clientOnly:[c.serverSideRendering,c.appPagesBrowser],bundled:[c.reactServerComponents,c.actionBrowser,c.serverSideRendering,c.appPagesBrowser,c.shared,c.instrument,c.middleware],appPages:[c.reactServerComponents,c.serverSideRendering,c.appPagesBrowser,c.actionBrowser]}})},511:(e,t,r)=>{"use strict";var n=r(921);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.withAuth=c;var a=r(931),i=r(991),o=n(r(677));async function s(e,t,r){var n,s,c,l,u,d,p,h,f,g,y;let{pathname:m,search:w,origin:b,basePath:v}=e.nextUrl,_=null!=(n=null==t||null==(s=t.pages)?void 0:s.signIn)?n:"/api/auth/signin",E=null!=(c=null==t||null==(l=t.pages)?void 0:l.error)?c:"/api/auth/error",S=(0,o.default)(process.env.NEXTAUTH_URL).path;if(`${v}${m}`.startsWith(S)||[_,E].includes(m)||["/_next","/favicon.ico"].some(e=>m.startsWith(e)))return;let A=null!=(u=null!=(d=null==t?void 0:t.secret)?d:process.env.NEXTAUTH_SECRET)?u:process.env.AUTH_SECRET;if(!A){console.error("[next-auth][error][NO_SECRET]",`
https://next-auth.js.org/errors#no_secret`);let e=new URL(`${v}${E}`,b);return e.searchParams.append("error","Configuration"),a.NextResponse.redirect(e)}let x=await (0,i.getToken)({req:e,decode:null==t||null==(p=t.jwt)?void 0:p.decode,cookieName:null==t||null==(h=t.cookies)||null==(h=h.sessionToken)?void 0:h.name,secret:A});if(null!=(f=await (null==t||null==(g=t.callbacks)||null==(y=g.authorized)?void 0:y.call(g,{req:e,token:x})))?f:!!x)return await (null==r?void 0:r(x));let C=new URL(`${v}${_}`,b);return C.searchParams.append("callbackUrl",`${v}${m}${w}`),a.NextResponse.redirect(C)}function c(...e){if(!e.length||e[0]instanceof Request)return s(...e);if("function"==typeof e[0]){let t=e[0],r=e[1];return async(...e)=>await s(e[0],r,async r=>(e[0].nextauth={token:r},await t(...e)))}let t=e[0];return async(...e)=>await s(e[0],t)}t.default=c},521:e=>{"use strict";e.exports=require("node:async_hooks")},535:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});let n=(0,r(58).xl)()},537:(e,t,r)=>{"use strict";r.r(t),r.d(t,{CompactEncrypt:()=>tp,CompactSign:()=>tg,EmbeddedJWK:()=>tA,EncryptJWT:()=>tv,FlattenedEncrypt:()=>e6,FlattenedSign:()=>tf,GeneralEncrypt:()=>e3,GeneralSign:()=>tm,SignJWT:()=>tb,UnsecuredJWT:()=>tI,base64url:()=>a,calculateJwkThumbprint:()=>tE,calculateJwkThumbprintUri:()=>tS,compactDecrypt:()=>eY,compactVerify:()=>tr,createLocalJWKSet:()=>tO,createRemoteJWKSet:()=>tN,cryptoRuntime:()=>t$,decodeJwt:()=>tj,decodeProtectedHeader:()=>tD,errors:()=>n,exportJWK:()=>e2,exportPKCS8:()=>e1,exportSPKI:()=>e0,flattenedDecrypt:()=>eX,flattenedVerify:()=>tt,generalDecrypt:()=>eZ,generalVerify:()=>tn,generateKeyPair:()=>tK,generateSecret:()=>tJ,importJWK:()=>eU,importPKCS8:()=>ej,importSPKI:()=>eM,importX509:()=>eD,jwtDecrypt:()=>td,jwtVerify:()=>tu});var n={};r.r(n),r.d(n,{JOSEAlgNotAllowed:()=>S,JOSEError:()=>v,JOSENotSupported:()=>A,JWEDecompressionFailed:()=>C,JWEDecryptionFailed:()=>x,JWEInvalid:()=>P,JWKInvalid:()=>T,JWKSInvalid:()=>k,JWKSMultipleMatchingKeys:()=>I,JWKSNoMatchingKey:()=>N,JWKSTimeout:()=>H,JWSInvalid:()=>R,JWSSignatureVerificationFailed:()=>M,JWTClaimValidationFailed:()=>_,JWTExpired:()=>E,JWTInvalid:()=>O});var a={};r.r(a),r.d(a,{decode:()=>tM,encode:()=>tH});let i=crypto,o=e=>e instanceof CryptoKey,s=async(e,t)=>{let r=`SHA-${e.slice(-3)}`;return new Uint8Array(await i.subtle.digest(r,t))},c=new TextEncoder,l=new TextDecoder;function u(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;return e.forEach(e=>{t.set(e,r),r+=e.length}),t}function d(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function p(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return d(r,t,0),d(r,e%0x100000000,4),r}function h(e){let t=new Uint8Array(4);return d(t,e),t}function f(e){return u(h(e.length),e)}async function g(e,t,r){let n=Math.ceil((t>>3)/32),a=new Uint8Array(32*n);for(let t=0;t<n;t++){let n=new Uint8Array(4+e.length+r.length);n.set(h(t+1)),n.set(e,4),n.set(r,4+e.length),a.set(await s("sha256",n),32*t)}return a.slice(0,t>>3)}let y=e=>{let t=e;"string"==typeof t&&(t=c.encode(t));let r=[];for(let e=0;e<t.length;e+=32768)r.push(String.fromCharCode.apply(null,t.subarray(e,e+32768)));return btoa(r.join(""))},m=e=>y(e).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_"),w=e=>{let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r},b=e=>{let t=e;t instanceof Uint8Array&&(t=l.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return w(t)}catch(e){throw TypeError("The input to be decoded is not correctly encoded.")}};class v extends Error{static get code(){return"ERR_JOSE_GENERIC"}constructor(e){var t;super(e),this.code="ERR_JOSE_GENERIC",this.name=this.constructor.name,null==(t=Error.captureStackTrace)||t.call(Error,this,this.constructor)}}class _ extends v{static get code(){return"ERR_JWT_CLAIM_VALIDATION_FAILED"}constructor(e,t="unspecified",r="unspecified"){super(e),this.code="ERR_JWT_CLAIM_VALIDATION_FAILED",this.claim=t,this.reason=r}}class E extends v{static get code(){return"ERR_JWT_EXPIRED"}constructor(e,t="unspecified",r="unspecified"){super(e),this.code="ERR_JWT_EXPIRED",this.claim=t,this.reason=r}}class S extends v{constructor(){super(...arguments),this.code="ERR_JOSE_ALG_NOT_ALLOWED"}static get code(){return"ERR_JOSE_ALG_NOT_ALLOWED"}}class A extends v{constructor(){super(...arguments),this.code="ERR_JOSE_NOT_SUPPORTED"}static get code(){return"ERR_JOSE_NOT_SUPPORTED"}}class x extends v{constructor(){super(...arguments),this.code="ERR_JWE_DECRYPTION_FAILED",this.message="decryption operation failed"}static get code(){return"ERR_JWE_DECRYPTION_FAILED"}}class C extends v{constructor(){super(...arguments),this.code="ERR_JWE_DECOMPRESSION_FAILED",this.message="decompression operation failed"}static get code(){return"ERR_JWE_DECOMPRESSION_FAILED"}}class P extends v{constructor(){super(...arguments),this.code="ERR_JWE_INVALID"}static get code(){return"ERR_JWE_INVALID"}}class R extends v{constructor(){super(...arguments),this.code="ERR_JWS_INVALID"}static get code(){return"ERR_JWS_INVALID"}}class O extends v{constructor(){super(...arguments),this.code="ERR_JWT_INVALID"}static get code(){return"ERR_JWT_INVALID"}}class T extends v{constructor(){super(...arguments),this.code="ERR_JWK_INVALID"}static get code(){return"ERR_JWK_INVALID"}}class k extends v{constructor(){super(...arguments),this.code="ERR_JWKS_INVALID"}static get code(){return"ERR_JWKS_INVALID"}}class N extends v{constructor(){super(...arguments),this.code="ERR_JWKS_NO_MATCHING_KEY",this.message="no applicable key found in the JSON Web Key Set"}static get code(){return"ERR_JWKS_NO_MATCHING_KEY"}}class I extends v{constructor(){super(...arguments),this.code="ERR_JWKS_MULTIPLE_MATCHING_KEYS",this.message="multiple matching keys found in the JSON Web Key Set"}static get code(){return"ERR_JWKS_MULTIPLE_MATCHING_KEYS"}}Symbol.asyncIterator;class H extends v{constructor(){super(...arguments),this.code="ERR_JWKS_TIMEOUT",this.message="request timed out"}static get code(){return"ERR_JWKS_TIMEOUT"}}class M extends v{constructor(){super(...arguments),this.code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED",this.message="signature verification failed"}static get code(){return"ERR_JWS_SIGNATURE_VERIFICATION_FAILED"}}let D=i.getRandomValues.bind(i);function j(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new A(`Unsupported JWE Algorithm: ${e}`)}}let U=e=>D(new Uint8Array(j(e)>>3)),W=(e,t)=>{if(t.length<<3!==j(e))throw new P("Invalid Initialization Vector length")},L=(e,t)=>{let r=e.byteLength<<3;if(r!==t)throw new P(`Invalid Content Encryption Key length. Expected ${t} bits, got ${r} bits`)},K=(e,t)=>{if(!(e instanceof Uint8Array))throw TypeError("First argument must be a buffer");if(!(t instanceof Uint8Array))throw TypeError("Second argument must be a buffer");if(e.length!==t.length)throw TypeError("Input buffers must have the same length");let r=e.length,n=0,a=-1;for(;++a<r;)n|=e[a]^t[a];return 0===n};function J(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function $(e,t){return e.name===t}function B(e){return parseInt(e.name.slice(4),10)}function V(e,t){if(t.length&&!t.some(t=>e.usages.includes(t))){let e="CryptoKey does not support this operation, its usages must include ";if(t.length>2){let r=t.pop();e+=`one of ${t.join(", ")}, or ${r}.`}else 2===t.length?e+=`one of ${t[0]} or ${t[1]}.`:e+=`${t[0]}.`;throw TypeError(e)}}function q(e,t,...r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!$(e.algorithm,"AES-GCM"))throw J("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw J(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!$(e.algorithm,"AES-KW"))throw J("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw J(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":case"X448":break;default:throw J("ECDH, X25519, or X448")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!$(e.algorithm,"PBKDF2"))throw J("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!$(e.algorithm,"RSA-OAEP"))throw J("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(B(e.algorithm.hash)!==r)throw J(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}V(e,r)}function G(e,t,...r){if(r.length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor&&t.constructor.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let z=(e,...t)=>G("Key must be ",e,...t);function F(e,t,...r){return G(`Key for the ${e} algorithm must be `,t,...r)}let X=e=>o(e),Y=["CryptoKey"];async function Z(e,t,r,n,a,o){let s,c;if(!(t instanceof Uint8Array))throw TypeError(z(t,"Uint8Array"));let l=parseInt(e.slice(1,4),10),d=await i.subtle.importKey("raw",t.subarray(l>>3),"AES-CBC",!1,["decrypt"]),h=await i.subtle.importKey("raw",t.subarray(0,l>>3),{hash:`SHA-${l<<1}`,name:"HMAC"},!1,["sign"]),f=u(o,n,r,p(o.length<<3)),g=new Uint8Array((await i.subtle.sign("HMAC",h,f)).slice(0,l>>3));try{s=K(a,g)}catch(e){}if(!s)throw new x;try{c=new Uint8Array(await i.subtle.decrypt({iv:n,name:"AES-CBC"},d,r))}catch(e){}if(!c)throw new x;return c}async function Q(e,t,r,n,a,o){let s;t instanceof Uint8Array?s=await i.subtle.importKey("raw",t,"AES-GCM",!1,["decrypt"]):(q(t,e,"decrypt"),s=t);try{return new Uint8Array(await i.subtle.decrypt({additionalData:o,iv:n,name:"AES-GCM",tagLength:128},s,u(r,a)))}catch(e){throw new x}}let ee=async(e,t,r,n,a,i)=>{if(!o(t)&&!(t instanceof Uint8Array))throw TypeError(z(t,...Y,"Uint8Array"));switch(W(e,n),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return t instanceof Uint8Array&&L(t,parseInt(e.slice(-3),10)),Z(e,t,r,n,a,i);case"A128GCM":case"A192GCM":case"A256GCM":return t instanceof Uint8Array&&L(t,parseInt(e.slice(1,4),10)),Q(e,t,r,n,a,i);default:throw new A("Unsupported JWE Content Encryption Algorithm")}},et=async()=>{throw new A('JWE "zip" (Compression Algorithm) Header Parameter is not supported by your javascript runtime. You need to use the `inflateRaw` decrypt option to provide Inflate Raw implementation.')},er=async()=>{throw new A('JWE "zip" (Compression Algorithm) Header Parameter is not supported by your javascript runtime. You need to use the `deflateRaw` encrypt option to provide Deflate Raw implementation.')},en=(...e)=>{let t,r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0};function ea(e){if("object"!=typeof e||null===e||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}let ei=[{hash:"SHA-256",name:"HMAC"},!0,["sign"]];function eo(e,t){if(e.algorithm.length!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}function es(e,t,r){if(o(e))return q(e,t,r),e;if(e instanceof Uint8Array)return i.subtle.importKey("raw",e,"AES-KW",!0,[r]);throw TypeError(z(e,...Y,"Uint8Array"))}let ec=async(e,t,r)=>{let n=await es(t,e,"wrapKey");eo(n,e);let a=await i.subtle.importKey("raw",r,...ei);return new Uint8Array(await i.subtle.wrapKey("raw",a,n,"AES-KW"))},el=async(e,t,r)=>{let n=await es(t,e,"unwrapKey");eo(n,e);let a=await i.subtle.unwrapKey("raw",r,n,"AES-KW",...ei);return new Uint8Array(await i.subtle.exportKey("raw",a))};async function eu(e,t,r,n,a=new Uint8Array(0),s=new Uint8Array(0)){let l;if(!o(e))throw TypeError(z(e,...Y));if(q(e,"ECDH"),!o(t))throw TypeError(z(t,...Y));q(t,"ECDH","deriveBits");let d=u(f(c.encode(r)),f(a),f(s),h(n));return l="X25519"===e.algorithm.name?256:"X448"===e.algorithm.name?448:Math.ceil(parseInt(e.algorithm.namedCurve.substr(-3),10)/8)<<3,g(new Uint8Array(await i.subtle.deriveBits({name:e.algorithm.name,public:e},t,l)),n,d)}async function ed(e){if(!o(e))throw TypeError(z(e,...Y));return i.subtle.generateKey(e.algorithm,!0,["deriveBits"])}function ep(e){if(!o(e))throw TypeError(z(e,...Y));return["P-256","P-384","P-521"].includes(e.algorithm.namedCurve)||"X25519"===e.algorithm.name||"X448"===e.algorithm.name}async function eh(e,t,r,n){if(!(e instanceof Uint8Array)||e.length<8)throw new P("PBES2 Salt Input must be 8 or more octets");let a=u(c.encode(t),new Uint8Array([0]),e),s=parseInt(t.slice(13,16),10),l={hash:`SHA-${t.slice(8,11)}`,iterations:r,name:"PBKDF2",salt:a},d=await function(e,t){if(e instanceof Uint8Array)return i.subtle.importKey("raw",e,"PBKDF2",!1,["deriveBits"]);if(o(e))return q(e,t,"deriveBits","deriveKey"),e;throw TypeError(z(e,...Y,"Uint8Array"))}(n,t);if(d.usages.includes("deriveBits"))return new Uint8Array(await i.subtle.deriveBits(l,d,s));if(d.usages.includes("deriveKey"))return i.subtle.deriveKey(l,d,{length:s,name:"AES-KW"},!1,["wrapKey","unwrapKey"]);throw TypeError('PBKDF2 key "usages" must include "deriveBits" or "deriveKey"')}let ef=async(e,t,r,n=2048,a=D(new Uint8Array(16)))=>{let i=await eh(a,e,n,t);return{encryptedKey:await ec(e.slice(-6),i,r),p2c:n,p2s:m(a)}},eg=async(e,t,r,n,a)=>{let i=await eh(a,e,n,t);return el(e.slice(-6),i,r)};function ey(e){switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return"RSA-OAEP";default:throw new A(`alg ${e} is not supported either by JOSE or your javascript runtime`)}}let em=(e,t)=>{if(e.startsWith("RS")||e.startsWith("PS")){let{modulusLength:r}=t.algorithm;if("number"!=typeof r||r<2048)throw TypeError(`${e} requires key modulusLength to be 2048 bits or larger`)}},ew=async(e,t,r)=>{if(!o(t))throw TypeError(z(t,...Y));if(q(t,e,"encrypt","wrapKey"),em(e,t),t.usages.includes("encrypt"))return new Uint8Array(await i.subtle.encrypt(ey(e),t,r));if(t.usages.includes("wrapKey")){let n=await i.subtle.importKey("raw",r,...ei);return new Uint8Array(await i.subtle.wrapKey("raw",n,t,ey(e)))}throw TypeError('RSA-OAEP key "usages" must include "encrypt" or "wrapKey" for this operation')},eb=async(e,t,r)=>{if(!o(t))throw TypeError(z(t,...Y));if(q(t,e,"decrypt","unwrapKey"),em(e,t),t.usages.includes("decrypt"))return new Uint8Array(await i.subtle.decrypt(ey(e),t,r));if(t.usages.includes("unwrapKey")){let n=await i.subtle.unwrapKey("raw",r,t,ey(e),...ei);return new Uint8Array(await i.subtle.exportKey("raw",n))}throw TypeError('RSA-OAEP key "usages" must include "decrypt" or "unwrapKey" for this operation')};function ev(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new A(`Unsupported JWE Algorithm: ${e}`)}}let e_=e=>D(new Uint8Array(ev(e)>>3)),eE=(e,t)=>{let r=(e.match(/.{1,64}/g)||[]).join("\n");return`-----BEGIN ${t}-----
${r}
-----END ${t}-----`},eS=async(e,t,r)=>{if(!o(r))throw TypeError(z(r,...Y));if(!r.extractable)throw TypeError("CryptoKey is not extractable");if(r.type!==e)throw TypeError(`key is not a ${e} key`);return eE(y(new Uint8Array(await i.subtle.exportKey(t,r))),`${e.toUpperCase()} KEY`)},eA=e=>eS("public","spki",e),ex=e=>eS("private","pkcs8",e),eC=(e,t,r=0)=>{0===r&&(t.unshift(t.length),t.unshift(6));let n=e.indexOf(t[0],r);if(-1===n)return!1;let a=e.subarray(n,n+t.length);return a.length===t.length&&(a.every((e,r)=>e===t[r])||eC(e,t,n+1))},eP=e=>{switch(!0){case eC(e,[42,134,72,206,61,3,1,7]):return"P-256";case eC(e,[43,129,4,0,34]):return"P-384";case eC(e,[43,129,4,0,35]):return"P-521";case eC(e,[43,101,110]):return"X25519";case eC(e,[43,101,111]):return"X448";case eC(e,[43,101,112]):return"Ed25519";case eC(e,[43,101,113]):return"Ed448";default:throw new A("Invalid or unsupported EC Key Curve or OKP Key Sub Type")}},eR=async(e,t,r,n,a)=>{var o;let s,c,l=new Uint8Array(atob(r.replace(e,"")).split("").map(e=>e.charCodeAt(0))),u="spki"===t;switch(n){case"PS256":case"PS384":case"PS512":s={name:"RSA-PSS",hash:`SHA-${n.slice(-3)}`},c=u?["verify"]:["sign"];break;case"RS256":case"RS384":case"RS512":s={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${n.slice(-3)}`},c=u?["verify"]:["sign"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":s={name:"RSA-OAEP",hash:`SHA-${parseInt(n.slice(-3),10)||1}`},c=u?["encrypt","wrapKey"]:["decrypt","unwrapKey"];break;case"ES256":s={name:"ECDSA",namedCurve:"P-256"},c=u?["verify"]:["sign"];break;case"ES384":s={name:"ECDSA",namedCurve:"P-384"},c=u?["verify"]:["sign"];break;case"ES512":s={name:"ECDSA",namedCurve:"P-521"},c=u?["verify"]:["sign"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let e=eP(l);s=e.startsWith("P-")?{name:"ECDH",namedCurve:e}:{name:e},c=u?[]:["deriveBits"];break}case"EdDSA":s={name:eP(l)},c=u?["verify"]:["sign"];break;default:throw new A('Invalid or unsupported "alg" (Algorithm) value')}return i.subtle.importKey(t,l,s,null!=(o=null==a?void 0:a.extractable)&&o,c)},eO=(e,t,r)=>eR(/(?:-----(?:BEGIN|END) PRIVATE KEY-----|\s)/g,"pkcs8",e,t,r),eT=(e,t,r)=>eR(/(?:-----(?:BEGIN|END) PUBLIC KEY-----|\s)/g,"spki",e,t,r);function ek(e){let t=[],r=0;for(;r<e.length;){let n=eN(e.subarray(r));t.push(n),r+=n.byteLength}return t}function eN(e){let t=0,r=31&e[0];if(t++,31===r){for(r=0;e[t]>=128;)r=128*r+e[t]-128,t++;r=128*r+e[t]-128,t++}let n=0;if(e[t]<128)n=e[t],t++;else if(128===n){for(n=0;0!==e[t+n]||0!==e[t+n+1];){if(n>e.byteLength)throw TypeError("invalid indefinite form length");n++}let r=t+n+2;return{byteLength:r,contents:e.subarray(t,t+n),raw:e.subarray(0,r)}}else{let r=127&e[t];t++,n=0;for(let a=0;a<r;a++)n=256*n+e[t],t++}let a=t+n;return{byteLength:a,contents:e.subarray(t,a),raw:e.subarray(0,a)}}let eI=(e,t,r)=>{let n;try{n=eE(function(e){let t=ek(ek(eN(e).contents)[0].contents);return y(t[160===t[0].raw[0]?6:5].raw)}(w(e.replace(/(?:-----(?:BEGIN|END) CERTIFICATE-----|\s)/g,""))),"PUBLIC KEY")}catch(e){throw TypeError("Failed to parse the X.509 certificate",{cause:e})}return eT(n,t,r)},eH=async e=>{var t,r;if(!e.alg)throw TypeError('"alg" argument is required when "jwk.alg" is not present');let{algorithm:n,keyUsages:a}=function(e){let t,r;switch(e.kty){case"oct":switch(e.alg){case"HS256":case"HS384":case"HS512":t={name:"HMAC",hash:`SHA-${e.alg.slice(-3)}`},r=["sign","verify"];break;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":throw new A(`${e.alg} keys cannot be imported as CryptoKey instances`);case"A128GCM":case"A192GCM":case"A256GCM":case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":t={name:"AES-GCM"},r=["encrypt","decrypt"];break;case"A128KW":case"A192KW":case"A256KW":t={name:"AES-KW"},r=["wrapKey","unwrapKey"];break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":t={name:"PBKDF2"},r=["deriveBits"];break;default:throw new A('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"RSA":switch(e.alg){case"PS256":case"PS384":case"PS512":t={name:"RSA-PSS",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RS256":case"RS384":case"RS512":t={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.alg.slice(-3)}`},r=e.d?["sign"]:["verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":t={name:"RSA-OAEP",hash:`SHA-${parseInt(e.alg.slice(-3),10)||1}`},r=e.d?["decrypt","unwrapKey"]:["encrypt","wrapKey"];break;default:throw new A('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"EC":switch(e.alg){case"ES256":t={name:"ECDSA",namedCurve:"P-256"},r=e.d?["sign"]:["verify"];break;case"ES384":t={name:"ECDSA",namedCurve:"P-384"},r=e.d?["sign"]:["verify"];break;case"ES512":t={name:"ECDSA",namedCurve:"P-521"},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:"ECDH",namedCurve:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new A('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;case"OKP":switch(e.alg){case"EdDSA":t={name:e.crv},r=e.d?["sign"]:["verify"];break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":t={name:e.crv},r=e.d?["deriveBits"]:[];break;default:throw new A('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}break;default:throw new A('Invalid or unsupported JWK "kty" (Key Type) Parameter value')}return{algorithm:t,keyUsages:r}}(e),o=[n,null!=(t=e.ext)&&t,null!=(r=e.key_ops)?r:a];if("PBKDF2"===n.name)return i.subtle.importKey("raw",b(e.k),...o);let s={...e};return delete s.alg,delete s.use,i.subtle.importKey("jwk",s,...o)};async function eM(e,t,r){if("string"!=typeof e||0!==e.indexOf("-----BEGIN PUBLIC KEY-----"))throw TypeError('"spki" must be SPKI formatted string');return eT(e,t,r)}async function eD(e,t,r){if("string"!=typeof e||0!==e.indexOf("-----BEGIN CERTIFICATE-----"))throw TypeError('"x509" must be X.509 formatted string');return eI(e,t,r)}async function ej(e,t,r){if("string"!=typeof e||0!==e.indexOf("-----BEGIN PRIVATE KEY-----"))throw TypeError('"pkcs8" must be PKCS#8 formatted string');return eO(e,t,r)}async function eU(e,t,r){var n;if(!ea(e))throw TypeError("JWK must be an object");switch(t||(t=e.alg),e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');if(null!=r||(r=!0!==e.ext),r)return eH({...e,alg:t,ext:null!=(n=e.ext)&&n});return b(e.k);case"RSA":if(void 0!==e.oth)throw new A('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return eH({...e,alg:t});default:throw new A('Unsupported "kty" (Key Type) Parameter value')}}let eW=(e,t)=>{if(!(t instanceof Uint8Array)){if(!X(t))throw TypeError(F(e,t,...Y,"Uint8Array"));if("secret"!==t.type)throw TypeError(`${Y.join(" or ")} instances for symmetric algorithms must be of type "secret"`)}},eL=(e,t,r)=>{if(!X(t))throw TypeError(F(e,t,...Y));if("secret"===t.type)throw TypeError(`${Y.join(" or ")} instances for asymmetric algorithms must not be of type "secret"`);if("sign"===r&&"public"===t.type)throw TypeError(`${Y.join(" or ")} instances for asymmetric algorithm signing must be of type "private"`);if("decrypt"===r&&"public"===t.type)throw TypeError(`${Y.join(" or ")} instances for asymmetric algorithm decryption must be of type "private"`);if(t.algorithm&&"verify"===r&&"private"===t.type)throw TypeError(`${Y.join(" or ")} instances for asymmetric algorithm verifying must be of type "public"`);if(t.algorithm&&"encrypt"===r&&"private"===t.type)throw TypeError(`${Y.join(" or ")} instances for asymmetric algorithm encryption must be of type "public"`)},eK=(e,t,r)=>{e.startsWith("HS")||"dir"===e||e.startsWith("PBES2")||/^A\d{3}(?:GCM)?KW$/.test(e)?eW(e,t):eL(e,t,r)};async function eJ(e,t,r,n,a){if(!(r instanceof Uint8Array))throw TypeError(z(r,"Uint8Array"));let o=parseInt(e.slice(1,4),10),s=await i.subtle.importKey("raw",r.subarray(o>>3),"AES-CBC",!1,["encrypt"]),c=await i.subtle.importKey("raw",r.subarray(0,o>>3),{hash:`SHA-${o<<1}`,name:"HMAC"},!1,["sign"]),l=new Uint8Array(await i.subtle.encrypt({iv:n,name:"AES-CBC"},s,t)),d=u(a,n,l,p(a.length<<3));return{ciphertext:l,tag:new Uint8Array((await i.subtle.sign("HMAC",c,d)).slice(0,o>>3))}}async function e$(e,t,r,n,a){let o;r instanceof Uint8Array?o=await i.subtle.importKey("raw",r,"AES-GCM",!1,["encrypt"]):(q(r,e,"encrypt"),o=r);let s=new Uint8Array(await i.subtle.encrypt({additionalData:a,iv:n,name:"AES-GCM",tagLength:128},o,t)),c=s.slice(-16);return{ciphertext:s.slice(0,-16),tag:c}}let eB=async(e,t,r,n,a)=>{if(!o(r)&&!(r instanceof Uint8Array))throw TypeError(z(r,...Y,"Uint8Array"));switch(W(e,n),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return r instanceof Uint8Array&&L(r,parseInt(e.slice(-3),10)),eJ(e,t,r,n,a);case"A128GCM":case"A192GCM":case"A256GCM":return r instanceof Uint8Array&&L(r,parseInt(e.slice(1,4),10)),e$(e,t,r,n,a);default:throw new A("Unsupported JWE Content Encryption Algorithm")}};async function eV(e,t,r,n){let a=e.slice(0,7);n||(n=U(a));let{ciphertext:i,tag:o}=await eB(a,r,t,n,new Uint8Array(0));return{encryptedKey:i,iv:m(n),tag:m(o)}}async function eq(e,t,r,n,a){return ee(e.slice(0,7),t,r,n,a,new Uint8Array(0))}async function eG(e,t,r,n,a){switch(eK(e,t,"decrypt"),e){case"dir":if(void 0!==r)throw new P("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new P("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let a,i;if(!ea(n.epk))throw new P('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(!ep(t))throw new A("ECDH with the provided key is not allowed or not supported by your javascript runtime");let o=await eU(n.epk,e);if(void 0!==n.apu){if("string"!=typeof n.apu)throw new P('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{a=b(n.apu)}catch(e){throw new P("Failed to base64url decode the apu")}}if(void 0!==n.apv){if("string"!=typeof n.apv)throw new P('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{i=b(n.apv)}catch(e){throw new P("Failed to base64url decode the apv")}}let s=await eu(o,t,"ECDH-ES"===e?n.enc:e,"ECDH-ES"===e?ev(n.enc):parseInt(e.slice(-5,-2),10),a,i);if("ECDH-ES"===e)return s;if(void 0===r)throw new P("JWE Encrypted Key missing");return el(e.slice(-6),s,r)}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new P("JWE Encrypted Key missing");return eb(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let i;if(void 0===r)throw new P("JWE Encrypted Key missing");if("number"!=typeof n.p2c)throw new P('JOSE Header "p2c" (PBES2 Count) missing or invalid');let o=(null==a?void 0:a.maxPBES2Count)||1e4;if(n.p2c>o)throw new P('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof n.p2s)throw new P('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{i=b(n.p2s)}catch(e){throw new P("Failed to base64url decode the p2s")}return eg(e,t,r,n.p2c,i)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new P("JWE Encrypted Key missing");return el(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let a,i;if(void 0===r)throw new P("JWE Encrypted Key missing");if("string"!=typeof n.iv)throw new P('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof n.tag)throw new P('JOSE Header "tag" (Authentication Tag) missing or invalid');try{a=b(n.iv)}catch(e){throw new P("Failed to base64url decode the iv")}try{i=b(n.tag)}catch(e){throw new P("Failed to base64url decode the tag")}return eq(e,t,r,a,i)}default:throw new A('Invalid or unsupported "alg" (JWE Algorithm) header value')}}let ez=function(e,t,r,n,a){let i;if(void 0!==a.crit&&void 0===n.crit)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!n||void 0===n.crit)return new Set;if(!Array.isArray(n.crit)||0===n.crit.length||n.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let o of(i=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,n.crit)){if(!i.has(o))throw new A(`Extension Header Parameter "${o}" is not recognized`);if(void 0===a[o])throw new e(`Extension Header Parameter "${o}" is missing`);if(i.get(o)&&void 0===n[o])throw new e(`Extension Header Parameter "${o}" MUST be integrity protected`)}return new Set(n.crit)},eF=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};async function eX(e,t,r){var n;let a,i,o,s,d,p,h;if(!ea(e))throw new P("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new P("JOSE Header missing");if("string"!=typeof e.iv)throw new P("JWE Initialization Vector missing or incorrect type");if("string"!=typeof e.ciphertext)throw new P("JWE Ciphertext missing or incorrect type");if("string"!=typeof e.tag)throw new P("JWE Authentication Tag missing or incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new P("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new P("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new P("JWE AAD incorrect type");if(void 0!==e.header&&!ea(e.header))throw new P("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!ea(e.unprotected))throw new P("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=b(e.protected);a=JSON.parse(l.decode(t))}catch(e){throw new P("JWE Protected Header is invalid")}if(!en(a,e.header,e.unprotected))throw new P("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let f={...a,...e.header,...e.unprotected};if(ez(P,new Map,null==r?void 0:r.crit,a,f),void 0!==f.zip){if(!a||!a.zip)throw new P('JWE "zip" (Compression Algorithm) Header MUST be integrity protected');if("DEF"!==f.zip)throw new A('Unsupported JWE "zip" (Compression Algorithm) Header Parameter value')}let{alg:g,enc:y}=f;if("string"!=typeof g||!g)throw new P("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof y||!y)throw new P("missing JWE Encryption Algorithm (enc) in JWE Header");let m=r&&eF("keyManagementAlgorithms",r.keyManagementAlgorithms),w=r&&eF("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(m&&!m.has(g))throw new S('"alg" (Algorithm) Header Parameter not allowed');if(w&&!w.has(y))throw new S('"enc" (Encryption Algorithm) Header Parameter not allowed');if(void 0!==e.encrypted_key)try{i=b(e.encrypted_key)}catch(e){throw new P("Failed to base64url decode the encrypted_key")}let v=!1;"function"==typeof t&&(t=await t(a,e),v=!0);try{o=await eG(g,t,i,f,r)}catch(e){if(e instanceof TypeError||e instanceof P||e instanceof A)throw e;o=e_(y)}try{s=b(e.iv)}catch(e){throw new P("Failed to base64url decode the iv")}try{d=b(e.tag)}catch(e){throw new P("Failed to base64url decode the tag")}let _=c.encode(null!=(n=e.protected)?n:"");p=void 0!==e.aad?u(_,c.encode("."),c.encode(e.aad)):_;try{h=b(e.ciphertext)}catch(e){throw new P("Failed to base64url decode the ciphertext")}let E=await ee(y,o,h,s,d,p);"DEF"===f.zip&&(E=await ((null==r?void 0:r.inflateRaw)||et)(E));let x={plaintext:E};if(void 0!==e.protected&&(x.protectedHeader=a),void 0!==e.aad)try{x.additionalAuthenticatedData=b(e.aad)}catch(e){throw new P("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(x.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(x.unprotectedHeader=e.header),v)?{...x,key:t}:x}async function eY(e,t,r){if(e instanceof Uint8Array&&(e=l.decode(e)),"string"!=typeof e)throw new P("Compact JWE must be a string or Uint8Array");let{0:n,1:a,2:i,3:o,4:s,length:c}=e.split(".");if(5!==c)throw new P("Invalid Compact JWE");let u=await eX({ciphertext:o,iv:i||void 0,protected:n||void 0,tag:s||void 0,encrypted_key:a||void 0},t,r),d={plaintext:u.plaintext,protectedHeader:u.protectedHeader};return"function"==typeof t?{...d,key:u.key}:d}async function eZ(e,t,r){if(!ea(e))throw new P("General JWE must be an object");if(!Array.isArray(e.recipients)||!e.recipients.every(ea))throw new P("JWE Recipients missing or incorrect type");if(!e.recipients.length)throw new P("JWE Recipients has no members");for(let n of e.recipients)try{return await eX({aad:e.aad,ciphertext:e.ciphertext,encrypted_key:n.encrypted_key,header:n.header,iv:e.iv,protected:e.protected,tag:e.tag,unprotected:e.unprotected},t,r)}catch(e){}throw new x}let eQ=async e=>{if(e instanceof Uint8Array)return{kty:"oct",k:m(e)};if(!o(e))throw TypeError(z(e,...Y,"Uint8Array"));if(!e.extractable)throw TypeError("non-extractable CryptoKey cannot be exported as a JWK");let{ext:t,key_ops:r,alg:n,use:a,...s}=await i.subtle.exportKey("jwk",e);return s};async function e0(e){return eA(e)}async function e1(e){return ex(e)}async function e2(e){return eQ(e)}async function e5(e,t,r,n,a={}){let i,o,s;switch(eK(e,r,"encrypt"),e){case"dir":s=r;break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{if(!ep(r))throw new A("ECDH with the provided key is not allowed or not supported by your javascript runtime");let{apu:c,apv:l}=a,{epk:u}=a;u||(u=(await ed(r)).privateKey);let{x:d,y:p,crv:h,kty:f}=await e2(u),g=await eu(r,u,"ECDH-ES"===e?t:e,"ECDH-ES"===e?ev(t):parseInt(e.slice(-5,-2),10),c,l);if(o={epk:{x:d,crv:h,kty:f}},"EC"===f&&(o.epk.y=p),c&&(o.apu=m(c)),l&&(o.apv=m(l)),"ECDH-ES"===e){s=g;break}s=n||e_(t);let y=e.slice(-6);i=await ec(y,g,s);break}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":s=n||e_(t),i=await ew(e,r,s);break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{s=n||e_(t);let{p2c:c,p2s:l}=a;({encryptedKey:i,...o}=await ef(e,r,s,c,l));break}case"A128KW":case"A192KW":case"A256KW":s=n||e_(t),i=await ec(e,r,s);break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{s=n||e_(t);let{iv:c}=a;({encryptedKey:i,...o}=await eV(e,r,s,c));break}default:throw new A('Invalid or unsupported "alg" (JWE Algorithm) header value')}return{cek:s,encryptedKey:i,parameters:o}}let e4=Symbol();class e6{constructor(e){if(!(e instanceof Uint8Array))throw TypeError("plaintext must be an instance of Uint8Array");this._plaintext=e}setKeyManagementParameters(e){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=e,this}setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setSharedUnprotectedHeader(e){if(this._sharedUnprotectedHeader)throw TypeError("setSharedUnprotectedHeader can only be called once");return this._sharedUnprotectedHeader=e,this}setUnprotectedHeader(e){if(this._unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}setAdditionalAuthenticatedData(e){return this._aad=e,this}setContentEncryptionKey(e){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=e,this}setInitializationVector(e){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=e,this}async encrypt(e,t){let r,n,a,i,o,s,d;if(!this._protectedHeader&&!this._unprotectedHeader&&!this._sharedUnprotectedHeader)throw new P("either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()");if(!en(this._protectedHeader,this._unprotectedHeader,this._sharedUnprotectedHeader))throw new P("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let p={...this._protectedHeader,...this._unprotectedHeader,...this._sharedUnprotectedHeader};if(ez(P,new Map,null==t?void 0:t.crit,this._protectedHeader,p),void 0!==p.zip){if(!this._protectedHeader||!this._protectedHeader.zip)throw new P('JWE "zip" (Compression Algorithm) Header MUST be integrity protected');if("DEF"!==p.zip)throw new A('Unsupported JWE "zip" (Compression Algorithm) Header Parameter value')}let{alg:h,enc:f}=p;if("string"!=typeof h||!h)throw new P('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("string"!=typeof f||!f)throw new P('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if("dir"===h){if(this._cek)throw TypeError("setContentEncryptionKey cannot be called when using Direct Encryption")}else if("ECDH-ES"===h&&this._cek)throw TypeError("setContentEncryptionKey cannot be called when using Direct Key Agreement");{let a;({cek:n,encryptedKey:r,parameters:a}=await e5(h,f,e,this._cek,this._keyManagementParameters)),a&&(t&&e4 in t?this._unprotectedHeader?this._unprotectedHeader={...this._unprotectedHeader,...a}:this.setUnprotectedHeader(a):this._protectedHeader?this._protectedHeader={...this._protectedHeader,...a}:this.setProtectedHeader(a))}if(this._iv||(this._iv=U(f)),i=this._protectedHeader?c.encode(m(JSON.stringify(this._protectedHeader))):c.encode(""),this._aad?(o=m(this._aad),a=u(i,c.encode("."),c.encode(o))):a=i,"DEF"===p.zip){let e=await ((null==t?void 0:t.deflateRaw)||er)(this._plaintext);({ciphertext:s,tag:d}=await eB(f,e,n,this._iv,a))}else({ciphertext:s,tag:d}=await eB(f,this._plaintext,n,this._iv,a));let g={ciphertext:m(s),iv:m(this._iv),tag:m(d)};return r&&(g.encrypted_key=m(r)),o&&(g.aad=o),this._protectedHeader&&(g.protected=l.decode(i)),this._sharedUnprotectedHeader&&(g.unprotected=this._sharedUnprotectedHeader),this._unprotectedHeader&&(g.header=this._unprotectedHeader),g}}class e8{constructor(e,t,r){this.parent=e,this.key=t,this.options=r}setUnprotectedHeader(e){if(this.unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this.unprotectedHeader=e,this}addRecipient(...e){return this.parent.addRecipient(...e)}encrypt(...e){return this.parent.encrypt(...e)}done(){return this.parent}}class e3{constructor(e){this._recipients=[],this._plaintext=e}addRecipient(e,t){let r=new e8(this,e,{crit:null==t?void 0:t.crit});return this._recipients.push(r),r}setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setSharedUnprotectedHeader(e){if(this._unprotectedHeader)throw TypeError("setSharedUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}setAdditionalAuthenticatedData(e){return this._aad=e,this}async encrypt(e){var t,r,n;let a;if(!this._recipients.length)throw new P("at least one recipient must be added");if(e={deflateRaw:null==e?void 0:e.deflateRaw},1===this._recipients.length){let[t]=this._recipients,r=await new e6(this._plaintext).setAdditionalAuthenticatedData(this._aad).setProtectedHeader(this._protectedHeader).setSharedUnprotectedHeader(this._unprotectedHeader).setUnprotectedHeader(t.unprotectedHeader).encrypt(t.key,{...t.options,...e}),n={ciphertext:r.ciphertext,iv:r.iv,recipients:[{}],tag:r.tag};return r.aad&&(n.aad=r.aad),r.protected&&(n.protected=r.protected),r.unprotected&&(n.unprotected=r.unprotected),r.encrypted_key&&(n.recipients[0].encrypted_key=r.encrypted_key),r.header&&(n.recipients[0].header=r.header),n}for(let e=0;e<this._recipients.length;e++){let t=this._recipients[e];if(!en(this._protectedHeader,this._unprotectedHeader,t.unprotectedHeader))throw new P("JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint");let r={...this._protectedHeader,...this._unprotectedHeader,...t.unprotectedHeader},{alg:n}=r;if("string"!=typeof n||!n)throw new P('JWE "alg" (Algorithm) Header Parameter missing or invalid');if("dir"===n||"ECDH-ES"===n)throw new P('"dir" and "ECDH-ES" alg may only be used with a single recipient');if("string"!=typeof r.enc||!r.enc)throw new P('JWE "enc" (Encryption Algorithm) Header Parameter missing or invalid');if(a){if(a!==r.enc)throw new P('JWE "enc" (Encryption Algorithm) Header Parameter must be the same for all recipients')}else a=r.enc;if(ez(P,new Map,t.options.crit,this._protectedHeader,r),void 0!==r.zip&&(!this._protectedHeader||!this._protectedHeader.zip))throw new P('JWE "zip" (Compression Algorithm) Header MUST be integrity protected')}let i=e_(a),o={ciphertext:"",iv:"",recipients:[],tag:""};for(let s=0;s<this._recipients.length;s++){let c=this._recipients[s],l={};o.recipients.push(l);let u=({...this._protectedHeader,...this._unprotectedHeader,...c.unprotectedHeader}).alg.startsWith("PBES2")?2048+s:void 0;if(0===s){let t=await new e6(this._plaintext).setAdditionalAuthenticatedData(this._aad).setContentEncryptionKey(i).setProtectedHeader(this._protectedHeader).setSharedUnprotectedHeader(this._unprotectedHeader).setUnprotectedHeader(c.unprotectedHeader).setKeyManagementParameters({p2c:u}).encrypt(c.key,{...c.options,...e,[e4]:!0});o.ciphertext=t.ciphertext,o.iv=t.iv,o.tag=t.tag,t.aad&&(o.aad=t.aad),t.protected&&(o.protected=t.protected),t.unprotected&&(o.unprotected=t.unprotected),l.encrypted_key=t.encrypted_key,t.header&&(l.header=t.header);continue}let{encryptedKey:d,parameters:p}=await e5((null==(t=c.unprotectedHeader)?void 0:t.alg)||(null==(r=this._protectedHeader)?void 0:r.alg)||(null==(n=this._unprotectedHeader)?void 0:n.alg),a,c.key,i,{p2c:u});l.encrypted_key=m(d),(c.unprotectedHeader||p)&&(l.header={...c.unprotectedHeader,...p})}return o}}function e9(e,t){let r=`SHA-${e.slice(-3)}`;switch(e){case"HS256":case"HS384":case"HS512":return{hash:r,name:"HMAC"};case"PS256":case"PS384":case"PS512":return{hash:r,name:"RSA-PSS",saltLength:e.slice(-3)>>3};case"RS256":case"RS384":case"RS512":return{hash:r,name:"RSASSA-PKCS1-v1_5"};case"ES256":case"ES384":case"ES512":return{hash:r,name:"ECDSA",namedCurve:t.namedCurve};case"EdDSA":return{name:t.name};default:throw new A(`alg ${e} is not supported either by JOSE or your javascript runtime`)}}function e7(e,t,r){if(o(t))return!function(e,t,...r){switch(t){case"HS256":case"HS384":case"HS512":{if(!$(e.algorithm,"HMAC"))throw J("HMAC");let r=parseInt(t.slice(2),10);if(B(e.algorithm.hash)!==r)throw J(`SHA-${r}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!$(e.algorithm,"RSASSA-PKCS1-v1_5"))throw J("RSASSA-PKCS1-v1_5");let r=parseInt(t.slice(2),10);if(B(e.algorithm.hash)!==r)throw J(`SHA-${r}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!$(e.algorithm,"RSA-PSS"))throw J("RSA-PSS");let r=parseInt(t.slice(2),10);if(B(e.algorithm.hash)!==r)throw J(`SHA-${r}`,"algorithm.hash");break}case"EdDSA":if("Ed25519"!==e.algorithm.name&&"Ed448"!==e.algorithm.name)throw J("Ed25519 or Ed448");break;case"ES256":case"ES384":case"ES512":{if(!$(e.algorithm,"ECDSA"))throw J("ECDSA");let r=function(e){switch(e){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw Error("unreachable")}}(t);if(e.algorithm.namedCurve!==r)throw J(r,"algorithm.namedCurve");break}default:throw TypeError("CryptoKey does not support this operation")}V(e,r)}(t,e,r),t;if(t instanceof Uint8Array){if(!e.startsWith("HS"))throw TypeError(z(t,...Y));return i.subtle.importKey("raw",t,{hash:`SHA-${e.slice(-3)}`,name:"HMAC"},!1,[r])}throw TypeError(z(t,...Y,"Uint8Array"))}let te=async(e,t,r,n)=>{let a=await e7(e,t,"verify");em(e,a);let o=e9(e,a.algorithm);try{return await i.subtle.verify(o,a,r,n)}catch(e){return!1}};async function tt(e,t,r){var n;let a,i;if(!ea(e))throw new R("Flattened JWS must be an object");if(void 0===e.protected&&void 0===e.header)throw new R('Flattened JWS must have either of the "protected" or "header" members');if(void 0!==e.protected&&"string"!=typeof e.protected)throw new R("JWS Protected Header incorrect type");if(void 0===e.payload)throw new R("JWS Payload missing");if("string"!=typeof e.signature)throw new R("JWS Signature missing or incorrect type");if(void 0!==e.header&&!ea(e.header))throw new R("JWS Unprotected Header incorrect type");let o={};if(e.protected)try{let t=b(e.protected);o=JSON.parse(l.decode(t))}catch(e){throw new R("JWS Protected Header is invalid")}if(!en(o,e.header))throw new R("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let s={...o,...e.header},d=ez(R,new Map([["b64",!0]]),null==r?void 0:r.crit,o,s),p=!0;if(d.has("b64")&&"boolean"!=typeof(p=o.b64))throw new R('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:h}=s;if("string"!=typeof h||!h)throw new R('JWS "alg" (Algorithm) Header Parameter missing or invalid');let f=r&&eF("algorithms",r.algorithms);if(f&&!f.has(h))throw new S('"alg" (Algorithm) Header Parameter not allowed');if(p){if("string"!=typeof e.payload)throw new R("JWS Payload must be a string")}else if("string"!=typeof e.payload&&!(e.payload instanceof Uint8Array))throw new R("JWS Payload must be a string or an Uint8Array instance");let g=!1;"function"==typeof t&&(t=await t(o,e),g=!0),eK(h,t,"verify");let y=u(c.encode(null!=(n=e.protected)?n:""),c.encode("."),"string"==typeof e.payload?c.encode(e.payload):e.payload);try{a=b(e.signature)}catch(e){throw new R("Failed to base64url decode the signature")}if(!await te(h,t,a,y))throw new M;if(p)try{i=b(e.payload)}catch(e){throw new R("Failed to base64url decode the payload")}else i="string"==typeof e.payload?c.encode(e.payload):e.payload;let m={payload:i};return(void 0!==e.protected&&(m.protectedHeader=o),void 0!==e.header&&(m.unprotectedHeader=e.header),g)?{...m,key:t}:m}async function tr(e,t,r){if(e instanceof Uint8Array&&(e=l.decode(e)),"string"!=typeof e)throw new R("Compact JWS must be a string or Uint8Array");let{0:n,1:a,2:i,length:o}=e.split(".");if(3!==o)throw new R("Invalid Compact JWS");let s=await tt({payload:a,protected:n,signature:i},t,r),c={payload:s.payload,protectedHeader:s.protectedHeader};return"function"==typeof t?{...c,key:s.key}:c}async function tn(e,t,r){if(!ea(e))throw new R("General JWS must be an object");if(!Array.isArray(e.signatures)||!e.signatures.every(ea))throw new R("JWS Signatures missing or incorrect type");for(let n of e.signatures)try{return await tt({header:n.header,payload:e.payload,protected:n.protected,signature:n.signature},t,r)}catch(e){}throw new M}let ta=e=>Math.floor(e.getTime()/1e3),ti=/^(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)$/i,to=e=>{let t=ti.exec(e);if(!t)throw TypeError("Invalid time period format");let r=parseFloat(t[1]);switch(t[2].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":return Math.round(r);case"minute":case"minutes":case"min":case"mins":case"m":return Math.round(60*r);case"hour":case"hours":case"hr":case"hrs":case"h":return Math.round(3600*r);case"day":case"days":case"d":return Math.round(86400*r);case"week":case"weeks":case"w":return Math.round(604800*r);default:return Math.round(0x1e187e0*r)}},ts=e=>e.toLowerCase().replace(/^application\//,""),tc=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e))),tl=(e,t,r={})=>{let n,a,{typ:i}=r;if(i&&("string"!=typeof e.typ||ts(e.typ)!==ts(i)))throw new _('unexpected "typ" JWT header value',"typ","check_failed");try{n=JSON.parse(l.decode(t))}catch(e){}if(!ea(n))throw new O("JWT Claims Set must be a top-level JSON object");let{requiredClaims:o=[],issuer:s,subject:c,audience:u,maxTokenAge:d}=r;for(let e of(void 0!==d&&o.push("iat"),void 0!==u&&o.push("aud"),void 0!==c&&o.push("sub"),void 0!==s&&o.push("iss"),new Set(o.reverse())))if(!(e in n))throw new _(`missing required "${e}" claim`,e,"missing");if(s&&!(Array.isArray(s)?s:[s]).includes(n.iss))throw new _('unexpected "iss" claim value',"iss","check_failed");if(c&&n.sub!==c)throw new _('unexpected "sub" claim value',"sub","check_failed");if(u&&!tc(n.aud,"string"==typeof u?[u]:u))throw new _('unexpected "aud" claim value',"aud","check_failed");switch(typeof r.clockTolerance){case"string":a=to(r.clockTolerance);break;case"number":a=r.clockTolerance;break;case"undefined":a=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:p}=r,h=ta(p||new Date);if((void 0!==n.iat||d)&&"number"!=typeof n.iat)throw new _('"iat" claim must be a number',"iat","invalid");if(void 0!==n.nbf){if("number"!=typeof n.nbf)throw new _('"nbf" claim must be a number',"nbf","invalid");if(n.nbf>h+a)throw new _('"nbf" claim timestamp check failed',"nbf","check_failed")}if(void 0!==n.exp){if("number"!=typeof n.exp)throw new _('"exp" claim must be a number',"exp","invalid");if(n.exp<=h-a)throw new E('"exp" claim timestamp check failed',"exp","check_failed")}if(d){let e=h-n.iat;if(e-a>("number"==typeof d?d:to(d)))throw new E('"iat" claim timestamp check failed (too far in the past)',"iat","check_failed");if(e<0-a)throw new _('"iat" claim timestamp check failed (it should be in the past)',"iat","check_failed")}return n};async function tu(e,t,r){var n;let a=await tr(e,t,r);if((null==(n=a.protectedHeader.crit)?void 0:n.includes("b64"))&&!1===a.protectedHeader.b64)throw new O("JWTs MUST NOT use unencoded payload");let i={payload:tl(a.protectedHeader,a.payload,r),protectedHeader:a.protectedHeader};return"function"==typeof t?{...i,key:a.key}:i}async function td(e,t,r){let n=await eY(e,t,r),a=tl(n.protectedHeader,n.plaintext,r),{protectedHeader:i}=n;if(void 0!==i.iss&&i.iss!==a.iss)throw new _('replicated "iss" claim header parameter mismatch',"iss","mismatch");if(void 0!==i.sub&&i.sub!==a.sub)throw new _('replicated "sub" claim header parameter mismatch',"sub","mismatch");if(void 0!==i.aud&&JSON.stringify(i.aud)!==JSON.stringify(a.aud))throw new _('replicated "aud" claim header parameter mismatch',"aud","mismatch");let o={payload:a,protectedHeader:i};return"function"==typeof t?{...o,key:n.key}:o}class tp{constructor(e){this._flattened=new e6(e)}setContentEncryptionKey(e){return this._flattened.setContentEncryptionKey(e),this}setInitializationVector(e){return this._flattened.setInitializationVector(e),this}setProtectedHeader(e){return this._flattened.setProtectedHeader(e),this}setKeyManagementParameters(e){return this._flattened.setKeyManagementParameters(e),this}async encrypt(e,t){let r=await this._flattened.encrypt(e,t);return[r.protected,r.encrypted_key,r.iv,r.ciphertext,r.tag].join(".")}}let th=async(e,t,r)=>{let n=await e7(e,t,"sign");return em(e,n),new Uint8Array(await i.subtle.sign(e9(e,n.algorithm),n,r))};class tf{constructor(e){if(!(e instanceof Uint8Array))throw TypeError("payload must be an instance of Uint8Array");this._payload=e}setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setUnprotectedHeader(e){if(this._unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}async sign(e,t){let r;if(!this._protectedHeader&&!this._unprotectedHeader)throw new R("either setProtectedHeader or setUnprotectedHeader must be called before #sign()");if(!en(this._protectedHeader,this._unprotectedHeader))throw new R("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let n={...this._protectedHeader,...this._unprotectedHeader},a=ez(R,new Map([["b64",!0]]),null==t?void 0:t.crit,this._protectedHeader,n),i=!0;if(a.has("b64")&&"boolean"!=typeof(i=this._protectedHeader.b64))throw new R('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:o}=n;if("string"!=typeof o||!o)throw new R('JWS "alg" (Algorithm) Header Parameter missing or invalid');eK(o,e,"sign");let s=this._payload;i&&(s=c.encode(m(s)));let d=u(r=this._protectedHeader?c.encode(m(JSON.stringify(this._protectedHeader))):c.encode(""),c.encode("."),s),p={signature:m(await th(o,e,d)),payload:""};return i&&(p.payload=l.decode(s)),this._unprotectedHeader&&(p.header=this._unprotectedHeader),this._protectedHeader&&(p.protected=l.decode(r)),p}}class tg{constructor(e){this._flattened=new tf(e)}setProtectedHeader(e){return this._flattened.setProtectedHeader(e),this}async sign(e,t){let r=await this._flattened.sign(e,t);if(void 0===r.payload)throw TypeError("use the flattened module for creating JWS with b64: false");return`${r.protected}.${r.payload}.${r.signature}`}}class ty{constructor(e,t,r){this.parent=e,this.key=t,this.options=r}setProtectedHeader(e){if(this.protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this.protectedHeader=e,this}setUnprotectedHeader(e){if(this.unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this.unprotectedHeader=e,this}addSignature(...e){return this.parent.addSignature(...e)}sign(...e){return this.parent.sign(...e)}done(){return this.parent}}class tm{constructor(e){this._signatures=[],this._payload=e}addSignature(e,t){let r=new ty(this,e,t);return this._signatures.push(r),r}async sign(){if(!this._signatures.length)throw new R("at least one signature must be added");let e={signatures:[],payload:""};for(let t=0;t<this._signatures.length;t++){let r=this._signatures[t],n=new tf(this._payload);n.setProtectedHeader(r.protectedHeader),n.setUnprotectedHeader(r.unprotectedHeader);let{payload:a,...i}=await n.sign(r.key,r.options);if(0===t)e.payload=a;else if(e.payload!==a)throw new R("inconsistent use of JWS Unencoded Payload (RFC7797)");e.signatures.push(i)}return e}}class tw{constructor(e){if(!ea(e))throw TypeError("JWT Claims Set MUST be an object");this._payload=e}setIssuer(e){return this._payload={...this._payload,iss:e},this}setSubject(e){return this._payload={...this._payload,sub:e},this}setAudience(e){return this._payload={...this._payload,aud:e},this}setJti(e){return this._payload={...this._payload,jti:e},this}setNotBefore(e){return"number"==typeof e?this._payload={...this._payload,nbf:e}:this._payload={...this._payload,nbf:ta(new Date)+to(e)},this}setExpirationTime(e){return"number"==typeof e?this._payload={...this._payload,exp:e}:this._payload={...this._payload,exp:ta(new Date)+to(e)},this}setIssuedAt(e){return void 0===e?this._payload={...this._payload,iat:ta(new Date)}:this._payload={...this._payload,iat:e},this}}class tb extends tw{setProtectedHeader(e){return this._protectedHeader=e,this}async sign(e,t){var r;let n=new tg(c.encode(JSON.stringify(this._payload)));if(n.setProtectedHeader(this._protectedHeader),Array.isArray(null==(r=this._protectedHeader)?void 0:r.crit)&&this._protectedHeader.crit.includes("b64")&&!1===this._protectedHeader.b64)throw new O("JWTs MUST NOT use unencoded payload");return n.sign(e,t)}}class tv extends tw{setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setKeyManagementParameters(e){if(this._keyManagementParameters)throw TypeError("setKeyManagementParameters can only be called once");return this._keyManagementParameters=e,this}setContentEncryptionKey(e){if(this._cek)throw TypeError("setContentEncryptionKey can only be called once");return this._cek=e,this}setInitializationVector(e){if(this._iv)throw TypeError("setInitializationVector can only be called once");return this._iv=e,this}replicateIssuerAsHeader(){return this._replicateIssuerAsHeader=!0,this}replicateSubjectAsHeader(){return this._replicateSubjectAsHeader=!0,this}replicateAudienceAsHeader(){return this._replicateAudienceAsHeader=!0,this}async encrypt(e,t){let r=new tp(c.encode(JSON.stringify(this._payload)));return this._replicateIssuerAsHeader&&(this._protectedHeader={...this._protectedHeader,iss:this._payload.iss}),this._replicateSubjectAsHeader&&(this._protectedHeader={...this._protectedHeader,sub:this._payload.sub}),this._replicateAudienceAsHeader&&(this._protectedHeader={...this._protectedHeader,aud:this._payload.aud}),r.setProtectedHeader(this._protectedHeader),this._iv&&r.setInitializationVector(this._iv),this._cek&&r.setContentEncryptionKey(this._cek),this._keyManagementParameters&&r.setKeyManagementParameters(this._keyManagementParameters),r.encrypt(e,t)}}let t_=(e,t)=>{if("string"!=typeof e||!e)throw new T(`${t} missing or invalid`)};async function tE(e,t){let r;if(!ea(e))throw TypeError("JWK must be an object");if(null!=t||(t="sha256"),"sha256"!==t&&"sha384"!==t&&"sha512"!==t)throw TypeError('digestAlgorithm must one of "sha256", "sha384", or "sha512"');switch(e.kty){case"EC":t_(e.crv,'"crv" (Curve) Parameter'),t_(e.x,'"x" (X Coordinate) Parameter'),t_(e.y,'"y" (Y Coordinate) Parameter'),r={crv:e.crv,kty:e.kty,x:e.x,y:e.y};break;case"OKP":t_(e.crv,'"crv" (Subtype of Key Pair) Parameter'),t_(e.x,'"x" (Public Key) Parameter'),r={crv:e.crv,kty:e.kty,x:e.x};break;case"RSA":t_(e.e,'"e" (Exponent) Parameter'),t_(e.n,'"n" (Modulus) Parameter'),r={e:e.e,kty:e.kty,n:e.n};break;case"oct":t_(e.k,'"k" (Key Value) Parameter'),r={k:e.k,kty:e.kty};break;default:throw new A('"kty" (Key Type) Parameter missing or unsupported')}let n=c.encode(JSON.stringify(r));return m(await s(t,n))}async function tS(e,t){null!=t||(t="sha256");let r=await tE(e,t);return`urn:ietf:params:oauth:jwk-thumbprint:sha-${t.slice(-3)}:${r}`}async function tA(e,t){let r={...e,...null==t?void 0:t.header};if(!ea(r.jwk))throw new R('"jwk" (JSON Web Key) Header Parameter must be a JSON object');let n=await eU({...r.jwk,ext:!0},r.alg,!0);if(n instanceof Uint8Array||"public"!==n.type)throw new R('"jwk" (JSON Web Key) Header Parameter must be a public key');return n}function tx(e){return e&&"object"==typeof e&&Array.isArray(e.keys)&&e.keys.every(tC)}function tC(e){return ea(e)}class tP{constructor(e){if(this._cached=new WeakMap,!tx(e))throw new k("JSON Web Key Set malformed");this._jwks=function(e){return"function"==typeof structuredClone?structuredClone(e):JSON.parse(JSON.stringify(e))}(e)}async getKey(e,t){let{alg:r,kid:n}={...e,...null==t?void 0:t.header},a=function(e){switch("string"==typeof e&&e.slice(0,2)){case"RS":case"PS":return"RSA";case"ES":return"EC";case"Ed":return"OKP";default:throw new A('Unsupported "alg" value for a JSON Web Key Set')}}(r),i=this._jwks.keys.filter(e=>{let t=a===e.kty;if(t&&"string"==typeof n&&(t=n===e.kid),t&&"string"==typeof e.alg&&(t=r===e.alg),t&&"string"==typeof e.use&&(t="sig"===e.use),t&&Array.isArray(e.key_ops)&&(t=e.key_ops.includes("verify")),t&&"EdDSA"===r&&(t="Ed25519"===e.crv||"Ed448"===e.crv),t)switch(r){case"ES256":t="P-256"===e.crv;break;case"ES256K":t="secp256k1"===e.crv;break;case"ES384":t="P-384"===e.crv;break;case"ES512":t="P-521"===e.crv}return t}),{0:o,length:s}=i;if(0===s)throw new N;if(1!==s){let e=new I,{_cached:t}=this;throw e[Symbol.asyncIterator]=async function*(){for(let e of i)try{yield await tR(t,e,r)}catch(e){continue}},e}return tR(this._cached,o,r)}}async function tR(e,t,r){let n=e.get(t)||e.set(t,{}).get(t);if(void 0===n[r]){let e=await eU({...t,ext:!0},r);if(e instanceof Uint8Array||"public"!==e.type)throw new k("JSON Web Key Set members must be public keys");n[r]=e}return n[r]}function tO(e){let t=new tP(e);return async function(e,r){return t.getKey(e,r)}}let tT=async(e,t,r)=>{let n,a,i=!1;"function"==typeof AbortController&&(n=new AbortController,a=setTimeout(()=>{i=!0,n.abort()},t));let o=await fetch(e.href,{signal:n?n.signal:void 0,redirect:"manual",headers:r.headers}).catch(e=>{if(i)throw new H;throw e});if(void 0!==a&&clearTimeout(a),200!==o.status)throw new v("Expected 200 OK from the JSON Web Key Set HTTP response");try{return await o.json()}catch(e){throw new v("Failed to parse the JSON Web Key Set HTTP response as JSON")}};class tk extends tP{constructor(e,t){if(super({keys:[]}),this._jwks=void 0,!(e instanceof URL))throw TypeError("url must be an instance of URL");this._url=new URL(e.href),this._options={agent:null==t?void 0:t.agent,headers:null==t?void 0:t.headers},this._timeoutDuration="number"==typeof(null==t?void 0:t.timeoutDuration)?null==t?void 0:t.timeoutDuration:5e3,this._cooldownDuration="number"==typeof(null==t?void 0:t.cooldownDuration)?null==t?void 0:t.cooldownDuration:3e4,this._cacheMaxAge="number"==typeof(null==t?void 0:t.cacheMaxAge)?null==t?void 0:t.cacheMaxAge:6e5}coolingDown(){return"number"==typeof this._jwksTimestamp&&Date.now()<this._jwksTimestamp+this._cooldownDuration}fresh(){return"number"==typeof this._jwksTimestamp&&Date.now()<this._jwksTimestamp+this._cacheMaxAge}async getKey(e,t){this._jwks&&this.fresh()||await this.reload();try{return await super.getKey(e,t)}catch(r){if(r instanceof N&&!1===this.coolingDown())return await this.reload(),super.getKey(e,t);throw r}}async reload(){this._pendingFetch&&("undefined"!=typeof WebSocketPair||"undefined"!=typeof navigator&&"Cloudflare-Workers"===navigator.userAgent)&&(this._pendingFetch=void 0),this._pendingFetch||(this._pendingFetch=tT(this._url,this._timeoutDuration,this._options).then(e=>{if(!tx(e))throw new k("JSON Web Key Set malformed");this._jwks={keys:e.keys},this._jwksTimestamp=Date.now(),this._pendingFetch=void 0}).catch(e=>{throw this._pendingFetch=void 0,e})),await this._pendingFetch}}function tN(e,t){let r=new tk(e,t);return async function(e,t){return r.getKey(e,t)}}class tI extends tw{encode(){let e=m(JSON.stringify({alg:"none"})),t=m(JSON.stringify(this._payload));return`${e}.${t}.`}static decode(e,t){let r;if("string"!=typeof e)throw new O("Unsecured JWT must be a string");let{0:n,1:a,2:i,length:o}=e.split(".");if(3!==o||""!==i)throw new O("Invalid Unsecured JWT");try{if(r=JSON.parse(l.decode(b(n))),"none"!==r.alg)throw Error()}catch(e){throw new O("Invalid Unsecured JWT")}return{payload:tl(r,b(a),t),header:r}}}let tH=m,tM=b;function tD(e){let t;if("string"==typeof e){let r=e.split(".");(3===r.length||5===r.length)&&([t]=r)}else if("object"==typeof e&&e)if("protected"in e)t=e.protected;else throw TypeError("Token does not contain a Protected Header");try{if("string"!=typeof t||!t)throw Error();let e=JSON.parse(l.decode(tM(t)));if(!ea(e))throw Error();return e}catch(e){throw TypeError("Invalid Token or Protected Header formatting")}}function tj(e){let t,r;if("string"!=typeof e)throw new O("JWTs must use Compact JWS serialization, JWT must be a string");let{1:n,length:a}=e.split(".");if(5===a)throw new O("Only JWTs using Compact JWS serialization can be decoded");if(3!==a)throw new O("Invalid JWT");if(!n)throw new O("JWTs must contain a payload");try{t=tM(n)}catch(e){throw new O("Failed to base64url decode the payload")}try{r=JSON.parse(l.decode(t))}catch(e){throw new O("Failed to parse the decoded payload as JSON")}if(!ea(r))throw new O("Invalid JWT Claims Set");return r}async function tU(e,t){var r;let n,a,o;switch(e){case"HS256":case"HS384":case"HS512":n=parseInt(e.slice(-3),10),a={name:"HMAC",hash:`SHA-${n}`,length:n},o=["sign","verify"];break;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return D(new Uint8Array((n=parseInt(e.slice(-3),10))>>3));case"A128KW":case"A192KW":case"A256KW":a={name:"AES-KW",length:n=parseInt(e.slice(1,4),10)},o=["wrapKey","unwrapKey"];break;case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":case"A128GCM":case"A192GCM":case"A256GCM":a={name:"AES-GCM",length:n=parseInt(e.slice(1,4),10)},o=["encrypt","decrypt"];break;default:throw new A('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}return i.subtle.generateKey(a,null!=(r=null==t?void 0:t.extractable)&&r,o)}function tW(e){var t;let r=null!=(t=null==e?void 0:e.modulusLength)?t:2048;if("number"!=typeof r||r<2048)throw new A("Invalid or unsupported modulusLength option provided, 2048 bits or larger keys must be used");return r}async function tL(e,t){var r,n,a;let o,s;switch(e){case"PS256":case"PS384":case"PS512":o={name:"RSA-PSS",hash:`SHA-${e.slice(-3)}`,publicExponent:new Uint8Array([1,0,1]),modulusLength:tW(t)},s=["sign","verify"];break;case"RS256":case"RS384":case"RS512":o={name:"RSASSA-PKCS1-v1_5",hash:`SHA-${e.slice(-3)}`,publicExponent:new Uint8Array([1,0,1]),modulusLength:tW(t)},s=["sign","verify"];break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":o={name:"RSA-OAEP",hash:`SHA-${parseInt(e.slice(-3),10)||1}`,publicExponent:new Uint8Array([1,0,1]),modulusLength:tW(t)},s=["decrypt","unwrapKey","encrypt","wrapKey"];break;case"ES256":o={name:"ECDSA",namedCurve:"P-256"},s=["sign","verify"];break;case"ES384":o={name:"ECDSA",namedCurve:"P-384"},s=["sign","verify"];break;case"ES512":o={name:"ECDSA",namedCurve:"P-521"},s=["sign","verify"];break;case"EdDSA":s=["sign","verify"];let c=null!=(r=null==t?void 0:t.crv)?r:"Ed25519";switch(c){case"Ed25519":case"Ed448":o={name:c};break;default:throw new A("Invalid or unsupported crv option provided")}break;case"ECDH-ES":case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{s=["deriveKey","deriveBits"];let e=null!=(n=null==t?void 0:t.crv)?n:"P-256";switch(e){case"P-256":case"P-384":case"P-521":o={name:"ECDH",namedCurve:e};break;case"X25519":case"X448":o={name:e};break;default:throw new A("Invalid or unsupported crv option provided, supported values are P-256, P-384, P-521, X25519, and X448")}break}default:throw new A('Invalid or unsupported JWK "alg" (Algorithm) Parameter value')}return i.subtle.generateKey(o,null!=(a=null==t?void 0:t.extractable)&&a,s)}async function tK(e,t){return tL(e,t)}async function tJ(e,t){return tU(e,t)}let t$="WebCryptoAPI"},552:(e,t,r)=>{"use strict";var n=r(356).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleFetch:function(){return s},interceptFetch:function(){return c},reader:function(){return i}});let a=r(201),i={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function o(e,t){let{url:r,method:a,headers:i,body:o,cache:s,credentials:c,integrity:l,mode:u,redirect:d,referrer:p,referrerPolicy:h}=t;return{testData:e,api:"fetch",request:{url:r,method:a,headers:[...Array.from(i),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:o?n.from(await t.arrayBuffer()).toString("base64"):null,cache:s,credentials:c,integrity:l,mode:u,redirect:d,referrer:p,referrerPolicy:h}}}async function s(e,t){let r=(0,a.getTestReqInfo)(t,i);if(!r)return e(t);let{testData:s,proxyPort:c}=r,l=await o(s,t),u=await e(`http://localhost:${c}`,{method:"POST",body:JSON.stringify(l),next:{internal:!0}});if(!u.ok)throw Object.defineProperty(Error(`Proxy request failed: ${u.status}`),"__NEXT_ERROR_CODE",{value:"E146",enumerable:!1,configurable:!0});let d=await u.json(),{api:p}=d;switch(p){case"continue":return e(t);case"abort":case"unhandled":throw Object.defineProperty(Error(`Proxy request aborted [${t.method} ${t.url}]`),"__NEXT_ERROR_CODE",{value:"E145",enumerable:!1,configurable:!0})}let{status:h,headers:f,body:g}=d.response;return new Response(g?n.from(g,"base64"):null,{status:h,headers:new Headers(f)})}function c(e){return r.g.fetch=function(t,r){var n;return(null==r||null==(n=r.next)?void 0:n.internal)?e(t,r):s(e,new Request(t,r))},()=>{r.g.fetch=e}}},556:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},600:(e,t,r)=>{"use strict";r.d(t,{CB:()=>n,Yq:()=>a,l_:()=>i});class n extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class a extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class i extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}},620:(e,t,r)=>{"use strict";r.d(t,{cg:()=>s,xl:()=>o});let n=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class a{disable(){throw n}getStore(){}run(){throw n}exit(){throw n}enterWith(){throw n}static bind(e){return e}}let i="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function o(){return i?new i:new a}function s(e){return i?i.bind(e):a.bind(e)}},648:(e,t,r)=>{"use strict";function n(e){return e.replace(/\/$/,"")||"/"}function a(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=a(e);return""+t+r+n+i}function o(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=a(e);return""+r+t+n+i}function s(e,t){if("string"!=typeof e)return!1;let{pathname:r}=a(e);return r===t||r.startsWith(t+"/")}r.d(t,{X:()=>h});let c=new WeakMap;function l(e,t){let r;if(!t)return{pathname:e};let n=c.get(t);n||(n=t.map(e=>e.toLowerCase()),c.set(t,n));let a=e.split("/",2);if(!a[1])return{pathname:e};let i=a[1].toLowerCase(),o=n.indexOf(i);return o<0?{pathname:e}:(r=t[o],{pathname:e=e.slice(r.length+1)||"/",detectedLocale:r})}let u=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function d(e,t){return new URL(String(e).replace(u,"localhost"),t&&String(t).replace(u,"localhost"))}let p=Symbol("NextURLInternal");class h{constructor(e,t,r){let n,a;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,a=r||{}):a=r||t||{},this[p]={url:d(e,n??a.base),options:a,basePath:""},this.analyze()}analyze(){var e,t,r,n,a;let i=function(e,t){var r,n;let{basePath:a,i18n:i,trailingSlash:o}=null!=(r=t.nextConfig)?r:{},c={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):o};a&&s(c.pathname,a)&&(c.pathname=function(e,t){if(!s(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(c.pathname,a),c.basePath=a);let u=c.pathname;if(c.pathname.startsWith("/_next/data/")&&c.pathname.endsWith(".json")){let e=c.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");c.buildId=e[0],u="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(c.pathname=u)}if(i){let e=t.i18nProvider?t.i18nProvider.analyze(c.pathname):l(c.pathname,i.locales);c.locale=e.detectedLocale,c.pathname=null!=(n=e.pathname)?n:c.pathname,!e.detectedLocale&&c.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(u):l(u,i.locales)).detectedLocale&&(c.locale=e.detectedLocale)}return c}(this[p].url.pathname,{nextConfig:this[p].options.nextConfig,parseData:!0,i18nProvider:this[p].options.i18nProvider}),o=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[p].url,this[p].options.headers);this[p].domainLocale=this[p].options.i18nProvider?this[p].options.i18nProvider.detectDomainLocale(o):function(e,t,r){if(e)for(let i of(r&&(r=r.toLowerCase()),e)){var n,a;if(t===(null==(n=i.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===i.defaultLocale.toLowerCase()||(null==(a=i.locales)?void 0:a.some(e=>e.toLowerCase()===r)))return i}}(null==(t=this[p].options.nextConfig)||null==(e=t.i18n)?void 0:e.domains,o);let c=(null==(r=this[p].domainLocale)?void 0:r.defaultLocale)||(null==(a=this[p].options.nextConfig)||null==(n=a.i18n)?void 0:n.defaultLocale);this[p].url.pathname=i.pathname,this[p].defaultLocale=c,this[p].basePath=i.basePath??"",this[p].buildId=i.buildId,this[p].locale=i.locale??c,this[p].trailingSlash=i.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,n){if(!t||t===r)return e;let a=e.toLowerCase();return!n&&(s(a,"/api")||s(a,"/"+t.toLowerCase()))?e:i(e,"/"+t)}((e={basePath:this[p].basePath,buildId:this[p].buildId,defaultLocale:this[p].options.forceLocale?void 0:this[p].defaultLocale,locale:this[p].locale,pathname:this[p].url.pathname,trailingSlash:this[p].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=n(t)),e.buildId&&(t=o(i(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=i(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:o(t,"/"):n(t)}formatSearch(){return this[p].url.search}get buildId(){return this[p].buildId}set buildId(e){this[p].buildId=e}get locale(){return this[p].locale??""}set locale(e){var t,r;if(!this[p].locale||!(null==(r=this[p].options.nextConfig)||null==(t=r.i18n)?void 0:t.locales.includes(e)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${e}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[p].locale=e}get defaultLocale(){return this[p].defaultLocale}get domainLocale(){return this[p].domainLocale}get searchParams(){return this[p].url.searchParams}get host(){return this[p].url.host}set host(e){this[p].url.host=e}get hostname(){return this[p].url.hostname}set hostname(e){this[p].url.hostname=e}get port(){return this[p].url.port}set port(e){this[p].url.port=e}get protocol(){return this[p].url.protocol}set protocol(e){this[p].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[p].url=d(e),this.analyze()}get origin(){return this[p].url.origin}get pathname(){return this[p].url.pathname}set pathname(e){this[p].url.pathname=e}get hash(){return this[p].url.hash}set hash(e){this[p].url.hash=e}get search(){return this[p].url.search}set search(e){this[p].url.search=e}get password(){return this[p].url.password}set password(e){this[p].url.password=e}get username(){return this[p].url.username}set username(e){this[p].url.username=e}get basePath(){return this[p].basePath}set basePath(e){this[p].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new h(String(this),this[p].options)}}},677:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t;let r=new URL("http://localhost:3000/api/auth");e&&!e.startsWith("http")&&(e=`https://${e}`);let n=new URL(null!=(t=e)?t:r),a=("/"===n.pathname?r.pathname:n.pathname).replace(/\/$/,""),i=`${n.origin}${a}`;return{origin:n.origin,host:n.host,path:a,base:i,toString:()=>i}}},709:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a.default}});var a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var s=a?Object.getOwnPropertyDescriptor(e,o):null;s&&(s.get||s.set)?Object.defineProperty(n,o,s):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(511));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))})},710:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,hkdf:()=>o});let n=()=>{if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;throw Error("unable to locate global object")},a=async(e,t,r,a,i)=>{let{crypto:{subtle:o}}=n();return new Uint8Array(await o.deriveBits({name:"HKDF",hash:`SHA-${e.substr(3)}`,salt:r,info:a},await o.importKey("raw",t,"HKDF",!1,["deriveBits"]),i<<3))};function i(e,t){if("string"==typeof e)return new TextEncoder().encode(e);if(!(e instanceof Uint8Array))throw TypeError(`"${t}"" must be an instance of Uint8Array or a string`);return e}async function o(e,t,r,n,o){return a(function(e){switch(e){case"sha256":case"sha384":case"sha512":case"sha1":return e;default:throw TypeError('unsupported "digest" value')}}(e),function(e){let t=i(e,"ikm");if(!t.byteLength)throw TypeError('"ikm" must be at least one byte in length');return t}(t),i(r,"salt"),function(e){let t=i(e,"info");if(t.byteLength>1024)throw TypeError('"info" must not contain more than 1024 bytes');return t}(n),function(e,t){if("number"!=typeof e||!Number.isInteger(e)||e<1)throw TypeError('"keylen" must be a positive integer');if(e>255*(parseInt(t.substr(3),10)>>3||20))throw TypeError('"keylen" too large');return e}(o,e))}},716:(e,t,r)=>{"use strict";r.d(t,{l:()=>n});class n{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},724:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,i={};function o(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function s(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,a]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=a?a:"true"))}catch{}}return t}function c(e){if(!e)return;let[[t,r],...n]=s(e),{domain:a,expires:i,httponly:o,maxage:c,path:d,samesite:p,secure:h,partitioned:f,priority:g}=Object.fromEntries(n.map(([e,t])=>[e.toLowerCase().replace(/-/g,""),t]));{var y,m,w={name:t,value:decodeURIComponent(r),domain:a,...i&&{expires:new Date(i)},...o&&{httpOnly:!0},..."string"==typeof c&&{maxAge:Number(c)},path:d,...p&&{sameSite:l.includes(y=(y=p).toLowerCase())?y:void 0},...h&&{secure:!0},...g&&{priority:u.includes(m=(m=g).toLowerCase())?m:void 0},...f&&{partitioned:!0}};let e={};for(let t in w)w[t]&&(e[t]=w[t]);return e}}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(i,{RequestCookies:()=>d,ResponseCookies:()=>p,parseCookie:()=>s,parseSetCookie:()=>c,stringifyCookie:()=>o}),e.exports=((e,i,o,s)=>{if(i&&"object"==typeof i||"function"==typeof i)for(let c of n(i))a.call(e,c)||c===o||t(e,c,{get:()=>i[c],enumerable:!(s=r(i,c))||s.enumerable});return e})(t({},"__esModule",{value:!0}),i);var l=["strict","lax","none"],u=["low","medium","high"],d=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of s(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>o(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>o(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},p=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let a=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(a)?a:function(e){if(!e)return[];var t,r,n,a,i,o=[],s=0;function c(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,i=!1;c();)if(","===(r=e.charAt(s))){for(n=s,s+=1,c(),a=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(i=!0,s=a,o.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!i||s>=e.length)&&o.push(e.substring(t,e.length))}return o}(a)){let t=c(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,a=this._parsed;return a.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=o(r);t.append("set-cookie",e)}}(a,this._headers),this}delete(...e){let[t,r]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0]];return this.set({...r,name:t,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(o).join("; ")}}},725:(e,t,r)=>{"use strict";r.d(t,{Ud:()=>n.stringifyCookie,VO:()=>n.ResponseCookies,tm:()=>n.RequestCookies});var n=r(724)},730:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});class n extends Error{constructor(e,t){super("Invariant: "+(e.endsWith(".")?e:e+".")+" This is a bug in Next.js.",t),this.name="InvariantError"}}},775:(e,t,r)=>{"use strict";r.d(t,{R:()=>u});var n=r(725),a=r(648),i=r(241),o=r(716);let s=Symbol("internal response"),c=new Set([301,302,303,307,308]);function l(e,t){var r;if(null==e||null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let r=[];for(let[n,a]of e.request.headers)t.set("x-middleware-request-"+n,a),r.push(n);t.set("x-middleware-override-headers",r.join(","))}}class u extends Response{constructor(e,t={}){super(e,t);let r=this.headers,c=new Proxy(new n.VO(r),{get(e,a,i){switch(a){case"delete":case"set":return(...i)=>{let o=Reflect.apply(e[a],e,i),s=new Headers(r);return o instanceof n.VO&&r.set("x-middleware-set-cookie",o.getAll().map(e=>(0,n.Ud)(e)).join(",")),l(t,s),o};default:return o.l.get(e,a,i)}}});this[s]={cookies:c,url:t.url?new a.X(t.url,{headers:(0,i.Cu)(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[s].cookies}static json(e,t){let r=Response.json(e,t);return new u(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!c.has(r))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let n="object"==typeof t?t:{},a=new Headers(null==n?void 0:n.headers);return a.set("Location",(0,i.qU)(e)),new u(null,{...n,headers:a,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",(0,i.qU)(e)),l(t,r),new u(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),l(e,t),new u(null,{...e,headers:t})}}},802:e=>{(()=>{"use strict";var t={993:e=>{var t=Object.prototype.hasOwnProperty,r="~";function n(){}function a(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function i(e,t,n,i,o){if("function"!=typeof n)throw TypeError("The listener must be a function");var s=new a(n,i||e,o),c=r?r+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],s]:e._events[c].push(s):(e._events[c]=s,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function s(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),s.prototype.eventNames=function(){var e,n,a=[];if(0===this._eventsCount)return a;for(n in e=this._events)t.call(e,n)&&a.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?a.concat(Object.getOwnPropertySymbols(e)):a},s.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var a=0,i=n.length,o=Array(i);a<i;a++)o[a]=n[a].fn;return o},s.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},s.prototype.emit=function(e,t,n,a,i,o){var s=r?r+e:e;if(!this._events[s])return!1;var c,l,u=this._events[s],d=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),d){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,a),!0;case 5:return u.fn.call(u.context,t,n,a,i),!0;case 6:return u.fn.call(u.context,t,n,a,i,o),!0}for(l=1,c=Array(d-1);l<d;l++)c[l-1]=arguments[l];u.fn.apply(u.context,c)}else{var p,h=u.length;for(l=0;l<h;l++)switch(u[l].once&&this.removeListener(e,u[l].fn,void 0,!0),d){case 1:u[l].fn.call(u[l].context);break;case 2:u[l].fn.call(u[l].context,t);break;case 3:u[l].fn.call(u[l].context,t,n);break;case 4:u[l].fn.call(u[l].context,t,n,a);break;default:if(!c)for(p=1,c=Array(d-1);p<d;p++)c[p-1]=arguments[p];u[l].fn.apply(u[l].context,c)}}return!0},s.prototype.on=function(e,t,r){return i(this,e,t,r,!1)},s.prototype.once=function(e,t,r){return i(this,e,t,r,!0)},s.prototype.removeListener=function(e,t,n,a){var i=r?r+e:e;if(!this._events[i])return this;if(!t)return o(this,i),this;var s=this._events[i];if(s.fn)s.fn!==t||a&&!s.once||n&&s.context!==n||o(this,i);else{for(var c=0,l=[],u=s.length;c<u;c++)(s[c].fn!==t||a&&!s[c].once||n&&s[c].context!==n)&&l.push(s[c]);l.length?this._events[i]=1===l.length?l[0]:l:o(this,i)}return this},s.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=r,s.EventEmitter=s,e.exports=s},213:e=>{e.exports=(e,t)=>(t=t||(()=>{}),e.then(e=>new Promise(e=>{e(t())}).then(()=>e),e=>new Promise(e=>{e(t())}).then(()=>{throw e})))},574:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){let n=0,a=e.length;for(;a>0;){let i=a/2|0,o=n+i;0>=r(e[o],t)?(n=++o,a-=i+1):a=i}return n}},821:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});let n=r(574);class a{constructor(){this._queue=[]}enqueue(e,t){let r={priority:(t=Object.assign({priority:0},t)).priority,run:e};if(this.size&&this._queue[this.size-1].priority>=t.priority)return void this._queue.push(r);let a=n.default(this._queue,r,(e,t)=>t.priority-e.priority);this._queue.splice(a,0,r)}dequeue(){let e=this._queue.shift();return null==e?void 0:e.run}filter(e){return this._queue.filter(t=>t.priority===e.priority).map(e=>e.run)}get size(){return this._queue.length}}t.default=a},816:(e,t,r)=>{let n=r(213);class a extends Error{constructor(e){super(e),this.name="TimeoutError"}}let i=(e,t,r)=>new Promise((i,o)=>{if("number"!=typeof t||t<0)throw TypeError("Expected `milliseconds` to be a positive number");if(t===1/0)return void i(e);let s=setTimeout(()=>{if("function"==typeof r){try{i(r())}catch(e){o(e)}return}let n="string"==typeof r?r:`Promise timed out after ${t} milliseconds`,s=r instanceof Error?r:new a(n);"function"==typeof e.cancel&&e.cancel(),o(s)},t);n(e.then(i,o),()=>{clearTimeout(s)})});e.exports=i,e.exports.default=i,e.exports.TimeoutError=a}},r={};function n(e){var a=r[e];if(void 0!==a)return a.exports;var i=r[e]={exports:{}},o=!0;try{t[e](i,i.exports,n),o=!1}finally{o&&delete r[e]}return i.exports}n.ab="//";var a={};(()=>{Object.defineProperty(a,"__esModule",{value:!0});let e=n(993),t=n(816),r=n(821),i=()=>{},o=new t.TimeoutError;class s extends e{constructor(e){var t,n,a,o;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=i,this._resolveIdle=i,!("number"==typeof(e=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:r.default},e)).intervalCap&&e.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!=(n=null==(t=e.intervalCap)?void 0:t.toString())?n:""}\` (${typeof e.intervalCap})`);if(void 0===e.interval||!(Number.isFinite(e.interval)&&e.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!=(o=null==(a=e.interval)?void 0:a.toString())?o:""}\` (${typeof e.interval})`);this._carryoverConcurrencyCount=e.carryoverConcurrencyCount,this._isIntervalIgnored=e.intervalCap===1/0||0===e.interval,this._intervalCap=e.intervalCap,this._interval=e.interval,this._queue=new e.queueClass,this._queueClass=e.queueClass,this.concurrency=e.concurrency,this._timeout=e.timeout,this._throwOnTimeout=!0===e.throwOnTimeout,this._isPaused=!1===e.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=i,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=i,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let e=Date.now();if(void 0===this._intervalId){let t=this._intervalEnd-e;if(!(t<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},t)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let e=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let t=this._queue.dequeue();return!!t&&(this.emit("active"),t(),e&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){this._isIntervalIgnored||void 0!==this._intervalId||(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(e){if(!("number"==typeof e&&e>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${e}\` (${typeof e})`);this._concurrency=e,this._processQueue()}async add(e,r={}){return new Promise((n,a)=>{let i=async()=>{this._pendingCount++,this._intervalCount++;try{let i=void 0===this._timeout&&void 0===r.timeout?e():t.default(Promise.resolve(e()),void 0===r.timeout?this._timeout:r.timeout,()=>{(void 0===r.throwOnTimeout?this._throwOnTimeout:r.throwOnTimeout)&&a(o)});n(await i)}catch(e){a(e)}this._next()};this._queue.enqueue(i,r),this._tryToStartAnother(),this.emit("add")})}async addAll(e,t){return Promise.all(e.map(async e=>this.add(e,t)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(e=>{let t=this._resolveEmpty;this._resolveEmpty=()=>{t(),e()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(e=>{let t=this._resolveIdle;this._resolveIdle=()=>{t(),e()}})}get size(){return this._queue.size}sizeBy(e){return this._queue.filter(e).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(e){this._timeout=e}}a.default=s})(),e.exports=a})()},815:(e,t,r)=>{"use strict";e.exports=r(35)},890:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var a={},i=t.split(n),o=(r||{}).decode||e,s=0;s<i.length;s++){var c=i[s],l=c.indexOf("=");if(!(l<0)){var u=c.substr(0,l).trim(),d=c.substr(++l,c.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==a[u]&&(a[u]=function(e,t){try{return t(e)}catch(t){return e}}(d,o))}}return a},t.serialize=function(e,t,n){var i=n||{},o=i.encode||r;if("function"!=typeof o)throw TypeError("option encode is invalid");if(!a.test(e))throw TypeError("argument name is invalid");var s=o(t);if(s&&!a.test(s))throw TypeError("argument val is invalid");var c=e+"="+s;if(null!=i.maxAge){var l=i.maxAge-0;if(isNaN(l)||!isFinite(l))throw TypeError("option maxAge is invalid");c+="; Max-Age="+Math.floor(l)}if(i.domain){if(!a.test(i.domain))throw TypeError("option domain is invalid");c+="; Domain="+i.domain}if(i.path){if(!a.test(i.path))throw TypeError("option path is invalid");c+="; Path="+i.path}if(i.expires){if("function"!=typeof i.expires.toUTCString)throw TypeError("option expires is invalid");c+="; Expires="+i.expires.toUTCString()}if(i.httpOnly&&(c+="; HttpOnly"),i.secure&&(c+="; Secure"),i.sameSite)switch("string"==typeof i.sameSite?i.sameSite.toLowerCase():i.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return c};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,a=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},905:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{interceptTestApis:function(){return i},wrapRequestHandler:function(){return o}});let n=r(201),a=r(552);function i(){return(0,a.interceptFetch)(r.g.fetch)}function o(e){return(t,r)=>(0,n.withRequest)(t,a.reader,()=>e(t,r))}},921:e=>{e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},928:(e,t,r)=>{"use strict";r.r(t),r.d(t,{NIL:()=>O,parse:()=>y,stringify:()=>p,v1:()=>g,v3:()=>x,v4:()=>C,v5:()=>R,validate:()=>l,version:()=>T});var n,a,i,o=new Uint8Array(16);function s(){if(!n&&!(n="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return n(o)}let c=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,l=function(e){return"string"==typeof e&&c.test(e)};for(var u=[],d=0;d<256;++d)u.push((d+256).toString(16).substr(1));let p=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=(u[e[t+0]]+u[e[t+1]]+u[e[t+2]]+u[e[t+3]]+"-"+u[e[t+4]]+u[e[t+5]]+"-"+u[e[t+6]]+u[e[t+7]]+"-"+u[e[t+8]]+u[e[t+9]]+"-"+u[e[t+10]]+u[e[t+11]]+u[e[t+12]]+u[e[t+13]]+u[e[t+14]]+u[e[t+15]]).toLowerCase();if(!l(r))throw TypeError("Stringified UUID is invalid");return r};var h=0,f=0;let g=function(e,t,r){var n=t&&r||0,o=t||Array(16),c=(e=e||{}).node||a,l=void 0!==e.clockseq?e.clockseq:i;if(null==c||null==l){var u=e.random||(e.rng||s)();null==c&&(c=a=[1|u[0],u[1],u[2],u[3],u[4],u[5]]),null==l&&(l=i=(u[6]<<8|u[7])&16383)}var d=void 0!==e.msecs?e.msecs:Date.now(),g=void 0!==e.nsecs?e.nsecs:f+1,y=d-h+(g-f)/1e4;if(y<0&&void 0===e.clockseq&&(l=l+1&16383),(y<0||d>h)&&void 0===e.nsecs&&(g=0),g>=1e4)throw Error("uuid.v1(): Can't create more than 10M uuids/sec");h=d,f=g,i=l;var m=((0xfffffff&(d+=122192928e5))*1e4+g)%0x100000000;o[n++]=m>>>24&255,o[n++]=m>>>16&255,o[n++]=m>>>8&255,o[n++]=255&m;var w=d/0x100000000*1e4&0xfffffff;o[n++]=w>>>8&255,o[n++]=255&w,o[n++]=w>>>24&15|16,o[n++]=w>>>16&255,o[n++]=l>>>8|128,o[n++]=255&l;for(var b=0;b<6;++b)o[n+b]=c[b];return t||p(o)},y=function(e){if(!l(e))throw TypeError("Invalid UUID");var t,r=new Uint8Array(16);return r[0]=(t=parseInt(e.slice(0,8),16))>>>24,r[1]=t>>>16&255,r[2]=t>>>8&255,r[3]=255&t,r[4]=(t=parseInt(e.slice(9,13),16))>>>8,r[5]=255&t,r[6]=(t=parseInt(e.slice(14,18),16))>>>8,r[7]=255&t,r[8]=(t=parseInt(e.slice(19,23),16))>>>8,r[9]=255&t,r[10]=(t=parseInt(e.slice(24,36),16))/0x10000000000&255,r[11]=t/0x100000000&255,r[12]=t>>>24&255,r[13]=t>>>16&255,r[14]=t>>>8&255,r[15]=255&t,r};function m(e,t,r){function n(e,n,a,i){if("string"==typeof e&&(e=function(e){e=unescape(encodeURIComponent(e));for(var t=[],r=0;r<e.length;++r)t.push(e.charCodeAt(r));return t}(e)),"string"==typeof n&&(n=y(n)),16!==n.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");var o=new Uint8Array(16+e.length);if(o.set(n),o.set(e,n.length),(o=r(o))[6]=15&o[6]|t,o[8]=63&o[8]|128,a){i=i||0;for(var s=0;s<16;++s)a[i+s]=o[s];return a}return p(o)}try{n.name=e}catch(e){}return n.DNS="6ba7b810-9dad-11d1-80b4-00c04fd430c8",n.URL="6ba7b811-9dad-11d1-80b4-00c04fd430c8",n}function w(e){return(e+64>>>9<<4)+14+1}function b(e,t){var r=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(r>>16)<<16|65535&r}function v(e,t,r,n,a,i){var o;return b((o=b(b(t,e),b(n,i)))<<a|o>>>32-a,r)}function _(e,t,r,n,a,i,o){return v(t&r|~t&n,e,t,a,i,o)}function E(e,t,r,n,a,i,o){return v(t&n|r&~n,e,t,a,i,o)}function S(e,t,r,n,a,i,o){return v(t^r^n,e,t,a,i,o)}function A(e,t,r,n,a,i,o){return v(r^(t|~n),e,t,a,i,o)}let x=m("v3",48,function(e){if("string"==typeof e){var t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(var r=0;r<t.length;++r)e[r]=t.charCodeAt(r)}return function(e){for(var t=[],r=32*e.length,n="0123456789abcdef",a=0;a<r;a+=8){var i=e[a>>5]>>>a%32&255,o=parseInt(n.charAt(i>>>4&15)+n.charAt(15&i),16);t.push(o)}return t}(function(e,t){e[t>>5]|=128<<t%32,e[w(t)-1]=t;for(var r=0x67452301,n=-0x10325477,a=-0x67452302,i=0x10325476,o=0;o<e.length;o+=16){var s=r,c=n,l=a,u=i;r=_(r,n,a,i,e[o],7,-0x28955b88),i=_(i,r,n,a,e[o+1],12,-0x173848aa),a=_(a,i,r,n,e[o+2],17,0x242070db),n=_(n,a,i,r,e[o+3],22,-0x3e423112),r=_(r,n,a,i,e[o+4],7,-0xa83f051),i=_(i,r,n,a,e[o+5],12,0x4787c62a),a=_(a,i,r,n,e[o+6],17,-0x57cfb9ed),n=_(n,a,i,r,e[o+7],22,-0x2b96aff),r=_(r,n,a,i,e[o+8],7,0x698098d8),i=_(i,r,n,a,e[o+9],12,-0x74bb0851),a=_(a,i,r,n,e[o+10],17,-42063),n=_(n,a,i,r,e[o+11],22,-0x76a32842),r=_(r,n,a,i,e[o+12],7,0x6b901122),i=_(i,r,n,a,e[o+13],12,-0x2678e6d),a=_(a,i,r,n,e[o+14],17,-0x5986bc72),n=_(n,a,i,r,e[o+15],22,0x49b40821),r=E(r,n,a,i,e[o+1],5,-0x9e1da9e),i=E(i,r,n,a,e[o+6],9,-0x3fbf4cc0),a=E(a,i,r,n,e[o+11],14,0x265e5a51),n=E(n,a,i,r,e[o],20,-0x16493856),r=E(r,n,a,i,e[o+5],5,-0x29d0efa3),i=E(i,r,n,a,e[o+10],9,0x2441453),a=E(a,i,r,n,e[o+15],14,-0x275e197f),n=E(n,a,i,r,e[o+4],20,-0x182c0438),r=E(r,n,a,i,e[o+9],5,0x21e1cde6),i=E(i,r,n,a,e[o+14],9,-0x3cc8f82a),a=E(a,i,r,n,e[o+3],14,-0xb2af279),n=E(n,a,i,r,e[o+8],20,0x455a14ed),r=E(r,n,a,i,e[o+13],5,-0x561c16fb),i=E(i,r,n,a,e[o+2],9,-0x3105c08),a=E(a,i,r,n,e[o+7],14,0x676f02d9),n=E(n,a,i,r,e[o+12],20,-0x72d5b376),r=S(r,n,a,i,e[o+5],4,-378558),i=S(i,r,n,a,e[o+8],11,-0x788e097f),a=S(a,i,r,n,e[o+11],16,0x6d9d6122),n=S(n,a,i,r,e[o+14],23,-0x21ac7f4),r=S(r,n,a,i,e[o+1],4,-0x5b4115bc),i=S(i,r,n,a,e[o+4],11,0x4bdecfa9),a=S(a,i,r,n,e[o+7],16,-0x944b4a0),n=S(n,a,i,r,e[o+10],23,-0x41404390),r=S(r,n,a,i,e[o+13],4,0x289b7ec6),i=S(i,r,n,a,e[o],11,-0x155ed806),a=S(a,i,r,n,e[o+3],16,-0x2b10cf7b),n=S(n,a,i,r,e[o+6],23,0x4881d05),r=S(r,n,a,i,e[o+9],4,-0x262b2fc7),i=S(i,r,n,a,e[o+12],11,-0x1924661b),a=S(a,i,r,n,e[o+15],16,0x1fa27cf8),n=S(n,a,i,r,e[o+2],23,-0x3b53a99b),r=A(r,n,a,i,e[o],6,-0xbd6ddbc),i=A(i,r,n,a,e[o+7],10,0x432aff97),a=A(a,i,r,n,e[o+14],15,-0x546bdc59),n=A(n,a,i,r,e[o+5],21,-0x36c5fc7),r=A(r,n,a,i,e[o+12],6,0x655b59c3),i=A(i,r,n,a,e[o+3],10,-0x70f3336e),a=A(a,i,r,n,e[o+10],15,-1051523),n=A(n,a,i,r,e[o+1],21,-0x7a7ba22f),r=A(r,n,a,i,e[o+8],6,0x6fa87e4f),i=A(i,r,n,a,e[o+15],10,-0x1d31920),a=A(a,i,r,n,e[o+6],15,-0x5cfebcec),n=A(n,a,i,r,e[o+13],21,0x4e0811a1),r=A(r,n,a,i,e[o+4],6,-0x8ac817e),i=A(i,r,n,a,e[o+11],10,-0x42c50dcb),a=A(a,i,r,n,e[o+2],15,0x2ad7d2bb),n=A(n,a,i,r,e[o+9],21,-0x14792c6f),r=b(r,s),n=b(n,c),a=b(a,l),i=b(i,u)}return[r,n,a,i]}(function(e){if(0===e.length)return[];for(var t=8*e.length,r=new Uint32Array(w(t)),n=0;n<t;n+=8)r[n>>5]|=(255&e[n/8])<<n%32;return r}(e),8*e.length))}),C=function(e,t,r){var n=(e=e||{}).random||(e.rng||s)();if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,t){r=r||0;for(var a=0;a<16;++a)t[r+a]=n[a];return t}return p(n)};function P(e,t){return e<<t|e>>>32-t}let R=m("v5",80,function(e){var t=[0x5a827999,0x6ed9eba1,0x8f1bbcdc,0xca62c1d6],r=[0x67452301,0xefcdab89,0x98badcfe,0x10325476,0xc3d2e1f0];if("string"==typeof e){var n=unescape(encodeURIComponent(e));e=[];for(var a=0;a<n.length;++a)e.push(n.charCodeAt(a))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);for(var i=Math.ceil((e.length/4+2)/16),o=Array(i),s=0;s<i;++s){for(var c=new Uint32Array(16),l=0;l<16;++l)c[l]=e[64*s+4*l]<<24|e[64*s+4*l+1]<<16|e[64*s+4*l+2]<<8|e[64*s+4*l+3];o[s]=c}o[i-1][14]=(e.length-1)*8/0x100000000,o[i-1][14]=Math.floor(o[i-1][14]),o[i-1][15]=(e.length-1)*8|0;for(var u=0;u<i;++u){for(var d=new Uint32Array(80),p=0;p<16;++p)d[p]=o[u][p];for(var h=16;h<80;++h)d[h]=P(d[h-3]^d[h-8]^d[h-14]^d[h-16],1);for(var f=r[0],g=r[1],y=r[2],m=r[3],w=r[4],b=0;b<80;++b){var v=Math.floor(b/20),_=P(f,5)+function(e,t,r,n){switch(e){case 0:return t&r^~t&n;case 1:case 3:return t^r^n;case 2:return t&r^t&n^r&n}}(v,g,y,m)+w+t[v]+d[b]>>>0;w=m,m=y,y=P(g,30)>>>0,g=f,f=_}r[0]=r[0]+f>>>0,r[1]=r[1]+g>>>0,r[2]=r[2]+y>>>0,r[3]=r[3]+m>>>0,r[4]=r[4]+w>>>0}return[r[0]>>24&255,r[0]>>16&255,r[0]>>8&255,255&r[0],r[1]>>24&255,r[1]>>16&255,r[1]>>8&255,255&r[1],r[2]>>24&255,r[2]>>16&255,r[2]>>8&255,255&r[2],r[3]>>24&255,r[3]>>16&255,r[3]>>8&255,255&r[3],r[4]>>24&255,r[4]>>16&255,r[4]>>8&255,255&r[4]]}),O="00000000-0000-0000-0000-000000000000",T=function(e){if(!l(e))throw TypeError("Invalid UUID");return parseInt(e.substr(14,1),16)}},931:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error('ImageResponse moved from "next/server" to "next/og" since Next.js 14, please import from "next/og" instead'),"__NEXT_ERROR_CODE",{value:"E183",enumerable:!1,configurable:!0})}r.r(t),r.d(t,{ImageResponse:()=>n,NextRequest:()=>a.J,NextResponse:()=>i.R,URLPattern:()=>u,after:()=>p,connection:()=>C,unstable_rootParams:()=>k,userAgent:()=>l,userAgentFromString:()=>c});var a=r(211),i=r(775),o=r(280),s=r.n(o);function c(e){return{...s()(e),isBot:void 0!==e&&/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}}function l({headers:e}){return c(e.get("user-agent")||void 0)}let u="undefined"==typeof URLPattern?void 0:URLPattern;var d=r(535);function p(e){let t=d.J.getStore();if(!t)throw Object.defineProperty(Error("`after` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context"),"__NEXT_ERROR_CODE",{value:"E468",enumerable:!1,configurable:!0});let{afterContext:r}=t;return r.after(e)}var h=r(115),f=r(815);class g extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest="DYNAMIC_SERVER_USAGE"}}class y extends Error{constructor(...e){super(...e),this.code="NEXT_STATIC_GEN_BAILOUT"}}class m extends Error{constructor(e){super(`During prerendering, ${e} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${e} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context.`),this.expression=e,this.digest="HANGING_PROMISE_REJECTION"}}let w=new WeakMap;function b(e,t){if(e.aborted)return Promise.reject(new m(t));{let r=new Promise((r,n)=>{let a=n.bind(null,new m(t)),i=w.get(e);if(i)i.push(a);else{let t=[a];w.set(e,t),e.addEventListener("abort",()=>{for(let e=0;e<t.length;e++)t[e]()},{once:!0})}});return r.catch(v),r}}function v(){}let _="function"==typeof f.unstable_postpone;function E(e,t,r){let n=Object.defineProperty(new g(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw r.revalidate=0,t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}function S(e,t,r){(function(){if(!_)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})})(),r&&r.dynamicAccesses.push({stack:r.isDebugDynamicAccesses?Error().stack:void 0,expression:t}),f.unstable_postpone(A(e,t))}function A(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}if(!1===function(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}(A("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});RegExp(`\\n\\s+at __next_metadata_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_viewport_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_outlet_boundary__[\\n\\s]`);var x=r(427);function C(){let e=d.J.getStore(),t=h.FP.getStore();if(e){if(t&&"after"===t.phase&&!function(){let e=x.Z.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}())throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside "after(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but "after(...)" executes after the request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E186",enumerable:!1,configurable:!0});if(e.forceStatic)return Promise.resolve(void 0);if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside "use cache". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E111",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "connection" inside a function cached with "unstable_cache(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E1",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new y(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`connection\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E562",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type)return b(t.renderSignal,"`connection()`");else"prerender-ppr"===t.type?S(e.route,"connection",t.dynamicTracking):"prerender-legacy"===t.type&&E("connection",e,t);t&&"cache"!==t.type&&"unstable-cache"!==t.type&&("prerender"===t.type||"prerender-legacy"===t.type)&&(t.revalidate=0)}return Promise.resolve(void 0)}var P=r(730);let R=/^[A-Za-z_$][A-Za-z0-9_$]*$/,O=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"]),T=new WeakMap;async function k(){let e=d.J.getStore();if(!e)throw Object.defineProperty(new P.z("Missing workStore in unstable_rootParams"),"__NEXT_ERROR_CODE",{value:"E615",enumerable:!1,configurable:!0});let t=h.FP.getStore();if(!t)throw Object.defineProperty(Error(`Route ${e.route} used \`unstable_rootParams()\` in Pages Router. This API is only available within App Router.`),"__NEXT_ERROR_CODE",{value:"E641",enumerable:!1,configurable:!0});switch(t.type){case"unstable-cache":case"cache":throw Object.defineProperty(Error(`Route ${e.route} used \`unstable_rootParams()\` inside \`"use cache"\` or \`unstable_cache\`. Support for this API inside cache scopes is planned for a future version of Next.js.`),"__NEXT_ERROR_CODE",{value:"E642",enumerable:!1,configurable:!0});case"prerender":case"prerender-ppr":case"prerender-legacy":return function(e,t,r){let n=t.fallbackRouteParams;if(n){let c=!1;for(let t in e)if(n.has(t)){c=!0;break}if(c){if("prerender"===r.type){let t=T.get(e);if(t)return t;let n=b(r.renderSignal,"`unstable_rootParams`");return T.set(e,n),n}var a=e,i=n,o=t,s=r;let c=T.get(a);if(c)return c;let l={...a},u=Promise.resolve(l);return T.set(a,u),Object.keys(a).forEach(e=>{O.has(e)||(i.has(e)?Object.defineProperty(l,e,{get(){var t;let r=(t="unstable_rootParams",R.test(e)?"`"+t+"."+e+"`":"`"+t+"["+JSON.stringify(e)+"]`");"prerender-ppr"===s.type?S(o.route,r,s.dynamicTracking):E(r,o,s)},enumerable:!0}):u[e]=a[e])}),u}}return Promise.resolve(e)}(t.rootParams,e,t);default:return Promise.resolve(t.rootParams)}}},956:(e,t,r)=>{(()=>{"use strict";var t={491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let n=r(223),a=r(172),i=r(930),o="context",s=new n.NoopContextManager;class c{constructor(){}static getInstance(){return this._instance||(this._instance=new c),this._instance}setGlobalContextManager(e){return(0,a.registerGlobal)(o,e,i.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...n){return this._getContextManager().with(e,t,r,...n)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,a.getGlobal)(o)||s}disable(){this._getContextManager().disable(),(0,a.unregisterGlobal)(o,i.DiagAPI.instance())}}t.ContextAPI=c},930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let n=r(56),a=r(912),i=r(957),o=r(172);class s{constructor(){function e(e){return function(...t){let r=(0,o.getGlobal)("diag");if(r)return r[e](...t)}}let t=this;t.setLogger=(e,r={logLevel:i.DiagLogLevel.INFO})=>{var n,s,c;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!=(n=e.stack)?n:e.message),!1}"number"==typeof r&&(r={logLevel:r});let l=(0,o.getGlobal)("diag"),u=(0,a.createLogLevelDiagLogger)(null!=(s=r.logLevel)?s:i.DiagLogLevel.INFO,e);if(l&&!r.suppressOverrideMessage){let e=null!=(c=Error().stack)?c:"<failed to generate stacktrace>";l.warn(`Current logger will be overwritten from ${e}`),u.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,o.registerGlobal)("diag",u,t,!0)},t.disable=()=>{(0,o.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new n.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new s),this._instance}}t.DiagAPI=s},653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let n=r(660),a=r(172),i=r(930),o="metrics";class s{constructor(){}static getInstance(){return this._instance||(this._instance=new s),this._instance}setGlobalMeterProvider(e){return(0,a.registerGlobal)(o,e,i.DiagAPI.instance())}getMeterProvider(){return(0,a.getGlobal)(o)||n.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,a.unregisterGlobal)(o,i.DiagAPI.instance())}}t.MetricsAPI=s},181:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let n=r(172),a=r(874),i=r(194),o=r(277),s=r(369),c=r(930),l="propagation",u=new a.NoopTextMapPropagator;class d{constructor(){this.createBaggage=s.createBaggage,this.getBaggage=o.getBaggage,this.getActiveBaggage=o.getActiveBaggage,this.setBaggage=o.setBaggage,this.deleteBaggage=o.deleteBaggage}static getInstance(){return this._instance||(this._instance=new d),this._instance}setGlobalPropagator(e){return(0,n.registerGlobal)(l,e,c.DiagAPI.instance())}inject(e,t,r=i.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=i.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,n.unregisterGlobal)(l,c.DiagAPI.instance())}_getGlobalPropagator(){return(0,n.getGlobal)(l)||u}}t.PropagationAPI=d},997:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let n=r(172),a=r(846),i=r(139),o=r(607),s=r(930),c="trace";class l{constructor(){this._proxyTracerProvider=new a.ProxyTracerProvider,this.wrapSpanContext=i.wrapSpanContext,this.isSpanContextValid=i.isSpanContextValid,this.deleteSpan=o.deleteSpan,this.getSpan=o.getSpan,this.getActiveSpan=o.getActiveSpan,this.getSpanContext=o.getSpanContext,this.setSpan=o.setSpan,this.setSpanContext=o.setSpanContext}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalTracerProvider(e){let t=(0,n.registerGlobal)(c,this._proxyTracerProvider,s.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,n.getGlobal)(c)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,n.unregisterGlobal)(c,s.DiagAPI.instance()),this._proxyTracerProvider=new a.ProxyTracerProvider}}t.TraceAPI=l},277:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let n=r(491),a=(0,r(780).createContextKey)("OpenTelemetry Baggage Key");function i(e){return e.getValue(a)||void 0}t.getBaggage=i,t.getActiveBaggage=function(){return i(n.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(a,t)},t.deleteBaggage=function(e){return e.deleteValue(a)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class r{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let n=new r(this._entries);return n._entries.set(e,t),n}removeEntry(e){let t=new r(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new r(this._entries);for(let r of e)t._entries.delete(r);return t}clear(){return new r}}t.BaggageImpl=r},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let n=r(930),a=r(993),i=r(830),o=n.DiagAPI.instance();t.createBaggage=function(e={}){return new a.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(o.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:i.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0,t.context=r(491).ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let n=r(780);class a{active(){return n.ROOT_CONTEXT}with(e,t,r,...n){return t.call(r,...n)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=a},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class r{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,n)=>{let a=new r(t._currentContext);return a._currentContext.set(e,n),a},t.deleteValue=e=>{let n=new r(t._currentContext);return n._currentContext.delete(e),n}}}t.ROOT_CONTEXT=new r},506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0,t.diag=r(930).DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let n=r(172);class a{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return i("debug",this._namespace,e)}error(...e){return i("error",this._namespace,e)}info(...e){return i("info",this._namespace,e)}warn(...e){return i("warn",this._namespace,e)}verbose(...e){return i("verbose",this._namespace,e)}}function i(e,t,r){let a=(0,n.getGlobal)("diag");if(a)return r.unshift(t),a[e](...r)}t.DiagComponentLogger=a},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let r=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class n{constructor(){for(let e=0;e<r.length;e++)this[r[e].n]=function(e){return function(...t){if(console){let r=console[e];if("function"!=typeof r&&(r=console.log),"function"==typeof r)return r.apply(console,t)}}}(r[e].c)}}t.DiagConsoleLogger=n},912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let n=r(957);t.createLogLevelDiagLogger=function(e,t){function r(r,n){let a=t[r];return"function"==typeof a&&e>=n?a.bind(t):function(){}}return e<n.DiagLogLevel.NONE?e=n.DiagLogLevel.NONE:e>n.DiagLogLevel.ALL&&(e=n.DiagLogLevel.ALL),t=t||{},{error:r("error",n.DiagLogLevel.ERROR),warn:r("warn",n.DiagLogLevel.WARN),info:r("info",n.DiagLogLevel.INFO),debug:r("debug",n.DiagLogLevel.DEBUG),verbose:r("verbose",n.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let n=r(200),a=r(521),i=r(130),o=a.VERSION.split(".")[0],s=Symbol.for(`opentelemetry.js.api.${o}`),c=n._globalThis;t.registerGlobal=function(e,t,r,n=!1){var i;let o=c[s]=null!=(i=c[s])?i:{version:a.VERSION};if(!n&&o[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return r.error(t.stack||t.message),!1}if(o.version!==a.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${o.version} for ${e} does not match previously registered API v${a.VERSION}`);return r.error(t.stack||t.message),!1}return o[e]=t,r.debug(`@opentelemetry/api: Registered a global for ${e} v${a.VERSION}.`),!0},t.getGlobal=function(e){var t,r;let n=null==(t=c[s])?void 0:t.version;if(n&&(0,i.isCompatible)(n))return null==(r=c[s])?void 0:r[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${a.VERSION}.`);let r=c[s];r&&delete r[e]}},130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let n=r(521),a=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function i(e){let t=new Set([e]),r=new Set,n=e.match(a);if(!n)return()=>!1;let i={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=i.prerelease)return function(t){return t===e};function o(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;let n=e.match(a);if(!n)return o(e);let s={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=s.prerelease||i.major!==s.major)return o(e);if(0===i.major)return i.minor===s.minor&&i.patch<=s.patch?(t.add(e),!0):o(e);return i.minor<=s.minor?(t.add(e),!0):o(e)}}t._makeCompatibilityCheck=i,t.isCompatible=i(n.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0,t.metrics=r(653).MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class r{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=r;class n{}t.NoopMetric=n;class a extends n{add(e,t){}}t.NoopCounterMetric=a;class i extends n{add(e,t){}}t.NoopUpDownCounterMetric=i;class o extends n{record(e,t){}}t.NoopHistogramMetric=o;class s{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=s;class c extends s{}t.NoopObservableCounterMetric=c;class l extends s{}t.NoopObservableGaugeMetric=l;class u extends s{}t.NoopObservableUpDownCounterMetric=u,t.NOOP_METER=new r,t.NOOP_COUNTER_METRIC=new a,t.NOOP_HISTOGRAM_METRIC=new o,t.NOOP_UP_DOWN_COUNTER_METRIC=new i,t.NOOP_OBSERVABLE_COUNTER_METRIC=new c,t.NOOP_OBSERVABLE_GAUGE_METRIC=new l,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new u,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let n=r(102);class a{getMeter(e,t,r){return n.NOOP_METER}}t.NoopMeterProvider=a,t.NOOP_METER_PROVIDER=new a},200:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),a(r(46),t)},651:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:r.g},46:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),a(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0,t.propagation=r(181).PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0;class r{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=r},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,r){null!=e&&(e[t]=r)}}},845:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0,t.trace=r(997).TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let n=r(476);class a{constructor(e=n.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}t.NonRecordingSpan=a},614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let n=r(491),a=r(607),i=r(403),o=r(139),s=n.ContextAPI.getInstance();class c{startSpan(e,t,r=s.active()){var n;if(null==t?void 0:t.root)return new i.NonRecordingSpan;let c=r&&(0,a.getSpanContext)(r);return"object"==typeof(n=c)&&"string"==typeof n.spanId&&"string"==typeof n.traceId&&"number"==typeof n.traceFlags&&(0,o.isSpanContextValid)(c)?new i.NonRecordingSpan(c):new i.NonRecordingSpan}startActiveSpan(e,t,r,n){let i,o,c;if(arguments.length<2)return;2==arguments.length?c=t:3==arguments.length?(i=t,c=r):(i=t,o=r,c=n);let l=null!=o?o:s.active(),u=this.startSpan(e,i,l),d=(0,a.setSpan)(l,u);return s.with(d,c,void 0,u)}}t.NoopTracer=c},124:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let n=r(614);class a{getTracer(e,t,r){return new n.NoopTracer}}t.NoopTracerProvider=a},125:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let n=new(r(614)).NoopTracer;class a{constructor(e,t,r,n){this._provider=e,this.name=t,this.version=r,this.options=n}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,n){let a=this._getTracer();return Reflect.apply(a.startActiveSpan,a,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):n}}t.ProxyTracer=a},846:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let n=r(125),a=new(r(124)).NoopTracerProvider;class i{getTracer(e,t,r){var a;return null!=(a=this.getDelegateTracer(e,t,r))?a:new n.ProxyTracer(this,e,t,r)}getDelegate(){var e;return null!=(e=this._delegate)?e:a}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var n;return null==(n=this._delegate)?void 0:n.getTracer(e,t,r)}}t.ProxyTracerProvider=i},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let n=r(780),a=r(403),i=r(491),o=(0,n.createContextKey)("OpenTelemetry Context Key SPAN");function s(e){return e.getValue(o)||void 0}function c(e,t){return e.setValue(o,t)}t.getSpan=s,t.getActiveSpan=function(){return s(i.ContextAPI.getInstance().active())},t.setSpan=c,t.deleteSpan=function(e){return e.deleteValue(o)},t.setSpanContext=function(e,t){return c(e,new a.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null==(t=s(e))?void 0:t.spanContext()}},325:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let n=r(564);class a{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let r=t.trim(),a=r.indexOf("=");if(-1!==a){let i=r.slice(0,a),o=r.slice(a+1,t.length);(0,n.validateKey)(i)&&(0,n.validateValue)(o)&&e.set(i,o)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new a;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=a},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let r="[_0-9a-z-*/]",n=`[a-z]${r}{0,255}`,a=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`,i=RegExp(`^(?:${n}|${a})$`),o=/^[ -~]{0,255}[!-~]$/,s=/,|=/;t.validateKey=function(e){return i.test(e)},t.validateValue=function(e){return o.test(e)&&!s.test(e)}},98:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let n=r(325);t.createTraceState=function(e){return new n.TraceStateImpl(e)}},476:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let n=r(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:n.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let n=r(476),a=r(403),i=/^([0-9a-f]{32})$/i,o=/^[0-9a-f]{16}$/i;function s(e){return i.test(e)&&e!==n.INVALID_TRACEID}function c(e){return o.test(e)&&e!==n.INVALID_SPANID}t.isValidTraceId=s,t.isValidSpanId=c,t.isSpanContextValid=function(e){return s(e.traceId)&&c(e.spanId)},t.wrapSpanContext=function(e){return new a.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},n={};function a(e){var r=n[e];if(void 0!==r)return r.exports;var i=n[e]={exports:{}},o=!0;try{t[e].call(i.exports,i,i.exports,a),o=!1}finally{o&&delete n[e]}return i.exports}a.ab="//";var i={};(()=>{Object.defineProperty(i,"__esModule",{value:!0}),i.trace=i.propagation=i.metrics=i.diag=i.context=i.INVALID_SPAN_CONTEXT=i.INVALID_TRACEID=i.INVALID_SPANID=i.isValidSpanId=i.isValidTraceId=i.isSpanContextValid=i.createTraceState=i.TraceFlags=i.SpanStatusCode=i.SpanKind=i.SamplingDecision=i.ProxyTracerProvider=i.ProxyTracer=i.defaultTextMapSetter=i.defaultTextMapGetter=i.ValueType=i.createNoopMeter=i.DiagLogLevel=i.DiagConsoleLogger=i.ROOT_CONTEXT=i.createContextKey=i.baggageEntryMetadataFromString=void 0;var e=a(369);Object.defineProperty(i,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=a(780);Object.defineProperty(i,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(i,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var r=a(972);Object.defineProperty(i,"DiagConsoleLogger",{enumerable:!0,get:function(){return r.DiagConsoleLogger}});var n=a(957);Object.defineProperty(i,"DiagLogLevel",{enumerable:!0,get:function(){return n.DiagLogLevel}});var o=a(102);Object.defineProperty(i,"createNoopMeter",{enumerable:!0,get:function(){return o.createNoopMeter}});var s=a(901);Object.defineProperty(i,"ValueType",{enumerable:!0,get:function(){return s.ValueType}});var c=a(194);Object.defineProperty(i,"defaultTextMapGetter",{enumerable:!0,get:function(){return c.defaultTextMapGetter}}),Object.defineProperty(i,"defaultTextMapSetter",{enumerable:!0,get:function(){return c.defaultTextMapSetter}});var l=a(125);Object.defineProperty(i,"ProxyTracer",{enumerable:!0,get:function(){return l.ProxyTracer}});var u=a(846);Object.defineProperty(i,"ProxyTracerProvider",{enumerable:!0,get:function(){return u.ProxyTracerProvider}});var d=a(996);Object.defineProperty(i,"SamplingDecision",{enumerable:!0,get:function(){return d.SamplingDecision}});var p=a(357);Object.defineProperty(i,"SpanKind",{enumerable:!0,get:function(){return p.SpanKind}});var h=a(847);Object.defineProperty(i,"SpanStatusCode",{enumerable:!0,get:function(){return h.SpanStatusCode}});var f=a(475);Object.defineProperty(i,"TraceFlags",{enumerable:!0,get:function(){return f.TraceFlags}});var g=a(98);Object.defineProperty(i,"createTraceState",{enumerable:!0,get:function(){return g.createTraceState}});var y=a(139);Object.defineProperty(i,"isSpanContextValid",{enumerable:!0,get:function(){return y.isSpanContextValid}}),Object.defineProperty(i,"isValidTraceId",{enumerable:!0,get:function(){return y.isValidTraceId}}),Object.defineProperty(i,"isValidSpanId",{enumerable:!0,get:function(){return y.isValidSpanId}});var m=a(476);Object.defineProperty(i,"INVALID_SPANID",{enumerable:!0,get:function(){return m.INVALID_SPANID}}),Object.defineProperty(i,"INVALID_TRACEID",{enumerable:!0,get:function(){return m.INVALID_TRACEID}}),Object.defineProperty(i,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return m.INVALID_SPAN_CONTEXT}});let w=a(67);Object.defineProperty(i,"context",{enumerable:!0,get:function(){return w.context}});let b=a(506);Object.defineProperty(i,"diag",{enumerable:!0,get:function(){return b.diag}});let v=a(886);Object.defineProperty(i,"metrics",{enumerable:!0,get:function(){return v.metrics}});let _=a(939);Object.defineProperty(i,"propagation",{enumerable:!0,get:function(){return _.propagation}});let E=a(845);Object.defineProperty(i,"trace",{enumerable:!0,get:function(){return E.trace}}),i.default={context:w.context,diag:b.diag,metrics:v.metrics,propagation:_.propagation,trace:E.trace}})(),e.exports=i})()},970:(e,t,r)=>{"use strict";let n;r.r(t),r.d(t,{default:()=>eF});var a={};async function i(){return"_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&await _ENTRIES.middleware_instrumentation}r.r(a),r.d(a,{config:()=>eV,default:()=>eB});let o=null;async function s(){if("phase-production-build"===process.env.NEXT_PHASE)return;o||(o=i());let e=await o;if(null==e?void 0:e.register)try{await e.register()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}async function c(...e){let t=await i();try{var r;await (null==t||null==(r=t.onRequestError)?void 0:r.call(t,...e))}catch(e){console.error("Error in instrumentation.onRequestError:",e)}}let l=null;function u(){return l||(l=s()),l}function d(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==r.g.process&&(process.env=r.g.process.env,r.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,r){if("then"===r)return{};throw Object.defineProperty(Error(d(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},construct(){throw Object.defineProperty(Error(d(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},apply(r,n,a){if("function"==typeof a[0])return a[0](t);throw Object.defineProperty(Error(d(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1}),u();var p=r(600),h=r(241);let f=Symbol("response"),g=Symbol("passThrough"),y=Symbol("waitUntil");class m{constructor(e,t){this[g]=!1,this[y]=t?{kind:"external",function:t}:{kind:"internal",promises:[]}}respondWith(e){this[f]||(this[f]=Promise.resolve(e))}passThroughOnException(){this[g]=!0}waitUntil(e){if("external"===this[y].kind)return(0,this[y].function)(e);this[y].promises.push(e)}}class w extends m{constructor(e){var t;super(e.request,null==(t=e.context)?void 0:t.waitUntil),this.sourcePage=e.page}get request(){throw Object.defineProperty(new p.CB({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new p.CB({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}var b=r(211),v=r(775);function _(e,t){let r="string"==typeof t?new URL(t):t,n=new URL(e,t),a=n.origin===r.origin;return{url:a?n.toString().slice(r.origin.length):n.toString(),isRelative:a}}var E=r(648);let S="Next-Router-Prefetch",A=["RSC","Next-Router-State-Tree",S,"Next-HMR-Refresh","Next-Router-Segment-Prefetch"],x="_rsc";var C=r(716);class P extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new P}}class R extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,n){if("symbol"==typeof r)return C.l.get(t,r,n);let a=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===a);if(void 0!==i)return C.l.get(t,i,n)},set(t,r,n,a){if("symbol"==typeof r)return C.l.set(t,r,n,a);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);return C.l.set(t,o??r,n,a)},has(t,r){if("symbol"==typeof r)return C.l.has(t,r);let n=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0!==a&&C.l.has(t,a)},deleteProperty(t,r){if("symbol"==typeof r)return C.l.deleteProperty(t,r);let n=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0===a||C.l.deleteProperty(t,a)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return P.callable;default:return C.l.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new R(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}var O=r(725),T=r(535),k=r(115);class N extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new N}}class I{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return N.callable;default:return C.l.get(e,t,r)}}})}}let H=Symbol.for("next.mutated.cookies");class M{static wrap(e,t){let r=new O.VO(new Headers);for(let t of e.getAll())r.set(t);let n=[],a=new Set,i=()=>{let e=T.J.getStore();if(e&&(e.pathWasRevalidated=!0),n=r.getAll().filter(e=>a.has(e.name)),t){let e=[];for(let t of n){let r=new O.VO(new Headers);r.set(t),e.push(r.toString())}t(e)}},o=new Proxy(r,{get(e,t,r){switch(t){case H:return n;case"delete":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),o}finally{i()}};case"set":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),o}finally{i()}};default:return C.l.get(e,t,r)}}});return o}}function D(e){if("action"!==(0,k.XN)(e).phase)throw new N}var j=r(430),U=function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(U||{}),W=function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(W||{}),L=function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(L||{}),K=function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(K||{}),J=function(e){return e.startServer="startServer.startServer",e}(J||{}),$=function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}($||{}),B=function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(B||{}),V=function(e){return e.executeRoute="Router.executeRoute",e}(V||{}),q=function(e){return e.runHandler="Node.runHandler",e}(q||{}),G=function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(G||{}),z=function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}(z||{}),F=function(e){return e.execute="Middleware.execute",e}(F||{});let X=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],Y=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"];function Z(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}let{context:Q,propagation:ee,trace:et,SpanStatusCode:er,SpanKind:en,ROOT_CONTEXT:ea}=n=r(956);class ei extends Error{constructor(e,t){super(),this.bubble=e,this.result=t}}let eo=(e,t)=>{(function(e){return"object"==typeof e&&null!==e&&e instanceof ei})(t)&&t.bubble?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:er.ERROR,message:null==t?void 0:t.message})),e.end()},es=new Map,ec=n.createContextKey("next.rootSpanId"),el=0,eu=()=>el++,ed={set(e,t,r){e.push({key:t,value:r})}};class ep{getTracerInstance(){return et.getTracer("next.js","0.0.1")}getContext(){return Q}getTracePropagationData(){let e=Q.active(),t=[];return ee.inject(e,t,ed),t}getActiveScopeSpan(){return et.getSpan(null==Q?void 0:Q.active())}withPropagatedContext(e,t,r){let n=Q.active();if(et.getSpanContext(n))return t();let a=ee.extract(n,e,r);return Q.with(a,t)}trace(...e){var t;let[r,n,a]=e,{fn:i,options:o}="function"==typeof n?{fn:n,options:{}}:{fn:a,options:{...n}},s=o.spanName??r;if(!X.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||o.hideSpan)return i();let c=this.getSpanContext((null==o?void 0:o.parentSpan)??this.getActiveScopeSpan()),l=!1;c?(null==(t=et.getSpanContext(c))?void 0:t.isRemote)&&(l=!0):(c=(null==Q?void 0:Q.active())??ea,l=!0);let u=eu();return o.attributes={"next.span_name":s,"next.span_type":r,...o.attributes},Q.with(c.setValue(ec,u),()=>this.getTracerInstance().startActiveSpan(s,o,e=>{let t="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,n=()=>{es.delete(u),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&Y.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};l&&es.set(u,new Map(Object.entries(o.attributes??{})));try{if(i.length>1)return i(e,t=>eo(e,t));let t=i(e);if(Z(t))return t.then(t=>(e.end(),t)).catch(t=>{throw eo(e,t),t}).finally(n);return e.end(),n(),t}catch(t){throw eo(e,t),n(),t}}))}wrap(...e){let t=this,[r,n,a]=3===e.length?e:[e[0],{},e[1]];return X.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=n;"function"==typeof e&&"function"==typeof a&&(e=e.apply(this,arguments));let i=arguments.length-1,o=arguments[i];if("function"!=typeof o)return t.trace(r,e,()=>a.apply(this,arguments));{let n=t.getContext().bind(Q.active(),o);return t.trace(r,e,(e,t)=>(arguments[i]=function(e){return null==t||t(e),n.apply(this,arguments)},a.apply(this,arguments)))}}:a}startSpan(...e){let[t,r]=e,n=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,n)}getSpanContext(e){return e?et.setSpan(Q.active(),e):void 0}getRootSpanAttributes(){let e=Q.active().getValue(ec);return es.get(e)}setRootSpanAttribute(e,t){let r=Q.active().getValue(ec),n=es.get(r);n&&n.set(e,t)}}let eh=(()=>{let e=new ep;return()=>e})(),ef="__prerender_bypass";Symbol("__next_preview_data"),Symbol(ef);class eg{constructor(e,t,r,n){var a;let i=e&&function(e,t){let r=R.from(e.headers);return{isOnDemandRevalidate:r.get(j.kz)===t.previewModeId,revalidateOnlyGenerated:r.has(j.r4)}}(t,e).isOnDemandRevalidate,o=null==(a=r.get(ef))?void 0:a.value;this._isEnabled=!!(!i&&o&&e&&o===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=n}get isEnabled(){return this._isEnabled}enable(){if(!this._previewModeId)throw Object.defineProperty(Error("Invariant: previewProps missing previewModeId this should never happen"),"__NEXT_ERROR_CODE",{value:"E93",enumerable:!1,configurable:!0});this._mutableCookies.set({name:ef,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"}),this._isEnabled=!0}disable(){this._mutableCookies.set({name:ef,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)}),this._isEnabled=!1}}function ey(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let r=e.headers["x-middleware-set-cookie"],n=new Headers;for(let e of(0,h.RD)(r))n.append("set-cookie",e);for(let e of new O.VO(n).getAll())t.set(e)}}var em=r(802),ew=r.n(em),eb=r(730);class ev{constructor(e,t){this.cache=new Map,this.sizes=new Map,this.totalSize=0,this.maxSize=e,this.calculateSize=t||(()=>1)}set(e,t){if(!e||!t)return;let r=this.calculateSize(t);if(r>this.maxSize)return void console.warn("Single item size exceeds maxSize");this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0),this.cache.set(e,t),this.sizes.set(e,r),this.totalSize+=r,this.touch(e)}has(e){return!!e&&(this.touch(e),!!this.cache.get(e))}get(e){if(!e)return;let t=this.cache.get(e);if(void 0!==t)return this.touch(e),t}touch(e){let t=this.cache.get(e);void 0!==t&&(this.cache.delete(e),this.cache.set(e,t),this.evictIfNecessary())}evictIfNecessary(){for(;this.totalSize>this.maxSize&&this.cache.size>0;)this.evictLeastRecentlyUsed()}evictLeastRecentlyUsed(){let e=this.cache.keys().next().value;if(void 0!==e){let t=this.sizes.get(e)||0;this.totalSize-=t,this.cache.delete(e),this.sizes.delete(e)}}reset(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}keys(){return[...this.cache.keys()]}remove(e){this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0,this.cache.delete(e),this.sizes.delete(e))}clear(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}get size(){return this.cache.size}get currentSize(){return this.totalSize}}r(356).Buffer,new ev(0x3200000,e=>e.size),process.env.NEXT_PRIVATE_DEBUG_CACHE&&console.debug.bind(console,"DefaultCacheHandler:"),process.env.NEXT_PRIVATE_DEBUG_CACHE,Symbol.for("@next/cache-handlers");let e_=Symbol.for("@next/cache-handlers-map"),eE=Symbol.for("@next/cache-handlers-set"),eS=globalThis;function eA(){if(eS[e_])return eS[e_].entries()}async function ex(e,t){if(!e)return t();let r=eC(e);try{return await t()}finally{let t=function(e,t){let r=new Set(e.pendingRevalidatedTags),n=new Set(e.pendingRevalidateWrites);return{pendingRevalidatedTags:t.pendingRevalidatedTags.filter(e=>!r.has(e)),pendingRevalidates:Object.fromEntries(Object.entries(t.pendingRevalidates).filter(([t])=>!(t in e.pendingRevalidates))),pendingRevalidateWrites:t.pendingRevalidateWrites.filter(e=>!n.has(e))}}(r,eC(e));await eR(e,t)}}function eC(e){return{pendingRevalidatedTags:e.pendingRevalidatedTags?[...e.pendingRevalidatedTags]:[],pendingRevalidates:{...e.pendingRevalidates},pendingRevalidateWrites:e.pendingRevalidateWrites?[...e.pendingRevalidateWrites]:[]}}async function eP(e,t){if(0===e.length)return;let r=[];t&&r.push(t.revalidateTag(e));let n=function(){if(eS[eE])return eS[eE].values()}();if(n)for(let t of n)r.push(t.expireTags(...e));await Promise.all(r)}async function eR(e,t){let r=(null==t?void 0:t.pendingRevalidatedTags)??e.pendingRevalidatedTags??[],n=(null==t?void 0:t.pendingRevalidates)??e.pendingRevalidates??{},a=(null==t?void 0:t.pendingRevalidateWrites)??e.pendingRevalidateWrites??[];return Promise.all([eP(r,e.incrementalCache),...Object.values(n),...a])}var eO=r(620),eT=r(427);class ek{constructor({waitUntil:e,onClose:t,onTaskError:r}){this.workUnitStores=new Set,this.waitUntil=e,this.onClose=t,this.onTaskError=r,this.callbackQueue=new(ew()),this.callbackQueue.pause()}after(e){if(Z(e))this.waitUntil||eN(),this.waitUntil(e.catch(e=>this.reportTaskError("promise",e)));else if("function"==typeof e)this.addCallback(e);else throw Object.defineProperty(Error("`after()`: Argument must be a promise or a function"),"__NEXT_ERROR_CODE",{value:"E50",enumerable:!1,configurable:!0})}addCallback(e){this.waitUntil||eN();let t=k.FP.getStore();t&&this.workUnitStores.add(t);let r=eT.Z.getStore(),n=r?r.rootTaskSpawnPhase:null==t?void 0:t.phase;this.runCallbacksOnClosePromise||(this.runCallbacksOnClosePromise=this.runCallbacksOnClose(),this.waitUntil(this.runCallbacksOnClosePromise));let a=(0,eO.cg)(async()=>{try{await eT.Z.run({rootTaskSpawnPhase:n},()=>e())}catch(e){this.reportTaskError("function",e)}});this.callbackQueue.add(a)}async runCallbacksOnClose(){return await new Promise(e=>this.onClose(e)),this.runCallbacks()}async runCallbacks(){if(0===this.callbackQueue.size)return;for(let e of this.workUnitStores)e.phase="after";let e=T.J.getStore();if(!e)throw Object.defineProperty(new eb.z("Missing workStore in AfterContext.runCallbacks"),"__NEXT_ERROR_CODE",{value:"E547",enumerable:!1,configurable:!0});return ex(e,()=>(this.callbackQueue.start(),this.callbackQueue.onIdle()))}reportTaskError(e,t){if(console.error("promise"===e?"A promise passed to `after()` rejected:":"An error occurred in a function passed to `after()`:",t),this.onTaskError)try{null==this.onTaskError||this.onTaskError.call(this,t)}catch(e){console.error(Object.defineProperty(new eb.z("`onTaskError` threw while handling an error thrown from an `after` task",{cause:e}),"__NEXT_ERROR_CODE",{value:"E569",enumerable:!1,configurable:!0}))}}}function eN(){throw Object.defineProperty(Error("`after()` will not work correctly, because `waitUntil` is not available in the current environment."),"__NEXT_ERROR_CODE",{value:"E91",enumerable:!1,configurable:!0})}function eI(e){let t,r={then:(n,a)=>(t||(t=e()),t.then(e=>{r.value=e}).catch(()=>{}),t.then(n,a))};return r}class eH{onClose(e){if(this.isClosed)throw Object.defineProperty(Error("Cannot subscribe to a closed CloseController"),"__NEXT_ERROR_CODE",{value:"E365",enumerable:!1,configurable:!0});this.target.addEventListener("close",e),this.listeners++}dispatchClose(){if(this.isClosed)throw Object.defineProperty(Error("Cannot close a CloseController multiple times"),"__NEXT_ERROR_CODE",{value:"E229",enumerable:!1,configurable:!0});this.listeners>0&&this.target.dispatchEvent(new Event("close")),this.isClosed=!0}constructor(){this.target=new EventTarget,this.listeners=0,this.isClosed=!1}}function eM(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}let eD=Symbol.for("@next/request-context"),ej=e=>{let t=["/layout"];if(e.startsWith("/")){let r=e.split("/");for(let e=1;e<r.length+1;e++){let n=r.slice(0,e).join("/");n&&(n.endsWith("/page")||n.endsWith("/route")||(n=`${n}${!n.endsWith("/")?"/":""}layout`),t.push(n))}}return t};async function eU(e,t,r){let n=[],a=r&&r.size>0;for(let t of ej(e))t=`${j.gW}${t}`,n.push(t);if(t.pathname&&!a){let e=`${j.gW}${t.pathname}`;n.push(e)}return{tags:n,expirationsByCacheKind:function(e){let t=new Map,r=eA();if(r)for(let[n,a]of r)"getExpiration"in a&&t.set(n,eI(async()=>a.getExpiration(...e)));return t}(n)}}class eW extends b.J{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw Object.defineProperty(new p.CB({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new p.CB({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}waitUntil(){throw Object.defineProperty(new p.CB({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}let eL={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},eK=(e,t)=>eh().withPropagatedContext(e.headers,t,eL),eJ=!1;async function e$(e){var t;let n,a;if(!eJ&&(eJ=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:e,wrapRequestHandler:t}=r(905);e(),eK=t(eK)}await u();let i=void 0!==globalThis.__BUILD_MANIFEST;e.request.url=e.request.url.replace(/\.rsc($|\?)/,"$1");let o=new E.X(e.request.url,{headers:e.request.headers,nextConfig:e.request.nextConfig});for(let e of[...o.searchParams.keys()]){let t=o.searchParams.getAll(e),r=(0,h.wN)(e);if(r){for(let e of(o.searchParams.delete(r),t))o.searchParams.append(r,e);o.searchParams.delete(e)}}let s=o.buildId;o.buildId="";let c=(0,h.p$)(e.request.headers),l=c.has("x-nextjs-data"),d="1"===c.get("RSC");l&&"/index"===o.pathname&&(o.pathname="/");let p=new Map;if(!i)for(let e of A){let t=e.toLowerCase(),r=c.get(t);null!==r&&(p.set(t,r),c.delete(t))}let f=new eW({page:e.page,input:(function(e){let t="string"==typeof e,r=t?new URL(e):e;return r.searchParams.delete(x),t?r.toString():r})(o).toString(),init:{body:e.request.body,headers:c,method:e.request.method,nextConfig:e.request.nextConfig,signal:e.request.signal}});l&&Object.defineProperty(f,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCache&&e.IncrementalCache&&(globalThis.__incrementalCache=new e.IncrementalCache({appDir:!0,fetchCache:!0,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:e.request.headers,requestProtocol:"https",getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:eM()})}));let g=e.request.waitUntil??(null==(t=function(){let e=globalThis[eD];return null==e?void 0:e.get()}())?void 0:t.waitUntil),m=new w({request:f,page:e.page,context:g?{waitUntil:g}:void 0});if((n=await eK(f,()=>{if("/middleware"===e.page||"/src/middleware"===e.page){let t=m.waitUntil.bind(m),r=new eH;return eh().trace(F.execute,{spanName:`middleware ${f.method} ${f.nextUrl.pathname}`,attributes:{"http.target":f.nextUrl.pathname,"http.method":f.method}},async()=>{try{var n,i,o,c,l,u;let d=eM(),p=await eU("/",f.nextUrl,null),h=(l=f.nextUrl,u=e=>{a=e},function(e,t,r,n,a,i,o,s,c,l,u){function d(e){r&&r.setHeader("Set-Cookie",e)}let p={};return{type:"request",phase:e,implicitTags:i,url:{pathname:n.pathname,search:n.search??""},rootParams:a,get headers(){return p.headers||(p.headers=function(e){let t=R.from(e);for(let e of A)t.delete(e.toLowerCase());return R.seal(t)}(t.headers)),p.headers},get cookies(){if(!p.cookies){let e=new O.tm(R.from(t.headers));ey(t,e),p.cookies=I.seal(e)}return p.cookies},set cookies(value){p.cookies=value},get mutableCookies(){if(!p.mutableCookies){let e=function(e,t){let r=new O.tm(R.from(e));return M.wrap(r,t)}(t.headers,o||(r?d:void 0));ey(t,e),p.mutableCookies=e}return p.mutableCookies},get userspaceMutableCookies(){return p.userspaceMutableCookies||(p.userspaceMutableCookies=function(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return D("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return D("cookies().set"),e.set(...r),t};default:return C.l.get(e,r,n)}}});return t}(this.mutableCookies)),p.userspaceMutableCookies},get draftMode(){return p.draftMode||(p.draftMode=new eg(c,t,this.cookies,this.mutableCookies)),p.draftMode},renderResumeDataCache:s??null,isHmrRefresh:l,serverComponentsHmrCache:u||globalThis.__serverComponentsHmrCache}}("action",f,void 0,l,{},p,u,void 0,d,!1,void 0)),g=function({page:e,fallbackRouteParams:t,renderOpts:r,requestEndedState:n,isPrefetchRequest:a,buildId:i,previouslyRevalidatedTags:o}){var s;let c={isStaticGeneration:!r.shouldWaitOnAllReady&&!r.supportsDynamicResponse&&!r.isDraftMode&&!r.isPossibleServerAction,page:e,fallbackRouteParams:t,route:(s=e.split("/").reduce((e,t,r,n)=>t?"("===t[0]&&t.endsWith(")")||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t:e,"")).startsWith("/")?s:"/"+s,incrementalCache:r.incrementalCache||globalThis.__incrementalCache,cacheLifeProfiles:r.cacheLifeProfiles,isRevalidate:r.isRevalidate,isPrerendering:r.nextExport,fetchCache:r.fetchCache,isOnDemandRevalidate:r.isOnDemandRevalidate,isDraftMode:r.isDraftMode,requestEndedState:n,isPrefetchRequest:a,buildId:i,reactLoadableManifest:(null==r?void 0:r.reactLoadableManifest)||{},assetPrefix:(null==r?void 0:r.assetPrefix)||"",afterContext:function(e){let{waitUntil:t,onClose:r,onAfterTaskError:n}=e;return new ek({waitUntil:t,onClose:r,onTaskError:n})}(r),dynamicIOEnabled:r.experimental.dynamicIO,dev:r.dev??!1,previouslyRevalidatedTags:o,refreshTagsByCacheKind:function(){let e=new Map,t=eA();if(t)for(let[r,n]of t)"refreshTags"in n&&e.set(r,eI(async()=>n.refreshTags()));return e}()};return r.store=c,c}({page:"/",fallbackRouteParams:null,renderOpts:{cacheLifeProfiles:null==(i=e.request.nextConfig)||null==(n=i.experimental)?void 0:n.cacheLife,experimental:{isRoutePPREnabled:!1,dynamicIO:!1,authInterrupts:!!(null==(c=e.request.nextConfig)||null==(o=c.experimental)?void 0:o.authInterrupts)},supportsDynamicResponse:!0,waitUntil:t,onClose:r.onClose.bind(r),onAfterTaskError:void 0},requestEndedState:{ended:!1},isPrefetchRequest:f.headers.has(S),buildId:s??"",previouslyRevalidatedTags:[]});return await T.J.run(g,()=>k.FP.run(h,e.handler,f,m))}finally{setTimeout(()=>{r.dispatchClose()},0)}})}return e.handler(f,m)}))&&!(n instanceof Response))throw Object.defineProperty(TypeError("Expected an instance of Response to be returned"),"__NEXT_ERROR_CODE",{value:"E567",enumerable:!1,configurable:!0});n&&a&&n.headers.set("set-cookie",a);let b=null==n?void 0:n.headers.get("x-middleware-rewrite");if(n&&b&&(d||!i)){let t=new E.X(b,{forceLocale:!0,headers:e.request.headers,nextConfig:e.request.nextConfig});i||t.host!==f.nextUrl.host||(t.buildId=s||t.buildId,n.headers.set("x-middleware-rewrite",String(t)));let{url:r,isRelative:a}=_(t.toString(),o.toString());!i&&l&&n.headers.set("x-nextjs-rewrite",r),d&&a&&(o.pathname!==t.pathname&&n.headers.set("x-nextjs-rewritten-path",t.pathname),o.search!==t.search&&n.headers.set("x-nextjs-rewritten-query",t.search.slice(1)))}let P=null==n?void 0:n.headers.get("Location");if(n&&P&&!i){let t=new E.X(P,{forceLocale:!1,headers:e.request.headers,nextConfig:e.request.nextConfig});n=new Response(n.body,n),t.host===o.host&&(t.buildId=s||t.buildId,n.headers.set("Location",t.toString())),l&&(n.headers.delete("Location"),n.headers.set("x-nextjs-redirect",_(t.toString(),o.toString()).url))}let N=n||v.R.next(),H=N.headers.get("x-middleware-override-headers"),j=[];if(H){for(let[e,t]of p)N.headers.set(`x-middleware-request-${e}`,t),j.push(e);j.length>0&&N.headers.set("x-middleware-override-headers",H+","+j.join(","))}return{response:N,waitUntil:("internal"===m[y].kind?Promise.all(m[y].promises).then(()=>{}):void 0)??Promise.resolve(),fetchMetrics:f.fetchMetrics}}let eB=(0,r(709).withAuth)(function(e){},{callbacks:{authorized:({token:e,req:t})=>{if(t.nextUrl.pathname.startsWith("/dashboard"))return!!e;if(t.nextUrl.pathname.startsWith("/api")&&!t.nextUrl.pathname.startsWith("/api/auth")){let r=t.headers.get("origin"),n=t.headers.get("referer");if(/^\/api\/blog\/[^\/]+$/.test(t.nextUrl.pathname)){let e=t.nextUrl.pathname.split("/"),r=e[e.length-1];if(!r.startsWith("c")||r.length<=20)return!0}return!!("http://localhost:3000"===r||n?.startsWith("http://localhost:3000"))||"OPTIONS"===t.method||!!e}return!0}}}),eV={matcher:["/dashboard/:path*","/api/:path*"]},eq=(Object.values({NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401}),{...a}),eG=eq.middleware||eq.default,ez="/src/middleware";if("function"!=typeof eG)throw Object.defineProperty(Error(`The Middleware "${ez}" must export a \`middleware\` or a \`default\` function`),"__NEXT_ERROR_CODE",{value:"E120",enumerable:!1,configurable:!0});function eF(e){return e$({...e,page:ez,handler:async(...e)=>{try{return await eG(...e)}catch(a){let t=e[0],r=new URL(t.url),n=r.pathname+r.search;throw await c(a,{path:n,method:t.method,headers:Object.fromEntries(t.headers.entries())},{routerKind:"Pages Router",routePath:"/middleware",routeType:"middleware",revalidateReason:void 0}),a}}})}},991:(e,t,r)=>{"use strict";var n=r(921);Object.defineProperty(t,"__esModule",{value:!0});var a={encode:!0,decode:!0,getToken:!0};t.decode=p,t.encode=d,t.getToken=h;var i=r(537),o=n(r(710)),s=r(928),c=r(131),l=r(556);Object.keys(l).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===l[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return l[e]}}))});let u=()=>Date.now()/1e3|0;async function d(e){let{token:t={},secret:r,maxAge:n=2592e3,salt:a=""}=e,o=await f(r,a);return await new i.EncryptJWT(t).setProtectedHeader({alg:"dir",enc:"A256GCM"}).setIssuedAt().setExpirationTime(u()+n).setJti((0,s.v4)()).encrypt(o)}async function p(e){let{token:t,secret:r,salt:n=""}=e;if(!t)return null;let a=await f(r,n),{payload:o}=await (0,i.jwtDecrypt)(t,a,{clockTolerance:15});return o}async function h(e){var t,r,n,a;let{req:i,secureCookie:o=null!=(t=null==(r=process.env.NEXTAUTH_URL)?void 0:r.startsWith("https://"))?t:!!process.env.VERCEL,cookieName:s=o?"__Secure-next-auth.session-token":"next-auth.session-token",raw:l,decode:u=p,logger:d=console,secret:h=null!=(n=process.env.NEXTAUTH_SECRET)?n:process.env.AUTH_SECRET}=e;if(!i)throw Error("Must pass `req` to JWT getToken()");let f=new c.SessionStore({name:s,options:{secure:o}},{cookies:i.cookies,headers:i.headers},d).value,g=i.headers instanceof Headers?i.headers.get("authorization"):null==(a=i.headers)?void 0:a.authorization;if(f||(null==g?void 0:g.split(" ")[0])!=="Bearer"||(f=decodeURIComponent(g.split(" ")[1])),!f)return null;if(l)return f;try{return await u({token:f,secret:h})}catch(e){return null}}async function f(e,t){return await (0,o.default)("sha256",e,t,`NextAuth.js Generated Encryption Key${t?` (${t})`:""}`,32)}}},e=>{var t=e(e.s=970);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_src/middleware"]=t}]);
//# sourceMappingURL=middleware.js.map