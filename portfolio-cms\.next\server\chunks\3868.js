"use strict";exports.id=3868,exports.ids=[3868],exports.modules={43:(e,t,n)=>{n.d(t,{jH:()=>i});var r=n(43210);n(60687);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},1359:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(43210),o=0;function i(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},9510:(e,t,n)=>{n.d(t,{N:()=>u});var r=n(43210),o=n(11273),i=n(98599),a=n(8730),l=n(60687);function u(e){let t=e+"CollectionProvider",[n,u]=(0,o.A)(t),[c,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:n}=e,o=r.useRef(null),i=r.useRef(new Map).current;return(0,l.jsx)(c,{scope:t,itemMap:i,collectionRef:o,children:n})};d.displayName=t;let f=e+"CollectionSlot",p=(0,a.TL)(f),m=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=s(f,n),a=(0,i.s)(t,o.collectionRef);return(0,l.jsx)(p,{ref:a,children:r})});m.displayName=f;let h=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,a.TL)(h),y=r.forwardRef((e,t)=>{let{scope:n,children:o,...a}=e,u=r.useRef(null),c=(0,i.s)(t,u),d=s(h,n);return r.useEffect(()=>(d.itemMap.set(u,{ref:u,...a}),()=>void d.itemMap.delete(u))),(0,l.jsx)(g,{...{[v]:""},ref:c,children:o})});return y.displayName=h,[{Provider:d,Slot:m,ItemSlot:y},function(t){let n=s(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${v}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},u]}var c=new WeakMap;function s(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=d(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function d(e){return e!=e||0===e?0:Math.trunc(e)}},10022:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},11096:(e,t,n)=>{n.d(t,{H4:()=>E,bL:()=>b});var r=n(43210),o=n(11273),i=n(13495),a=n(66156),l=n(14163),u=n(57379);function c(){return()=>{}}var s=n(60687),d="Avatar",[f,p]=(0,o.A)(d),[m,h]=f(d),v=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[i,a]=r.useState("idle");return(0,s.jsx)(m,{scope:n,imageLoadingStatus:i,onImageLoadingStatusChange:a,children:(0,s.jsx)(l.sG.span,{...o,ref:t})})});v.displayName=d;var g="AvatarImage";r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:d=()=>{},...f}=e,p=h(g,n),m=function(e,{referrerPolicy:t,crossOrigin:n}){let o=(0,u.useSyncExternalStore)(c,()=>!0,()=>!1),i=r.useRef(null),l=o?(i.current||(i.current=new window.Image),i.current):null,[s,d]=r.useState(()=>x(l,e));return(0,a.N)(()=>{d(x(l,e))},[l,e]),(0,a.N)(()=>{let e=e=>()=>{d(e)};if(!l)return;let r=e("loaded"),o=e("error");return l.addEventListener("load",r),l.addEventListener("error",o),t&&(l.referrerPolicy=t),"string"==typeof n&&(l.crossOrigin=n),()=>{l.removeEventListener("load",r),l.removeEventListener("error",o)}},[l,n,t]),s}(o,f),v=(0,i.c)(e=>{d(e),p.onImageLoadingStatusChange(e)});return(0,a.N)(()=>{"idle"!==m&&v(m)},[m,v]),"loaded"===m?(0,s.jsx)(l.sG.img,{...f,ref:t,src:o}):null}).displayName=g;var y="AvatarFallback",w=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...i}=e,a=h(y,n),[u,c]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>c(!0),o);return()=>window.clearTimeout(e)}},[o]),u&&"loaded"!==a.imageLoadingStatus?(0,s.jsx)(l.sG.span,{...i,ref:t}):null});function x(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}w.displayName=y;var b=v,E=w},11273:(e,t,n)=>{n.d(t,{A:()=>a,q:()=>i});var r=n(43210),o=n(60687);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,a=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:a,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function a(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let a=r.createContext(i),l=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,c=n?.[e]?.[l]||a,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[l]||a,c=r.useContext(u);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},13495:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(43210);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},14163:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>l});var r=n(43210),o=n(51215),i=n(8730),a=n(60687),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},16189:(e,t,n)=>{var r=n(65773);n.o(r,"notFound")&&n.d(t,{notFound:function(){return r.notFound}}),n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}})},18179:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("folder-open",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]])},18853:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(43210),o=n(66156);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},25028:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(43210),o=n(51215),i=n(14163),a=n(66156),l=n(60687),u=r.forwardRef((e,t)=>{let{container:n,...u}=e,[c,s]=r.useState(!1);(0,a.N)(()=>s(!0),[]);let d=n||c&&globalThis?.document?.body;return d?o.createPortal((0,l.jsx)(i.sG.div,{...u,ref:t}),d):null});u.displayName="Portal"},27351:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])},29398:(e,t,n)=>{n.d(t,{UC:()=>e9,q7:()=>tt,JU:()=>te,ZL:()=>e7,bL:()=>e8,wv:()=>tn,l9:()=>e4});var r=n(43210),o=n(70569),i=n(98599),a=n(11273),l=n(65551),u=n(14163),c=n(9510),s=n(43),d=n(31355),f=n(1359),p=n(32547),m=n(96963),h=n(55509),v=n(25028),g=n(46059),y=n(13495),w=n(60687),x="rovingFocusGroup.onEntryFocus",b={bubbles:!1,cancelable:!0},E="RovingFocusGroup",[R,C,M]=(0,c.N)(E),[A,S]=(0,a.A)(E,[M]),[k,T]=A(E),L=r.forwardRef((e,t)=>(0,w.jsx)(R.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,w.jsx)(R.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,w.jsx)(N,{...e,ref:t})})}));L.displayName=E;var N=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:c=!1,dir:d,currentTabStopId:f,defaultCurrentTabStopId:p,onCurrentTabStopIdChange:m,onEntryFocus:h,preventScrollOnEntryFocus:v=!1,...g}=e,R=r.useRef(null),M=(0,i.s)(t,R),A=(0,s.jH)(d),[S,T]=(0,l.i)({prop:f,defaultProp:p??null,onChange:m,caller:E}),[L,N]=r.useState(!1),P=(0,y.c)(h),D=C(n),O=r.useRef(!1),[I,F]=r.useState(0);return r.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(x,P),()=>e.removeEventListener(x,P)},[P]),(0,w.jsx)(k,{scope:n,orientation:a,dir:A,loop:c,currentTabStopId:S,onItemFocus:r.useCallback(e=>T(e),[T]),onItemShiftTab:r.useCallback(()=>N(!0),[]),onFocusableItemAdd:r.useCallback(()=>F(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>F(e=>e-1),[]),children:(0,w.jsx)(u.sG.div,{tabIndex:L||0===I?-1:0,"data-orientation":a,...g,ref:M,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!O.current;if(e.target===e.currentTarget&&t&&!L){let t=new CustomEvent(x,b);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=D().filter(e=>e.focusable);j([e.find(e=>e.active),e.find(e=>e.id===S),...e].filter(Boolean).map(e=>e.ref.current),v)}}O.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>N(!1))})})}),P="RovingFocusGroupItem",D=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:a=!1,tabStopId:l,children:c,...s}=e,d=(0,m.B)(),f=l||d,p=T(P,n),h=p.currentTabStopId===f,v=C(n),{onFocusableItemAdd:g,onFocusableItemRemove:y,currentTabStopId:x}=p;return r.useEffect(()=>{if(i)return g(),()=>y()},[i,g,y]),(0,w.jsx)(R.ItemSlot,{scope:n,id:f,focusable:i,active:a,children:(0,w.jsx)(u.sG.span,{tabIndex:h?0:-1,"data-orientation":p.orientation,...s,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?p.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void p.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return O[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=p.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>j(n))}}),children:"function"==typeof c?c({isCurrentTabStop:h,hasTabStop:null!=x}):c})})});D.displayName=P;var O={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function j(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var I=n(8730),F=n(63376),_=n(42247),W=["Enter"," "],H=["ArrowUp","PageDown","End"],B=["ArrowDown","PageUp","Home",...H],K={ltr:[...W,"ArrowRight"],rtl:[...W,"ArrowLeft"]},G={ltr:["ArrowLeft"],rtl:["ArrowRight"]},z="Menu",[U,V,$]=(0,c.N)(z),[X,q]=(0,a.A)(z,[$,h.Bk,S]),Y=(0,h.Bk)(),Z=S(),[J,Q]=X(z),[ee,et]=X(z),en=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:i,onOpenChange:a,modal:l=!0}=e,u=Y(t),[c,d]=r.useState(null),f=r.useRef(!1),p=(0,y.c)(a),m=(0,s.jH)(i);return r.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,w.jsx)(h.bL,{...u,children:(0,w.jsx)(J,{scope:t,open:n,onOpenChange:p,content:c,onContentChange:d,children:(0,w.jsx)(ee,{scope:t,onClose:r.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:m,modal:l,children:o})})})};en.displayName=z;var er=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=Y(n);return(0,w.jsx)(h.Mz,{...o,...r,ref:t})});er.displayName="MenuAnchor";var eo="MenuPortal",[ei,ea]=X(eo,{forceMount:void 0}),el=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=Q(eo,t);return(0,w.jsx)(ei,{scope:t,forceMount:n,children:(0,w.jsx)(g.C,{present:n||i.open,children:(0,w.jsx)(v.Z,{asChild:!0,container:o,children:r})})})};el.displayName=eo;var eu="MenuContent",[ec,es]=X(eu),ed=r.forwardRef((e,t)=>{let n=ea(eu,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=Q(eu,e.__scopeMenu),a=et(eu,e.__scopeMenu);return(0,w.jsx)(U.Provider,{scope:e.__scopeMenu,children:(0,w.jsx)(g.C,{present:r||i.open,children:(0,w.jsx)(U.Slot,{scope:e.__scopeMenu,children:a.modal?(0,w.jsx)(ef,{...o,ref:t}):(0,w.jsx)(ep,{...o,ref:t})})})})}),ef=r.forwardRef((e,t)=>{let n=Q(eu,e.__scopeMenu),a=r.useRef(null),l=(0,i.s)(t,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,F.Eq)(e)},[]),(0,w.jsx)(eh,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),ep=r.forwardRef((e,t)=>{let n=Q(eu,e.__scopeMenu);return(0,w.jsx)(eh,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),em=(0,I.TL)("MenuContent.ScrollLock"),eh=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:a=!1,trapFocus:l,onOpenAutoFocus:u,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:m,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:x,onDismiss:b,disableOutsideScroll:E,...R}=e,C=Q(eu,n),M=et(eu,n),A=Y(n),S=Z(n),k=V(n),[T,N]=r.useState(null),P=r.useRef(null),D=(0,i.s)(t,P,C.onContentChange),O=r.useRef(0),j=r.useRef(""),I=r.useRef(0),F=r.useRef(null),W=r.useRef("right"),K=r.useRef(0),G=E?_.A:r.Fragment,z=e=>{let t=j.current+e,n=k().filter(e=>!e.disabled),r=document.activeElement,o=n.find(e=>e.ref.current===r)?.textValue,i=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=Math.max(i,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let l=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}(n.map(e=>e.textValue),t,o),a=n.find(e=>e.textValue===i)?.ref.current;!function e(t){j.current=t,window.clearTimeout(O.current),""!==t&&(O.current=window.setTimeout(()=>e(""),1e3))}(t),a&&setTimeout(()=>a.focus())};r.useEffect(()=>()=>window.clearTimeout(O.current),[]),(0,f.Oh)();let U=r.useCallback(e=>W.current===F.current?.side&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],l=t[i],u=a.x,c=a.y,s=l.x,d=l.y;c>r!=d>r&&n<(s-u)*(r-c)/(d-c)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,F.current?.area),[]);return(0,w.jsx)(ec,{scope:n,searchRef:j,onItemEnter:r.useCallback(e=>{U(e)&&e.preventDefault()},[U]),onItemLeave:r.useCallback(e=>{U(e)||(P.current?.focus(),N(null))},[U]),onTriggerLeave:r.useCallback(e=>{U(e)&&e.preventDefault()},[U]),pointerGraceTimerRef:I,onPointerGraceIntentChange:r.useCallback(e=>{F.current=e},[]),children:(0,w.jsx)(G,{...E?{as:em,allowPinchZoom:!0}:void 0,children:(0,w.jsx)(p.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.m)(u,e=>{e.preventDefault(),P.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,w.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:x,onDismiss:b,children:(0,w.jsx)(L,{asChild:!0,...S,dir:M.dir,orientation:"vertical",loop:a,currentTabStopId:T,onCurrentTabStopIdChange:N,onEntryFocus:(0,o.m)(m,e=>{M.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,w.jsx)(h.UC,{role:"menu","aria-orientation":"vertical","data-state":eB(C.open),"data-radix-menu-content":"",dir:M.dir,...A,...R,ref:D,style:{outline:"none",...R.style},onKeyDown:(0,o.m)(R.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&z(e.key));let o=P.current;if(e.target!==o||!B.includes(e.key))return;e.preventDefault();let i=k().filter(e=>!e.disabled).map(e=>e.ref.current);H.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(O.current),j.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,ez(e=>{let t=e.target,n=K.current!==e.clientX;e.currentTarget.contains(t)&&n&&(W.current=e.clientX>K.current?"right":"left",K.current=e.clientX)}))})})})})})})});ed.displayName=eu;var ev=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,w.jsx)(u.sG.div,{role:"group",...r,ref:t})});ev.displayName="MenuGroup";var eg=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,w.jsx)(u.sG.div,{...r,ref:t})});eg.displayName="MenuLabel";var ey="MenuItem",ew="menu.itemSelect",ex=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:a,...l}=e,c=r.useRef(null),s=et(ey,e.__scopeMenu),d=es(ey,e.__scopeMenu),f=(0,i.s)(t,c),p=r.useRef(!1);return(0,w.jsx)(eb,{...l,ref:f,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=c.current;if(!n&&e){let t=new CustomEvent(ew,{bubbles:!0,cancelable:!0});e.addEventListener(ew,e=>a?.(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{p.current||e.currentTarget?.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;n||t&&" "===e.key||W.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ex.displayName=ey;var eb=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:a=!1,textValue:l,...c}=e,s=es(ey,n),d=Z(n),f=r.useRef(null),p=(0,i.s)(t,f),[m,h]=r.useState(!1),[v,g]=r.useState("");return r.useEffect(()=>{let e=f.current;e&&g((e.textContent??"").trim())},[c.children]),(0,w.jsx)(U.ItemSlot,{scope:n,disabled:a,textValue:l??v,children:(0,w.jsx)(D,{asChild:!0,...d,focusable:!a,children:(0,w.jsx)(u.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...c,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,ez(e=>{a?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,ez(e=>s.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>h(!0)),onBlur:(0,o.m)(e.onBlur,()=>h(!1))})})})}),eE=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...i}=e;return(0,w.jsx)(eL,{scope:e.__scopeMenu,checked:n,children:(0,w.jsx)(ex,{role:"menuitemcheckbox","aria-checked":eK(n)?"mixed":n,...i,ref:t,"data-state":eG(n),onSelect:(0,o.m)(i.onSelect,()=>r?.(!!eK(n)||!n),{checkForDefaultPrevented:!1})})})});eE.displayName="MenuCheckboxItem";var eR="MenuRadioGroup",[eC,eM]=X(eR,{value:void 0,onValueChange:()=>{}}),eA=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,y.c)(r);return(0,w.jsx)(eC,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,w.jsx)(ev,{...o,ref:t})})});eA.displayName=eR;var eS="MenuRadioItem",ek=r.forwardRef((e,t)=>{let{value:n,...r}=e,i=eM(eS,e.__scopeMenu),a=n===i.value;return(0,w.jsx)(eL,{scope:e.__scopeMenu,checked:a,children:(0,w.jsx)(ex,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":eG(a),onSelect:(0,o.m)(r.onSelect,()=>i.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});ek.displayName=eS;var eT="MenuItemIndicator",[eL,eN]=X(eT,{checked:!1}),eP=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=eN(eT,n);return(0,w.jsx)(g.C,{present:r||eK(i.checked)||!0===i.checked,children:(0,w.jsx)(u.sG.span,{...o,ref:t,"data-state":eG(i.checked)})})});eP.displayName=eT;var eD=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,w.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});eD.displayName="MenuSeparator";var eO=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=Y(n);return(0,w.jsx)(h.i3,{...o,...r,ref:t})});eO.displayName="MenuArrow";var[ej,eI]=X("MenuSub"),eF="MenuSubTrigger",e_=r.forwardRef((e,t)=>{let n=Q(eF,e.__scopeMenu),a=et(eF,e.__scopeMenu),l=eI(eF,e.__scopeMenu),u=es(eF,e.__scopeMenu),c=r.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:d}=u,f={__scopeMenu:e.__scopeMenu},p=r.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return r.useEffect(()=>p,[p]),r.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),d(null)}},[s,d]),(0,w.jsx)(er,{asChild:!0,...f,children:(0,w.jsx)(eb,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":l.contentId,"data-state":eB(n.open),...e,ref:(0,i.t)(t,l.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,ez(t=>{u.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||c.current||(u.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,ez(e=>{p();let t=n.content?.getBoundingClientRect();if(t){let r=n.content?.dataset.side,o="right"===r,i=t[o?"left":"right"],a=t[o?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:i,y:t.top},{x:a,y:t.top},{x:a,y:t.bottom},{x:i,y:t.bottom}],side:r}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let r=""!==u.searchRef.current;e.disabled||r&&" "===t.key||K[a.dir].includes(t.key)&&(n.onOpenChange(!0),n.content?.focus(),t.preventDefault())})})})});e_.displayName=eF;var eW="MenuSubContent",eH=r.forwardRef((e,t)=>{let n=ea(eu,e.__scopeMenu),{forceMount:a=n.forceMount,...l}=e,u=Q(eu,e.__scopeMenu),c=et(eu,e.__scopeMenu),s=eI(eW,e.__scopeMenu),d=r.useRef(null),f=(0,i.s)(t,d);return(0,w.jsx)(U.Provider,{scope:e.__scopeMenu,children:(0,w.jsx)(g.C,{present:a||u.open,children:(0,w.jsx)(U.Slot,{scope:e.__scopeMenu,children:(0,w.jsx)(eh,{id:s.contentId,"aria-labelledby":s.triggerId,...l,ref:f,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{c.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=G[c.dir].includes(e.key);t&&n&&(u.onOpenChange(!1),s.trigger?.focus(),e.preventDefault())})})})})})});function eB(e){return e?"open":"closed"}function eK(e){return"indeterminate"===e}function eG(e){return eK(e)?"indeterminate":e?"checked":"unchecked"}function ez(e){return t=>"mouse"===t.pointerType?e(t):void 0}eH.displayName=eW;var eU="DropdownMenu",[eV,e$]=(0,a.A)(eU,[q]),eX=q(),[eq,eY]=eV(eU),eZ=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:i,defaultOpen:a,onOpenChange:u,modal:c=!0}=e,s=eX(t),d=r.useRef(null),[f,p]=(0,l.i)({prop:i,defaultProp:a??!1,onChange:u,caller:eU});return(0,w.jsx)(eq,{scope:t,triggerId:(0,m.B)(),triggerRef:d,contentId:(0,m.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,w.jsx)(en,{...s,open:f,onOpenChange:p,dir:o,modal:c,children:n})})};eZ.displayName=eU;var eJ="DropdownMenuTrigger",eQ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...a}=e,l=eY(eJ,n),c=eX(n);return(0,w.jsx)(er,{asChild:!0,...c,children:(0,w.jsx)(u.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...a,ref:(0,i.t)(t,l.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eQ.displayName=eJ;var e0=e=>{let{__scopeDropdownMenu:t,...n}=e,r=eX(t);return(0,w.jsx)(el,{...r,...n})};e0.displayName="DropdownMenuPortal";var e1="DropdownMenuContent",e2=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=eY(e1,n),l=eX(n),u=r.useRef(!1);return(0,w.jsx)(ed,{id:a.contentId,"aria-labelledby":a.triggerId,...l,...i,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{u.current||a.triggerRef.current?.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!a.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e2.displayName=e1,r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eX(n);return(0,w.jsx)(ev,{...o,...r,ref:t})}).displayName="DropdownMenuGroup";var e6=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eX(n);return(0,w.jsx)(eg,{...o,...r,ref:t})});e6.displayName="DropdownMenuLabel";var e3=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eX(n);return(0,w.jsx)(ex,{...o,...r,ref:t})});e3.displayName="DropdownMenuItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eX(n);return(0,w.jsx)(eE,{...o,...r,ref:t})}).displayName="DropdownMenuCheckboxItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eX(n);return(0,w.jsx)(eA,{...o,...r,ref:t})}).displayName="DropdownMenuRadioGroup",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eX(n);return(0,w.jsx)(ek,{...o,...r,ref:t})}).displayName="DropdownMenuRadioItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eX(n);return(0,w.jsx)(eP,{...o,...r,ref:t})}).displayName="DropdownMenuItemIndicator";var e5=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eX(n);return(0,w.jsx)(eD,{...o,...r,ref:t})});e5.displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eX(n);return(0,w.jsx)(eO,{...o,...r,ref:t})}).displayName="DropdownMenuArrow",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eX(n);return(0,w.jsx)(e_,{...o,...r,ref:t})}).displayName="DropdownMenuSubTrigger",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eX(n);return(0,w.jsx)(eH,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var e8=eZ,e4=eQ,e7=e0,e9=e2,te=e6,tt=e3,tn=e5},31355:(e,t,n)=>{n.d(t,{qW:()=>f});var r,o=n(43210),i=n(70569),a=n(14163),l=n(98599),u=n(13495),c=n(60687),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:f,onPointerDownOutside:h,onFocusOutside:v,onInteractOutside:g,onDismiss:y,...w}=e,x=o.useContext(d),[b,E]=o.useState(null),R=b?.ownerDocument??globalThis?.document,[,C]=o.useState({}),M=(0,l.s)(t,e=>E(e)),A=Array.from(x.layers),[S]=[...x.layersWithOutsidePointerEventsDisabled].slice(-1),k=A.indexOf(S),T=b?A.indexOf(b):-1,L=x.layersWithOutsidePointerEventsDisabled.size>0,N=T>=k,P=function(e,t=globalThis?.document){let n=(0,u.c)(e),r=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){m("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=r,t.addEventListener("click",i.current,{once:!0})):r()}else t.removeEventListener("click",i.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...x.branches].some(e=>e.contains(t));N&&!n&&(h?.(e),g?.(e),e.defaultPrevented||y?.())},R),D=function(e,t=globalThis?.document){let n=(0,u.c)(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&m("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...x.branches].some(e=>e.contains(t))&&(v?.(e),g?.(e),e.defaultPrevented||y?.())},R);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{T===x.layers.size-1&&(f?.(e),!e.defaultPrevented&&y&&(e.preventDefault(),y()))},R),o.useEffect(()=>{if(b)return n&&(0===x.layersWithOutsidePointerEventsDisabled.size&&(r=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),x.layersWithOutsidePointerEventsDisabled.add(b)),x.layers.add(b),p(),()=>{n&&1===x.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=r)}},[b,R,n,x]),o.useEffect(()=>()=>{b&&(x.layers.delete(b),x.layersWithOutsidePointerEventsDisabled.delete(b),p())},[b,x]),o.useEffect(()=>{let e=()=>C({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(a.sG.div,{...w,ref:M,style:{pointerEvents:L?N?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,D.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,P.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function m(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,a.hO)(o,i):o.dispatchEvent(i)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,l.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(a.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},32547:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(43210),o=n(98599),i=n(14163),a=n(13495),l=n(60687),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[w,x]=r.useState(null),b=(0,a.c)(v),E=(0,a.c)(g),R=r.useRef(null),C=(0,o.s)(t,e=>x(e)),M=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(M.paused||!w)return;let t=e.target;w.contains(t)?R.current=t:m(R.current,{select:!0})},t=function(e){if(M.paused||!w)return;let t=e.relatedTarget;null!==t&&(w.contains(t)||m(R.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,M.paused]),r.useEffect(()=>{if(w){h.add(M);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,s);w.addEventListener(u,b),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(m(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(w))}return()=>{w.removeEventListener(u,b),setTimeout(()=>{let t=new CustomEvent(c,s);w.addEventListener(c,E),w.dispatchEvent(t),t.defaultPrevented||m(e??document.body,{select:!0}),w.removeEventListener(c,E),h.remove(M)},0)}}},[w,b,E,M]);let A=r.useCallback(e=>{if(!n&&!d||M.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&m(i,{select:!0})):(e.preventDefault(),n&&m(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,M.paused]);return(0,l.jsx)(i.sG.div,{tabIndex:-1,...y,ref:C,onKeyDown:A})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function m(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var h=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},40083:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},42247:(e,t,n)=>{n.d(t,{A:()=>V});var r,o,i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function a(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var l=("function"==typeof SuppressedError&&SuppressedError,n(43210)),u="right-scroll-bar-position",c="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,f=new WeakMap;function p(e){return e}var m=function(e){void 0===e&&(e={});var t,n,r,o,a=(t=null,void 0===n&&(n=p),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=i({async:!0,ssr:!1},e),a}(),h=function(){},v=l.forwardRef(function(e,t){var n,r,o,u,c=l.useRef(null),p=l.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),v=p[0],g=p[1],y=e.forwardProps,w=e.children,x=e.className,b=e.removeScrollBar,E=e.enabled,R=e.shards,C=e.sideCar,M=e.noRelative,A=e.noIsolation,S=e.inert,k=e.allowPinchZoom,T=e.as,L=e.gapMode,N=a(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),P=(n=[c,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,d(function(){var e=f.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}f.set(u,n)},[n]),u),D=i(i({},N),v);return l.createElement(l.Fragment,null,E&&l.createElement(C,{sideCar:m,removeScrollBar:b,shards:R,noRelative:M,noIsolation:A,inert:S,setCallbacks:g,allowPinchZoom:!!k,lockRef:c,gapMode:L}),y?l.cloneElement(l.Children.only(w),i(i({},D),{ref:P})):l.createElement(void 0===T?"div":T,i({},D,{className:x,ref:P}),w))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:c,zeroRight:u};var g=function(e){var t=e.sideCar,n=a(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,i({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=y();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},x=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},R=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return b;var t=R(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},M=x(),A="data-scroll-locked",S=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(A,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(A,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},k=function(){var e=parseInt(document.body.getAttribute(A)||"0",10);return isFinite(e)?e:0},T=function(){l.useEffect(function(){return document.body.setAttribute(A,(k()+1).toString()),function(){var e=k()-1;e<=0?document.body.removeAttribute(A):document.body.setAttribute(A,e.toString())}},[])},L=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;T();var i=l.useMemo(function(){return C(o)},[o]);return l.createElement(M,{styles:S(i,!t,o,n?"":"!important")})},N=!1;if("undefined"!=typeof window)try{var P=Object.defineProperty({},"passive",{get:function(){return N=!0,!0}});window.addEventListener("test",P,P),window.removeEventListener("test",P,P)}catch(e){N=!1}var D=!!N&&{passive:!1},O=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},j=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),I(e,r)){var o=F(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},I=function(e,t){return"v"===e?O(t,"overflowY"):O(t,"overflowX")},F=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},_=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,u=n.target,c=t.contains(u),s=!1,d=l>0,f=0,p=0;do{if(!u)break;var m=F(e,u),h=m[0],v=m[1]-m[2]-a*h;(h||v)&&I(e,u)&&(f+=v,p+=h);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&l>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},W=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},H=function(e){return[e.deltaX,e.deltaY]},B=function(e){return e&&"current"in e?e.current:e},K=0,G=[];let z=(r=function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(K++)[0],i=l.useState(x)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(B),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=W(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=j(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=j(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return _(p,t,e,"h"===p?u:c,!0)},[]),c=l.useCallback(function(e){if(G.length&&G[G.length-1]===i){var n="deltaY"in e?H(e):W(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(B).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){n.current=W(e),r.current=void 0},[]),f=l.useCallback(function(t){s(t.type,H(t),t.target,u(t,e.lockRef.current))},[]),p=l.useCallback(function(t){s(t.type,W(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return G.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,D),document.addEventListener("touchmove",c,D),document.addEventListener("touchstart",d,D),function(){G=G.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,D),document.removeEventListener("touchmove",c,D),document.removeEventListener("touchstart",d,D)}},[]);var m=e.removeScrollBar,h=e.inert;return l.createElement(l.Fragment,null,h?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?l.createElement(L,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},m.useMedium(r),g);var U=l.forwardRef(function(e,t){return l.createElement(v,i({},e,{ref:t,sideCar:z}))});U.classNames=v.classNames;let V=U},46059:(e,t,n)=>{n.d(t,{C:()=>a});var r=n(43210),o=n(98599),i=n(66156),a=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[o,a]=r.useState(),u=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=l(u.current);s.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=u.current,n=c.current;if(n!==e){let r=s.current,o=l(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,i.N)(()=>{if(o){let e,t=o.ownerDocument.defaultView??window,n=n=>{let r=l(u.current).includes(n.animationName);if(n.target===o&&r&&(f("ANIMATION_END"),!c.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(s.current=l(u.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,a(e)},[])}}(t),u="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),c=(0,o.s)(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||a.isPresent?r.cloneElement(u,{ref:c}):null};function l(e){return e?.animationName||"none"}a.displayName="Presence"},49625:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},53332:(e,t,n)=>{var r=n(43210),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,a=r.useEffect,l=r.useLayoutEffect,u=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),o=r[0].inst,s=r[1];return l(function(){o.value=n,o.getSnapshot=t,c(o)&&s({inst:o})},[e,n,t]),a(function(){return c(o)&&s({inst:o}),e(function(){c(o)&&s({inst:o})})},[e]),u(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:s},53411:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},55509:(e,t,n)=>{n.d(t,{Mz:()=>te,i3:()=>tn,UC:()=>tt,bL:()=>e9,Bk:()=>e$});var r=n(43210);let o=["top","right","bottom","left"],i=Math.min,a=Math.max,l=Math.round,u=Math.floor,c=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function m(e){return e.split("-")[1]}function h(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}let g=new Set(["top","bottom"]);function y(e){return g.has(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>d[e])}let x=["left","right"],b=["right","left"],E=["top","bottom"],R=["bottom","top"];function C(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function M(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function A(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function S(e,t,n){let r,{reference:o,floating:i}=e,a=y(t),l=h(y(t)),u=v(l),c=p(t),s="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,g=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(m(t)){case"start":r[l]-=g*(n&&s?-1:1);break;case"end":r[l]+=g*(n&&s?-1:1)}return r}let k=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),u=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=S(c,r,u),f=r,p={},m=0;for(let n=0;n<l.length;n++){let{name:i,fn:h}=l[n],{x:v,y:g,data:y,reset:w}=await h({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});s=null!=v?v:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=S(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function T(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:p=!1,padding:m=0}=f(t,e),h=M(m),v=l[p?"floating"===d?"reference":"floating":d],g=A(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:s,strategy:u})),y="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),x=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},b=A(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:w,strategy:u}):y);return{top:(g.top-b.top+h.top)/x.y,bottom:(b.bottom-g.bottom+h.bottom)/x.y,left:(g.left-b.left+h.left)/x.x,right:(b.right-g.right+h.right)/x.x}}function L(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function N(e){return o.some(t=>e[t]>=0)}let P=new Set(["left","top"]);async function D(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=p(n),l=m(n),u="y"===y(n),c=P.has(a)?-1:1,s=i&&u?-1:1,d=f(t,e),{mainAxis:h,crossAxis:v,alignmentAxis:g}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof g&&(v="end"===l?-1*g:g),u?{x:v*s,y:h*c}:{x:h*c,y:v*s}}function O(){return"undefined"!=typeof window}function j(e){return _(e)?(e.nodeName||"").toLowerCase():"#document"}function I(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function F(e){var t;return null==(t=(_(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function _(e){return!!O()&&(e instanceof Node||e instanceof I(e).Node)}function W(e){return!!O()&&(e instanceof Element||e instanceof I(e).Element)}function H(e){return!!O()&&(e instanceof HTMLElement||e instanceof I(e).HTMLElement)}function B(e){return!!O()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof I(e).ShadowRoot)}let K=new Set(["inline","contents"]);function G(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!K.has(o)}let z=new Set(["table","td","th"]),U=[":popover-open",":modal"];function V(e){return U.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let $=["transform","translate","scale","rotate","perspective"],X=["transform","translate","scale","rotate","perspective","filter"],q=["paint","layout","strict","content"];function Y(e){let t=Z(),n=W(e)?ee(e):e;return $.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||X.some(e=>(n.willChange||"").includes(e))||q.some(e=>(n.contain||"").includes(e))}function Z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let J=new Set(["html","body","#document"]);function Q(e){return J.has(j(e))}function ee(e){return I(e).getComputedStyle(e)}function et(e){return W(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function en(e){if("html"===j(e))return e;let t=e.assignedSlot||e.parentNode||B(e)&&e.host||F(e);return B(t)?t.host:t}function er(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=en(t);return Q(n)?t.ownerDocument?t.ownerDocument.body:t.body:H(n)&&G(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=I(o);if(i){let e=eo(a);return t.concat(a,a.visualViewport||[],G(o)?o:[],e&&n?er(e):[])}return t.concat(o,er(o,[],n))}function eo(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ei(e){let t=ee(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=H(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,u=l(n)!==i||l(r)!==a;return u&&(n=i,r=a),{width:n,height:r,$:u}}function ea(e){return W(e)?e:e.contextElement}function el(e){let t=ea(e);if(!H(t))return c(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ei(t),a=(i?l(n.width):n.width)/r,u=(i?l(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),u&&Number.isFinite(u)||(u=1),{x:a,y:u}}let eu=c(0);function ec(e){let t=I(e);return Z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eu}function es(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=ea(e),l=c(1);t&&(r?W(r)&&(l=el(r)):l=el(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===I(a))&&o)?ec(a):c(0),s=(i.left+u.x)/l.x,d=(i.top+u.y)/l.y,f=i.width/l.x,p=i.height/l.y;if(a){let e=I(a),t=r&&W(r)?I(r):r,n=e,o=eo(n);for(;o&&r&&t!==n;){let e=el(o),t=o.getBoundingClientRect(),r=ee(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,d*=e.y,f*=e.x,p*=e.y,s+=i,d+=a,o=eo(n=I(o))}}return A({width:f,height:p,x:s,y:d})}function ed(e,t){let n=et(e).scrollLeft;return t?t.left+n:es(F(e)).left+n}function ef(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ed(e,r)),y:r.top+t.scrollTop}}let ep=new Set(["absolute","fixed"]);function em(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=I(e),r=F(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,u=0;if(o){i=o.width,a=o.height;let e=Z();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,u=o.offsetTop)}return{width:i,height:a,x:l,y:u}}(e,n);else if("document"===t)r=function(e){let t=F(e),n=et(e),r=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+ed(e),u=-n.scrollTop;return"rtl"===ee(r).direction&&(l+=a(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:u}}(F(e));else if(W(t))r=function(e,t){let n=es(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=H(e)?el(e):c(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=ec(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return A(r)}function eh(e){return"static"===ee(e).position}function ev(e,t){if(!H(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let n=e.offsetParent;return F(e)===n&&(n=n.ownerDocument.body),n}function eg(e,t){var n;let r=I(e);if(V(e))return r;if(!H(e)){let t=en(e);for(;t&&!Q(t);){if(W(t)&&!eh(t))return t;t=en(t)}return r}let o=ev(e,t);for(;o&&(n=o,z.has(j(n)))&&eh(o);)o=ev(o,t);return o&&Q(o)&&eh(o)&&!Y(o)?r:o||function(e){let t=en(e);for(;H(t)&&!Q(t);){if(Y(t))return t;if(V(t))break;t=en(t)}return null}(e)||r}let ey=async function(e){let t=this.getOffsetParent||eg,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=H(t),o=F(t),i="fixed"===n,a=es(e,!0,i,t),l={scrollLeft:0,scrollTop:0},u=c(0);if(r||!r&&!i)if(("body"!==j(t)||G(o))&&(l=et(t)),r){let e=es(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=ed(o));i&&!r&&o&&(u.x=ed(o));let s=!o||r||i?c(0):ef(o,l);return{x:a.left+l.scrollLeft-u.x-s.x,y:a.top+l.scrollTop-u.y-s.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ew={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=F(r),l=!!t&&V(t.floating);if(r===a||l&&i)return n;let u={scrollLeft:0,scrollTop:0},s=c(1),d=c(0),f=H(r);if((f||!f&&!i)&&(("body"!==j(r)||G(a))&&(u=et(r)),H(r))){let e=es(r);s=el(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!a||f||i?c(0):ef(a,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+d.x+p.x,y:n.y*s.y-u.scrollTop*s.y+d.y+p.y}},getDocumentElement:F,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?V(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=er(e,[],!1).filter(e=>W(e)&&"body"!==j(e)),o=null,i="fixed"===ee(e).position,a=i?en(e):e;for(;W(a)&&!Q(a);){let t=ee(a),n=Y(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&ep.has(o.position)||G(a)&&!n&&function e(t,n){let r=en(t);return!(r===n||!W(r)||Q(r))&&("fixed"===ee(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=en(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=l[0],c=l.reduce((e,n)=>{let r=em(t,n,o);return e.top=a(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=a(r.left,e.left),e},em(t,u,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:eg,getElementRects:ey,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ei(e);return{width:t,height:n}},getScale:el,isElement:W,isRTL:function(e){return"rtl"===ee(e).direction}};function ex(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eb=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:u,elements:c,middlewareData:s}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let g=M(p),w={x:n,y:r},x=h(y(o)),b=v(x),E=await u.getDimensions(d),R="y"===x,C=R?"clientHeight":"clientWidth",A=l.reference[b]+l.reference[x]-w[x]-l.floating[b],S=w[x]-l.reference[x],k=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),T=k?k[C]:0;T&&await (null==u.isElement?void 0:u.isElement(k))||(T=c.floating[C]||l.floating[b]);let L=T/2-E[b]/2-1,N=i(g[R?"top":"left"],L),P=i(g[R?"bottom":"right"],L),D=T-E[b]-P,O=T/2-E[b]/2+(A/2-S/2),j=a(N,i(O,D)),I=!s.arrow&&null!=m(o)&&O!==j&&l.reference[b]/2-(O<N?N:P)-E[b]/2<0,F=I?O<N?O-N:O-D:0;return{[x]:w[x]+F,data:{[x]:j,centerOffset:O-j-F,...I&&{alignmentOffset:F}},reset:I}}}),eE=(e,t,n)=>{let r=new Map,o={platform:ew,...n},i={...o.platform,_c:r};return k(e,t,{...o,platform:i})};var eR=n(51215),eC="undefined"!=typeof document?r.useLayoutEffect:function(){};function eM(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eM(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eM(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eA(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eS(e,t){let n=eA(e);return Math.round(t*n)/n}function ek(e){let t=r.useRef(e);return eC(()=>{t.current=e}),t}let eT=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eb({element:n.current,padding:r}).fn(t):{}:n?eb({element:n,padding:r}).fn(t):{}}}),eL=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,u=await D(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:a}}}}}(e),options:[e,t]}),eN=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=f(e,t),d={x:n,y:r},m=await T(t,s),v=y(p(o)),g=h(v),w=d[g],x=d[v];if(l){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=w+m[e],r=w-m[t];w=a(n,i(w,r))}if(u){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=x+m[e],r=x-m[t];x=a(n,i(x,r))}let b=c.fn({...t,[g]:w,[v]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[g]:l,[v]:u}}}}}}(e),options:[e,t]}),eP=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:u=!0,crossAxis:c=!0}=f(e,t),s={x:n,y:r},d=y(o),m=h(d),v=s[m],g=s[d],w=f(l,t),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===m?"height":"width",t=i.reference[m]-i.floating[e]+x.mainAxis,n=i.reference[m]+i.reference[e]-x.mainAxis;v<t?v=t:v>n&&(v=n)}if(c){var b,E;let e="y"===m?"width":"height",t=P.has(p(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(b=a.offset)?void 0:b[d])||0)+(t?0:x.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(E=a.offset)?void 0:E[d])||0)-(t?x.crossAxis:0);g<n?g=n:g>r&&(g=r)}return{[m]:v,[d]:g}}}}(e),options:[e,t]}),eD=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:g}=t,{mainAxis:M=!0,crossAxis:A=!0,fallbackPlacements:S,fallbackStrategy:k="bestFit",fallbackAxisSideDirection:L="none",flipAlignment:N=!0,...P}=f(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let D=p(l),O=y(s),j=p(s)===s,I=await (null==d.isRTL?void 0:d.isRTL(g.floating)),F=S||(j||!N?[C(s)]:function(e){let t=C(e);return[w(e),t,w(t)]}(s)),_="none"!==L;!S&&_&&F.push(...function(e,t,n,r){let o=m(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?b:x;return t?x:b;case"left":case"right":return t?E:R;default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(w)))),i}(s,N,L,I));let W=[s,...F],H=await T(t,P),B=[],K=(null==(r=u.flip)?void 0:r.overflows)||[];if(M&&B.push(H[D]),A){let e=function(e,t,n){void 0===n&&(n=!1);let r=m(e),o=h(y(e)),i=v(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=C(a)),[a,C(a)]}(l,c,I);B.push(H[e[0]],H[e[1]])}if(K=[...K,{placement:l,overflows:B}],!B.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=W[e];if(t&&("alignment"!==A||O===y(t)||K.every(e=>e.overflows[0]>0&&y(e.placement)===O)))return{data:{index:e,overflows:K},reset:{placement:t}};let n=null==(i=K.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(k){case"bestFit":{let e=null==(a=K.filter(e=>{if(_){let t=y(e.placement);return t===O||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=s}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eO=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l,{placement:u,rects:c,platform:s,elements:d}=t,{apply:h=()=>{},...v}=f(e,t),g=await T(t,v),w=p(u),x=m(u),b="y"===y(u),{width:E,height:R}=c.floating;"top"===w||"bottom"===w?(o=w,l=x===(await (null==s.isRTL?void 0:s.isRTL(d.floating))?"start":"end")?"left":"right"):(l=w,o="end"===x?"top":"bottom");let C=R-g.top-g.bottom,M=E-g.left-g.right,A=i(R-g[o],C),S=i(E-g[l],M),k=!t.middlewareData.shift,L=A,N=S;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(N=M),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(L=C),k&&!x){let e=a(g.left,0),t=a(g.right,0),n=a(g.top,0),r=a(g.bottom,0);b?N=E-2*(0!==e||0!==t?e+t:a(g.left,g.right)):L=R-2*(0!==n||0!==r?n+r:a(g.top,g.bottom))}await h({...t,availableWidth:N,availableHeight:L});let P=await s.getDimensions(d.floating);return E!==P.width||R!==P.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ej=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=L(await T(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:N(e)}}}case"escaped":{let e=L(await T(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:N(e)}}}default:return{}}}}}(e),options:[e,t]}),eI=(e,t)=>({...eT(e),options:[e,t]});var eF=n(14163),e_=n(60687),eW=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,e_.jsx)(eF.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,e_.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eW.displayName="Arrow";var eH=n(98599),eB=n(11273),eK=n(13495),eG=n(66156),ez=n(18853),eU="Popper",[eV,e$]=(0,eB.A)(eU),[eX,eq]=eV(eU),eY=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,e_.jsx)(eX,{scope:t,anchor:o,onAnchorChange:i,children:n})};eY.displayName=eU;var eZ="PopperAnchor",eJ=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,a=eq(eZ,n),l=r.useRef(null),u=(0,eH.s)(t,l);return r.useEffect(()=>{a.onAnchorChange(o?.current||l.current)}),o?null:(0,e_.jsx)(eF.sG.div,{...i,ref:u})});eJ.displayName=eZ;var eQ="PopperContent",[e0,e1]=eV(eQ),e2=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:l=0,align:c="center",alignOffset:s=0,arrowPadding:d=0,avoidCollisions:f=!0,collisionBoundary:p=[],collisionPadding:m=0,sticky:h="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:y,...w}=e,x=eq(eQ,n),[b,E]=r.useState(null),R=(0,eH.s)(t,e=>E(e)),[C,M]=r.useState(null),A=(0,ez.X)(C),S=A?.width??0,k=A?.height??0,T="number"==typeof m?m:{top:0,right:0,bottom:0,left:0,...m},L=Array.isArray(p)?p:[p],N=L.length>0,P={padding:T,boundary:L.filter(e8),altBoundary:N},{refs:D,floatingStyles:O,placement:j,isPositioned:I,middlewareData:_}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:l}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=r.useState(o);eM(p,o)||m(o);let[h,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),x=r.useCallback(e=>{e!==C.current&&(C.current=e,y(e))},[]),b=a||h,E=l||g,R=r.useRef(null),C=r.useRef(null),M=r.useRef(d),A=null!=c,S=ek(c),k=ek(i),T=ek(s),L=r.useCallback(()=>{if(!R.current||!C.current)return;let e={placement:t,strategy:n,middleware:p};k.current&&(e.platform=k.current),eE(R.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==T.current};N.current&&!eM(M.current,t)&&(M.current=t,eR.flushSync(()=>{f(t)}))})},[p,t,n,k,T]);eC(()=>{!1===s&&M.current.isPositioned&&(M.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[s]);let N=r.useRef(!1);eC(()=>(N.current=!0,()=>{N.current=!1}),[]),eC(()=>{if(b&&(R.current=b),E&&(C.current=E),b&&E){if(S.current)return S.current(b,E,L);L()}},[b,E,L,S,A]);let P=r.useMemo(()=>({reference:R,floating:C,setReference:w,setFloating:x}),[w,x]),D=r.useMemo(()=>({reference:b,floating:E}),[b,E]),O=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!D.floating)return e;let t=eS(D.floating,d.x),r=eS(D.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...eA(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,D.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:L,refs:P,elements:D,floatingStyles:O}),[d,L,P,D,O])}({strategy:"fixed",placement:o+("center"!==c?"-"+c:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=ea(e),m=l||c?[...p?er(p):[],...er(t)]:[];m.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let h=p&&d?function(e,t){let n,r=null,o=F(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function c(s,d){void 0===s&&(s=!1),void 0===d&&(d=1),l();let f=e.getBoundingClientRect(),{left:p,top:m,width:h,height:v}=f;if(s||t(),!h||!v)return;let g=u(m),y=u(o.clientWidth-(p+h)),w={rootMargin:-g+"px "+-y+"px "+-u(o.clientHeight-(m+v))+"px "+-u(p)+"px",threshold:a(0,i(1,d))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==d){if(!x)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||ex(f,e.getBoundingClientRect())||c(),x=!1}try{r=new IntersectionObserver(b,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),l}(p,n):null,v=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let y=f?es(e):null;return f&&function t(){let r=es(e);y&&!ex(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;m.forEach(e=>{l&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==h||h(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===g}),elements:{reference:x.anchor},middleware:[eL({mainAxis:l+k,alignmentAxis:s}),f&&eN({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?eP():void 0,...P}),f&&eD({...P}),eO({...P,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),C&&eI({element:C,padding:d}),e4({arrowWidth:S,arrowHeight:k}),v&&ej({strategy:"referenceHidden",...P})]}),[W,H]=e7(j),B=(0,eK.c)(y);(0,eG.N)(()=>{I&&B?.()},[I,B]);let K=_.arrow?.x,G=_.arrow?.y,z=_.arrow?.centerOffset!==0,[U,V]=r.useState();return(0,eG.N)(()=>{b&&V(window.getComputedStyle(b).zIndex)},[b]),(0,e_.jsx)("div",{ref:D.setFloating,"data-radix-popper-content-wrapper":"",style:{...O,transform:I?O.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:U,"--radix-popper-transform-origin":[_.transformOrigin?.x,_.transformOrigin?.y].join(" "),..._.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,e_.jsx)(e0,{scope:n,placedSide:W,onArrowChange:M,arrowX:K,arrowY:G,shouldHideArrow:z,children:(0,e_.jsx)(eF.sG.div,{"data-side":W,"data-align":H,...w,ref:R,style:{...w.style,animation:I?void 0:"none"}})})})});e2.displayName=eQ;var e6="PopperArrow",e3={top:"bottom",right:"left",bottom:"top",left:"right"},e5=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e1(e6,n),i=e3[o.placedSide];return(0,e_.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,e_.jsx)(eW,{...r,ref:t,style:{...r.style,display:"block"}})})});function e8(e){return null!==e}e5.displayName=e6;var e4=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[u,c]=e7(n),s={start:"0%",center:"50%",end:"100%"}[c],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+l/2,p="",m="";return"bottom"===u?(p=i?s:`${d}px`,m=`${-l}px`):"top"===u?(p=i?s:`${d}px`,m=`${r.floating.height+l}px`):"right"===u?(p=`${-l}px`,m=i?s:`${f}px`):"left"===u&&(p=`${r.floating.width+l}px`,m=i?s:`${f}px`),{data:{x:p,y:m}}}});function e7(e){let[t,n="center"]=e.split("-");return[t,n]}var e9=eY,te=eJ,tt=e2,tn=e5},57379:(e,t,n)=>{e.exports=n(53332)},57800:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},58869:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},58887:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},63376:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},l=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],d=[],f=new Set,p=new Set(c),m=function(e){!e||f.has(e)||(f.add(e),m(e.parentNode))};c.forEach(m);var h=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))h(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,l=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,l),s.set(e,u),d.push(e),1===l&&a&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),f.clear(),l++,function(){d.forEach(function(e){var t=o.get(e)-1,a=s.get(e)-1;o.set(e,t),s.set(e,a),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),a||e.removeAttribute(n)}),--l||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),c(o,i,n,"aria-hidden")):function(){return null}}},65551:(e,t,n)=>{n.d(t,{i:()=>l});var r,o=n(43210),i=n(66156),a=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function l({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,l,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),l=o.useRef(t);return a(()=>{l.current=t},[t]),o.useEffect(()=>{i.current!==n&&(l.current?.(n),i.current=n)},[n,i]),[n,r,l]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else l(t)},[c,e,l,u])]}Symbol("RADIX:SYNC_STATE")},66156:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(43210),o=globalThis?.document?r.useLayoutEffect:()=>{}},70569:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},79410:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},80375:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]])},84027:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},86561:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},96963:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(43210),i=n(66156),a=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function u(e){let[t,n]=o.useState(a());return(0,i.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}}};