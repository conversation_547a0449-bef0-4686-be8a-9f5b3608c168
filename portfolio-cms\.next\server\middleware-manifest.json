{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/dashboard/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "hJugotEV2xd7oO4-36Vj6", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GmR490q78K+bBIm3nmrLlRQO8rHHbnjcdhaBgWmTT64=", "__NEXT_PREVIEW_MODE_ID": "01c666d35083597f23fe08e1a47f92b9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "be10231b3a158bc6f7a78db676fcd152ccd7d7452336233becf9e7cc9687e1f6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4889342e36945fc62e8315e92132ae99fd27a2ece42b36f25db4d65ca7dbaabd"}}}, "functions": {}, "sortedMiddleware": ["/"]}