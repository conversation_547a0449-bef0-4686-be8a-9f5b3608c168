(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5953],{13717:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},14186:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},26126:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var a=r(95155);r(12115);var s=r(99708),n=r(74466),i=r(59434);let d=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:r,asChild:n=!1,...o}=e,c=n?s.DX:"span";return(0,a.jsx)(c,{"data-slot":"badge",className:(0,i.cn)(d({variant:r}),t),...o})}},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>o});var a=r(95155);r(12115);var s=r(99708),n=r(74466),i=r(59434);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:n,asChild:o=!1,...c}=e,l=o?s.DX:"button";return(0,a.jsx)(l,{"data-slot":"button",className:(0,i.cn)(d({variant:r,size:n,className:t})),...c})}},35169:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"notFound")&&r.d(t,{notFound:function(){return a.notFound}}),r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}})},36147:(e,t,r)=>{Promise.resolve().then(r.bind(r,96199))},59434:(e,t,r)=>{"use strict";r.d(t,{KE:()=>m,_C:()=>u,cn:()=>c,z9:()=>l});var a=r(52596),s=r(39688),n=r(96262),i=r.n(n),d=r(60430),o=r.n(d);function c(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}function l(e){return i()(e,{lower:!0,strict:!0,remove:/[*+~.()'"!:@]/g})}function u(e){return Math.ceil(o()(e).minutes)}function m(e){try{let t=JSON.parse(e);if("doc"===t.type&&t.content)return function e(t){let r="";for(let a of t)"text"===a.type?r+=a.text||"":a.content&&(r+=e(a.content)),["paragraph","heading","listItem"].includes(a.type)&&(r+=" ");return r.trim()}(t.content)}catch(e){}return e.replace(/<[^>]*>/g," ").replace(/\s+/g," ").trim()}},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>n,aR:()=>i});var a=r(95155);r(12115);var s=r(59434);function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...r})}},69074:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},96199:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var a=r(95155),s=r(12115),n=r(35695),i=r(6874),d=r.n(i),o=r(30285),c=r(26126),l=r(66695),u=r(35169),m=r(13717),v=r(69074),h=r(14186),x=r(92657),g=r(66766);function f(){let e=(0,n.useParams)().slug,[t,r]=(0,s.useState)(null),[i,f]=(0,s.useState)(!0);(0,s.useEffect)(()=>{e&&b()},[e]);let b=async()=>{try{let t=await fetch("/api/blog/".concat(e));if(!t.ok)throw 404===t.status&&(0,n.notFound)(),Error("Failed to fetch blog post");let a=await t.json();r(a)}catch(e){console.error("Error fetching blog post:",e),(0,n.notFound)()}finally{f(!1)}};return i?(0,a.jsx)("div",{className:"min-h-screen bg-background p-8",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsx)("div",{className:"text-center",children:"Loading blog post..."})})}):(t||(0,n.notFound)(),(0,a.jsx)("div",{className:"min-h-screen bg-background p-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsx)(d(),{href:"/blog",children:(0,a.jsxs)(o.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Back to Blog"]})}),(0,a.jsx)(d(),{href:"/dashboard/blog/".concat(t.id,"/edit"),children:(0,a.jsxs)(o.$,{size:"sm",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Edit Post"]})})]}),(0,a.jsx)(l.Zp,{children:(0,a.jsxs)(l.Wu,{className:"p-0",children:[t.image&&(0,a.jsxs)("div",{className:"relative h-64 md:h-80 overflow-hidden rounded-t-lg",children:[(0,a.jsx)(g.default,{src:t.image,alt:t.title,fill:!0,className:"object-cover"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,a.jsx)("div",{className:"absolute bottom-4 left-4 right-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(c.E,{variant:"secondary",className:"bg-background/80 backdrop-blur-sm",children:t.category}),t.featured&&(0,a.jsx)(c.E,{variant:"default",children:"Featured"}),!t.published&&(0,a.jsx)(c.E,{variant:"destructive",children:"Draft"})]})})]}),(0,a.jsxs)("div",{className:"p-8",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h1",{className:"text-3xl md:text-4xl font-bold mb-4",children:t.title}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(v.A,{className:"mr-1 h-4 w-4"}),new Date(t.publishedAt||t.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"mr-1 h-4 w-4"}),t.readTime||5," min read"]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(x.A,{className:"mr-1 h-4 w-4"}),t.views," views"]}),(0,a.jsxs)("div",{children:["By ",t.author.name]})]}),(0,a.jsx)("p",{className:"text-lg text-muted-foreground mb-6",children:t.excerpt}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:t.tags.map((e,t)=>(0,a.jsx)(c.E,{variant:"outline",children:e},t))})]}),(0,a.jsx)("div",{className:"prose prose-gray dark:prose-invert max-w-none",dangerouslySetInnerHTML:{__html:t.content}})]})]})})]})}))}}},e=>{var t=t=>e(e.s=t);e.O(0,[4854,8858,6766,8441,1684,7358],()=>t(36147)),_N_E=e.O()}]);