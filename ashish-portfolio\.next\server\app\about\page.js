(()=>{var e={};e.id=220,e.ids=[220],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7986:(e,t,s)=>{"use strict";s.d(t,{Skills:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Skills() from the server but Skills is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\about\\skills.tsx","Skills")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28770:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m,metadata:()=>d});var a=s(37413),r=s(64544),i=s(64947),o=s(55952),n=s(28852),l=s(7986),c=s(43360);let d={title:"About - Ashish Kamat",description:"Learn more about Ashish Kamat, a passionate full-stack developer and UI/UX designer with expertise in modern web technologies.",openGraph:{title:"About - Ashish Kamat",description:"Learn more about Ashish Kamat, a passionate full-stack developer and UI/UX designer with expertise in modern web technologies."}};function m(){return(0,a.jsxs)("div",{className:"min-h-screen",children:[(0,a.jsx)(r.Navigation,{}),(0,a.jsxs)("main",{className:"pt-16",children:[(0,a.jsx)(o.AboutHero,{}),(0,a.jsx)(n.Experience,{}),(0,a.jsx)(l.Skills,{}),(0,a.jsx)(c.Education,{})]}),(0,a.jsx)(i.Footer,{})]})}},28852:(e,t,s)=>{"use strict";s.d(t,{Experience:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Experience() from the server but Experience is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\about\\experience.tsx","Experience")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32401:(e,t,s)=>{"use strict";s.d(t,{Education:()=>u});var a=s(60687),r=s(26001),i=s(37472),o=s(62688);let n=(0,o.A)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]);(0,o.A)("school",[["path",{d:"M14 22v-4a2 2 0 1 0-4 0v4",key:"hhkicm"}],["path",{d:"m18 10 3.447 1.724a1 1 0 0 1 .553.894V20a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-7.382a1 1 0 0 1 .553-.894L6 10",key:"1xqip1"}],["path",{d:"M18 5v17",key:"1sw6gf"}],["path",{d:"m4 6 7.106-3.553a2 2 0 0 1 1.788 0L20 6",key:"9d2mlk"}],["path",{d:"M6 5v17",key:"1xfsm0"}],["circle",{cx:"12",cy:"9",r:"2",key:"1092wv"}]]);let l=(0,o.A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);(0,o.A)("trophy",[["path",{d:"M10 14.66v1.626a2 2 0 0 1-.976 1.696A5 5 0 0 0 7 21.978",key:"1n3hpd"}],["path",{d:"M14 14.66v1.626a2 2 0 0 0 .976 1.696A5 5 0 0 1 17 21.978",key:"rfe1zi"}],["path",{d:"M18 9h1.5a1 1 0 0 0 0-5H18",key:"7xy6bh"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M6 9a6 6 0 0 0 12 0V3a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1z",key:"1mhfuq"}],["path",{d:"M6 9H4.5a1 1 0 0 1 0-5H6",key:"tex48p"}]]);var c=s(82080),d=s(51790);s(14371);var m=s(44493),x=s(96834);s(62185);var h=s(43210);let p=["Advanced React Patterns - Kent C. Dodds","TypeScript Masterclass - Marius Schulz","Node.js Design Patterns - Mario Casciaro","System Design Interview - Alex Xu","AWS Solutions Architecture - A Cloud Guru","UI/UX Design Fundamentals - Google UX"];function u(){let[e,t]=(0,i.Wx)({triggerOnce:!0,threshold:.1}),[s,o]=(0,h.useState)([]),[u,g]=(0,h.useState)([]),[b,f]=(0,h.useState)(!0);return b?(0,a.jsx)("section",{className:"py-20",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-300 rounded w-64 mx-auto mb-4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-300 rounded w-96 mx-auto"})]})})})}):(0,a.jsx)("section",{ref:e,className:"py-20",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(r.P.div,{className:"text-center mb-16",initial:{opacity:0,y:50},animate:t?{opacity:1,y:0}:{},transition:{duration:.8},children:[(0,a.jsx)("h2",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6",children:(0,a.jsx)("span",{className:"gradient-text",children:"Education & Learning"})}),(0,a.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"My educational background and continuous learning journey in technology and software development."})]}),(0,a.jsxs)("div",{className:"mb-16",children:[(0,a.jsxs)(r.P.h3,{className:"text-2xl font-bold mb-8 flex items-center",initial:{opacity:0,x:-20},animate:t?{opacity:1,x:0}:{},transition:{duration:.6,delay:.2},children:[(0,a.jsx)(n,{className:"mr-3 h-6 w-6 text-primary"}),"Formal Education"]}),(0,a.jsx)(r.P.div,{initial:{opacity:0},animate:t?{opacity:1}:{},transition:{duration:.8,delay:.3},children:(0,a.jsx)(d.VerticalTimeline,{children:s.map(e=>(0,a.jsx)(d.VerticalTimelineElement,{className:"vertical-timeline-element--education",contentStyle:{background:"hsl(var(--card))",color:"hsl(var(--card-foreground))",border:"1px solid hsl(var(--border))",borderRadius:"12px",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"},contentArrowStyle:{borderRight:"7px solid hsl(var(--border))"},date:e.period,iconStyle:{background:"#3b82f6",color:"#fff",display:"flex",alignItems:"center",justifyContent:"center",border:"3px solid hsl(var(--background))",boxShadow:"0 0 0 2px hsl(var(--border))",width:"60px",height:"60px"},icon:(0,a.jsx)(n,{className:"h-7 w-7"}),children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold",children:e.degree}),(0,a.jsx)("div",{className:"text-primary font-medium",children:e.institution}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:e.location}),(0,a.jsx)(x.E,{variant:"secondary",className:"mt-2",children:e.grade})]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:e.description}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold mb-2",children:"Key Highlights:"}),(0,a.jsx)("ul",{className:"space-y-1",children:e.highlights.map((e,t)=>(0,a.jsxs)("li",{className:"flex items-start space-x-2 text-sm text-muted-foreground",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0"}),(0,a.jsx)("span",{children:e})]},t))})]})]})},e.id))})})]}),(0,a.jsxs)("div",{className:"mb-16",children:[(0,a.jsxs)(r.P.h3,{className:"text-2xl font-bold mb-8 flex items-center",initial:{opacity:0,x:-20},animate:t?{opacity:1,x:0}:{},transition:{duration:.6,delay:.4},children:[(0,a.jsx)(l,{className:"mr-3 h-6 w-6 text-primary"}),"Certifications"]}),(0,a.jsx)(r.P.div,{initial:{opacity:0},animate:t?{opacity:1}:{},transition:{duration:.8,delay:.5},children:(0,a.jsx)(d.VerticalTimeline,{children:u.map(e=>(0,a.jsx)(d.VerticalTimelineElement,{className:"vertical-timeline-element--certification",contentStyle:{background:"hsl(var(--card))",color:"hsl(var(--card-foreground))",border:"1px solid hsl(var(--border))",borderRadius:"12px",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"},contentArrowStyle:{borderRight:"7px solid hsl(var(--border))"},date:e.date,iconStyle:{background:"#f59e0b",color:"#fff",display:"flex",alignItems:"center",justifyContent:"center",border:"3px solid hsl(var(--background))",boxShadow:"0 0 0 2px hsl(var(--border))",width:"60px",height:"60px"},icon:(0,a.jsx)(l,{className:"h-6 w-6"}),children:(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"text-2xl",children:e.emoji}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"font-bold text-lg",children:e.title}),(0,a.jsx)("p",{className:"text-primary font-medium",children:e.issuer}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.description}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-2",children:["Credential ID: ",e.credentialId]})]})]})})},e.id))})})]}),(0,a.jsxs)(r.P.div,{initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{},transition:{duration:.6,delay:.6},children:[(0,a.jsxs)("h3",{className:"text-2xl font-bold mb-8 flex items-center",children:[(0,a.jsx)(c.A,{className:"mr-3 h-6 w-6 text-primary"}),"Continuous Learning"]}),(0,a.jsxs)(m.Zp,{className:"border-border/50",children:[(0,a.jsx)(m.aR,{children:(0,a.jsx)(m.ZB,{children:"Recent Courses & Training"})}),(0,a.jsxs)(m.Wu,{children:[(0,a.jsx)("div",{className:"grid md:grid-cols-2 gap-4",children:p.map((e,s)=>(0,a.jsxs)(r.P.div,{className:"flex items-center space-x-3 p-3 rounded-lg bg-muted/50 hover:bg-muted transition-colors duration-200",initial:{opacity:0,x:-20},animate:t?{opacity:1,x:0}:{},transition:{duration:.4,delay:.7+.05*s},children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full bg-primary flex-shrink-0"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:e})]},s))}),(0,a.jsx)("div",{className:"mt-6 p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg border border-border/50",children:(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[(0,a.jsx)("strong",{children:"Learning Philosophy:"})," I believe in continuous learning and staying updated with the latest technologies. I dedicate at least 5 hours per week to learning new skills and exploring emerging trends in web development and software engineering."]})})]})]})]})]})})}},33873:e=>{"use strict";e.exports=require("path")},43360:(e,t,s)=>{"use strict";s.d(t,{Education:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Education() from the server but Education is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\about\\education.tsx","Education")},48950:(e,t,s)=>{"use strict";s.d(t,{AboutHero:()=>x});var a=s(60687),r=s(30474),i=s(26001),o=s(31158),n=s(13166),l=s(97992),c=s(40228),d=s(29523),m=s(44493);function x(){return(0,a.jsx)("section",{className:"py-20 bg-muted/30",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[(0,a.jsxs)(i.P.div,{className:"space-y-8",initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8},children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(i.P.h1,{className:"text-4xl sm:text-5xl lg:text-6xl font-bold",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:["About ",(0,a.jsx)("span",{className:"gradient-text-blue",children:"Me"})]}),(0,a.jsx)(i.P.p,{className:"text-xl text-muted-foreground leading-relaxed",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:"I'm a passionate full-stack developer and UI/UX designer with over 3 years of experience creating innovative digital solutions that make a real impact."})]}),(0,a.jsxs)(i.P.div,{className:"prose prose-lg dark:prose-invert max-w-none",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:[(0,a.jsx)("p",{children:"My journey in tech started with a curiosity about how things work behind the scenes. What began as tinkering with HTML and CSS has evolved into a deep passion for creating seamless, user-centered digital experiences."}),(0,a.jsx)("p",{children:"I specialize in modern web technologies like React, Next.js, and TypeScript, with a strong focus on performance, accessibility, and user experience. I believe that great software should not only function flawlessly but also delight users at every interaction."}),(0,a.jsx)("p",{children:"When I'm not coding, you'll find me exploring new technologies, contributing to open-source projects, or sharing knowledge with the developer community through blog posts and mentoring."})]}),(0,a.jsxs)(i.P.div,{className:"flex flex-col sm:flex-row gap-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},children:[(0,a.jsxs)(d.$,{size:"lg",className:"group",children:[(0,a.jsx)(o.A,{className:"mr-2 h-4 w-4 group-hover:animate-bounce"}),"Download Resume"]}),(0,a.jsxs)(d.$,{variant:"outline",size:"lg",children:[(0,a.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"Let's Chat"]})]})]}),(0,a.jsxs)(i.P.div,{className:"relative",initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.2},children:[(0,a.jsxs)("div",{className:"relative w-80 h-80 mx-auto lg:w-96 lg:h-96 mb-8",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl animate-pulse-glow"}),(0,a.jsx)("div",{className:"absolute inset-2 bg-background rounded-2xl overflow-hidden",children:(0,a.jsx)(r.default,{src:"/ashish-profile.svg",alt:"Ashish Kamat",fill:!0,className:"object-cover",priority:!0})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},children:(0,a.jsx)(m.Zp,{className:"text-center hover-lift",children:(0,a.jsxs)(m.Wu,{className:"p-4",children:[(0,a.jsx)(l.A,{className:"h-6 w-6 text-primary mx-auto mb-2"}),(0,a.jsx)("div",{className:"font-semibold",children:"Location"}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Kathmandu, Nepal"})]})})}),(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7},children:(0,a.jsx)(m.Zp,{className:"text-center hover-lift",children:(0,a.jsxs)(m.Wu,{className:"p-4",children:[(0,a.jsx)(c.A,{className:"h-6 w-6 text-primary mx-auto mb-2"}),(0,a.jsx)("div",{className:"font-semibold",children:"Experience"}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"3+ Years"})]})})})]})]})]})})})}},55952:(e,t,s)=>{"use strict";s.d(t,{AboutHero:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call AboutHero() from the server but AboutHero is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\about\\about-hero.tsx","AboutHero")},62185:(e,t,s)=>{"use strict";s.d(t,{$_:()=>l,FH:()=>r,LE:()=>d,O6:()=>m,OA:()=>n,Wq:()=>c,a0:()=>h,h1:()=>x,tx:()=>o});let a="http://localhost:3001",r={getProjects:async()=>{let e=await fetch(`${a}/api/projects`);if(!e.ok)throw Error("Failed to fetch projects");return e.json()},getProject:async e=>{let t=await fetch(`${a}/api/projects/${e}`);if(!t.ok)throw Error("Failed to fetch project");return t.json()},getBlogPosts:async()=>{let e=await fetch(`${a}/api/blog`);if(!e.ok)throw Error("Failed to fetch blog posts");return e.json()},getBlogPost:async e=>{let t=await fetch(`${a}/api/blog/${e}`);if(!t.ok)throw Error("Failed to fetch blog post");return t.json()},getServices:async()=>{let e=await fetch(`${a}/api/services`);if(!e.ok)throw Error("Failed to fetch services");return e.json()},getTechStack:async()=>{let e=await fetch(`${a}/api/tech-stack`);if(!e.ok)throw Error("Failed to fetch tech stack");return e.json()},getTestimonials:async()=>{let e=await fetch(`${a}/api/testimonials`);if(!e.ok)throw Error("Failed to fetch testimonials");return e.json()},getExperiences:async()=>{let e=await fetch(`${a}/api/experiences`);if(!e.ok)throw Error("Failed to fetch experiences");return e.json()},getEducation:async()=>{let e=await fetch(`${a}/api/education`);if(!e.ok)throw Error("Failed to fetch education");return e.json()},getCertifications:async()=>{let e=await fetch(`${a}/api/certifications`);if(!e.ok)throw Error("Failed to fetch certifications");return e.json()}},i=e=>e.filter(e=>e.published).sort((e,t)=>e.order-t.order),o=e=>e.filter(e=>e.published&&e.featured).sort((e,t)=>e.order-t.order),n=(e,t)=>"All"===t?i(e):e.filter(e=>e.published&&e.category===t).sort((e,t)=>e.order-t.order),l=e=>e.filter(e=>e.published).sort((e,t)=>e.order-t.order),c=e=>e.filter(e=>e.published).reduce((e,t)=>(e[t.category]||(e[t.category]=[]),e[t.category].push(t),e),{}),d=e=>e.filter(e=>e.published).sort((e,t)=>e.order-t.order),m=e=>e.filter(e=>e.published).sort((e,t)=>e.order-t.order),x=e=>e.filter(e=>e.published).sort((e,t)=>e.order-t.order),h=e=>e.filter(e=>e.published).sort((e,t)=>e.order-t.order)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65923:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var a=s(65239),r=s(48088),i=s(88170),o=s.n(i),n=s(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let c={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,28770)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\about\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\about\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},66396:(e,t,s)=>{Promise.resolve().then(s.bind(s,55952)),Promise.resolve().then(s.bind(s,43360)),Promise.resolve().then(s.bind(s,28852)),Promise.resolve().then(s.bind(s,7986)),Promise.resolve().then(s.bind(s,64947)),Promise.resolve().then(s.bind(s,64544))},76124:(e,t,s)=>{Promise.resolve().then(s.bind(s,48950)),Promise.resolve().then(s.bind(s,32401)),Promise.resolve().then(s.bind(s,79342)),Promise.resolve().then(s.bind(s,84804)),Promise.resolve().then(s.bind(s,94101)),Promise.resolve().then(s.bind(s,89650))},79342:(e,t,s)=>{"use strict";s.d(t,{Experience:()=>h});var a=s(60687),r=s(26001),i=s(37472),o=s(57800),n=s(25334),l=s(97992),c=s(51790);s(14371);var d=s(96834);s(62185);var m=s(43210),x=s(30474);function h(){let[e,t]=(0,i.Wx)({triggerOnce:!0,threshold:.1}),[s,h]=(0,m.useState)([]),[p,u]=(0,m.useState)(!0);return p?(0,a.jsx)("section",{className:"py-20",children:(0,a.jsx)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-300 rounded w-64 mx-auto mb-4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-300 rounded w-96 mx-auto"})]})})})}):(0,a.jsx)("section",{ref:e,className:"py-20",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(r.P.div,{className:"text-center mb-16",initial:{opacity:0,y:50},animate:t?{opacity:1,y:0}:{},transition:{duration:.8},children:[(0,a.jsx)("h2",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6",children:(0,a.jsx)("span",{className:"gradient-text",children:"Work Experience"})}),(0,a.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"My professional journey in web development, from junior developer to senior full-stack engineer."})]}),(0,a.jsx)(r.P.div,{initial:{opacity:0},animate:t?{opacity:1}:{},transition:{duration:.8,delay:.2},children:(0,a.jsx)(c.VerticalTimeline,{children:s.map(e=>(0,a.jsx)(c.VerticalTimelineElement,{className:"vertical-timeline-element--work",contentStyle:{background:"hsl(var(--card))",color:"hsl(var(--card-foreground))",border:"1px solid hsl(var(--border))",borderRadius:"12px",boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"},contentArrowStyle:{borderRight:"7px solid hsl(var(--border))"},date:e.period,iconStyle:{background:"#ffffff",color:"#000",display:"flex",alignItems:"center",justifyContent:"center",border:"3px solid hsl(var(--background))",boxShadow:"0 0 0 2px hsl(var(--border))",width:"60px",height:"60px"},icon:e.companyLogo?(0,a.jsx)(x.default,{src:e.companyLogo,alt:`${e.company} logo`,width:40,height:40,className:"rounded-full object-contain"}):(0,a.jsx)(o.A,{className:"h-6 w-6"}),children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-muted-foreground mt-1",children:[(0,a.jsx)("span",{className:"font-medium text-primary",children:e.company}),e.website&&(0,a.jsx)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"hover:text-primary transition-colors",children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 text-sm text-muted-foreground mt-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(l.A,{className:"h-4 w-4 shrink-0 translate-y-[1px]"}),(0,a.jsx)("span",{className:"leading-none",children:e.location})]}),(0,a.jsx)(d.E,{variant:"outline",className:"w-fit",children:e.type})]})]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:e.description}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold mb-2",children:"Key Achievements:"}),(0,a.jsx)("ul",{className:"space-y-1",children:e.achievements.map((e,t)=>(0,a.jsxs)("li",{className:"flex items-start space-x-2 text-sm text-muted-foreground",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0"}),(0,a.jsx)("span",{children:e})]},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold mb-2",children:"Technologies:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:e.technologies.map(e=>(0,a.jsx)(d.E,{variant:"secondary",className:"text-xs",children:e},e))})]})]})},e.id))})})]})})}},79551:e=>{"use strict";e.exports=require("url")},82080:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},84804:(e,t,s)=>{"use strict";s.d(t,{Skills:()=>l});var a=s(60687),r=s(26001),i=s(37472),o=s(44493);let n=[{title:"Frontend Development",skills:[{name:"React/Next.js",level:95,color:"bg-blue-500"},{name:"TypeScript",level:90,color:"bg-blue-600"},{name:"Tailwind CSS",level:92,color:"bg-cyan-500"},{name:"JavaScript (ES6+)",level:88,color:"bg-yellow-500"},{name:"HTML5 & CSS3",level:95,color:"bg-orange-500"},{name:"Framer Motion",level:85,color:"bg-purple-500"}]},{title:"Backend Development",skills:[{name:"Node.js",level:88,color:"bg-green-600"},{name:"Express.js",level:85,color:"bg-gray-600"},{name:"PostgreSQL",level:82,color:"bg-blue-800"},{name:"MongoDB",level:80,color:"bg-green-500"},{name:"Prisma ORM",level:85,color:"bg-indigo-600"},{name:"REST APIs",level:90,color:"bg-teal-500"}]},{title:"Tools & Technologies",skills:[{name:"Git & GitHub",level:92,color:"bg-gray-800"},{name:"Docker",level:75,color:"bg-blue-500"},{name:"AWS",level:70,color:"bg-orange-500"},{name:"Vercel",level:88,color:"bg-black"},{name:"Figma",level:85,color:"bg-purple-500"},{name:"VS Code",level:95,color:"bg-blue-600"}]},{title:"Soft Skills",skills:[{name:"Problem Solving",level:92,color:"bg-emerald-500"},{name:"Team Leadership",level:85,color:"bg-rose-500"},{name:"Communication",level:88,color:"bg-amber-500"},{name:"Project Management",level:80,color:"bg-violet-500"},{name:"Mentoring",level:82,color:"bg-pink-500"},{name:"Adaptability",level:90,color:"bg-sky-500"}]}];function l(){let[e,t]=(0,i.Wx)({triggerOnce:!0,threshold:.1});return(0,a.jsx)("section",{ref:e,className:"py-20 bg-muted/30",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)(r.P.div,{className:"text-center mb-16",initial:{opacity:0,y:50},animate:t?{opacity:1,y:0}:{},transition:{duration:.8},children:[(0,a.jsx)("h2",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6",children:(0,a.jsx)("span",{className:"gradient-text",children:"Skills & Expertise"})}),(0,a.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"A comprehensive overview of my technical skills and proficiency levels across different technologies and domains."})]}),(0,a.jsx)("div",{className:"grid md:grid-cols-2 gap-8",children:n.map((e,s)=>(0,a.jsx)(r.P.div,{initial:{opacity:0,y:50},animate:t?{opacity:1,y:0}:{},transition:{duration:.8,delay:.1*s},children:(0,a.jsxs)(o.Zp,{className:"h-full hover-lift border-border/50 hover:border-border transition-all duration-300",children:[(0,a.jsx)(o.aR,{children:(0,a.jsx)(o.ZB,{className:"text-xl font-bold",children:e.title})}),(0,a.jsx)(o.Wu,{children:(0,a.jsx)("div",{className:"space-y-6",children:e.skills.map((e,i)=>(0,a.jsxs)(r.P.div,{className:"space-y-2",initial:{opacity:0,x:-20},animate:t?{opacity:1,x:0}:{},transition:{duration:.5,delay:.1*s+.05*i},children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"font-medium text-sm",children:e.name}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.level,"%"]})]}),(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)("div",{className:"w-full bg-muted rounded-full h-2",children:(0,a.jsx)(r.P.div,{className:`h-2 rounded-full ${e.color}`,initial:{width:0},animate:t?{width:`${e.level}%`}:{width:0},transition:{duration:1,delay:.1*s+.05*i+.2,ease:"easeOut"}})})})]},e.name))})})]})},e.title))}),(0,a.jsx)(r.P.div,{className:"mt-16 text-center",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{},transition:{duration:.8,delay:.6},children:(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl p-8 border border-border/50",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold mb-4",children:"Continuous Learning"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-6 max-w-2xl mx-auto",children:"I'm passionate about staying up-to-date with the latest technologies and best practices. Currently exploring AI/ML integration in web applications and advanced React patterns."}),(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-4",children:["AI/ML","Web3","GraphQL","Microservices","DevOps","Mobile Development"].map(e=>(0,a.jsx)("span",{className:"px-3 py-1 bg-background border border-border rounded-full text-sm font-medium",children:e},e))})]})})]})})}},97992:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,760,658,373,474,834,468],()=>s(65923));module.exports=a})();