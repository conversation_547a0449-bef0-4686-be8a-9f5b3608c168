"use strict";exports.id=7658,exports.ids=[7658],exports.modules={15079:(e,t,r)=>{r.d(t,{bq:()=>u,eb:()=>h,gC:()=>p,l6:()=>o,yv:()=>c});var s=r(60687);r(43210);var a=r(97822),i=r(78272),n=r(13964),l=r(3589),d=r(4780);function o({...e}){return(0,s.jsx)(a.bL,{"data-slot":"select",...e})}function c({...e}){return(0,s.jsx)(a.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:r,...n}){return(0,s.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[r,(0,s.jsx)(a.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:t,position:r="popper",...i}){return(0,s.jsx)(a.ZL,{children:(0,s.jsxs)(a.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...i,children:[(0,s.jsx)(x,{}),(0,s.jsx)(a.LM,{className:(0,d.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(g,{})]})})}function h({className:e,children:t,...r}){return(0,s.jsxs)(a.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(a.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(a.p4,{children:t})]})}function x({className:e,...t}){return(0,s.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(l.A,{className:"size-4"})})}function g({className:e,...t}){return(0,s.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(i.A,{className:"size-4"})})}},17658:(e,t,r)=>{r.d(t,{ProjectForm:()=>b});var s=r(60687),a=r(43210),i=r(16189),n=r(29523),l=r(89667),d=r(80013),o=r(34729),c=r(56896),u=r(15079),p=r(44493),h=r(19312),x=r(96834),g=r(11860),v=r(52581);let m=["Full Stack","Frontend","Backend","Mobile","AI/ML","DevOps","Design"];function b({projectId:e,onSuccess:t}){let r=(0,i.useRouter)(),[b,f]=(0,a.useState)(!1),[j,y]=(0,a.useState)(""),[w,N]=(0,a.useState)({title:"",description:"",longDescription:"",image:"",category:"",technologies:[],liveUrl:"",githubUrl:"",featured:!1,published:!0,order:0}),k=async s=>{s.preventDefault(),f(!0);try{let s=e?`/api/projects/${e}`:"/api/projects";if((await fetch(s,{method:e?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(w)})).ok)t?.(),e||r.push("/dashboard/projects");else throw Error("Failed to save project")}catch(e){console.error("Error saving project:",e),v.oR.error("Failed to save project. Please try again.")}finally{f(!1)}},C=()=>{j.trim()&&!w.technologies.includes(j.trim())&&(N(e=>({...e,technologies:[...e.technologies,j.trim()]})),y(""))},z=e=>{N(t=>({...t,technologies:t.technologies.filter(t=>t!==e)}))};return(0,s.jsxs)("form",{onSubmit:k,className:"space-y-6",children:[(0,s.jsxs)(p.Zp,{children:[(0,s.jsxs)(p.aR,{children:[(0,s.jsx)(p.ZB,{children:"Basic Information"}),(0,s.jsx)(p.BT,{children:"Enter the basic details about your project"})]}),(0,s.jsxs)(p.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{htmlFor:"title",children:"Project Title *"}),(0,s.jsx)(l.p,{id:"title",value:w.title,onChange:e=>N(t=>({...t,title:e.target.value})),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{htmlFor:"description",children:"Short Description *"}),(0,s.jsx)(o.T,{id:"description",value:w.description,onChange:e=>N(t=>({...t,description:e.target.value})),rows:3,required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{htmlFor:"longDescription",children:"Detailed Description"}),(0,s.jsx)(o.T,{id:"longDescription",value:w.longDescription,onChange:e=>N(t=>({...t,longDescription:e.target.value})),rows:5})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{htmlFor:"category",children:"Category *"}),(0,s.jsxs)(u.l6,{value:w.category,onValueChange:e=>N(t=>({...t,category:e})),children:[(0,s.jsx)(u.bq,{children:(0,s.jsx)(u.yv,{placeholder:"Select a category"})}),(0,s.jsx)(u.gC,{children:m.map(e=>(0,s.jsx)(u.eb,{value:e,children:e},e))})]})]})]})]}),(0,s.jsxs)(p.Zp,{children:[(0,s.jsxs)(p.aR,{children:[(0,s.jsx)(p.ZB,{children:"Project Image"}),(0,s.jsx)(p.BT,{children:"Upload a screenshot or preview image of your project"})]}),(0,s.jsx)(p.Wu,{children:(0,s.jsx)(h.B,{value:w.image,onChange:e=>N(t=>({...t,image:e})),onRemove:()=>N(e=>({...e,image:""})),disabled:b})})]}),(0,s.jsxs)(p.Zp,{children:[(0,s.jsxs)(p.aR,{children:[(0,s.jsx)(p.ZB,{children:"Technologies"}),(0,s.jsx)(p.BT,{children:"Add the technologies and tools used in this project"})]}),(0,s.jsxs)(p.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(l.p,{placeholder:"Enter technology (e.g., React, Node.js)",value:j,onChange:e=>y(e.target.value),onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),C())}}),(0,s.jsx)(n.$,{type:"button",onClick:C,children:"Add"})]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:w.technologies.map(e=>(0,s.jsxs)(x.E,{variant:"secondary",className:"flex items-center gap-1",children:[e,(0,s.jsx)(g.A,{className:"h-3 w-3 cursor-pointer",onClick:()=>z(e)})]},e))})]})]}),(0,s.jsxs)(p.Zp,{children:[(0,s.jsxs)(p.aR,{children:[(0,s.jsx)(p.ZB,{children:"Links"}),(0,s.jsx)(p.BT,{children:"Add links to the live project and source code"})]}),(0,s.jsxs)(p.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{htmlFor:"liveUrl",children:"Live URL"}),(0,s.jsx)(l.p,{id:"liveUrl",type:"url",placeholder:"https://example.com",value:w.liveUrl,onChange:e=>N(t=>({...t,liveUrl:e.target.value}))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{htmlFor:"githubUrl",children:"GitHub URL"}),(0,s.jsx)(l.p,{id:"githubUrl",type:"url",placeholder:"https://github.com/username/repo",value:w.githubUrl,onChange:e=>N(t=>({...t,githubUrl:e.target.value}))})]})]})]}),(0,s.jsxs)(p.Zp,{children:[(0,s.jsxs)(p.aR,{children:[(0,s.jsx)(p.ZB,{children:"Settings"}),(0,s.jsx)(p.BT,{children:"Configure project visibility and display options"})]}),(0,s.jsxs)(p.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(c.S,{id:"featured",checked:w.featured,onCheckedChange:e=>N(t=>({...t,featured:e}))}),(0,s.jsx)(d.J,{htmlFor:"featured",children:"Featured Project"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(c.S,{id:"published",checked:w.published,onCheckedChange:e=>N(t=>({...t,published:e}))}),(0,s.jsx)(d.J,{htmlFor:"published",children:"Published"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{htmlFor:"order",children:"Display Order"}),(0,s.jsx)(l.p,{id:"order",type:"number",min:"0",value:w.order,onChange:e=>N(t=>({...t,order:parseInt(e.target.value)||0}))})]})]})]}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(n.$,{type:"submit",disabled:b,children:b?"Saving...":e?"Update Project":"Create Project"}),(0,s.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>r.back(),children:"Cancel"})]})]})}},19312:(e,t,r)=>{r.d(t,{B:()=>h});var s=r(60687),a=r(43210),i=r(29523),n=r(89667),l=r(80013),d=r(11860),o=r(9005),c=r(16023),u=r(30474),p=r(52581);function h({value:e,onChange:t,onRemove:r,disabled:h,label:x="Upload Image",className:g=""}){let[v,m]=(0,a.useState)(!1),b=(0,a.useRef)(null),f=async e=>{let r=e.target.files?.[0];if(r){m(!0);try{let e=new FormData;e.append("file",r);let s=await fetch("/api/upload",{method:"POST",body:e});if(!s.ok)throw Error("Upload failed");let a=await s.json();t(a.url)}catch(e){console.error("Error uploading image:",e),p.oR.error("Failed to upload image. Please try again.")}finally{m(!1)}}};return(0,s.jsxs)("div",{className:`space-y-2 ${g}`,children:[(0,s.jsx)(l.J,{children:x}),e?(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"relative w-full h-48 rounded-lg overflow-hidden border",children:(0,s.jsx)(u.default,{src:e,alt:"Uploaded image",fill:!0,className:"object-cover"})}),(0,s.jsx)(i.$,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2",onClick:r,disabled:h,children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})]}):(0,s.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(o.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>{b.current?.click()},disabled:h||v,children:[(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4"}),v?"Uploading...":"Choose Image"]})}),(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"PNG, JPG, GIF up to 10MB"})]})}),(0,s.jsx)(n.p,{ref:b,type:"file",accept:"image/*",onChange:f,className:"hidden",disabled:h||v})]})}},34729:(e,t,r)=>{r.d(t,{T:()=>i});var s=r(60687);r(43210);var a=r(4780);function i({className:e,...t}){return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},56896:(e,t,r)=>{r.d(t,{S:()=>l});var s=r(60687);r(43210);var a=r(40211),i=r(13964),n=r(4780);function l({className:e,...t}){return(0,s.jsx)(a.bL,{"data-slot":"checkbox",className:(0,n.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(a.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(i.A,{className:"size-3.5"})})})}},80013:(e,t,r)=>{r.d(t,{J:()=>n});var s=r(60687);r(43210);var a=r(78148),i=r(4780);function n({className:e,...t}){return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},89667:(e,t,r)=>{r.d(t,{p:()=>i});var s=r(60687);r(43210);var a=r(4780);function i({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},96834:(e,t,r)=>{r.d(t,{E:()=>d});var s=r(60687);r(43210);var a=r(8730),i=r(24224),n=r(4780);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:r=!1,...i}){let d=r?a.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(l({variant:t}),e),...i})}}};