{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/command-palette.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { \n  Search, \n  Home, \n  User, \n  Briefcase, \n  Mail, \n  FileText,\n  ExternalLink,\n  Github,\n  Linkedin,\n  Twitter\n} from \"lucide-react\";\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n} from \"@/components/ui/dialog\";\nimport { Input } from \"@/components/ui/input\";\nimport { Badge } from \"@/components/ui/badge\";\n\ninterface Command {\n  id: string;\n  title: string;\n  description: string;\n  icon: React.ComponentType<any>;\n  action: () => void;\n  category: string;\n  keywords: string[];\n}\n\ninterface CommandPaletteProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n}\n\nexport function CommandPalette({ open, onOpenChange }: CommandPaletteProps) {\n  const [search, setSearch] = useState(\"\");\n  const [selectedIndex, setSelectedIndex] = useState(0);\n  const router = useRouter();\n\n  const commands: Command[] = [\n    // Navigation\n    {\n      id: \"home\",\n      title: \"Home\",\n      description: \"Go to homepage\",\n      icon: Home,\n      action: () => router.push(\"/\"),\n      category: \"Navigation\",\n      keywords: [\"home\", \"main\", \"landing\"],\n    },\n    {\n      id: \"about\",\n      title: \"About\",\n      description: \"Learn more about me\",\n      icon: User,\n      action: () => router.push(\"/about\"),\n      category: \"Navigation\",\n      keywords: [\"about\", \"bio\", \"experience\", \"skills\"],\n    },\n    {\n      id: \"projects\",\n      title: \"Projects\",\n      description: \"View my work and projects\",\n      icon: Briefcase,\n      action: () => router.push(\"/projects\"),\n      category: \"Navigation\",\n      keywords: [\"projects\", \"work\", \"portfolio\", \"showcase\"],\n    },\n    {\n      id: \"blog\",\n      title: \"Blog\",\n      description: \"Read my latest articles\",\n      icon: FileText,\n      action: () => router.push(\"/blog\"),\n      category: \"Navigation\",\n      keywords: [\"blog\", \"articles\", \"writing\", \"posts\"],\n    },\n    {\n      id: \"contact\",\n      title: \"Contact\",\n      description: \"Get in touch with me\",\n      icon: Mail,\n      action: () => router.push(\"/contact\"),\n      category: \"Navigation\",\n      keywords: [\"contact\", \"email\", \"message\", \"hire\"],\n    },\n    // External Links\n    {\n      id: \"github\",\n      title: \"GitHub\",\n      description: \"View my GitHub profile\",\n      icon: Github,\n      action: () => window.open(\"https://github.com/ashishkamat\", \"_blank\"),\n      category: \"Social\",\n      keywords: [\"github\", \"code\", \"repositories\", \"open source\"],\n    },\n    {\n      id: \"linkedin\",\n      title: \"LinkedIn\",\n      description: \"Connect with me on LinkedIn\",\n      icon: Linkedin,\n      action: () => window.open(\"https://linkedin.com/in/ashishkamat\", \"_blank\"),\n      category: \"Social\",\n      keywords: [\"linkedin\", \"professional\", \"network\", \"career\"],\n    },\n    {\n      id: \"twitter\",\n      title: \"Twitter\",\n      description: \"Follow me on Twitter\",\n      icon: Twitter,\n      action: () => window.open(\"https://twitter.com/ashishkamat\", \"_blank\"),\n      category: \"Social\",\n      keywords: [\"twitter\", \"social\", \"updates\", \"thoughts\"],\n    },\n    // Quick Actions\n    {\n      id: \"email\",\n      title: \"Send Email\",\n      description: \"Send me an email directly\",\n      icon: Mail,\n      action: () => window.open(\"mailto:<EMAIL>\", \"_blank\"),\n      category: \"Quick Actions\",\n      keywords: [\"email\", \"contact\", \"message\", \"hire\"],\n    },\n    {\n      id: \"resume\",\n      title: \"Download Resume\",\n      description: \"Download my latest resume\",\n      icon: ExternalLink,\n      action: () => window.open(\"/resume.pdf\", \"_blank\"),\n      category: \"Quick Actions\",\n      keywords: [\"resume\", \"cv\", \"download\", \"hire\"],\n    },\n  ];\n\n  const filteredCommands = commands.filter((command) => {\n    const searchLower = search.toLowerCase();\n    return (\n      command.title.toLowerCase().includes(searchLower) ||\n      command.description.toLowerCase().includes(searchLower) ||\n      command.keywords.some((keyword) => keyword.includes(searchLower))\n    );\n  });\n\n  const groupedCommands = filteredCommands.reduce((acc, command) => {\n    if (!acc[command.category]) {\n      acc[command.category] = [];\n    }\n    acc[command.category].push(command);\n    return acc;\n  }, {} as Record<string, Command[]>);\n\n  useEffect(() => {\n    setSelectedIndex(0);\n  }, [search]);\n\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (!open) return;\n\n      if (e.key === \"ArrowDown\") {\n        e.preventDefault();\n        setSelectedIndex((prev) => \n          prev < filteredCommands.length - 1 ? prev + 1 : 0\n        );\n      } else if (e.key === \"ArrowUp\") {\n        e.preventDefault();\n        setSelectedIndex((prev) => \n          prev > 0 ? prev - 1 : filteredCommands.length - 1\n        );\n      } else if (e.key === \"Enter\") {\n        e.preventDefault();\n        if (filteredCommands[selectedIndex]) {\n          filteredCommands[selectedIndex].action();\n          onOpenChange(false);\n          setSearch(\"\");\n        }\n      }\n    };\n\n    window.addEventListener(\"keydown\", handleKeyDown);\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\n  }, [open, selectedIndex, filteredCommands, onOpenChange]);\n\n  const handleCommandSelect = (command: Command) => {\n    command.action();\n    onOpenChange(false);\n    setSearch(\"\");\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-2xl p-0 overflow-hidden\">\n        <DialogHeader className=\"p-4 pb-0\">\n          <DialogTitle className=\"sr-only\">Command Palette</DialogTitle>\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <Input\n              placeholder=\"Type a command or search...\"\n              value={search}\n              onChange={(e) => setSearch(e.target.value)}\n              className=\"pl-10 border-0 focus-visible:ring-0 text-base\"\n              autoFocus\n            />\n          </div>\n        </DialogHeader>\n\n        <div className=\"max-h-96 overflow-y-auto p-4 pt-0\">\n          {Object.keys(groupedCommands).length === 0 ? (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              No commands found for \"{search}\"\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {Object.entries(groupedCommands).map(([category, commands]) => (\n                <div key={category}>\n                  <div className=\"text-xs font-medium text-muted-foreground uppercase tracking-wider mb-2 px-2\">\n                    {category}\n                  </div>\n                  <div className=\"space-y-1\">\n                    {commands.map((command, index) => {\n                      const globalIndex = filteredCommands.indexOf(command);\n                      const Icon = command.icon;\n                      \n                      return (\n                        <motion.button\n                          key={command.id}\n                          onClick={() => handleCommandSelect(command)}\n                          className={`w-full text-left p-3 rounded-lg transition-colors duration-200 flex items-center space-x-3 ${\n                            globalIndex === selectedIndex\n                              ? \"bg-accent text-accent-foreground\"\n                              : \"hover:bg-accent/50\"\n                          }`}\n                          whileHover={{ scale: 1.02 }}\n                          whileTap={{ scale: 0.98 }}\n                        >\n                          <div className={`p-2 rounded-md ${\n                            globalIndex === selectedIndex\n                              ? \"bg-primary text-primary-foreground\"\n                              : \"bg-muted\"\n                          }`}>\n                            <Icon className=\"h-4 w-4\" />\n                          </div>\n                          <div className=\"flex-1 min-w-0\">\n                            <div className=\"font-medium\">{command.title}</div>\n                            <div className=\"text-sm text-muted-foreground truncate\">\n                              {command.description}\n                            </div>\n                          </div>\n                          {command.category === \"Social\" && (\n                            <ExternalLink className=\"h-3 w-3 text-muted-foreground\" />\n                          )}\n                        </motion.button>\n                      );\n                    })}\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n\n        <div className=\"border-t p-3 text-xs text-muted-foreground flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-1\">\n              <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5\">↑↓</Badge>\n              <span>Navigate</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5\">↵</Badge>\n              <span>Select</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5\">Esc</Badge>\n              <span>Close</span>\n            </div>\n          </div>\n          <div className=\"text-muted-foreground/60\">\n            {filteredCommands.length} result{filteredCommands.length !== 1 ? 's' : ''}\n          </div>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAMA;AACA;;;AAxBA;;;;;;;;AAyCO,SAAS,eAAe,EAAE,IAAI,EAAE,YAAY,EAAuB;;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,WAAsB;QAC1B,aAAa;QACb;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,sMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAQ;gBAAQ;aAAU;QACvC;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,qMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAS;gBAAO;gBAAc;aAAS;QACpD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,+MAAA,CAAA,YAAS;YACf,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAY;gBAAQ;gBAAa;aAAW;QACzD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,iNAAA,CAAA,WAAQ;YACd,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAQ;gBAAY;gBAAW;aAAQ;QACpD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,qMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAW;gBAAS;gBAAW;aAAO;QACnD;QACA,iBAAiB;QACjB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,yMAAA,CAAA,SAAM;YACZ,QAAQ,IAAM,OAAO,IAAI,CAAC,kCAAkC;YAC5D,UAAU;YACV,UAAU;gBAAC;gBAAU;gBAAQ;gBAAgB;aAAc;QAC7D;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,6MAAA,CAAA,WAAQ;YACd,QAAQ,IAAM,OAAO,IAAI,CAAC,uCAAuC;YACjE,UAAU;YACV,UAAU;gBAAC;gBAAY;gBAAgB;gBAAW;aAAS;QAC7D;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,2MAAA,CAAA,UAAO;YACb,QAAQ,IAAM,OAAO,IAAI,CAAC,mCAAmC;YAC7D,UAAU;YACV,UAAU;gBAAC;gBAAW;gBAAU;gBAAW;aAAW;QACxD;QACA,gBAAgB;QAChB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,qMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC,gCAAgC;YAC1D,UAAU;YACV,UAAU;gBAAC;gBAAS;gBAAW;gBAAW;aAAO;QACnD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,yNAAA,CAAA,eAAY;YAClB,QAAQ,IAAM,OAAO,IAAI,CAAC,eAAe;YACzC,UAAU;YACV,UAAU;gBAAC;gBAAU;gBAAM;gBAAY;aAAO;QAChD;KACD;IAED,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC;QACxC,MAAM,cAAc,OAAO,WAAW;QACtC,OACE,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACrC,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAC3C,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC,UAAY,QAAQ,QAAQ,CAAC;IAExD;IAEA,MAAM,kBAAkB,iBAAiB,MAAM,CAAC,CAAC,KAAK;QACpD,IAAI,CAAC,GAAG,CAAC,QAAQ,QAAQ,CAAC,EAAE;YAC1B,GAAG,CAAC,QAAQ,QAAQ,CAAC,GAAG,EAAE;QAC5B;QACA,GAAG,CAAC,QAAQ,QAAQ,CAAC,CAAC,IAAI,CAAC;QAC3B,OAAO;IACT,GAAG,CAAC;IAEJ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,iBAAiB;QACnB;mCAAG;QAAC;KAAO;IAEX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;0DAAgB,CAAC;oBACrB,IAAI,CAAC,MAAM;oBAEX,IAAI,EAAE,GAAG,KAAK,aAAa;wBACzB,EAAE,cAAc;wBAChB;sEAAiB,CAAC,OAChB,OAAO,iBAAiB,MAAM,GAAG,IAAI,OAAO,IAAI;;oBAEpD,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW;wBAC9B,EAAE,cAAc;wBAChB;sEAAiB,CAAC,OAChB,OAAO,IAAI,OAAO,IAAI,iBAAiB,MAAM,GAAG;;oBAEpD,OAAO,IAAI,EAAE,GAAG,KAAK,SAAS;wBAC5B,EAAE,cAAc;wBAChB,IAAI,gBAAgB,CAAC,cAAc,EAAE;4BACnC,gBAAgB,CAAC,cAAc,CAAC,MAAM;4BACtC,aAAa;4BACb,UAAU;wBACZ;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;4CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;mCAAG;QAAC;QAAM;QAAe;QAAkB;KAAa;IAExD,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,MAAM;QACd,aAAa;QACb,UAAU;IACZ;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;sCAAU;;;;;;sCACjC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oCACzC,WAAU;oCACV,SAAS;;;;;;;;;;;;;;;;;;8BAKf,6LAAC;oBAAI,WAAU;8BACZ,OAAO,IAAI,CAAC,iBAAiB,MAAM,KAAK,kBACvC,6LAAC;wBAAI,WAAU;;4BAAyC;4BAC9B;4BAAO;;;;;;6CAGjC,6LAAC;wBAAI,WAAU;kCACZ,OAAO,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,UAAU,SAAS,iBACxD,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDACZ;;;;;;kDAEH,6LAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,SAAS;4CACtB,MAAM,cAAc,iBAAiB,OAAO,CAAC;4CAC7C,MAAM,OAAO,QAAQ,IAAI;4CAEzB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gDAEZ,SAAS,IAAM,oBAAoB;gDACnC,WAAW,CAAC,2FAA2F,EACrG,gBAAgB,gBACZ,qCACA,sBACJ;gDACF,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;;kEAExB,6LAAC;wDAAI,WAAW,CAAC,eAAe,EAC9B,gBAAgB,gBACZ,uCACA,YACJ;kEACA,cAAA,6LAAC;4DAAK,WAAU;;;;;;;;;;;kEAElB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAe,QAAQ,KAAK;;;;;;0EAC3C,6LAAC;gEAAI,WAAU;0EACZ,QAAQ,WAAW;;;;;;;;;;;;oDAGvB,QAAQ,QAAQ,KAAK,0BACpB,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;+CAxBrB,QAAQ,EAAE;;;;;wCA4BrB;;;;;;;+BAvCM;;;;;;;;;;;;;;;8BA+ClB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;sDAC3D,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;sDAC3D,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;sDAC3D,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAGV,6LAAC;4BAAI,WAAU;;gCACZ,iBAAiB,MAAM;gCAAC;gCAAQ,iBAAiB,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAMnF;GA1PgB;;QAGC,qIAAA,CAAA,YAAS;;;KAHV", "debugId": null}}, {"offset": {"line": 907, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/navigation.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { useTheme } from \"next-themes\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport {\n  Moon,\n  Sun,\n  Menu,\n  X,\n  Home,\n  User,\n  Briefcase,\n  Mail,\n  FileText,\n  Search\n} from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { CommandPalette } from \"@/components/command-palette\";\nimport { cn } from \"@/lib/utils\";\n\nconst navItems = [\n  { name: \"Home\", href: \"/\", icon: Home },\n  { name: \"About\", href: \"/about\", icon: User },\n  { name: \"Projects\", href: \"/projects\", icon: Briefcase },\n  { name: \"Blog\", href: \"/blog\", icon: FileText },\n  { name: \"Contact\", href: \"/contact\", icon: Mail },\n];\n\nexport function Navigation() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n  const [commandPaletteOpen, setCommandPaletteOpen] = useState(false);\n  const { theme, setTheme } = useTheme();\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if ((e.metaKey || e.ctrlKey) && e.key === \"k\") {\n        e.preventDefault();\n        setCommandPaletteOpen(true);\n      }\n    };\n\n    window.addEventListener(\"keydown\", handleKeyDown);\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\n  }, []);\n\n  const toggleTheme = () => {\n    setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n  };\n\n  if (!mounted) {\n    return null;\n  }\n\n  return (\n    <motion.header\n      className={cn(\n        \"fixed top-0 left-0 right-0 z-50 transition-all duration-300\",\n        scrolled\n          ? \"bg-background/80 backdrop-blur-md border-b border-border\"\n          : \"bg-transparent\"\n      )}\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      <nav className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <motion.div\n            className=\"flex-shrink-0\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Link href=\"/\" className=\"text-2xl font-bold gradient-text-blue\">\n              AK\n            </Link>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-4\">\n              {navItems.map((item) => (\n                <motion.div\n                  key={item.name}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <Link\n                    href={item.href}\n                    className=\"text-foreground/80 hover:text-foreground px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 hover:bg-accent\"\n                  >\n                    {item.name}\n                  </Link>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n\n          {/* Command Palette, Theme Toggle & Mobile Menu Button */}\n          <div className=\"flex items-center space-x-2\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setCommandPaletteOpen(true)}\n              className=\"hidden md:flex items-center space-x-2 text-muted-foreground hover:text-foreground\"\n            >\n              <Search className=\"h-4 w-4\" />\n              <span className=\"text-sm\">Search</span>\n              <kbd className=\"pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100\">\n                <span className=\"text-xs\">⌘</span>K\n              </kbd>\n            </Button>\n\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={toggleTheme}\n              className=\"w-9 h-9\"\n            >\n              {theme === \"dark\" ? (\n                <Sun className=\"h-4 w-4\" />\n              ) : (\n                <Moon className=\"h-4 w-4\" />\n              )}\n            </Button>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={() => setIsOpen(!isOpen)}\n                className=\"w-9 h-9\"\n              >\n                {isOpen ? (\n                  <X className=\"h-4 w-4\" />\n                ) : (\n                  <Menu className=\"h-4 w-4\" />\n                )}\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              className=\"md:hidden\"\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: \"auto\" }}\n              exit={{ opacity: 0, height: 0 }}\n              transition={{ duration: 0.3 }}\n            >\n              <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-background/95 backdrop-blur-md rounded-lg mt-2 border border-border\">\n                {navItems.map((item) => {\n                  const Icon = item.icon;\n                  return (\n                    <motion.div\n                      key={item.name}\n                      whileHover={{ scale: 1.02 }}\n                      whileTap={{ scale: 0.98 }}\n                    >\n                      <Link\n                        href={item.href}\n                        className=\"text-foreground/80 hover:text-foreground flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 hover:bg-accent\"\n                        onClick={() => setIsOpen(false)}\n                      >\n                        <Icon className=\"h-4 w-4 mr-3\" />\n                        {item.name}\n                      </Link>\n                    </motion.div>\n                  );\n                })}\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </nav>\n\n      <CommandPalette\n        open={commandPaletteOpen}\n        onOpenChange={setCommandPaletteOpen}\n      />\n    </motion.header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;;;AApBA;;;;;;;;;AAsBA,MAAM,WAAW;IACf;QAAE,MAAM;QAAQ,MAAM;QAAK,MAAM,sMAAA,CAAA,OAAI;IAAC;IACtC;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,qMAAA,CAAA,OAAI;IAAC;IAC5C;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,+MAAA,CAAA,YAAS;IAAC;IACvD;QAAE,MAAM;QAAQ,MAAM;QAAS,MAAM,iNAAA,CAAA,WAAQ;IAAC;IAC9C;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,qMAAA,CAAA,OAAI;IAAC;CACjD;AAEM,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,WAAW;QACb;+BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,YAAY,OAAO,OAAO,GAAG;gBAC/B;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;sDAAgB,CAAC;oBACrB,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;wBAC7C,EAAE,cAAc;wBAChB,sBAAsB;oBACxB;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;+BAAG,EAAE;IAEL,MAAM,cAAc;QAClB,SAAS,UAAU,SAAS,UAAU;IACxC;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA,WACI,6DACA;QAEN,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;;0BAE5B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;0CAExB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAwC;;;;;;;;;;;0CAMnE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;sDAExB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CARP,KAAK,IAAI;;;;;;;;;;;;;;;0CAgBtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,sBAAsB;wCACrC,WAAU;;0DAEV,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAQ;;;;;;;;;;;;;kDAItC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAET,UAAU,uBACT,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;iEAEf,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAKpB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,UAAU,CAAC;4CAC1B,WAAU;sDAET,uBACC,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;qEAEb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ1B,6LAAC,4LAAA,CAAA,kBAAe;kCACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BACjC,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAO;4BACtC,MAAM;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BAC9B,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC;oCACb,MAAM,OAAO,KAAK,IAAI;oCACtB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS,IAAM,UAAU;;8DAEzB,6LAAC;oDAAK,WAAU;;;;;;gDACf,KAAK,IAAI;;;;;;;uCAVP,KAAK,IAAI;;;;;gCAcpB;;;;;;;;;;;;;;;;;;;;;;0BAOV,6LAAC,2IAAA,CAAA,iBAAc;gBACb,MAAM;gBACN,cAAc;;;;;;;;;;;;AAItB;GA7KgB;;QAIc,mJAAA,CAAA,WAAQ;;;KAJtB", "debugId": null}}, {"offset": {"line": 1309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/footer.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { \n  Github, \n  Linkedin, \n  Twitter, \n  Mail, \n  Heart, \n  ArrowUp,\n  Code,\n  Coffee\n} from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\n\nconst footerLinks = {\n  navigation: [\n    { name: \"Home\", href: \"/\" },\n    { name: \"About\", href: \"/about\" },\n    { name: \"Projects\", href: \"/projects\" },\n    { name: \"Blog\", href: \"/blog\" },\n    { name: \"Contact\", href: \"/contact\" },\n  ],\n  services: [\n    { name: \"Full Stack Development\", href: \"/#services\" },\n    { name: \"UI/UX Design\", href: \"/#services\" },\n    { name: \"Mobile Development\", href: \"/#services\" },\n    { name: \"Consulting\", href: \"/#services\" },\n  ],\n  resources: [\n    { name: \"Blog\", href: \"/blog\" },\n    { name: \"Projects\", href: \"/projects\" },\n    { name: \"Tech Stack\", href: \"/#tech-stack\" },\n    { name: \"Contact\", href: \"/contact\" },\n  ],\n};\n\nconst socialLinks = [\n  {\n    name: \"GitHub\",\n    href: \"https://github.com/ashishkamat\",\n    icon: Github,\n    color: \"hover:text-gray-600 dark:hover:text-gray-300\",\n  },\n  {\n    name: \"LinkedIn\",\n    href: \"https://linkedin.com/in/ashishkamat\",\n    icon: Linkedin,\n    color: \"hover:text-blue-600\",\n  },\n  {\n    name: \"Twitter\",\n    href: \"https://twitter.com/ashishkamat\",\n    icon: Twitter,\n    color: \"hover:text-blue-500\",\n  },\n  {\n    name: \"Email\",\n    href: \"mailto:<EMAIL>\",\n    icon: Mail,\n    color: \"hover:text-green-600\",\n  },\n];\n\nexport function Footer() {\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: \"smooth\" });\n  };\n\n  return (\n    <footer className=\"bg-background border-t border-border\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-12 lg:py-16\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-2\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5 }}\n                viewport={{ once: true }}\n              >\n                <Link href=\"/\" className=\"text-2xl font-bold gradient-text-blue mb-4 inline-block\">\n                  Ashish Kamat\n                </Link>\n                <p className=\"text-muted-foreground mb-6 max-w-md\">\n                  Full Stack Developer & UI/UX Designer passionate about creating \n                  innovative digital experiences that make a difference.\n                </p>\n                \n                {/* Social Links */}\n                <div className=\"flex space-x-4\">\n                  {socialLinks.map((social, index) => {\n                    const Icon = social.icon;\n                    return (\n                      <motion.a\n                        key={social.name}\n                        href={social.href}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className={`p-2 rounded-lg bg-muted hover:bg-accent transition-all duration-300 ${social.color}`}\n                        whileHover={{ scale: 1.1 }}\n                        whileTap={{ scale: 0.9 }}\n                        initial={{ opacity: 0, scale: 0 }}\n                        whileInView={{ opacity: 1, scale: 1 }}\n                        transition={{ duration: 0.3, delay: index * 0.1 }}\n                        viewport={{ once: true }}\n                      >\n                        <Icon className=\"h-5 w-5\" />\n                      </motion.a>\n                    );\n                  })}\n                </div>\n              </motion.div>\n            </div>\n\n            {/* Navigation Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.1 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"font-semibold text-foreground mb-4\">Navigation</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.navigation.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-muted-foreground hover:text-foreground transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n\n            {/* Services Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.2 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"font-semibold text-foreground mb-4\">Services</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.services.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-muted-foreground hover:text-foreground transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n\n            {/* Resources Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.3 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"font-semibold text-foreground mb-4\">Resources</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.resources.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-muted-foreground hover:text-foreground transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n          </div>\n        </div>\n\n        {/* Bottom Footer */}\n        <motion.div\n          className=\"py-6 border-t border-border\"\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.5, delay: 0.4 }}\n          viewport={{ once: true }}\n        >\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            {/* Copyright */}\n            <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n              <span>© 2024 Ashish Kamat. Made with</span>\n              <Heart className=\"h-4 w-4 text-red-500 animate-pulse\" />\n              <span>and</span>\n              <Coffee className=\"h-4 w-4 text-amber-600\" />\n              <span>in Kathmandu</span>\n            </div>\n\n            {/* Tech Stack & Back to Top */}\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n                <Code className=\"h-4 w-4\" />\n                <span>Built with Next.js & Tailwind CSS</span>\n              </div>\n              \n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                onClick={scrollToTop}\n                className=\"rounded-full hover:scale-110 transition-transform duration-200\"\n              >\n                <ArrowUp className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Fun Easter Egg */}\n        <motion.div\n          className=\"text-center py-4 border-t border-border/50\"\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.5, delay: 0.6 }}\n          viewport={{ once: true }}\n        >\n          <p className=\"text-xs text-muted-foreground/70\">\n            🚀 This website is powered by{\" \"}\n            <span className=\"font-mono bg-muted px-1 py-0.5 rounded text-xs\">\n              {Math.floor(Math.random() * 100)}% coffee\n            </span>{\" \"}\n            and{\" \"}\n            <span className=\"font-mono bg-muted px-1 py-0.5 rounded text-xs\">\n              {Math.floor(Math.random() * 100)}% passion\n            </span>\n          </p>\n        </motion.div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAdA;;;;;;AAgBA,MAAM,cAAc;IAClB,YAAY;QACV;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IACD,UAAU;QACR;YAAE,MAAM;YAA0B,MAAM;QAAa;QACrD;YAAE,MAAM;YAAgB,MAAM;QAAa;QAC3C;YAAE,MAAM;YAAsB,MAAM;QAAa;QACjD;YAAE,MAAM;YAAc,MAAM;QAAa;KAC1C;IACD,WAAW;QACT;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAc,MAAM;QAAe;QAC3C;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;AACH;AAEA,MAAM,cAAc;IAClB;QACE,MAAM;QACN,MAAM;QACN,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;QACd,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,2MAAA,CAAA,UAAO;QACb,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;IACT;CACD;AAEM,SAAS;IACd,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;;sDAEvB,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAA0D;;;;;;sDAGnF,6LAAC;4CAAE,WAAU;sDAAsC;;;;;;sDAMnD,6LAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,QAAQ;gDACxB,MAAM,OAAO,OAAO,IAAI;gDACxB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oDAEP,MAAM,OAAO,IAAI;oDACjB,QAAO;oDACP,KAAI;oDACJ,WAAW,CAAC,oEAAoE,EAAE,OAAO,KAAK,EAAE;oDAChG,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAI;oDACvB,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,aAAa;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDACpC,YAAY;wDAAE,UAAU;wDAAK,OAAO,QAAQ;oDAAI;oDAChD,UAAU;wDAAE,MAAM;oDAAK;8DAEvB,cAAA,6LAAC;wDAAK,WAAU;;;;;;mDAZX,OAAO,IAAI;;;;;4CAetB;;;;;;;;;;;;;;;;;0CAMN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,6LAAC;wCAAG,WAAU;kDACX,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,qBAC3B,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,6LAAC;wCAAG,WAAU;kDACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACzB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,6LAAC;wCAAG,WAAU;kDACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAe5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAK;;;;;;kDACN,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;kDAAK;;;;;;kDACN,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAK;;;;;;;;;;;;0CAIR,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;kDAGR,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,+MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,6LAAC;wBAAE,WAAU;;4BAAmC;4BAChB;0CAC9B,6LAAC;gCAAK,WAAU;;oCACb,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;oCAAK;;;;;;;4BAC3B;4BAAI;4BACR;0CACJ,6LAAC;gCAAK,WAAU;;oCACb,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;oCAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C;KApLgB", "debugId": null}}, {"offset": {"line": 1899, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 2014, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/about/about-hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport Image from \"next/image\";\nimport { motion } from \"framer-motion\";\nimport { Download, MapPin, Calendar, Coffee } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\n\nexport function AboutHero() {\n  return (\n    <section className=\"py-20 bg-muted/30\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Content */}\n          <motion.div\n            className=\"space-y-8\"\n            initial={{ opacity: 0, x: -50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n          >\n            <div className=\"space-y-4\">\n              <motion.h1\n                className=\"text-4xl sm:text-5xl lg:text-6xl font-bold\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.2 }}\n              >\n                About <span className=\"gradient-text-blue\">Me</span>\n              </motion.h1>\n              \n              <motion.p\n                className=\"text-xl text-muted-foreground leading-relaxed\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 }}\n              >\n                I'm a passionate full-stack developer and UI/UX designer with over 3 years of experience \n                creating innovative digital solutions that make a real impact.\n              </motion.p>\n            </div>\n\n            <motion.div\n              className=\"prose prose-lg dark:prose-invert max-w-none\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.4 }}\n            >\n              <p>\n                My journey in tech started with a curiosity about how things work behind the scenes. \n                What began as tinkering with HTML and CSS has evolved into a deep passion for creating \n                seamless, user-centered digital experiences.\n              </p>\n              <p>\n                I specialize in modern web technologies like React, Next.js, and TypeScript, with a \n                strong focus on performance, accessibility, and user experience. I believe that great \n                software should not only function flawlessly but also delight users at every interaction.\n              </p>\n              <p>\n                When I'm not coding, you'll find me exploring new technologies, contributing to open-source \n                projects, or sharing knowledge with the developer community through blog posts and mentoring.\n              </p>\n            </motion.div>\n\n            <motion.div\n              className=\"flex flex-col sm:flex-row gap-4\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.5 }}\n            >\n              <Button size=\"lg\" className=\"group\">\n                <Download className=\"mr-2 h-4 w-4 group-hover:animate-bounce\" />\n                Download Resume\n              </Button>\n              <Button variant=\"outline\" size=\"lg\">\n                <Coffee className=\"mr-2 h-4 w-4\" />\n                Let's Chat\n              </Button>\n            </motion.div>\n          </motion.div>\n\n          {/* Image and Info Cards */}\n          <motion.div\n            className=\"relative\"\n            initial={{ opacity: 0, x: 50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n          >\n            <div className=\"relative w-80 h-80 mx-auto lg:w-96 lg:h-96 mb-8\">\n              <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl animate-pulse-glow\" />\n              <div className=\"absolute inset-2 bg-background rounded-2xl overflow-hidden\">\n                <Image\n                  src=\"/ashish-profile.svg\"\n                  alt=\"Ashish Kamat\"\n                  fill\n                  className=\"object-cover\"\n                  priority\n                />\n              </div>\n            </div>\n\n            {/* Info Cards */}\n            <div className=\"grid grid-cols-2 gap-4\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.6 }}\n              >\n                <Card className=\"text-center hover-lift\">\n                  <CardContent className=\"p-4\">\n                    <MapPin className=\"h-6 w-6 text-primary mx-auto mb-2\" />\n                    <div className=\"font-semibold\">Location</div>\n                    <div className=\"text-sm text-muted-foreground\">Kathmandu, Nepal</div>\n                  </CardContent>\n                </Card>\n              </motion.div>\n\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.7 }}\n              >\n                <Card className=\"text-center hover-lift\">\n                  <CardContent className=\"p-4\">\n                    <Calendar className=\"h-6 w-6 text-primary mx-auto mb-2\" />\n                    <div className=\"font-semibold\">Experience</div>\n                    <div className=\"text-sm text-muted-foreground\">3+ Years</div>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;4CAC1B;0DACO,6LAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;;kDAG7C,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;kDAC1B;;;;;;;;;;;;0CAMH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;gCAAI;;kDAEzB,6LAAC;kDAAE;;;;;;kDAKH,6LAAC;kDAAE;;;;;;kDAKH,6LAAC;kDAAE;;;;;;;;;;;;0CAML,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;gCAAI;;kDAEzB,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;;0DAC1B,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAA4C;;;;;;;kDAGlE,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;;0DAC7B,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;kCAOzC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAExC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,IAAI;4CACJ,WAAU;4CACV,QAAQ;;;;;;;;;;;;;;;;;0CAMd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;kDAEzB,cAAA,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,6LAAC;wDAAI,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;;;;;kDAKrD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;kDAEzB,cAAA,6LAAC,mIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,6LAAC;wDAAI,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnE;KA/HgB", "debugId": null}}, {"offset": {"line": 2420, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/lib/api.ts"], "sourcesContent": ["// API client for CMS integration\nconst CMS_BASE_URL = process.env.NEXT_PUBLIC_CMS_URL || 'http://localhost:3000'\n\nexport interface Project {\n  id: string\n  title: string\n  description: string\n  longDescription?: string\n  image?: string\n  category: string\n  technologies: string[]\n  liveUrl?: string\n  githubUrl?: string\n  featured: boolean\n  published: boolean\n  order: number\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface BlogPost {\n  id: string\n  title: string\n  slug: string\n  excerpt: string\n  content: string\n  image?: string\n  category: string\n  tags: string[]\n  published: boolean\n  featured: boolean\n  readTime?: number\n  views: number\n  createdAt: string\n  updatedAt: string\n  publishedAt?: string\n}\n\nexport interface Experience {\n  id: string\n  title: string\n  company: string\n  companyLogo?: string\n  location: string\n  period: string\n  type: string\n  description: string\n  achievements: string[]\n  technologies: string[]\n  website?: string\n  order: number\n  published: boolean\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Education {\n  id: string\n  degree: string\n  institution: string\n  location: string\n  period: string\n  grade?: string\n  description: string\n  highlights: string[]\n  order: number\n  published: boolean\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Certification {\n  id: string\n  title: string\n  issuer: string\n  date: string\n  credentialId?: string\n  emoji?: string\n  description: string\n  order: number\n  published: boolean\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Service {\n  id: string\n  title: string\n  description: string\n  features: string[]\n  icon: string\n  color: string\n  bgColor: string\n  order: number\n  published: boolean\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface TechStack {\n  id: string\n  name: string\n  logo: string\n  color: string\n  category: string\n  order: number\n  published: boolean\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Testimonial {\n  id: string\n  name: string\n  role: string\n  company: string\n  content: string\n  avatar?: string\n  rating: number\n  featured: boolean\n  published: boolean\n  order: number\n  createdAt: string\n  updatedAt: string\n}\n\n// API functions\nexport const api = {\n  // Projects\n  getProjects: async (): Promise<Project[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/projects`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch projects')\n    }\n    return response.json()\n  },\n\n  getProject: async (id: string): Promise<Project> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/projects/${id}`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch project')\n    }\n    return response.json()\n  },\n\n  // Blog Posts\n  getBlogPosts: async (): Promise<BlogPost[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/blog`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch blog posts')\n    }\n    return response.json()\n  },\n\n  getBlogPost: async (slug: string): Promise<BlogPost> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/blog/${slug}`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch blog post')\n    }\n    return response.json()\n  },\n\n  // Services\n  getServices: async (): Promise<Service[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/services`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch services')\n    }\n    return response.json()\n  },\n\n  // Tech Stack\n  getTechStack: async (): Promise<TechStack[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/tech-stack`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch tech stack')\n    }\n    return response.json()\n  },\n\n  // Testimonials\n  getTestimonials: async (): Promise<Testimonial[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/testimonials`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch testimonials')\n    }\n    return response.json()\n  },\n\n  // Experiences\n  getExperiences: async (): Promise<Experience[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/experiences`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch experiences')\n    }\n    return response.json()\n  },\n\n  // Education\n  getEducation: async (): Promise<Education[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/education`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch education')\n    }\n    return response.json()\n  },\n\n  // Certifications\n  getCertifications: async (): Promise<Certification[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/certifications`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch certifications')\n    }\n    return response.json()\n  },\n\n  // Contact form submission\n  submitContact: async (data: {\n    name: string\n    email: string\n    subject: string\n    message: string\n  }): Promise<{ success: boolean; message: string; id?: string }> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/contact`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      const errorData = await response.json()\n      throw new Error(errorData.error || 'Failed to send message')\n    }\n\n    return response.json()\n  },\n}\n\n// Helper functions\nexport const getPublishedProjects = (projects: Project[]) => \n  projects.filter(project => project.published).sort((a, b) => a.order - b.order)\n\nexport const getFeaturedProjects = (projects: Project[]) => \n  projects.filter(project => project.published && project.featured).sort((a, b) => a.order - b.order)\n\nexport const getProjectsByCategory = (projects: Project[], category: string) => \n  category === 'All' \n    ? getPublishedProjects(projects)\n    : projects.filter(project => project.published && project.category === category).sort((a, b) => a.order - b.order)\n\nexport const getPublishedServices = (services: Service[]) => \n  services.filter(service => service.published).sort((a, b) => a.order - b.order)\n\nexport const getTechStackByCategory = (techStack: TechStack[]) => {\n  const published = techStack.filter(tech => tech.published)\n  return published.reduce((acc, tech) => {\n    if (!acc[tech.category]) {\n      acc[tech.category] = []\n    }\n    acc[tech.category].push(tech)\n    return acc\n  }, {} as Record<string, TechStack[]>)\n}\n\nexport const getPublishedTestimonials = (testimonials: Testimonial[]) => \n  testimonials.filter(testimonial => testimonial.published).sort((a, b) => a.order - b.order)\n\nexport const getFeaturedTestimonials = (testimonials: Testimonial[]) =>\n  testimonials.filter(testimonial => testimonial.published && testimonial.featured).sort((a, b) => a.order - b.order)\n\nexport const getPublishedExperiences = (experiences: Experience[]) =>\n  experiences.filter(experience => experience.published).sort((a, b) => a.order - b.order)\n\nexport const getPublishedEducation = (education: Education[]) =>\n  education.filter(edu => edu.published).sort((a, b) => a.order - b.order)\n\nexport const getPublishedCertifications = (certifications: Certification[]) =>\n  certifications.filter(cert => cert.published).sort((a, b) => a.order - b.order)\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;;;;;;;;;AACZ;AAArB,MAAM,eAAe,6DAAmC;AA8HjD,MAAM,MAAM;IACjB,WAAW;IACX,aAAa;QACX,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,CAAC;QAC3D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,EAAE,IAAI;QACjE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc;QACZ,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC;QACvD,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,MAAM;QAC/D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,aAAa;QACX,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,CAAC;QAC3D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc;QACZ,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC;QAC7D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,iBAAiB;QACf,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,iBAAiB,CAAC;QAC/D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,gBAAgB;QACd,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,gBAAgB,CAAC;QAC9D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY;IACZ,cAAc;QACZ,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC;QAC5D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;IACjB,mBAAmB;QACjB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,mBAAmB,CAAC;QACjE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,eAAe,OAAO;QAMpB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,YAAY,CAAC,EAAE;YAC1D,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;QACrC;QAEA,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,uBAAuB,CAAC,WACnC,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAEzE,MAAM,sBAAsB,CAAC,WAClC,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,IAAI,QAAQ,QAAQ,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAE7F,MAAM,wBAAwB,CAAC,UAAqB,WACzD,aAAa,QACT,qBAAqB,YACrB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,IAAI,QAAQ,QAAQ,KAAK,UAAU,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAE9G,MAAM,uBAAuB,CAAC,WACnC,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAEzE,MAAM,yBAAyB,CAAC;IACrC,MAAM,YAAY,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS;IACzD,OAAO,UAAU,MAAM,CAAC,CAAC,KAAK;QAC5B,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,EAAE;YACvB,GAAG,CAAC,KAAK,QAAQ,CAAC,GAAG,EAAE;QACzB;QACA,GAAG,CAAC,KAAK,QAAQ,CAAC,CAAC,IAAI,CAAC;QACxB,OAAO;IACT,GAAG,CAAC;AACN;AAEO,MAAM,2BAA2B,CAAC,eACvC,aAAa,MAAM,CAAC,CAAA,cAAe,YAAY,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAErF,MAAM,0BAA0B,CAAC,eACtC,aAAa,MAAM,CAAC,CAAA,cAAe,YAAY,SAAS,IAAI,YAAY,QAAQ,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAE7G,MAAM,0BAA0B,CAAC,cACtC,YAAY,MAAM,CAAC,CAAA,aAAc,WAAW,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAElF,MAAM,wBAAwB,CAAC,YACpC,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAElE,MAAM,6BAA6B,CAAC,iBACzC,eAAe,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK", "debugId": null}}, {"offset": {"line": 2559, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/about/experience.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { Calendar, MapPin, ExternalLink, Briefcase } from \"lucide-react\";\nimport { VerticalTimeline, VerticalTimelineElement } from 'react-vertical-timeline-component';\nimport 'react-vertical-timeline-component/style.min.css';\nimport { Badge } from \"@/components/ui/badge\";\nimport { api, getPublishedExperiences, type Experience } from \"@/lib/api\";\nimport { useEffect, useState } from \"react\";\nimport Image from \"next/image\";\n\n// Fallback experiences data (will be replaced by CMS data)\nconst fallbackExperiences = [\n  {\n    title: \"Senior Full Stack Developer\",\n    company: \"TechCorp Solutions\",\n    location: \"Mumbai, India\",\n    period: \"2022 - Present\",\n    type: \"Full-time\",\n    description: \"Leading development of scalable web applications using React, Next.js, and Node.js. Mentoring junior developers and implementing best practices for code quality and performance.\",\n    achievements: [\n      \"Increased application performance by 40% through optimization\",\n      \"Led a team of 5 developers on multiple projects\",\n      \"Implemented CI/CD pipelines reducing deployment time by 60%\",\n      \"Architected microservices handling 1M+ requests daily\"\n    ],\n    technologies: [\"React\", \"Next.js\", \"TypeScript\", \"Node.js\", \"PostgreSQL\", \"AWS\"],\n    website: \"https://techcorp.com\"\n  },\n  {\n    title: \"Full Stack Developer\",\n    company: \"StartupXYZ\",\n    location: \"Remote\",\n    period: \"2021 - 2022\",\n    type: \"Full-time\",\n    description: \"Developed and maintained multiple client projects using modern web technologies. Collaborated with design teams to create pixel-perfect user interfaces.\",\n    achievements: [\n      \"Built 10+ responsive web applications from scratch\",\n      \"Reduced page load times by 50% through optimization\",\n      \"Implemented real-time features using WebSocket\",\n      \"Mentored 3 junior developers\"\n    ],\n    technologies: [\"React\", \"Vue.js\", \"Express.js\", \"MongoDB\", \"Firebase\"],\n    website: \"https://startupxyz.com\"\n  },\n  {\n    title: \"Frontend Developer\",\n    company: \"Digital Agency Pro\",\n    location: \"Mumbai, India\",\n    period: \"2020 - 2021\",\n    type: \"Full-time\",\n    description: \"Focused on creating beautiful and functional user interfaces for various client projects. Worked closely with designers to bring mockups to life.\",\n    achievements: [\n      \"Delivered 20+ client projects on time and within budget\",\n      \"Improved client satisfaction scores by 25%\",\n      \"Implemented responsive designs for mobile-first approach\",\n      \"Created reusable component library\"\n    ],\n    technologies: [\"HTML\", \"CSS\", \"JavaScript\", \"React\", \"Sass\", \"Figma\"],\n    website: \"https://digitalagencypro.com\"\n  },\n  {\n    title: \"Junior Web Developer\",\n    company: \"WebDev Studio\",\n    location: \"Mumbai, India\",\n    period: \"2019 - 2020\",\n    type: \"Full-time\",\n    description: \"Started my professional journey learning modern web development practices and contributing to various client projects.\",\n    achievements: [\n      \"Completed 15+ small to medium-sized projects\",\n      \"Learned modern JavaScript frameworks and tools\",\n      \"Contributed to team's coding standards documentation\",\n      \"Achieved 95% client satisfaction rating\"\n    ],\n    technologies: [\"HTML\", \"CSS\", \"JavaScript\", \"jQuery\", \"Bootstrap\", \"PHP\"],\n    website: \"https://webdevstudio.com\"\n  }\n];\n\nexport function Experience() {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const [experiences, setExperiences] = useState<Experience[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchExperiences = async () => {\n      try {\n        const data = await api.getExperiences();\n        setExperiences(getPublishedExperiences(data));\n      } catch (error) {\n        console.error('Failed to fetch experiences:', error);\n        // Use fallback data if API fails\n        setExperiences(fallbackExperiences.map((exp, index) => ({\n          id: `fallback-${index}`,\n          title: exp.title,\n          company: exp.company,\n          companyLogo: undefined,\n          location: exp.location,\n          period: exp.period,\n          type: exp.type,\n          description: exp.description,\n          achievements: exp.achievements,\n          technologies: exp.technologies,\n          website: exp.website,\n          order: index,\n          published: true,\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString(),\n        })));\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchExperiences();\n  }, []);\n\n  if (loading) {\n    return (\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <div className=\"animate-pulse\">\n              <div className=\"h-8 bg-gray-300 rounded w-64 mx-auto mb-4\"></div>\n              <div className=\"h-4 bg-gray-300 rounded w-96 mx-auto\"></div>\n            </div>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section ref={ref} className=\"py-20\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Work Experience</span>\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            My professional journey in web development, from junior developer to senior full-stack engineer.\n          </p>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={inView ? { opacity: 1 } : {}}\n          transition={{ duration: 0.8, delay: 0.2 }}\n        >\n          <VerticalTimeline>\n            {experiences.map((experience) => {\n              return (\n                <VerticalTimelineElement\n                  key={experience.id}\n                  className=\"vertical-timeline-element--work\"\n                  contentStyle={{\n                    background: 'hsl(var(--card))',\n                    color: 'hsl(var(--card-foreground))',\n                    border: '1px solid hsl(var(--border))',\n                    borderRadius: '12px',\n                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n                  }}\n                  contentArrowStyle={{\n                    borderRight: '7px solid hsl(var(--border))',\n                  }}\n                  date={experience.period}\n                  iconStyle={{\n                    background: '#ffffff',\n                    color: '#000',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    border: '3px solid hsl(var(--background))',\n                    boxShadow: '0 0 0 2px hsl(var(--border))',\n                    width: '60px',\n                    height: '60px',\n                  }}\n                  icon={\n                    experience.companyLogo ? (\n                      <Image\n                        src={experience.companyLogo}\n                        alt={`${experience.company} logo`}\n                        width={40}\n                        height={40}\n                        className=\"rounded-full object-contain\"\n                      />\n                    ) : (\n                      <Briefcase className=\"h-6 w-6\" />\n                    )\n                  }\n                >\n                  <div className=\"space-y-4\">\n                    <div>\n                      <h3 className=\"text-xl font-bold\">{experience.title}</h3>\n                      <div className=\"flex items-center space-x-2 text-muted-foreground mt-1\">\n                        <span className=\"font-medium text-primary\">{experience.company}</span>\n                        {experience.website && (\n                          <a\n                            href={experience.website}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"hover:text-primary transition-colors\"\n                          >\n                            <ExternalLink className=\"h-4 w-4\" />\n                          </a>\n                        )}\n                      </div>\n                      <div className=\"flex flex-col sm:flex-row gap-2 text-sm text-muted-foreground mt-2\">\n                        <div className=\"flex items-center space-x-1\">\n                          <MapPin className=\"h-4 w-4 shrink-0 translate-y-[1px]\" />\n                          <span className=\"leading-none\">{experience.location}</span>\n                        </div>\n                        <Badge variant=\"outline\" className=\"w-fit\">{experience.type}</Badge>\n                      </div>\n                    </div>\n\n                    <p className=\"text-muted-foreground\">{experience.description}</p>\n\n                    <div>\n                      <h4 className=\"font-semibold mb-2\">Key Achievements:</h4>\n                      <ul className=\"space-y-1\">\n                        {experience.achievements.map((achievement, achievementIndex) => (\n                          <li key={achievementIndex} className=\"flex items-start space-x-2 text-sm text-muted-foreground\">\n                            <div className=\"w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0\" />\n                            <span>{achievement}</span>\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n\n                    <div>\n                      <h4 className=\"font-semibold mb-2\">Technologies:</h4>\n                      <div className=\"flex flex-wrap gap-2\">\n                        {experience.technologies.map((tech) => (\n                          <Badge key={tech} variant=\"secondary\" className=\"text-xs\">\n                            {tech}\n                          </Badge>\n                        ))}\n                      </div>\n                    </div>\n                  </div>\n                </VerticalTimelineElement>\n              );\n            })}\n          </VerticalTimeline>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAYA,2DAA2D;AAC3D,MAAM,sBAAsB;IAC1B;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,MAAM;QACN,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAS;YAAW;YAAc;YAAW;YAAc;SAAM;QAChF,SAAS;IACX;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,MAAM;QACN,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAS;YAAU;YAAc;YAAW;SAAW;QACtE,SAAS;IACX;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,MAAM;QACN,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAQ;YAAO;YAAc;YAAS;YAAQ;SAAQ;QACrE,SAAS;IACX;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,MAAM;QACN,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,cAAc;YAAC;YAAQ;YAAO;YAAc;YAAU;YAAa;SAAM;QACzE,SAAS;IACX;CACD;AAEM,SAAS;;IACd,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;yDAAmB;oBACvB,IAAI;wBACF,MAAM,OAAO,MAAM,oHAAA,CAAA,MAAG,CAAC,cAAc;wBACrC,eAAe,CAAA,GAAA,oHAAA,CAAA,0BAAuB,AAAD,EAAE;oBACzC,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,gCAAgC;wBAC9C,iCAAiC;wBACjC,eAAe,oBAAoB,GAAG;qEAAC,CAAC,KAAK,QAAU,CAAC;oCACtD,IAAI,CAAC,SAAS,EAAE,OAAO;oCACvB,OAAO,IAAI,KAAK;oCAChB,SAAS,IAAI,OAAO;oCACpB,aAAa;oCACb,UAAU,IAAI,QAAQ;oCACtB,QAAQ,IAAI,MAAM;oCAClB,MAAM,IAAI,IAAI;oCACd,aAAa,IAAI,WAAW;oCAC5B,cAAc,IAAI,YAAY;oCAC9B,cAAc,IAAI,YAAY;oCAC9B,SAAS,IAAI,OAAO;oCACpB,OAAO;oCACP,WAAW;oCACX,WAAW,IAAI,OAAO,WAAW;oCACjC,WAAW,IAAI,OAAO,WAAW;gCACnC,CAAC;;oBACH,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;+BAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,6LAAC;YAAQ,WAAU;sBACjB,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM3B;IAEA,qBACE,6LAAC;QAAQ,KAAK;QAAK,WAAU;kBAC3B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,6LAAC;4BAAG,WAAU;sCACZ,cAAA,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,6LAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAKjE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS,SAAS;wBAAE,SAAS;oBAAE,IAAI,CAAC;oBACpC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAExC,cAAA,6LAAC,qLAAA,CAAA,mBAAgB;kCACd,YAAY,GAAG,CAAC,CAAC;4BAChB,qBACE,6LAAC,qLAAA,CAAA,0BAAuB;gCAEtB,WAAU;gCACV,cAAc;oCACZ,YAAY;oCACZ,OAAO;oCACP,QAAQ;oCACR,cAAc;oCACd,WAAW;gCACb;gCACA,mBAAmB;oCACjB,aAAa;gCACf;gCACA,MAAM,WAAW,MAAM;gCACvB,WAAW;oCACT,YAAY;oCACZ,OAAO;oCACP,SAAS;oCACT,YAAY;oCACZ,gBAAgB;oCAChB,QAAQ;oCACR,WAAW;oCACX,OAAO;oCACP,QAAQ;gCACV;gCACA,MACE,WAAW,WAAW,iBACpB,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAK,WAAW,WAAW;oCAC3B,KAAK,GAAG,WAAW,OAAO,CAAC,KAAK,CAAC;oCACjC,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;2DAGZ,6LAAC,+MAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;0CAIzB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAqB,WAAW,KAAK;;;;;;8DACnD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAA4B,WAAW,OAAO;;;;;;wDAC7D,WAAW,OAAO,kBACjB,6LAAC;4DACC,MAAM,WAAW,OAAO;4DACxB,QAAO;4DACP,KAAI;4DACJ,WAAU;sEAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAI9B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,6LAAC;oEAAK,WAAU;8EAAgB,WAAW,QAAQ;;;;;;;;;;;;sEAErD,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAAS,WAAW,IAAI;;;;;;;;;;;;;;;;;;sDAI/D,6LAAC;4CAAE,WAAU;sDAAyB,WAAW,WAAW;;;;;;sDAE5D,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,6LAAC;oDAAG,WAAU;8DACX,WAAW,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,iCACzC,6LAAC;4DAA0B,WAAU;;8EACnC,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;8EAAM;;;;;;;2DAFA;;;;;;;;;;;;;;;;sDAQf,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,6LAAC;oDAAI,WAAU;8DACZ,WAAW,YAAY,CAAC,GAAG,CAAC,CAAC,qBAC5B,6LAAC,oIAAA,CAAA,QAAK;4DAAY,SAAQ;4DAAY,WAAU;sEAC7C;2DADS;;;;;;;;;;;;;;;;;;;;;;+BAjFf,WAAW,EAAE;;;;;wBA0FxB;;;;;;;;;;;;;;;;;;;;;;AAMZ;GAnLgB;;QACQ,sKAAA,CAAA,YAAS;;;KADjB", "debugId": null}}, {"offset": {"line": 3105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/about/skills.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from \"@/components/ui/card\";\nimport { Progress } from \"@/components/ui/progress\";\n\nconst skillCategories = [\n  {\n    title: \"Frontend Development\",\n    skills: [\n      { name: \"React/Next.js\", level: 95, color: \"bg-blue-500\" },\n      { name: \"TypeScript\", level: 90, color: \"bg-blue-600\" },\n      { name: \"Tailwind CSS\", level: 92, color: \"bg-cyan-500\" },\n      { name: \"JavaScript (ES6+)\", level: 88, color: \"bg-yellow-500\" },\n      { name: \"HTML5 & CSS3\", level: 95, color: \"bg-orange-500\" },\n      { name: \"Framer Motion\", level: 85, color: \"bg-purple-500\" },\n    ]\n  },\n  {\n    title: \"Backend Development\",\n    skills: [\n      { name: \"Node.js\", level: 88, color: \"bg-green-600\" },\n      { name: \"Express.js\", level: 85, color: \"bg-gray-600\" },\n      { name: \"PostgreSQL\", level: 82, color: \"bg-blue-800\" },\n      { name: \"MongoDB\", level: 80, color: \"bg-green-500\" },\n      { name: \"Prisma ORM\", level: 85, color: \"bg-indigo-600\" },\n      { name: \"REST APIs\", level: 90, color: \"bg-teal-500\" },\n    ]\n  },\n  {\n    title: \"Tools & Technologies\",\n    skills: [\n      { name: \"Git & GitHub\", level: 92, color: \"bg-gray-800\" },\n      { name: \"Docker\", level: 75, color: \"bg-blue-500\" },\n      { name: \"AWS\", level: 70, color: \"bg-orange-500\" },\n      { name: \"Vercel\", level: 88, color: \"bg-black\" },\n      { name: \"Figma\", level: 85, color: \"bg-purple-500\" },\n      { name: \"VS Code\", level: 95, color: \"bg-blue-600\" },\n    ]\n  },\n  {\n    title: \"Soft Skills\",\n    skills: [\n      { name: \"Problem Solving\", level: 92, color: \"bg-emerald-500\" },\n      { name: \"Team Leadership\", level: 85, color: \"bg-rose-500\" },\n      { name: \"Communication\", level: 88, color: \"bg-amber-500\" },\n      { name: \"Project Management\", level: 80, color: \"bg-violet-500\" },\n      { name: \"Mentoring\", level: 82, color: \"bg-pink-500\" },\n      { name: \"Adaptability\", level: 90, color: \"bg-sky-500\" },\n    ]\n  }\n];\n\nexport function Skills() {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  return (\n    <section ref={ref} className=\"py-20 bg-muted/30\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Skills & Expertise</span>\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            A comprehensive overview of my technical skills and proficiency levels across different technologies and domains.\n          </p>\n        </motion.div>\n\n        <div className=\"grid md:grid-cols-2 gap-8\">\n          {skillCategories.map((category, categoryIndex) => (\n            <motion.div\n              key={category.title}\n              initial={{ opacity: 0, y: 50 }}\n              animate={inView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.8, delay: categoryIndex * 0.1 }}\n            >\n              <Card className=\"h-full hover-lift border-border/50 hover:border-border transition-all duration-300\">\n                <CardHeader>\n                  <CardTitle className=\"text-xl font-bold\">{category.title}</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-6\">\n                    {category.skills.map((skill, skillIndex) => (\n                      <motion.div\n                        key={skill.name}\n                        className=\"space-y-2\"\n                        initial={{ opacity: 0, x: -20 }}\n                        animate={inView ? { opacity: 1, x: 0 } : {}}\n                        transition={{ \n                          duration: 0.5, \n                          delay: categoryIndex * 0.1 + skillIndex * 0.05 \n                        }}\n                      >\n                        <div className=\"flex justify-between items-center\">\n                          <span className=\"font-medium text-sm\">{skill.name}</span>\n                          <span className=\"text-sm text-muted-foreground\">{skill.level}%</span>\n                        </div>\n                        <div className=\"relative\">\n                          <div className=\"w-full bg-muted rounded-full h-2\">\n                            <motion.div\n                              className={`h-2 rounded-full ${skill.color}`}\n                              initial={{ width: 0 }}\n                              animate={inView ? { width: `${skill.level}%` } : { width: 0 }}\n                              transition={{ \n                                duration: 1, \n                                delay: categoryIndex * 0.1 + skillIndex * 0.05 + 0.2,\n                                ease: \"easeOut\"\n                              }}\n                            />\n                          </div>\n                        </div>\n                      </motion.div>\n                    ))}\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Additional Skills Summary */}\n        <motion.div\n          className=\"mt-16 text-center\"\n          initial={{ opacity: 0, y: 30 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.6 }}\n        >\n          <div className=\"bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl p-8 border border-border/50\">\n            <h3 className=\"text-2xl font-bold mb-4\">Continuous Learning</h3>\n            <p className=\"text-muted-foreground mb-6 max-w-2xl mx-auto\">\n              I'm passionate about staying up-to-date with the latest technologies and best practices. \n              Currently exploring AI/ML integration in web applications and advanced React patterns.\n            </p>\n            <div className=\"flex flex-wrap justify-center gap-4\">\n              {[\"AI/ML\", \"Web3\", \"GraphQL\", \"Microservices\", \"DevOps\", \"Mobile Development\"].map((skill) => (\n                <span \n                  key={skill}\n                  className=\"px-3 py-1 bg-background border border-border rounded-full text-sm font-medium\"\n                >\n                  {skill}\n                </span>\n              ))}\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAOA,MAAM,kBAAkB;IACtB;QACE,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAiB,OAAO;gBAAI,OAAO;YAAc;YACzD;gBAAE,MAAM;gBAAc,OAAO;gBAAI,OAAO;YAAc;YACtD;gBAAE,MAAM;gBAAgB,OAAO;gBAAI,OAAO;YAAc;YACxD;gBAAE,MAAM;gBAAqB,OAAO;gBAAI,OAAO;YAAgB;YAC/D;gBAAE,MAAM;gBAAgB,OAAO;gBAAI,OAAO;YAAgB;YAC1D;gBAAE,MAAM;gBAAiB,OAAO;gBAAI,OAAO;YAAgB;SAC5D;IACH;IACA;QACE,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAW,OAAO;gBAAI,OAAO;YAAe;YACpD;gBAAE,MAAM;gBAAc,OAAO;gBAAI,OAAO;YAAc;YACtD;gBAAE,MAAM;gBAAc,OAAO;gBAAI,OAAO;YAAc;YACtD;gBAAE,MAAM;gBAAW,OAAO;gBAAI,OAAO;YAAe;YACpD;gBAAE,MAAM;gBAAc,OAAO;gBAAI,OAAO;YAAgB;YACxD;gBAAE,MAAM;gBAAa,OAAO;gBAAI,OAAO;YAAc;SACtD;IACH;IACA;QACE,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAgB,OAAO;gBAAI,OAAO;YAAc;YACxD;gBAAE,MAAM;gBAAU,OAAO;gBAAI,OAAO;YAAc;YAClD;gBAAE,MAAM;gBAAO,OAAO;gBAAI,OAAO;YAAgB;YACjD;gBAAE,MAAM;gBAAU,OAAO;gBAAI,OAAO;YAAW;YAC/C;gBAAE,MAAM;gBAAS,OAAO;gBAAI,OAAO;YAAgB;YACnD;gBAAE,MAAM;gBAAW,OAAO;gBAAI,OAAO;YAAc;SACpD;IACH;IACA;QACE,OAAO;QACP,QAAQ;YACN;gBAAE,MAAM;gBAAmB,OAAO;gBAAI,OAAO;YAAiB;YAC9D;gBAAE,MAAM;gBAAmB,OAAO;gBAAI,OAAO;YAAc;YAC3D;gBAAE,MAAM;gBAAiB,OAAO;gBAAI,OAAO;YAAe;YAC1D;gBAAE,MAAM;gBAAsB,OAAO;gBAAI,OAAO;YAAgB;YAChE;gBAAE,MAAM;gBAAa,OAAO;gBAAI,OAAO;YAAc;YACrD;gBAAE,MAAM;gBAAgB,OAAO;gBAAI,OAAO;YAAa;SACxD;IACH;CACD;AAEM,SAAS;;IACd,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,qBACE,6LAAC;QAAQ,KAAK;QAAK,WAAU;kBAC3B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,6LAAC;4BAAG,WAAU;sCACZ,cAAA,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,6LAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAKjE,6LAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC,UAAU,8BAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO,gBAAgB;4BAAI;sCAExD,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAqB,SAAS,KAAK;;;;;;;;;;;kDAE1D,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,2BAC3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAG;oDAC9B,SAAS,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE,IAAI,CAAC;oDAC1C,YAAY;wDACV,UAAU;wDACV,OAAO,gBAAgB,MAAM,aAAa;oDAC5C;;sEAEA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAuB,MAAM,IAAI;;;;;;8EACjD,6LAAC;oEAAK,WAAU;;wEAAiC,MAAM,KAAK;wEAAC;;;;;;;;;;;;;sEAE/D,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oEACT,WAAW,CAAC,iBAAiB,EAAE,MAAM,KAAK,EAAE;oEAC5C,SAAS;wEAAE,OAAO;oEAAE;oEACpB,SAAS,SAAS;wEAAE,OAAO,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC;oEAAC,IAAI;wEAAE,OAAO;oEAAE;oEAC5D,YAAY;wEACV,UAAU;wEACV,OAAO,gBAAgB,MAAM,aAAa,OAAO;wEACjD,MAAM;oEACR;;;;;;;;;;;;;;;;;mDAvBD,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;2BAbpB,SAAS,KAAK;;;;;;;;;;8BAkDzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAExC,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,6LAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAI5D,6LAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAS;oCAAQ;oCAAW;oCAAiB;oCAAU;iCAAqB,CAAC,GAAG,CAAC,CAAC,sBAClF,6LAAC;wCAEC,WAAU;kDAET;uCAHI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYvB;GAvGgB;;QACQ,sKAAA,CAAA,YAAS;;;KADjB", "debugId": null}}, {"offset": {"line": 3554, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/about/education.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { GraduationCap, Award, BookOpen, Calendar, School, Trophy } from \"lucide-react\";\nimport { VerticalTimeline, VerticalTimelineElement } from 'react-vertical-timeline-component';\nimport 'react-vertical-timeline-component/style.min.css';\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { api, getPublishedEducation, getPublishedCertifications, type Education, type Certification } from \"@/lib/api\";\nimport { useEffect, useState } from \"react\";\n\n// Fallback education data\nconst fallbackEducation = [\n  {\n    degree: \"Bachelor of Engineering in Computer Science\",\n    institution: \"Chandigarh University\",\n    location: \"Chandigarh, India\",\n    period: \"2020 - 2024\",\n    grade: \"First Class with Distinction (8.5/10 CGPA)\",\n    description: \"Focused on software engineering, data structures, algorithms, and web technologies. Completed final year project on e-commerce platform development.\",\n    highlights: [\n      \"Dean's List for 3 consecutive semesters\",\n      \"Led university coding club with 200+ members\",\n      \"Won inter-college hackathon for innovative web solution\",\n      \"Published research paper on web performance optimization\"\n    ],\n    icon: GraduationCap,\n    iconBg: \"#3b82f6\",\n    date: \"2016 - 2020\"\n  },\n  {\n    degree: \"Higher Secondary Certificate (Science)\",\n    institution: \"St. Xavier's College\",\n    location: \"Mumbai, India\",\n    period: \"2014 - 2016\",\n    grade: \"92.5%\",\n    description: \"Specialized in Mathematics, Physics, and Chemistry with additional focus on computer science fundamentals.\",\n    highlights: [\n      \"School topper in Computer Science\",\n      \"Represented school in state-level science exhibition\",\n      \"Active member of robotics club\"\n    ],\n    icon: School,\n    iconBg: \"#10b981\",\n    date: \"2014 - 2016\"\n  }\n];\n\nconst fallbackCertifications = [\n  {\n    title: \"AWS Certified Solutions Architect\",\n    issuer: \"Amazon Web Services\",\n    date: \"2023\",\n    credentialId: \"AWS-CSA-2023-001\",\n    emoji: \"☁️\",\n    icon: Award,\n    iconBg: \"#f59e0b\",\n    description: \"Comprehensive certification covering AWS architecture best practices, security, and scalability.\"\n  },\n  {\n    title: \"React Developer Certification\",\n    issuer: \"Meta (Facebook)\",\n    date: \"2022\",\n    credentialId: \"META-REACT-2022-456\",\n    emoji: \"⚛️\",\n    icon: Trophy,\n    iconBg: \"#3b82f6\",\n    description: \"Advanced React development patterns, hooks, and modern React ecosystem.\"\n  },\n  {\n    title: \"Google Analytics Certified\",\n    issuer: \"Google\",\n    date: \"2022\",\n    credentialId: \"GOOGLE-GA-2022-789\",\n    emoji: \"📊\",\n    icon: Award,\n    iconBg: \"#10b981\",\n    description: \"Digital analytics, data interpretation, and conversion optimization strategies.\"\n  },\n  {\n    title: \"MongoDB Developer Certification\",\n    issuer: \"MongoDB University\",\n    date: \"2021\",\n    credentialId: \"MONGO-DEV-2021-123\",\n    emoji: \"🍃\",\n    icon: Trophy,\n    iconBg: \"#8b5cf6\",\n    description: \"NoSQL database design, aggregation pipelines, and performance optimization.\"\n  }\n];\n\nconst courses = [\n  \"Advanced React Patterns - Kent C. Dodds\",\n  \"TypeScript Masterclass - Marius Schulz\",\n  \"Node.js Design Patterns - Mario Casciaro\",\n  \"System Design Interview - Alex Xu\",\n  \"AWS Solutions Architecture - A Cloud Guru\",\n  \"UI/UX Design Fundamentals - Google UX\"\n];\n\nexport function Education() {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const [education, setEducation] = useState<Education[]>([]);\n  const [certifications, setCertifications] = useState<Certification[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const [educationData, certificationsData] = await Promise.all([\n          api.getEducation(),\n          api.getCertifications()\n        ]);\n\n        setEducation(getPublishedEducation(educationData));\n        setCertifications(getPublishedCertifications(certificationsData));\n      } catch (error) {\n        console.error('Failed to fetch education data:', error);\n        // Use fallback data if API fails\n        setEducation(fallbackEducation.map((edu, index) => ({\n          id: `fallback-edu-${index}`,\n          degree: edu.degree,\n          institution: edu.institution,\n          location: edu.location,\n          period: edu.period,\n          grade: edu.grade,\n          description: edu.description,\n          highlights: edu.highlights,\n          order: index,\n          published: true,\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString(),\n        })));\n\n        setCertifications(fallbackCertifications.map((cert, index) => ({\n          id: `fallback-cert-${index}`,\n          title: cert.title,\n          issuer: cert.issuer,\n          date: cert.date,\n          credentialId: cert.credentialId,\n          emoji: cert.emoji,\n          description: cert.description,\n          order: index,\n          published: true,\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString(),\n        })));\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  if (loading) {\n    return (\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <div className=\"animate-pulse\">\n              <div className=\"h-8 bg-gray-300 rounded w-64 mx-auto mb-4\"></div>\n              <div className=\"h-4 bg-gray-300 rounded w-96 mx-auto\"></div>\n            </div>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section ref={ref} className=\"py-20\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Education & Learning</span>\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            My educational background and continuous learning journey in technology and software development.\n          </p>\n        </motion.div>\n\n        {/* Formal Education */}\n        <div className=\"mb-16\">\n          <motion.h3\n            className=\"text-2xl font-bold mb-8 flex items-center\"\n            initial={{ opacity: 0, x: -20 }}\n            animate={inView ? { opacity: 1, x: 0 } : {}}\n            transition={{ duration: 0.6, delay: 0.2 }}\n          >\n            <GraduationCap className=\"mr-3 h-6 w-6 text-primary\" />\n            Formal Education\n          </motion.h3>\n\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={inView ? { opacity: 1 } : {}}\n            transition={{ duration: 0.8, delay: 0.3 }}\n          >\n            <VerticalTimeline>\n              {education.map((edu) => {\n                return (\n                  <VerticalTimelineElement\n                    key={edu.id}\n                    className=\"vertical-timeline-element--education\"\n                    contentStyle={{\n                      background: 'hsl(var(--card))',\n                      color: 'hsl(var(--card-foreground))',\n                      border: '1px solid hsl(var(--border))',\n                      borderRadius: '12px',\n                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n                    }}\n                    contentArrowStyle={{\n                      borderRight: '7px solid hsl(var(--border))',\n                    }}\n                    date={edu.period}\n                    iconStyle={{\n                      background: '#3b82f6',\n                      color: '#fff',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      border: '3px solid hsl(var(--background))',\n                      boxShadow: '0 0 0 2px hsl(var(--border))',\n                      width: '60px',\n                      height: '60px',\n                    }}\n                    icon={<GraduationCap className=\"h-7 w-7\" />}\n                  >\n                    <div className=\"space-y-4\">\n                      <div>\n                        <h3 className=\"text-xl font-bold\">{edu.degree}</h3>\n                        <div className=\"text-primary font-medium\">{edu.institution}</div>\n                        <div className=\"text-sm text-muted-foreground\">{edu.location}</div>\n                        <Badge variant=\"secondary\" className=\"mt-2\">{edu.grade}</Badge>\n                      </div>\n\n                      <p className=\"text-muted-foreground\">{edu.description}</p>\n\n                      <div>\n                        <h4 className=\"font-semibold mb-2\">Key Highlights:</h4>\n                        <ul className=\"space-y-1\">\n                          {edu.highlights.map((highlight, highlightIndex) => (\n                            <li key={highlightIndex} className=\"flex items-start space-x-2 text-sm text-muted-foreground\">\n                              <div className=\"w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0\" />\n                              <span>{highlight}</span>\n                            </li>\n                          ))}\n                        </ul>\n                      </div>\n                    </div>\n                  </VerticalTimelineElement>\n                );\n              })}\n            </VerticalTimeline>\n          </motion.div>\n        </div>\n\n        {/* Certifications */}\n        <div className=\"mb-16\">\n          <motion.h3\n            className=\"text-2xl font-bold mb-8 flex items-center\"\n            initial={{ opacity: 0, x: -20 }}\n            animate={inView ? { opacity: 1, x: 0 } : {}}\n            transition={{ duration: 0.6, delay: 0.4 }}\n          >\n            <Award className=\"mr-3 h-6 w-6 text-primary\" />\n            Certifications\n          </motion.h3>\n\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={inView ? { opacity: 1 } : {}}\n            transition={{ duration: 0.8, delay: 0.5 }}\n          >\n            <VerticalTimeline>\n              {certifications.map((cert) => {\n                return (\n                  <VerticalTimelineElement\n                    key={cert.id}\n                    className=\"vertical-timeline-element--certification\"\n                    contentStyle={{\n                      background: 'hsl(var(--card))',\n                      color: 'hsl(var(--card-foreground))',\n                      border: '1px solid hsl(var(--border))',\n                      borderRadius: '12px',\n                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\n                    }}\n                    contentArrowStyle={{\n                      borderRight: '7px solid hsl(var(--border))',\n                    }}\n                    date={cert.date}\n                    iconStyle={{\n                      background: '#f59e0b',\n                      color: '#fff',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      border: '3px solid hsl(var(--background))',\n                      boxShadow: '0 0 0 2px hsl(var(--border))',\n                      width: '60px',\n                      height: '60px',\n                    }}\n                    icon={<Award className=\"h-6 w-6\" />}\n                  >\n                    <div className=\"space-y-3\">\n                      <div className=\"flex items-start space-x-3\">\n                        <div className=\"text-2xl\">{cert.emoji}</div>\n                        <div className=\"flex-1\">\n                          <h4 className=\"font-bold text-lg\">{cert.title}</h4>\n                          <p className=\"text-primary font-medium\">{cert.issuer}</p>\n                          <p className=\"text-sm text-muted-foreground mt-1\">{cert.description}</p>\n                          <p className=\"text-xs text-muted-foreground mt-2\">\n                            Credential ID: {cert.credentialId}\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                  </VerticalTimelineElement>\n                );\n              })}\n            </VerticalTimeline>\n          </motion.div>\n        </div>\n\n        {/* Continuous Learning */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.6, delay: 0.6 }}\n        >\n          <h3 className=\"text-2xl font-bold mb-8 flex items-center\">\n            <BookOpen className=\"mr-3 h-6 w-6 text-primary\" />\n            Continuous Learning\n          </h3>\n          \n          <Card className=\"border-border/50\">\n            <CardHeader>\n              <CardTitle>Recent Courses & Training</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid md:grid-cols-2 gap-4\">\n                {courses.map((course, index) => (\n                  <motion.div\n                    key={index}\n                    className=\"flex items-center space-x-3 p-3 rounded-lg bg-muted/50 hover:bg-muted transition-colors duration-200\"\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={inView ? { opacity: 1, x: 0 } : {}}\n                    transition={{ duration: 0.4, delay: 0.7 + index * 0.05 }}\n                  >\n                    <div className=\"w-2 h-2 rounded-full bg-primary flex-shrink-0\" />\n                    <span className=\"text-sm font-medium\">{course}</span>\n                  </motion.div>\n                ))}\n              </div>\n              \n              <div className=\"mt-6 p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg border border-border/50\">\n                <p className=\"text-sm text-muted-foreground\">\n                  <strong>Learning Philosophy:</strong> I believe in continuous learning and staying updated with the latest \n                  technologies. I dedicate at least 5 hours per week to learning new skills and exploring emerging trends \n                  in web development and software engineering.\n                </p>\n              </div>\n            </CardContent>\n          </Card>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAYA,0BAA0B;AAC1B,MAAM,oBAAoB;IACxB;QACE,QAAQ;QACR,aAAa;QACb,UAAU;QACV,QAAQ;QACR,OAAO;QACP,aAAa;QACb,YAAY;YACV;YACA;YACA;YACA;SACD;QACD,MAAM,2NAAA,CAAA,gBAAa;QACnB,QAAQ;QACR,MAAM;IACR;IACA;QACE,QAAQ;QACR,aAAa;QACb,UAAU;QACV,QAAQ;QACR,OAAO;QACP,aAAa;QACb,YAAY;YACV;YACA;YACA;SACD;QACD,MAAM,yMAAA,CAAA,SAAM;QACZ,QAAQ;QACR,MAAM;IACR;CACD;AAED,MAAM,yBAAyB;IAC7B;QACE,OAAO;QACP,QAAQ;QACR,MAAM;QACN,cAAc;QACd,OAAO;QACP,MAAM,uMAAA,CAAA,QAAK;QACX,QAAQ;QACR,aAAa;IACf;IACA;QACE,OAAO;QACP,QAAQ;QACR,MAAM;QACN,cAAc;QACd,OAAO;QACP,MAAM,yMAAA,CAAA,SAAM;QACZ,QAAQ;QACR,aAAa;IACf;IACA;QACE,OAAO;QACP,QAAQ;QACR,MAAM;QACN,cAAc;QACd,OAAO;QACP,MAAM,uMAAA,CAAA,QAAK;QACX,QAAQ;QACR,aAAa;IACf;IACA;QACE,OAAO;QACP,QAAQ;QACR,MAAM;QACN,cAAc;QACd,OAAO;QACP,MAAM,yMAAA,CAAA,SAAM;QACZ,QAAQ;QACR,aAAa;IACf;CACD;AAED,MAAM,UAAU;IACd;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAAS;;IACd,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC1D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;iDAAY;oBAChB,IAAI;wBACF,MAAM,CAAC,eAAe,mBAAmB,GAAG,MAAM,QAAQ,GAAG,CAAC;4BAC5D,oHAAA,CAAA,MAAG,CAAC,YAAY;4BAChB,oHAAA,CAAA,MAAG,CAAC,iBAAiB;yBACtB;wBAED,aAAa,CAAA,GAAA,oHAAA,CAAA,wBAAqB,AAAD,EAAE;wBACnC,kBAAkB,CAAA,GAAA,oHAAA,CAAA,6BAA0B,AAAD,EAAE;oBAC/C,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,mCAAmC;wBACjD,iCAAiC;wBACjC,aAAa,kBAAkB,GAAG;6DAAC,CAAC,KAAK,QAAU,CAAC;oCAClD,IAAI,CAAC,aAAa,EAAE,OAAO;oCAC3B,QAAQ,IAAI,MAAM;oCAClB,aAAa,IAAI,WAAW;oCAC5B,UAAU,IAAI,QAAQ;oCACtB,QAAQ,IAAI,MAAM;oCAClB,OAAO,IAAI,KAAK;oCAChB,aAAa,IAAI,WAAW;oCAC5B,YAAY,IAAI,UAAU;oCAC1B,OAAO;oCACP,WAAW;oCACX,WAAW,IAAI,OAAO,WAAW;oCACjC,WAAW,IAAI,OAAO,WAAW;gCACnC,CAAC;;wBAED,kBAAkB,uBAAuB,GAAG;6DAAC,CAAC,MAAM,QAAU,CAAC;oCAC7D,IAAI,CAAC,cAAc,EAAE,OAAO;oCAC5B,OAAO,KAAK,KAAK;oCACjB,QAAQ,KAAK,MAAM;oCACnB,MAAM,KAAK,IAAI;oCACf,cAAc,KAAK,YAAY;oCAC/B,OAAO,KAAK,KAAK;oCACjB,aAAa,KAAK,WAAW;oCAC7B,OAAO;oCACP,WAAW;oCACX,WAAW,IAAI,OAAO,WAAW;oCACjC,WAAW,IAAI,OAAO,WAAW;gCACnC,CAAC;;oBACH,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;8BAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,6LAAC;YAAQ,WAAU;sBACjB,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM3B;IAEA,qBACE,6LAAC;QAAQ,KAAK;QAAK,WAAU;kBAC3B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,6LAAC;4BAAG,WAAU;sCACZ,cAAA,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,6LAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAMjE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAExC,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAA8B;;;;;;;sCAIzD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS,SAAS;gCAAE,SAAS;4BAAE,IAAI,CAAC;4BACpC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,6LAAC,qLAAA,CAAA,mBAAgB;0CACd,UAAU,GAAG,CAAC,CAAC;oCACd,qBACE,6LAAC,qLAAA,CAAA,0BAAuB;wCAEtB,WAAU;wCACV,cAAc;4CACZ,YAAY;4CACZ,OAAO;4CACP,QAAQ;4CACR,cAAc;4CACd,WAAW;wCACb;wCACA,mBAAmB;4CACjB,aAAa;wCACf;wCACA,MAAM,IAAI,MAAM;wCAChB,WAAW;4CACT,YAAY;4CACZ,OAAO;4CACP,SAAS;4CACT,YAAY;4CACZ,gBAAgB;4CAChB,QAAQ;4CACR,WAAW;4CACX,OAAO;4CACP,QAAQ;wCACV;wCACA,oBAAM,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;kDAE/B,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAqB,IAAI,MAAM;;;;;;sEAC7C,6LAAC;4DAAI,WAAU;sEAA4B,IAAI,WAAW;;;;;;sEAC1D,6LAAC;4DAAI,WAAU;sEAAiC,IAAI,QAAQ;;;;;;sEAC5D,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;sEAAQ,IAAI,KAAK;;;;;;;;;;;;8DAGxD,6LAAC;oDAAE,WAAU;8DAAyB,IAAI,WAAW;;;;;;8DAErD,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAqB;;;;;;sEACnC,6LAAC;4DAAG,WAAU;sEACX,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,+BAC9B,6LAAC;oEAAwB,WAAU;;sFACjC,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;sFAAM;;;;;;;mEAFA;;;;;;;;;;;;;;;;;;;;;;uCAxCZ,IAAI,EAAE;;;;;gCAkDjB;;;;;;;;;;;;;;;;;8BAMN,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAExC,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAA8B;;;;;;;sCAIjD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS,SAAS;gCAAE,SAAS;4BAAE,IAAI,CAAC;4BACpC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,6LAAC,qLAAA,CAAA,mBAAgB;0CACd,eAAe,GAAG,CAAC,CAAC;oCACnB,qBACE,6LAAC,qLAAA,CAAA,0BAAuB;wCAEtB,WAAU;wCACV,cAAc;4CACZ,YAAY;4CACZ,OAAO;4CACP,QAAQ;4CACR,cAAc;4CACd,WAAW;wCACb;wCACA,mBAAmB;4CACjB,aAAa;wCACf;wCACA,MAAM,KAAK,IAAI;wCACf,WAAW;4CACT,YAAY;4CACZ,OAAO;4CACP,SAAS;4CACT,YAAY;4CACZ,gBAAgB;4CAChB,QAAQ;4CACR,WAAW;4CACX,OAAO;4CACP,QAAQ;wCACV;wCACA,oBAAM,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;kDAEvB,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAY,KAAK,KAAK;;;;;;kEACrC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAqB,KAAK,KAAK;;;;;;0EAC7C,6LAAC;gEAAE,WAAU;0EAA4B,KAAK,MAAM;;;;;;0EACpD,6LAAC;gEAAE,WAAU;0EAAsC,KAAK,WAAW;;;;;;0EACnE,6LAAC;gEAAE,WAAU;;oEAAqC;oEAChC,KAAK,YAAY;;;;;;;;;;;;;;;;;;;;;;;;uCAlCpC,KAAK,EAAE;;;;;gCAyClB;;;;;;;;;;;;;;;;;8BAMN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;;sCAExC,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAA8B;;;;;;;sCAIpD,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAG;oDAC9B,SAAS,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE,IAAI,CAAC;oDAC1C,YAAY;wDAAE,UAAU;wDAAK,OAAO,MAAM,QAAQ;oDAAK;;sEAEvD,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAK,WAAU;sEAAuB;;;;;;;mDAPlC;;;;;;;;;;sDAYX,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;kEAAO;;;;;;oDAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvD;GAtRgB;;QACQ,sKAAA,CAAA,YAAS;;;KADjB", "debugId": null}}]}