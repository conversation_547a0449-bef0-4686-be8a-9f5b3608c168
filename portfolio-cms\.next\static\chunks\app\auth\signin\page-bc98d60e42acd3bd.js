(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4680],{28956:(e,t,r)=>{Promise.resolve().then(r.bind(r,43738))},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var a=r(95155);r(12115);var n=r(99708),s=r(74466),i=r(59434);let o=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:r,size:s,asChild:d=!1,...l}=e,c=d?n.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:r,size:s,className:t})),...l})}},35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"notFound")&&r.d(t,{notFound:function(){return a.notFound}}),r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}})},40968:(e,t,r)=>{"use strict";r.d(t,{b:()=>o});var a=r(12115),n=r(63655),s=r(95155),i=a.forwardRef((e,t)=>(0,s.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=i},43738:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var a=r(95155),n=r(12115),s=r(12108),i=r(35695),o=r(30285),d=r(62523),l=r(85057),c=r(66695);function u(){let[e,t]=(0,n.useState)(""),[r,u]=(0,n.useState)(""),[f,v]=(0,n.useState)(!1),[p,m]=(0,n.useState)(""),g=(0,i.useRouter)(),x=async t=>{t.preventDefault(),v(!0),m("");try{let t=await (0,s.signIn)("credentials",{email:e,password:r,redirect:!1});(null==t?void 0:t.error)?m("Invalid credentials"):await (0,s.getSession)()&&g.push("/dashboard")}catch(e){m("An error occurred. Please try again.")}finally{v(!1)}};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,a.jsxs)(c.Zp,{className:"w-full max-w-md",children:[(0,a.jsxs)(c.aR,{className:"space-y-1",children:[(0,a.jsx)(c.ZB,{className:"text-2xl text-center",children:"Portfolio CMS"}),(0,a.jsx)(c.BT,{className:"text-center",children:"Sign in to access the content management system"})]}),(0,a.jsx)(c.Wu,{children:(0,a.jsxs)("form",{onSubmit:x,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(l.J,{htmlFor:"email",children:"Email"}),(0,a.jsx)(d.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:e,onChange:e=>t(e.target.value),required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(l.J,{htmlFor:"password",children:"Password"}),(0,a.jsx)(d.p,{id:"password",type:"password",value:r,onChange:e=>u(e.target.value),required:!0})]}),p&&(0,a.jsx)("div",{className:"text-red-500 text-sm text-center",children:p}),(0,a.jsx)(o.$,{type:"submit",className:"w-full",disabled:f,children:f?"Signing in...":"Sign In"})]})})]})})}},59434:(e,t,r)=>{"use strict";r.d(t,{KE:()=>f,_C:()=>u,cn:()=>l,z9:()=>c});var a=r(52596),n=r(39688),s=r(96262),i=r.n(s),o=r(60430),d=r.n(o);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,a.$)(t))}function c(e){return i()(e,{lower:!0,strict:!0,remove:/[*+~.()'"!:@]/g})}function u(e){return Math.ceil(d()(e).minutes)}function f(e){try{let t=JSON.parse(e);if("doc"===t.type&&t.content)return function e(t){let r="";for(let a of t)"text"===a.type?r+=a.text||"":a.content&&(r+=e(a.content)),["paragraph","heading","listItem"].includes(a.type)&&(r+=" ");return r.trim()}(t.content)}catch(e){}return e.replace(/<[^>]*>/g," ").replace(/\s+/g," ").trim()}},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>s});var a=r(95155);r(12115);var n=r(59434);function s(e){let{className:t,type:r,...s}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},63655:(e,t,r)=>{"use strict";r.d(t,{hO:()=>d,sG:()=>o});var a=r(12115),n=r(47650),s=r(99708),i=r(95155),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,s.TL)(`Primitive.${t}`),n=a.forwardRef((e,a)=>{let{asChild:n,...s}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(n?r:t,{...s,ref:a})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function d(e,t){e&&n.flushSync(()=>e.dispatchEvent(t))}},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>s,aR:()=>i});var a=r(95155);r(12115);var n=r(59434);function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...r})}},85057:(e,t,r)=>{"use strict";r.d(t,{J:()=>i});var a=r(95155);r(12115);var n=r(40968),s=r(59434);function i(e){let{className:t,...r}=e;return(0,a.jsx)(n.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4854,2108,8441,1684,7358],()=>t(28956)),_N_E=e.O()}]);