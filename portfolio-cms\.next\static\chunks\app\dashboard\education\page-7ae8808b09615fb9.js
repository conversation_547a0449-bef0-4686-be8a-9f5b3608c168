(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5840],{4516:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},13717:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},26126:(e,s,t)=>{"use strict";t.d(s,{E:()=>n});var a=t(95155);t(12115);var r=t(99708),i=t(74466),d=t(59434);let c=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:s,variant:t,asChild:i=!1,...n}=e,l=i?r.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,d.cn)(c({variant:t}),s),...n})}},50930:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var a=t(95155),r=t(12115),i=t(35695),d=t(83930),c=t(30285),n=t(66695),l=t(26126),o=t(84616),h=t(87949),u=t(4516),x=t(69074),m=t(13717),p=t(62525),v=t(56671);function y(){let e=(0,i.useRouter)(),[s,t]=(0,r.useState)([]),[y,f]=(0,r.useState)(!0);(0,r.useEffect)(()=>{g()},[]);let g=async()=>{try{let e=await fetch("/api/education");if(!e.ok)throw Error("Failed to fetch education");let s=await e.json();t(s)}catch(e){console.error("Error fetching education:",e),v.oR.error("Failed to fetch education")}finally{f(!1)}},j=async e=>{(0,v.oR)("Are you sure you want to delete this education record?",{action:{label:"Delete",onClick:async()=>{try{if(!(await fetch("/api/education/".concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete education");t(s.filter(s=>s.id!==e)),v.oR.success("Education record deleted successfully")}catch(e){console.error("Error deleting education:",e),v.oR.error("Failed to delete education")}}},cancel:{label:"Cancel",onClick:()=>{}}})},b=async(e,a)=>{try{if(!(await fetch("/api/education/".concat(e),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({published:!a})})).ok)throw Error("Failed to update education");t(s.map(s=>s.id===e?{...s,published:!a}:s)),v.oR.success("Education ".concat(a?"unpublished":"published"))}catch(e){console.error("Error updating education:",e),v.oR.error("Failed to update education")}};return y?(0,a.jsx)(d.DashboardLayout,{children:(0,a.jsx)("div",{className:"container mx-auto py-8",children:(0,a.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-300 rounded w-64"}),(0,a.jsx)("div",{className:"grid gap-4",children:[1,2].map(e=>(0,a.jsx)("div",{className:"h-48 bg-gray-300 rounded"},e))})]})})}):(0,a.jsx)(d.DashboardLayout,{children:(0,a.jsxs)("div",{className:"container mx-auto py-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Education"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage your educational background"})]}),(0,a.jsxs)(c.$,{onClick:()=>e.push("/dashboard/education/add"),children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Add Education"]})]}),(0,a.jsx)("div",{className:"grid gap-6",children:s.map(s=>(0,a.jsxs)(n.Zp,{className:"overflow-hidden",children:[(0,a.jsx)(n.aR,{className:"pb-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center",children:(0,a.jsx)(h.A,{className:"h-6 w-6 text-primary"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(n.ZB,{className:"text-xl",children:s.degree}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-muted-foreground mt-1",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-1"}),s.institution]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-1"}),s.location]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-1"}),s.period]})]}),s.grade&&(0,a.jsxs)("div",{className:"text-sm text-primary font-medium mt-1",children:["Grade: ",s.grade]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(l.E,{variant:s.published?"default":"secondary",children:s.published?"Published":"Draft"}),(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>b(s.id,s.published),children:s.published?"Unpublish":"Publish"}),(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>e.push("/dashboard/education/".concat(s.id)),children:(0,a.jsx)(m.A,{className:"h-4 w-4"})}),(0,a.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>j(s.id),children:(0,a.jsx)(p.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:s.description}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-2",children:"Key Highlights"}),(0,a.jsx)("ul",{className:"list-disc list-inside text-sm space-y-1",children:s.highlights.map((e,s)=>(0,a.jsx)("li",{children:e},s))})]})]})]},s.id))}),0===s.length&&(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"py-8 text-center",children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"No education records found. Add your first education record to get started."})})})]})})}},62525:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},69074:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},76526:(e,s,t)=>{Promise.resolve().then(t.bind(t,50930))},84616:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[4854,2108,8858,9368,6671,9008,8441,1684,7358],()=>s(76526)),_N_E=e.O()}]);