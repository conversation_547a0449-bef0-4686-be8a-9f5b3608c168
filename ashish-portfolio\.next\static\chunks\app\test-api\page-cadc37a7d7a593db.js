(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[990],{2659:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(5155),o=t(2115);function l(){let[e,s]=(0,o.useState)(null),[t,l]=(0,o.useState)(!0),[a,n]=(0,o.useState)(null);return((0,o.useEffect)(()=>{(async()=>{try{console.log("Testing API call to CMS...");let e=await fetch("http://localhost:3001/api/projects");if(console.log("Response status:",e.status),console.log("Response headers:",e.headers),!e.ok)throw Error("HTTP error! status: ".concat(e.status));let t=await e.json();console.log("API Response:",t),s(t)}catch(e){console.error("API Error:",e),n(e instanceof Error?e.message:"Unknown error")}finally{l(!1)}})()},[]),t)?(0,r.jsx)("div",{className:"p-8",children:"Loading..."}):a?(0,r.jsxs)("div",{className:"p-8 text-red-500",children:["Error: ",a]}):(0,r.jsxs)("div",{className:"p-8",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"API Test Results"}),(0,r.jsx)("pre",{className:"bg-gray-100 p-4 rounded overflow-auto",children:JSON.stringify(e,null,2)})]})}},2686:(e,s,t)=>{Promise.resolve().then(t.bind(t,2659))}},e=>{var s=s=>e(e.s=s);e.O(0,[441,684,358],()=>s(2686)),_N_E=e.O()}]);