"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7481],{5196:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},27213:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},29869:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},38715:(e,t,r)=>{r.d(t,{UC:()=>eM,In:()=>eP,q7:()=>eL,VF:()=>e_,p4:()=>eA,ZL:()=>eI,bL:()=>eE,wn:()=>eB,PP:()=>eH,l9:()=>eN,WT:()=>eT,LM:()=>eD});var n=r(12115),l=r(47650);function o(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(85185),i=r(37328),s=r(6101),d=r(46081),u=r(94315),c=r(19178),p=r(92293),f=r(25519),h=r(61285),v=r(35152),m=r(34378),w=r(63655),g=r(99708),x=r(39033),y=r(5845),b=r(52712),S=r(45503),C=r(95155),k=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});n.forwardRef((e,t)=>(0,C.jsx)(w.sG.span,{...e,ref:t,style:{...k,...e.style}})).displayName="VisuallyHidden";var j=r(38168),R=r(93795),E=[" ","Enter","ArrowUp","ArrowDown"],N=[" ","Enter"],T="Select",[P,I,M]=(0,i.N)(T),[D,L]=(0,d.A)(T,[M,v.Bk]),A=(0,v.Bk)(),[_,H]=D(T),[B,G]=D(T),V=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:d,dir:c,name:p,autoComplete:f,disabled:m,required:w,form:g}=e,x=A(t),[b,S]=n.useState(null),[k,j]=n.useState(null),[R,E]=n.useState(!1),N=(0,u.jH)(c),[I,M]=(0,y.i)({prop:l,defaultProp:null!=o&&o,onChange:a,caller:T}),[D,L]=(0,y.i)({prop:i,defaultProp:s,onChange:d,caller:T}),H=n.useRef(null),G=!b||g||!!b.closest("form"),[V,O]=n.useState(new Set),F=Array.from(V).map(e=>e.props.value).join(";");return(0,C.jsx)(v.bL,{...x,children:(0,C.jsxs)(_,{required:w,scope:t,trigger:b,onTriggerChange:S,valueNode:k,onValueNodeChange:j,valueNodeHasChildren:R,onValueNodeHasChildrenChange:E,contentId:(0,h.B)(),value:D,onValueChange:L,open:I,onOpenChange:M,dir:N,triggerPointerDownPosRef:H,disabled:m,children:[(0,C.jsx)(P.Provider,{scope:t,children:(0,C.jsx)(B,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{O(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{O(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),G?(0,C.jsxs)(eC,{"aria-hidden":!0,required:w,tabIndex:-1,name:p,autoComplete:f,value:D,onChange:e=>L(e.target.value),disabled:m,form:g,children:[void 0===D?(0,C.jsx)("option",{value:""}):null,Array.from(V)]},F):null]})})};V.displayName=T;var O="SelectTrigger",F=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=A(r),d=H(O,r),u=d.disabled||l,c=(0,s.s)(t,d.onTriggerChange),p=I(r),f=n.useRef("touch"),[h,m,g]=ej(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===d.value),n=eR(t,e,r);void 0!==n&&d.onValueChange(n.value)}),x=e=>{u||(d.onOpenChange(!0),g()),e&&(d.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,C.jsx)(v.Mz,{asChild:!0,...i,children:(0,C.jsx)(w.sG.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":ek(d.value)?"":void 0,...o,ref:c,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&x(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(x(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&E.includes(e.key)&&(x(),e.preventDefault())})})})});F.displayName=O;var K="SelectValue",W=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,d=H(K,r),{onValueNodeHasChildrenChange:u}=d,c=void 0!==o,p=(0,s.s)(t,d.onValueNodeChange);return(0,b.N)(()=>{u(c)},[u,c]),(0,C.jsx)(w.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:ek(d.value)?(0,C.jsx)(C.Fragment,{children:a}):o})});W.displayName=K;var U=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,C.jsx)(w.sG.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});U.displayName="SelectIcon";var q=e=>(0,C.jsx)(m.Z,{asChild:!0,...e});q.displayName="SelectPortal";var z="SelectContent",Z=n.forwardRef((e,t)=>{let r=H(z,e.__scopeSelect),[o,a]=n.useState();return((0,b.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,C.jsx)(Q,{...e,ref:t}):o?l.createPortal((0,C.jsx)(X,{scope:e.__scopeSelect,children:(0,C.jsx)(P.Slot,{scope:e.__scopeSelect,children:(0,C.jsx)("div",{children:e.children})})}),o):null});Z.displayName=z;var[X,Y]=D(z),J=(0,g.TL)("SelectContent.RemoveScroll"),Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:d,side:u,sideOffset:h,align:v,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:x,sticky:y,hideWhenDetached:b,avoidCollisions:S,...k}=e,E=H(z,r),[N,T]=n.useState(null),[P,M]=n.useState(null),D=(0,s.s)(t,e=>T(e)),[L,A]=n.useState(null),[_,B]=n.useState(null),G=I(r),[V,O]=n.useState(!1),F=n.useRef(!1);n.useEffect(()=>{if(N)return(0,j.Eq)(N)},[N]),(0,p.Oh)();let K=n.useCallback(e=>{let[t,...r]=G().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&P&&(P.scrollTop=0),r===n&&P&&(P.scrollTop=P.scrollHeight),null==r||r.focus(),document.activeElement!==l))return},[G,P]),W=n.useCallback(()=>K([L,N]),[K,L,N]);n.useEffect(()=>{V&&W()},[V,W]);let{onOpenChange:U,triggerPointerDownPosRef:q}=E;n.useEffect(()=>{if(N){let e={x:0,y:0},t=t=>{var r,n,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!=(l=null==(r=q.current)?void 0:r.x)?l:0)),y:Math.abs(Math.round(t.pageY)-(null!=(o=null==(n=q.current)?void 0:n.y)?o:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():N.contains(r.target)||U(!1),document.removeEventListener("pointermove",t),q.current=null};return null!==q.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[N,U,q]),n.useEffect(()=>{let e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);let[Z,Y]=ej(e=>{let t=G().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eR(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),Q=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==E.value&&E.value===t||n)&&(A(e),n&&(F.current=!0))},[E.value]),et=n.useCallback(()=>null==N?void 0:N.focus(),[N]),er=n.useCallback((e,t,r)=>{let n=!F.current&&!r;(void 0!==E.value&&E.value===t||n)&&B(e)},[E.value]),en="popper"===l?ee:$,el=en===ee?{side:u,sideOffset:h,align:v,alignOffset:m,arrowPadding:w,collisionBoundary:g,collisionPadding:x,sticky:y,hideWhenDetached:b,avoidCollisions:S}:{};return(0,C.jsx)(X,{scope:r,content:N,viewport:P,onViewportChange:M,itemRefCallback:Q,selectedItem:L,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:W,selectedItemText:_,position:l,isPositioned:V,searchRef:Z,children:(0,C.jsx)(R.A,{as:J,allowPinchZoom:!0,children:(0,C.jsx)(f.n,{asChild:!0,trapped:E.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{var t;null==(t=E.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,C.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>E.onOpenChange(!1),children:(0,C.jsx)(en,{role:"listbox",id:E.contentId,"data-state":E.open?"open":"closed",dir:E.dir,onContextMenu:e=>e.preventDefault(),...k,...el,onPlaced:()=>O(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...k.style},onKeyDown:(0,a.m)(k.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=G().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>K(t)),e.preventDefault()}})})})})})})});Q.displayName="SelectContentImpl";var $=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=H(z,r),d=Y(z,r),[u,c]=n.useState(null),[p,f]=n.useState(null),h=(0,s.s)(t,e=>f(e)),v=I(r),m=n.useRef(!1),g=n.useRef(!0),{viewport:x,selectedItem:y,selectedItemText:S,focusSelectedItem:k}=d,j=n.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&p&&x&&y&&S){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=S.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,s=e.width+i,d=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.left=c+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,s=e.width+i,d=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.right=c+"px"}let a=v(),s=window.innerHeight-20,d=x.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),h=parseInt(c.paddingTop,10),w=parseInt(c.borderBottomWidth,10),g=f+h+d+parseInt(c.paddingBottom,10)+w,b=Math.min(5*y.offsetHeight,g),C=window.getComputedStyle(x),k=parseInt(C.paddingTop,10),j=parseInt(C.paddingBottom,10),R=e.top+e.height/2-10,E=y.offsetHeight/2,N=f+h+(y.offsetTop+E);if(N<=R){let e=a.length>0&&y===a[a.length-1].ref.current;u.style.bottom="0px";let t=Math.max(s-R,E+(e?j:0)+(p.clientHeight-x.offsetTop-x.offsetHeight)+w);u.style.height=N+t+"px"}else{let e=a.length>0&&y===a[0].ref.current;u.style.top="0px";let t=Math.max(R,f+x.offsetTop+(e?k:0)+E);u.style.height=t+(g-N)+"px",x.scrollTop=N-R+x.offsetTop}u.style.margin="".concat(10,"px 0"),u.style.minHeight=b+"px",u.style.maxHeight=s+"px",null==l||l(),requestAnimationFrame(()=>m.current=!0)}},[v,i.trigger,i.valueNode,u,p,x,y,S,i.dir,l]);(0,b.N)(()=>j(),[j]);let[R,E]=n.useState();(0,b.N)(()=>{p&&E(window.getComputedStyle(p).zIndex)},[p]);let N=n.useCallback(e=>{e&&!0===g.current&&(j(),null==k||k(),g.current=!1)},[j,k]);return(0,C.jsx)(et,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:m,onScrollButtonChange:N,children:(0,C.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,C.jsx)(w.sG.div,{...a,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});$.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=A(r);return(0,C.jsx)(v.UC,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=D(z,{}),en="SelectViewport",el=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=Y(en,r),d=er(en,r),u=(0,s.s)(t,i.onViewportChange),c=n.useRef(0);return(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,C.jsx)(P.Slot,{scope:r,children:(0,C.jsx)(w.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=d;if((null==n?void 0:n.current)&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});el.displayName=en;var eo="SelectGroup",[ea,ei]=D(eo);n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,h.B)();return(0,C.jsx)(ea,{scope:r,id:l,children:(0,C.jsx)(w.sG.div,{role:"group","aria-labelledby":l,...n,ref:t})})}).displayName=eo;var es="SelectLabel";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=ei(es,r);return(0,C.jsx)(w.sG.div,{id:l.id,...n,ref:t})}).displayName=es;var ed="SelectItem",[eu,ec]=D(ed),ep=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...d}=e,u=H(ed,r),c=Y(ed,r),p=u.value===l,[f,v]=n.useState(null!=i?i:""),[m,g]=n.useState(!1),x=(0,s.s)(t,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,l,o)}),y=(0,h.B)(),b=n.useRef("touch"),S=()=>{o||(u.onValueChange(l),u.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,C.jsx)(eu,{scope:r,value:l,disabled:o,textId:y,isSelected:p,onItemTextChange:n.useCallback(e=>{v(t=>{var r;return t||(null!=(r=null==e?void 0:e.textContent)?r:"").trim()})},[]),children:(0,C.jsx)(P.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,C.jsx)(w.sG.div,{role:"option","aria-labelledby":y,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...d,ref:x,onFocus:(0,a.m)(d.onFocus,()=>g(!0)),onBlur:(0,a.m)(d.onBlur,()=>g(!1)),onClick:(0,a.m)(d.onClick,()=>{"mouse"!==b.current&&S()}),onPointerUp:(0,a.m)(d.onPointerUp,()=>{"mouse"===b.current&&S()}),onPointerDown:(0,a.m)(d.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.m)(d.onPointerMove,e=>{if(b.current=e.pointerType,o){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(d.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:(0,a.m)(d.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(N.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});ep.displayName=ed;var ef="SelectItemText",eh=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,d=H(ef,r),u=Y(ef,r),c=ec(ef,r),p=G(ef,r),[f,h]=n.useState(null),v=(0,s.s)(t,e=>h(e),c.onItemTextChange,e=>{var t;return null==(t=u.itemTextRefCallback)?void 0:t.call(u,e,c.value,c.disabled)}),m=null==f?void 0:f.textContent,g=n.useMemo(()=>(0,C.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:x,onNativeOptionRemove:y}=p;return(0,b.N)(()=>(x(g),()=>y(g)),[x,y,g]),(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)(w.sG.span,{id:c.textId,...i,ref:v}),c.isSelected&&d.valueNode&&!d.valueNodeHasChildren?l.createPortal(i.children,d.valueNode):null]})});eh.displayName=ef;var ev="SelectItemIndicator",em=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ec(ev,r).isSelected?(0,C.jsx)(w.sG.span,{"aria-hidden":!0,...n,ref:t}):null});em.displayName=ev;var ew="SelectScrollUpButton",eg=n.forwardRef((e,t)=>{let r=Y(ew,e.__scopeSelect),l=er(ew,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,C.jsx)(eb,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eg.displayName=ew;var ex="SelectScrollDownButton",ey=n.forwardRef((e,t)=>{let r=Y(ex,e.__scopeSelect),l=er(ex,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,C.jsx)(eb,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ey.displayName=ex;var eb=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=Y("SelectScrollButton",r),s=n.useRef(null),d=I(r),u=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>u(),[u]),(0,b.N)(()=>{var e;let t=d().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[d]),(0,C.jsx)(w.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{var e;null==(e=i.onItemLeave)||e.call(i),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{u()})})});n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,C.jsx)(w.sG.div,{"aria-hidden":!0,...n,ref:t})}).displayName="SelectSeparator";var eS="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=A(r),o=H(eS,r),a=Y(eS,r);return o.open&&"popper"===a.position?(0,C.jsx)(v.i3,{...l,...n,ref:t}):null}).displayName=eS;var eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,...o}=e,a=n.useRef(null),i=(0,s.s)(t,a),d=(0,S.Z)(l);return n.useEffect(()=>{let e=a.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(d!==l&&t){let r=new Event("change",{bubbles:!0});t.call(e,l),e.dispatchEvent(r)}},[d,l]),(0,C.jsx)(w.sG.select,{...o,style:{...k,...o.style},ref:i,defaultValue:l})});function ek(e){return""===e||void 0===e}function ej(e){let t=(0,x.c)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function eR(e,t,r){var n,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(n=e,l=Math.max(a,0),n.map((e,t)=>n[(l+t)%n.length]));1===o.length&&(i=i.filter(e=>e!==r));let s=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}eC.displayName="SelectBubbleInput";var eE=V,eN=F,eT=W,eP=U,eI=q,eM=Z,eD=el,eL=ep,eA=eh,e_=em,eH=eg,eB=ey},40968:(e,t,r)=>{r.d(t,{b:()=>i});var n=r(12115),l=r(63655),o=r(95155),a=n.forwardRef((e,t)=>(0,o.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var i=a},45503:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(12115);function l(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},47863:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},54416:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},66474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},76981:(e,t,r)=>{r.d(t,{C1:()=>C,bL:()=>b});var n=r(12115),l=r(6101),o=r(46081),a=r(85185),i=r(5845),s=r(45503),d=r(11275),u=r(28905),c=r(63655),p=r(95155),f="Checkbox",[h,v]=(0,o.A)(f),[m,w]=h(f);function g(e){let{__scopeCheckbox:t,checked:r,children:l,defaultChecked:o,disabled:a,form:s,name:d,onCheckedChange:u,required:c,value:h="on",internal_do_not_use_render:v}=e,[w,g]=(0,i.i)({prop:r,defaultProp:null!=o&&o,onChange:u,caller:f}),[x,y]=n.useState(null),[b,S]=n.useState(null),C=n.useRef(!1),k=!x||!!s||!!x.closest("form"),j={checked:w,disabled:a,setChecked:g,control:x,setControl:y,name:d,form:s,value:h,hasConsumerStoppedPropagationRef:C,required:c,defaultChecked:!R(o)&&o,isFormControl:k,bubbleInput:b,setBubbleInput:S};return(0,p.jsx)(m,{scope:t,...j,children:"function"==typeof v?v(j):l})}var x="CheckboxTrigger",y=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,onKeyDown:o,onClick:i,...s}=e,{control:d,value:u,disabled:f,checked:h,required:v,setControl:m,setChecked:g,hasConsumerStoppedPropagationRef:y,isFormControl:b,bubbleInput:S}=w(x,r),C=(0,l.s)(t,m),k=n.useRef(h);return n.useEffect(()=>{let e=null==d?void 0:d.form;if(e){let t=()=>g(k.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[d,g]),(0,p.jsx)(c.sG.button,{type:"button",role:"checkbox","aria-checked":R(h)?"mixed":h,"aria-required":v,"data-state":E(h),"data-disabled":f?"":void 0,disabled:f,value:u,...s,ref:C,onKeyDown:(0,a.m)(o,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,a.m)(i,e=>{g(e=>!!R(e)||!e),S&&b&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})})});y.displayName=x;var b=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:l,defaultChecked:o,required:a,disabled:i,value:s,onCheckedChange:d,form:u,...c}=e;return(0,p.jsx)(g,{__scopeCheckbox:r,checked:l,defaultChecked:o,disabled:i,required:a,onCheckedChange:d,name:n,form:u,value:s,internal_do_not_use_render:e=>{let{isFormControl:n}=e;return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(y,{...c,ref:t,__scopeCheckbox:r}),n&&(0,p.jsx)(j,{__scopeCheckbox:r})]})}})});b.displayName=f;var S="CheckboxIndicator",C=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...l}=e,o=w(S,r);return(0,p.jsx)(u.C,{present:n||R(o.checked)||!0===o.checked,children:(0,p.jsx)(c.sG.span,{"data-state":E(o.checked),"data-disabled":o.disabled?"":void 0,...l,ref:t,style:{pointerEvents:"none",...e.style}})})});C.displayName=S;var k="CheckboxBubbleInput",j=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,...o}=e,{control:a,hasConsumerStoppedPropagationRef:i,checked:u,defaultChecked:f,required:h,disabled:v,name:m,value:g,form:x,bubbleInput:y,setBubbleInput:b}=w(k,r),S=(0,l.s)(t,b),C=(0,s.Z)(u),j=(0,d.X)(a);n.useEffect(()=>{if(!y)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!i.current;if(C!==u&&e){let r=new Event("click",{bubbles:t});y.indeterminate=R(u),e.call(y,!R(u)&&u),y.dispatchEvent(r)}},[y,C,u,i]);let E=n.useRef(!R(u)&&u);return(0,p.jsx)(c.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=f?f:E.current,required:h,disabled:v,name:m,value:g,form:x,...o,tabIndex:-1,ref:S,style:{...o.style,...j,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function R(e){return"indeterminate"===e}function E(e){return R(e)?"indeterminate":e?"checked":"unchecked"}j.displayName=k}}]);