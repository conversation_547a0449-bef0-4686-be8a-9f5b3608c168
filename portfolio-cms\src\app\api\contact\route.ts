import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { withCors } from '@/lib/cors'
import * as z from 'zod'

const contactSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  subject: z.string().min(5, "Subject must be at least 5 characters"),
  message: z.string().min(10, "Message must be at least 10 characters"),
})

export async function POST(request: NextRequest) {
  try {
    const origin = request.headers.get('origin')
    const body = await request.json()

    // Validate the request body
    const validationResult = contactSchema.safeParse(body)
    if (!validationResult.success) {
      return withCors(NextResponse.json(
        { 
          error: 'Validation failed', 
          details: validationResult.error.errors 
        },
        { status: 400 }
      ), origin)
    }

    const { name, email, subject, message } = validationResult.data

    // Save to database
    const contact = await prisma.contact.create({
      data: {
        name,
        email,
        subject,
        message,
      },
    })

    // Here you could also send an email notification to yourself
    // using a service like Resend, SendGrid, or Nodemailer

    return withCors(NextResponse.json(
      { 
        success: true, 
        message: 'Message sent successfully!',
        id: contact.id 
      },
      { status: 201 }
    ), origin)
  } catch (error) {
    console.error('Error processing contact form:', error)
    const origin = request.headers.get('origin')
    return withCors(NextResponse.json(
      { error: 'Failed to send message. Please try again.' },
      { status: 500 }
    ), origin)
  }
}

export async function GET(request: NextRequest) {
  try {
    const origin = request.headers.get('origin')

    // This endpoint could be used by the admin to view contact submissions
    const contacts = await prisma.contact.findMany({
      orderBy: {
        createdAt: 'desc',
      },
    })

    return withCors(NextResponse.json(contacts), origin)
  } catch (error) {
    console.error('Error fetching contacts:', error)
    const origin = request.headers.get('origin')
    return withCors(NextResponse.json(
      { error: 'Failed to fetch contacts' },
      { status: 500 }
    ), origin)
  }
}

export async function OPTIONS(request: NextRequest) {
  const origin = request.headers.get('origin')
  return withCors(new NextResponse(null, { status: 200 }), origin)
}
