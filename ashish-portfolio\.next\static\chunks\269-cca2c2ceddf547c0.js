"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[269],{285:(e,t,a)=>{a.d(t,{$:()=>d});var s=a(5155);a(2115);var r=a(9708),n=a(2085),i=a(9434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:a,size:n,asChild:d=!1,...c}=e,l=d?r.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,i.cn)(o({variant:a,size:n,className:t})),...c})}},2523:(e,t,a)=>{a.d(t,{p:()=>n});var s=a(5155);a(2115);var r=a(9434);function n(e){let{className:t,type:a,...n}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},3317:(e,t,a)=>{a.d(t,{Footer:()=>v});var s=a(5155),r=a(6874),n=a.n(r),i=a(6408),o=a(9099),d=a(2894),c=a(8175),l=a(8883),m=a(1976),u=a(7312),h=a(9621),x=a(9881),p=a(285);let g={navigation:[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Projects",href:"/projects"},{name:"Blog",href:"/blog"},{name:"Contact",href:"/contact"}],services:[{name:"Full Stack Development",href:"/#services"},{name:"UI/UX Design",href:"/#services"},{name:"Mobile Development",href:"/#services"},{name:"Consulting",href:"/#services"}],resources:[{name:"Blog",href:"/blog"},{name:"Projects",href:"/projects"},{name:"Tech Stack",href:"/#tech-stack"},{name:"Contact",href:"/contact"}]},f=[{name:"GitHub",href:"https://github.com/ash-333",icon:o.A,color:"hover:text-gray-600 dark:hover:text-gray-300"},{name:"LinkedIn",href:"https://www.linkedin.com/in/ashishkamat0/",icon:d.A,color:"hover:text-blue-600"},{name:"Twitter",href:"https://twitter.com/ashishkamat4",icon:c.A,color:"hover:text-blue-500"},{name:"Email",href:"mailto:<EMAIL>",icon:l.A,color:"hover:text-green-600"}];function v(){return(0,s.jsx)("footer",{className:"bg-background border-t border-border",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsx)("div",{className:"py-12 lg:py-16",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8",children:[(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5},viewport:{once:!0},children:[(0,s.jsx)(n(),{href:"/",className:"text-2xl font-bold gradient-text-blue mb-4 inline-block",children:"Ashish Kamat"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-6 max-w-md",children:"Full Stack Developer & UI/UX Designer passionate about creating innovative digital experiences that make a difference."}),(0,s.jsx)("div",{className:"flex space-x-4",children:f.map((e,t)=>{let a=e.icon;return(0,s.jsx)(i.P.a,{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"p-2 rounded-lg bg-muted hover:bg-accent transition-all duration-300 ".concat(e.color),whileHover:{scale:1.1},whileTap:{scale:.9},initial:{opacity:0,scale:0},whileInView:{opacity:1,scale:1},transition:{duration:.3,delay:.1*t},viewport:{once:!0},children:(0,s.jsx)(a,{className:"h-5 w-5"})},e.name)})})]})}),(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1},viewport:{once:!0},children:[(0,s.jsx)("h3",{className:"font-semibold text-foreground mb-4",children:"Navigation"}),(0,s.jsx)("ul",{className:"space-y-3",children:g.navigation.map(e=>(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:e.href,className:"text-muted-foreground hover:text-foreground transition-colors duration-200",children:e.name})},e.name))})]}),(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.2},viewport:{once:!0},children:[(0,s.jsx)("h3",{className:"font-semibold text-foreground mb-4",children:"Services"}),(0,s.jsx)("ul",{className:"space-y-3",children:g.services.map(e=>(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:e.href,className:"text-muted-foreground hover:text-foreground transition-colors duration-200",children:e.name})},e.name))})]}),(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.3},viewport:{once:!0},children:[(0,s.jsx)("h3",{className:"font-semibold text-foreground mb-4",children:"Resources"}),(0,s.jsx)("ul",{className:"space-y-3",children:g.resources.map(e=>(0,s.jsx)("li",{children:(0,s.jsx)(n(),{href:e.href,className:"text-muted-foreground hover:text-foreground transition-colors duration-200",children:e.name})},e.name))})]})]})}),(0,s.jsx)(i.P.div,{className:"py-6 border-t border-border",initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.5,delay:.4},viewport:{once:!0},children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-muted-foreground",children:[(0,s.jsx)("span",{children:"\xa9 2024 Ashish Kamat. Made with"}),(0,s.jsx)(m.A,{className:"h-4 w-4 text-red-500 animate-pulse"}),(0,s.jsx)("span",{children:"and"}),(0,s.jsx)(u.A,{className:"h-4 w-4 text-amber-600"}),(0,s.jsx)("span",{children:"in Kathmandu"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-muted-foreground",children:[(0,s.jsx)(h.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Built with Next.js & Tailwind CSS"})]}),(0,s.jsx)(p.$,{variant:"outline",size:"icon",onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},className:"rounded-full hover:scale-110 transition-transform duration-200",children:(0,s.jsx)(x.A,{className:"h-4 w-4"})})]})]})}),(0,s.jsx)(i.P.div,{className:"text-center py-4 border-t border-border/50",initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.5,delay:.6},viewport:{once:!0},children:(0,s.jsxs)("p",{className:"text-xs text-muted-foreground/70",children:["\uD83D\uDE80 This website is powered by"," ",(0,s.jsxs)("span",{className:"font-mono bg-muted px-1 py-0.5 rounded text-xs",children:[Math.floor(100*Math.random()),"% coffee"]})," ","and"," ",(0,s.jsxs)("span",{className:"font-mono bg-muted px-1 py-0.5 rounded text-xs",children:[Math.floor(100*Math.random()),"% passion"]})]})})]})})}},6126:(e,t,a)=>{a.d(t,{E:()=>d});var s=a(5155);a(2115);var r=a(9708),n=a(2085),i=a(9434);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:a,asChild:n=!1,...d}=e,c=n?r.DX:"span";return(0,s.jsx)(c,{"data-slot":"badge",className:(0,i.cn)(o({variant:a}),t),...d})}},6695:(e,t,a)=>{a.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>i});var s=a(5155);a(2115);var r=a(9434);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}},8211:(e,t,a)=>{a.d(t,{Navigation:()=>B});var s=a(5155),r=a(2115),n=a(6874),i=a.n(n),o=a(1362),d=a(6408),c=a(760),l=a(7340),m=a(1007),u=a(7576),h=a(7434),x=a(8883),p=a(7924),g=a(2098),f=a(3509),v=a(4416),b=a(4783),w=a(285),j=a(5695),y=a(9099),N=a(2894),k=a(8175),A=a(3786),C=a(518),E=a(9434);function S(e){let{...t}=e;return(0,s.jsx)(C.bL,{"data-slot":"dialog",...t})}function P(e){let{...t}=e;return(0,s.jsx)(C.ZL,{"data-slot":"dialog-portal",...t})}function D(e){let{className:t,...a}=e;return(0,s.jsx)(C.hJ,{"data-slot":"dialog-overlay",className:(0,E.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function _(e){let{className:t,children:a,showCloseButton:r=!0,...n}=e;return(0,s.jsxs)(P,{"data-slot":"dialog-portal",children:[(0,s.jsx)(D,{}),(0,s.jsxs)(C.UC,{"data-slot":"dialog-content",className:(0,E.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...n,children:[a,r&&(0,s.jsxs)(C.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(v.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function z(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,E.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function L(e){let{className:t,...a}=e;return(0,s.jsx)(C.hE,{"data-slot":"dialog-title",className:(0,E.cn)("text-lg leading-none font-semibold",t),...a})}var T=a(2523),I=a(6126);function H(e){let{open:t,onOpenChange:a}=e,[n,i]=(0,r.useState)(""),[o,c]=(0,r.useState)(0),g=(0,j.useRouter)(),f=[{id:"home",title:"Home",description:"Go to homepage",icon:l.A,action:()=>g.push("/"),category:"Navigation",keywords:["home","main","landing"]},{id:"about",title:"About",description:"Learn more about me",icon:m.A,action:()=>g.push("/about"),category:"Navigation",keywords:["about","bio","experience","skills"]},{id:"projects",title:"Projects",description:"View my work and projects",icon:u.A,action:()=>g.push("/projects"),category:"Navigation",keywords:["projects","work","portfolio","showcase"]},{id:"blog",title:"Blog",description:"Read my latest articles",icon:h.A,action:()=>g.push("/blog"),category:"Navigation",keywords:["blog","articles","writing","posts"]},{id:"contact",title:"Contact",description:"Get in touch with me",icon:x.A,action:()=>g.push("/contact"),category:"Navigation",keywords:["contact","email","message","hire"]},{id:"github",title:"GitHub",description:"View my GitHub profile",icon:y.A,action:()=>window.open("https://github.com/ashishkamat","_blank"),category:"Social",keywords:["github","code","repositories","open source"]},{id:"linkedin",title:"LinkedIn",description:"Connect with me on LinkedIn",icon:N.A,action:()=>window.open("https://linkedin.com/in/ashishkamat","_blank"),category:"Social",keywords:["linkedin","professional","network","career"]},{id:"twitter",title:"Twitter",description:"Follow me on Twitter",icon:k.A,action:()=>window.open("https://twitter.com/ashishkamat","_blank"),category:"Social",keywords:["twitter","social","updates","thoughts"]},{id:"email",title:"Send Email",description:"Send me an email directly",icon:x.A,action:()=>window.open("mailto:<EMAIL>","_blank"),category:"Quick Actions",keywords:["email","contact","message","hire"]},{id:"resume",title:"Download Resume",description:"Download my latest resume",icon:A.A,action:()=>window.open("/resume.pdf","_blank"),category:"Quick Actions",keywords:["resume","cv","download","hire"]}].filter(e=>{let t=n.toLowerCase();return e.title.toLowerCase().includes(t)||e.description.toLowerCase().includes(t)||e.keywords.some(e=>e.includes(t))}),v=f.reduce((e,t)=>(e[t.category]||(e[t.category]=[]),e[t.category].push(t),e),{});(0,r.useEffect)(()=>{c(0)},[n]),(0,r.useEffect)(()=>{let e=e=>{t&&("ArrowDown"===e.key?(e.preventDefault(),c(e=>e<f.length-1?e+1:0)):"ArrowUp"===e.key?(e.preventDefault(),c(e=>e>0?e-1:f.length-1)):"Enter"===e.key&&(e.preventDefault(),f[o]&&(f[o].action(),a(!1),i(""))))};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[t,o,f,a]);let b=e=>{e.action(),a(!1),i("")};return(0,s.jsx)(S,{open:t,onOpenChange:a,children:(0,s.jsxs)(_,{className:"max-w-2xl p-0 overflow-hidden",children:[(0,s.jsxs)(z,{className:"p-4 pb-0",children:[(0,s.jsx)(L,{className:"sr-only",children:"Command Palette"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(T.p,{placeholder:"Type a command or search...",value:n,onChange:e=>i(e.target.value),className:"pl-10 border-0 focus-visible:ring-0 text-base",autoFocus:!0})]})]}),(0,s.jsx)("div",{className:"max-h-96 overflow-y-auto p-4 pt-0",children:0===Object.keys(v).length?(0,s.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:['No commands found for "',n,'"']}):(0,s.jsx)("div",{className:"space-y-4",children:Object.entries(v).map(e=>{let[t,a]=e;return(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wider mb-2 px-2",children:t}),(0,s.jsx)("div",{className:"space-y-1",children:a.map((e,t)=>{let a=f.indexOf(e),r=e.icon;return(0,s.jsxs)(d.P.button,{onClick:()=>b(e),className:"w-full text-left p-3 rounded-lg transition-colors duration-200 flex items-center space-x-3 ".concat(a===o?"bg-accent text-accent-foreground":"hover:bg-accent/50"),whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,s.jsx)("div",{className:"p-2 rounded-md ".concat(a===o?"bg-primary text-primary-foreground":"bg-muted"),children:(0,s.jsx)(r,{className:"h-4 w-4"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("div",{className:"font-medium",children:e.title}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground truncate",children:e.description})]}),"Social"===e.category&&(0,s.jsx)(A.A,{className:"h-3 w-3 text-muted-foreground"})]},e.id)})})]},t)})})}),(0,s.jsxs)("div",{className:"border-t p-3 text-xs text-muted-foreground flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)(I.E,{variant:"outline",className:"text-xs px-1.5 py-0.5",children:"↑↓"}),(0,s.jsx)("span",{children:"Navigate"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)(I.E,{variant:"outline",className:"text-xs px-1.5 py-0.5",children:"↵"}),(0,s.jsx)("span",{children:"Select"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)(I.E,{variant:"outline",className:"text-xs px-1.5 py-0.5",children:"Esc"}),(0,s.jsx)("span",{children:"Close"})]})]}),(0,s.jsxs)("div",{className:"text-muted-foreground/60",children:[f.length," result",1!==f.length?"s":""]})]})]})})}let V=[{name:"Home",href:"/",icon:l.A},{name:"About",href:"/about",icon:m.A},{name:"Projects",href:"/projects",icon:u.A},{name:"Blog",href:"/blog",icon:h.A},{name:"Contact",href:"/contact",icon:x.A}];function B(){let[e,t]=(0,r.useState)(!1),[a,n]=(0,r.useState)(!1),[l,m]=(0,r.useState)(!1),{theme:u,setTheme:h}=(0,o.D)(),[x,j]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{j(!0)},[]),(0,r.useEffect)(()=>{let e=()=>{n(window.scrollY>50)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,r.useEffect)(()=>{let e=e=>{(e.metaKey||e.ctrlKey)&&"k"===e.key&&(e.preventDefault(),m(!0))};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[]),x)?(0,s.jsxs)(d.P.header,{className:(0,E.cn)("fixed top-0 left-0 right-0 z-50 transition-all duration-300",a?"bg-background/80 backdrop-blur-md border-b border-border":"bg-transparent"),initial:{y:-100},animate:{y:0},transition:{duration:.5},children:[(0,s.jsxs)("nav",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,s.jsx)(d.P.div,{className:"flex-shrink-0",whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,s.jsx)(i(),{href:"/",className:"text-2xl font-bold gradient-text-blue",children:"AK"})}),(0,s.jsx)("div",{className:"hidden md:block",children:(0,s.jsx)("div",{className:"ml-10 flex items-baseline space-x-4",children:V.map(e=>(0,s.jsx)(d.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,s.jsx)(i(),{href:e.href,className:"text-foreground/80 hover:text-foreground px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 hover:bg-accent",children:e.name})},e.name))})}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)(w.$,{variant:"ghost",size:"sm",onClick:()=>m(!0),className:"hidden md:flex items-center space-x-2 text-muted-foreground hover:text-foreground",children:[(0,s.jsx)(p.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"text-sm",children:"Search"}),(0,s.jsxs)("kbd",{className:"pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100",children:[(0,s.jsx)("span",{className:"text-xs",children:"⌘"}),"K"]})]}),(0,s.jsx)(w.$,{variant:"ghost",size:"icon",onClick:()=>{h("dark"===u?"light":"dark")},className:"w-9 h-9",children:"dark"===u?(0,s.jsx)(g.A,{className:"h-4 w-4"}):(0,s.jsx)(f.A,{className:"h-4 w-4"})}),(0,s.jsx)("div",{className:"md:hidden",children:(0,s.jsx)(w.$,{variant:"ghost",size:"icon",onClick:()=>t(!e),className:"w-9 h-9",children:e?(0,s.jsx)(v.A,{className:"h-4 w-4"}):(0,s.jsx)(b.A,{className:"h-4 w-4"})})})]})]}),(0,s.jsx)(c.N,{children:e&&(0,s.jsx)(d.P.div,{className:"md:hidden",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:(0,s.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-background/95 backdrop-blur-md rounded-lg mt-2 border border-border",children:V.map(e=>{let a=e.icon;return(0,s.jsx)(d.P.div,{whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,s.jsxs)(i(),{href:e.href,className:"text-foreground/80 hover:text-foreground flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 hover:bg-accent",onClick:()=>t(!1),children:[(0,s.jsx)(a,{className:"h-4 w-4 mr-3"}),e.name]})},e.name)})})})})]}),(0,s.jsx)(H,{open:l,onOpenChange:m})]}):null}},9434:(e,t,a)=>{a.d(t,{cn:()=>n});var s=a(2596),r=a(9688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}}}]);