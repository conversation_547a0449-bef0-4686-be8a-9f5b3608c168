(()=>{var e={};e.id=8927,e.ids=[8927],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9005:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12752:(e,t,r)=>{Promise.resolve().then(r.bind(r,76167))},16023:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19312:(e,t,r)=>{"use strict";r.d(t,{B:()=>h});var s=r(60687),a=r(43210),i=r(29523),n=r(89667),o=r(80013),l=r(11860),d=r(9005),c=r(16023),p=r(30474),u=r(52581);function h({value:e,onChange:t,onRemove:r,disabled:h,label:m="Upload Image",className:x=""}){let[v,f]=(0,a.useState)(!1),b=(0,a.useRef)(null),g=async e=>{let r=e.target.files?.[0];if(r){f(!0);try{let e=new FormData;e.append("file",r);let s=await fetch("/api/upload",{method:"POST",body:e});if(!s.ok)throw Error("Upload failed");let a=await s.json();t(a.url)}catch(e){console.error("Error uploading image:",e),u.oR.error("Failed to upload image. Please try again.")}finally{f(!1)}}};return(0,s.jsxs)("div",{className:`space-y-2 ${x}`,children:[(0,s.jsx)(o.J,{children:m}),e?(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"relative w-full h-48 rounded-lg overflow-hidden border",children:(0,s.jsx)(p.default,{src:e,alt:"Uploaded image",fill:!0,className:"object-cover"})}),(0,s.jsx)(i.$,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2",onClick:r,disabled:h,children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})]}):(0,s.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(d.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsxs)(i.$,{type:"button",variant:"outline",onClick:()=>{b.current?.click()},disabled:h||v,children:[(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4"}),v?"Uploading...":"Choose Image"]})}),(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"PNG, JPG, GIF up to 10MB"})]})}),(0,s.jsx)(n.p,{ref:b,type:"file",accept:"image/*",onChange:g,className:"hidden",disabled:h||v})]})}},27479:(e,t,r)=>{"use strict";r.d(t,{d:()=>w});var s=r(60687),a=r(43210),i=r(70569),n=r(98599),o=r(11273),l=r(65551),d=r(83721),c=r(18853),p=r(14163),u="Switch",[h,m]=(0,o.A)(u),[x,v]=h(u),f=a.forwardRef((e,t)=>{let{__scopeSwitch:r,name:o,checked:d,defaultChecked:c,required:h,disabled:m,value:v="on",onCheckedChange:f,form:b,...g}=e,[k,w]=a.useState(null),C=(0,n.s)(t,e=>w(e)),N=a.useRef(!1),A=!k||b||!!k.closest("form"),[P,E]=(0,l.i)({prop:d,defaultProp:c??!1,onChange:f,caller:u});return(0,s.jsxs)(x,{scope:r,checked:P,disabled:m,children:[(0,s.jsx)(p.sG.button,{type:"button",role:"switch","aria-checked":P,"aria-required":h,"data-state":j(P),"data-disabled":m?"":void 0,disabled:m,value:v,...g,ref:C,onClick:(0,i.m)(e.onClick,e=>{E(e=>!e),A&&(N.current=e.isPropagationStopped(),N.current||e.stopPropagation())})}),A&&(0,s.jsx)(y,{control:k,bubbles:!N.current,name:o,value:v,checked:P,required:h,disabled:m,form:b,style:{transform:"translateX(-100%)"}})]})});f.displayName=u;var b="SwitchThumb",g=a.forwardRef((e,t)=>{let{__scopeSwitch:r,...a}=e,i=v(b,r);return(0,s.jsx)(p.sG.span,{"data-state":j(i.checked),"data-disabled":i.disabled?"":void 0,...a,ref:t})});g.displayName=b;var y=a.forwardRef(({__scopeSwitch:e,control:t,checked:r,bubbles:i=!0,...o},l)=>{let p=a.useRef(null),u=(0,n.s)(p,l),h=(0,d.Z)(r),m=(0,c.X)(t);return a.useEffect(()=>{let e=p.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==r&&t){let s=new Event("click",{bubbles:i});t.call(e,r),e.dispatchEvent(s)}},[h,r,i]),(0,s.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...o,tabIndex:-1,ref:u,style:{...o.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function j(e){return e?"checked":"unchecked"}y.displayName="SwitchBubbleInput";var k=r(4780);let w=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(f,{className:(0,k.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:r,children:(0,s.jsx)(g,{className:(0,k.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));w.displayName=f.displayName},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var s=r(60687);r(43210);var a=r(4780);function i({className:e,...t}){return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},41646:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["dashboard",{children:["experiences",{children:["add",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,65305)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\experiences\\add\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\experiences\\add\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/experiences/add/page",pathname:"/dashboard/experiences/add",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},54608:(e,t,r)=>{Promise.resolve().then(r.bind(r,65305))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65305:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\portfolio\\\\portfolio-cms\\\\src\\\\app\\\\dashboard\\\\experiences\\\\add\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\experiences\\add\\page.tsx","default")},76167:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var s=r(60687),a=r(43210),i=r(16189),n=r(62280),o=r(29523),l=r(44493),d=r(89667),c=r(80013),p=r(34729),u=r(27479),h=r(19312),m=r(28559),x=r(88233),v=r(96474),f=r(52581);function b(){let e=(0,i.useRouter)(),[t,r]=(0,a.useState)(!1),[b,g]=(0,a.useState)({title:"",company:"",companyLogo:"",location:"",period:"",type:"Full-time",description:"",achievements:[""],technologies:[""],website:"",order:0,published:!0}),y=(e,t)=>{g(r=>({...r,[e]:t}))},j=(e,t,r)=>{g(s=>({...s,[e]:s[e].map((e,s)=>s===t?r:e)}))},k=e=>{g(t=>({...t,[e]:[...t[e],""]}))},w=(e,t)=>{g(r=>({...r,[e]:r[e].filter((e,r)=>r!==t)}))},C=async t=>{t.preventDefault(),r(!0);try{let t={...b,achievements:b.achievements.filter(e=>""!==e.trim()),technologies:b.technologies.filter(e=>""!==e.trim())};if(!(await fetch("/api/experiences",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)})).ok)throw Error("Failed to create experience");f.oR.success("Experience created successfully"),e.push("/dashboard/experiences")}catch(e){console.error("Error creating experience:",e),f.oR.error("Failed to create experience")}finally{r(!1)}};return(0,s.jsx)(n.DashboardLayout,{children:(0,s.jsxs)("div",{className:"container mx-auto py-8",children:[(0,s.jsxs)("div",{className:"flex items-center mb-8",children:[(0,s.jsxs)(o.$,{variant:"outline",size:"sm",onClick:()=>e.back(),className:"mr-4",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Add Experience"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Create a new work experience entry"})]})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{children:(0,s.jsx)(l.ZB,{children:"Experience Details"})}),(0,s.jsx)(l.Wu,{children:(0,s.jsxs)("form",{onSubmit:C,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"title",children:"Job Title *"}),(0,s.jsx)(d.p,{id:"title",value:b.title,onChange:e=>y("title",e.target.value),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"company",children:"Company *"}),(0,s.jsx)(d.p,{id:"company",value:b.company,onChange:e=>y("company",e.target.value),required:!0})]}),(0,s.jsx)("div",{className:"md:col-span-2",children:(0,s.jsx)(h.B,{label:"Company Logo",value:b.companyLogo,onChange:e=>y("companyLogo",e),onRemove:()=>y("companyLogo","")})}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"location",children:"Location *"}),(0,s.jsx)(d.p,{id:"location",value:b.location,onChange:e=>y("location",e.target.value),required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"period",children:"Period *"}),(0,s.jsx)(d.p,{id:"period",value:b.period,onChange:e=>y("period",e.target.value),placeholder:"e.g., 2022 - Present",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"type",children:"Employment Type"}),(0,s.jsx)(d.p,{id:"type",value:b.type,onChange:e=>y("type",e.target.value),placeholder:"e.g., Full-time, Part-time, Contract"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"website",children:"Company Website"}),(0,s.jsx)(d.p,{id:"website",value:b.website,onChange:e=>y("website",e.target.value),placeholder:"https://company.com"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"order",children:"Display Order"}),(0,s.jsx)(d.p,{id:"order",type:"number",value:b.order,onChange:e=>y("order",parseInt(e.target.value)||0)})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"description",children:"Description *"}),(0,s.jsx)(p.T,{id:"description",value:b.description,onChange:e=>y("description",e.target.value),rows:4,required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{children:"Key Achievements"}),b.achievements.map((e,t)=>(0,s.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,s.jsx)(d.p,{value:e,onChange:e=>j("achievements",t,e.target.value),placeholder:"Enter an achievement"}),(0,s.jsx)(o.$,{type:"button",variant:"outline",size:"sm",onClick:()=>w("achievements",t),children:(0,s.jsx)(x.A,{className:"h-4 w-4"})})]},t)),(0,s.jsxs)(o.$,{type:"button",variant:"outline",size:"sm",onClick:()=>k("achievements"),className:"mt-2",children:[(0,s.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Add Achievement"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{children:"Technologies"}),b.technologies.map((e,t)=>(0,s.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,s.jsx)(d.p,{value:e,onChange:e=>j("technologies",t,e.target.value),placeholder:"Enter a technology"}),(0,s.jsx)(o.$,{type:"button",variant:"outline",size:"sm",onClick:()=>w("technologies",t),children:(0,s.jsx)(x.A,{className:"h-4 w-4"})})]},t)),(0,s.jsxs)(o.$,{type:"button",variant:"outline",size:"sm",onClick:()=>k("technologies"),className:"mt-2",children:[(0,s.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Add Technology"]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(u.d,{id:"published",checked:b.published,onCheckedChange:e=>y("published",e)}),(0,s.jsx)(c.J,{htmlFor:"published",children:"Published"})]}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(o.$,{type:"submit",disabled:t,children:t?"Creating...":"Create Experience"}),(0,s.jsx)(o.$,{type:"button",variant:"outline",onClick:()=>e.back(),children:"Cancel"})]})]})})]})]})})}},78148:(e,t,r)=>{"use strict";r.d(t,{b:()=>o});var s=r(43210),a=r(14163),i=r(60687),n=s.forwardRef((e,t)=>(0,i.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var o=n},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var s=r(60687);r(43210);var a=r(78148),i=r(4780);function n({className:e,...t}){return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},83721:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(43210);function a(e){let t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},88233:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(60687);r(43210);var a=r(4780);function i({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},96474:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,3310,1658,8580,4258,3868,2581,474,6929],()=>r(41646));module.exports=s})();