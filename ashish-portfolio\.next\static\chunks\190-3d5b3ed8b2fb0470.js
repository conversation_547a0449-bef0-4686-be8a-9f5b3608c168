(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[190],{1633:(e,t,n)=>{"use strict";t.A=void 0;var r=l(n(2115)),i=l(n(8637)),o=l(n(9300));function l(e){return e&&e.__esModule?e:{default:e}}let a=({animate:e=!0,className:t="",layout:n="2-columns",lineColor:i="#FFF",children:l})=>("object"==typeof window&&document.documentElement.style.setProperty("--line-color",i),r.default.createElement("div",{className:(0,o.default)(t,"vertical-timeline",{"vertical-timeline--animate":e,"vertical-timeline--two-columns":"2-columns"===n,"vertical-timeline--one-column-left":"1-column"===n||"1-column-left"===n,"vertical-timeline--one-column-right":"1-column-right"===n})},l));a.propTypes={children:i.default.oneOfType([i.default.arrayOf(i.default.node),i.default.node]).isRequired,className:i.default.string,animate:i.default.bool,layout:i.default.oneOf(["1-column-left","1-column","2-columns","1-column-right"]),lineColor:i.default.string},t.A=a},1788:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},1891:(e,t,n)=>{"use strict";n.r(t),n.d(t,{InView:()=>h,default:()=>h,defaultFallbackInView:()=>c,observe:()=>d,useInView:()=>v});var r=n(2115);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function o(e,t){return(o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}var l=new Map,a=new WeakMap,s=0,u=void 0;function c(e){u=e}function d(e,t,n,r){if(void 0===n&&(n={}),void 0===r&&(r=u),void 0===window.IntersectionObserver&&void 0!==r){var i=e.getBoundingClientRect();return t(r,{isIntersecting:r,target:e,intersectionRatio:"number"==typeof n.threshold?n.threshold:0,time:0,boundingClientRect:i,intersectionRect:i,rootBounds:i}),function(){}}var o=function(e){var t=Object.keys(e).sort().filter(function(t){return void 0!==e[t]}).map(function(t){var n;return t+"_"+("root"===t?(n=e.root)?(a.has(n)||(s+=1,a.set(n,s.toString())),a.get(n)):"0":e[t])}).toString(),n=l.get(t);if(!n){var r,i=new Map,o=new IntersectionObserver(function(t){t.forEach(function(t){var n,o=t.isIntersecting&&r.some(function(e){return t.intersectionRatio>=e});e.trackVisibility&&void 0===t.isVisible&&(t.isVisible=o),null==(n=i.get(t.target))||n.forEach(function(e){e(o,t)})})},e);r=o.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),n={id:t,observer:o,elements:i},l.set(t,n)}return n}(n),c=o.id,d=o.observer,f=o.elements,p=f.get(e)||[];return f.has(e)||f.set(e,p),p.push(t),d.observe(e),function(){p.splice(p.indexOf(t),1),0===p.length&&(f.delete(e),d.unobserve(e)),0===f.size&&(d.disconnect(),l.delete(c))}}var f=["children","as","triggerOnce","threshold","root","rootMargin","onChange","skip","trackVisibility","delay","initialInView","fallbackInView"];function p(e){return"function"!=typeof e.children}var h=function(e){function t(t){var n;return(n=e.call(this,t)||this).node=null,n._unobserveCb=null,n.handleNode=function(e){n.node&&(n.unobserve(),e||n.props.triggerOnce||n.props.skip||n.setState({inView:!!n.props.initialInView,entry:void 0})),n.node=e||null,n.observeNode()},n.handleChange=function(e,t){e&&n.props.triggerOnce&&n.unobserve(),p(n.props)||n.setState({inView:e,entry:t}),n.props.onChange&&n.props.onChange(e,t)},n.state={inView:!!t.initialInView,entry:void 0},n}t.prototype=Object.create(e.prototype),t.prototype.constructor=t,o(t,e);var n=t.prototype;return n.componentDidUpdate=function(e){(e.rootMargin!==this.props.rootMargin||e.root!==this.props.root||e.threshold!==this.props.threshold||e.skip!==this.props.skip||e.trackVisibility!==this.props.trackVisibility||e.delay!==this.props.delay)&&(this.unobserve(),this.observeNode())},n.componentWillUnmount=function(){this.unobserve(),this.node=null},n.observeNode=function(){if(this.node&&!this.props.skip){var e=this.props,t=e.threshold,n=e.root,r=e.rootMargin,i=e.trackVisibility,o=e.delay,l=e.fallbackInView;this._unobserveCb=d(this.node,this.handleChange,{threshold:t,root:n,rootMargin:r,trackVisibility:i,delay:o},l)}},n.unobserve=function(){this._unobserveCb&&(this._unobserveCb(),this._unobserveCb=null)},n.render=function(){if(!p(this.props)){var e=this.state,t=e.inView,n=e.entry;return this.props.children({inView:t,entry:n,ref:this.handleNode})}var o=this.props,l=o.children,a=o.as,s=function(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(o,f);return r.createElement(a||"div",i({ref:this.handleNode},s),l)},t}(r.Component);function v(e){var t=void 0===e?{}:e,n=t.threshold,i=t.delay,o=t.trackVisibility,l=t.rootMargin,a=t.root,s=t.triggerOnce,u=t.skip,c=t.initialInView,f=t.fallbackInView,p=r.useRef(),h=r.useState({inView:!!c}),v=h[0],y=h[1],m=r.useCallback(function(e){void 0!==p.current&&(p.current(),p.current=void 0),!u&&e&&(p.current=d(e,function(e,t){y({inView:e,entry:t}),t.isIntersecting&&s&&p.current&&(p.current(),p.current=void 0)},{root:a,rootMargin:l,threshold:n,trackVisibility:o,delay:i},f))},[Array.isArray(n)?n.toString():n,a,l,s,u,o,f,i]);(0,r.useEffect)(function(){p.current||!v.entry||s||u||y({inView:!!c})});var g=[m,v.inView,v.entry];return g.ref=g[0],g.inView=g[1],g.entry=g[2],g}h.displayName="InView",h.defaultProps={threshold:0,triggerOnce:!1,initialInView:!1}},2948:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},2992:(e,t,n)=>{"use strict";e.exports={VerticalTimeline:n(1633).A,VerticalTimelineElement:n(4383).A}},4383:(e,t,n)=>{"use strict";t.A=void 0;var r=a(n(2115)),i=a(n(8637)),o=a(n(9300)),l=n(1891);function a(e){return e&&e.__esModule?e:{default:e}}let s=({children:e="",className:t="",contentArrowStyle:n=null,contentStyle:i=null,date:a="",dateClassName:s="",icon:u=null,iconClassName:c="",iconOnClick:d=null,onTimelineElementClick:f=null,iconStyle:p=null,id:h="",position:v="",style:y=null,textClassName:m="",intersectionObserverProps:g={rootMargin:"0px 0px -40px 0px",triggerOnce:!0},visible:b=!1,shadowSize:w="small"})=>r.default.createElement(l.InView,g,({inView:l,ref:g})=>r.default.createElement("div",{ref:g,id:h,className:(0,o.default)(t,"vertical-timeline-element",{"vertical-timeline-element--left":"left"===v,"vertical-timeline-element--right":"right"===v,"vertical-timeline-element--no-children":""===e}),style:y},r.default.createElement(r.default.Fragment,null,r.default.createElement("span",{style:p,onClick:d,className:(0,o.default)(c,"vertical-timeline-element-icon",`shadow-size-${w}`,{"bounce-in":l||b,"is-hidden":!(l||b)})},u),r.default.createElement("div",{style:i,onClick:f,className:(0,o.default)(m,"vertical-timeline-element-content",{"bounce-in":l||b,"is-hidden":!(l||b)})},r.default.createElement("div",{style:n,className:"vertical-timeline-element-content-arrow"}),e,r.default.createElement("span",{className:(0,o.default)(s,"vertical-timeline-element-date")},a)))));s.propTypes={children:i.default.oneOfType([i.default.arrayOf(i.default.node),i.default.node]),className:i.default.string,contentArrowStyle:i.default.shape({}),contentStyle:i.default.shape({}),date:i.default.node,dateClassName:i.default.string,icon:i.default.element,iconClassName:i.default.string,iconStyle:i.default.shape({}),iconOnClick:i.default.func,onTimelineElementClick:i.default.func,id:i.default.string,position:i.default.string,style:i.default.shape({}),textClassName:i.default.string,visible:i.default.bool,shadowSize:i.default.string,intersectionObserverProps:i.default.shape({root:i.default.object,rootMargin:i.default.string,threshold:i.default.number,triggerOnce:i.default.bool})},t.A=s},7917:()=>{},8637:(e,t,n)=>{e.exports=n(9399)()},9074:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9300:(e,t)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function i(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=o(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return i.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)r.call(e,n)&&e[n]&&(t=o(t,n));return t}(n)))}return e}function o(e,t){return t?e?e+" "+t:e+t:e}e.exports?(i.default=i,e.exports=i):void 0===(n=(function(){return i}).apply(t,[]))||(e.exports=n)}()},9399:(e,t,n)=>{"use strict";var r=n(2948);function i(){}function o(){}o.resetWarningCache=i,e.exports=function(){function e(e,t,n,i,o,l){if(l!==r){var a=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:i};return n.PropTypes=n,n}}}]);