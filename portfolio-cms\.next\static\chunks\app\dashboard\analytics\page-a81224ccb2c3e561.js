(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[754],{14186:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},26126:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var r=t(95155);t(12115);var a=t(99708),i=t(74466),n=t(59434);let c=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:t,asChild:i=!1,...l}=e,d=i?a.DX:"span";return(0,r.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(c({variant:t}),s),...l})}},48236:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var r=t(95155),a=t(12115),i=t(83930),n=t(66695),c=t(26126),l=t(92657),d=t(19946);let o=(0,d.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);var x=t(72713);let m=(0,d.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);var u=t(14186);let h=(0,d.A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]]);function g(){let[e,s]=(0,a.useState)(null),[t,d]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{setTimeout(()=>{s({pageViews:12543,sessions:8921,users:6789,bounceRate:42.3,avgDuration:185,topPages:[{page:"/",views:4521},{page:"/projects",views:2134},{page:"/about",views:1876},{page:"/blog",views:1543},{page:"/contact",views:987}],topSources:[{source:"Direct",sessions:3456},{source:"Google",sessions:2890},{source:"LinkedIn",sessions:1234},{source:"GitHub",sessions:987},{source:"Twitter",sessions:354}]}),d(!1)},1e3)},[]),(0,r.jsx)(i.DashboardLayout,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Analytics"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Portfolio website performance and visitor insights"})]}),t?(0,r.jsx)("div",{className:"text-center py-8",children:"Loading analytics..."}):e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Page Views"}),(0,r.jsx)(l.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.pageViews.toLocaleString()}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[(0,r.jsx)(o,{className:"inline h-3 w-3 mr-1"}),"+12.5% from last month"]})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Sessions"}),(0,r.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.sessions.toLocaleString()}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[(0,r.jsx)(o,{className:"inline h-3 w-3 mr-1"}),"+8.2% from last month"]})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Users"}),(0,r.jsx)(m,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.users.toLocaleString()}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[(0,r.jsx)(o,{className:"inline h-3 w-3 mr-1"}),"+15.3% from last month"]})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Avg. Duration"}),(0,r.jsx)(u.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:(e=>{let s=Math.floor(e/60);return"".concat(s,":").concat((e%60).toString().padStart(2,"0"))})(e.avgDuration)}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[(0,r.jsx)(h,{className:"inline h-3 w-3 mr-1"}),"-2.1% from last month"]})]})]})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Top Pages"}),(0,r.jsx)(n.BT,{children:"Most visited pages on your portfolio"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"space-y-3",children:e.topPages.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(c.E,{variant:"outline",className:"w-6 h-6 p-0 flex items-center justify-center text-xs",children:s+1}),(0,r.jsx)("span",{className:"font-medium",children:e.page})]}),(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.views.toLocaleString()," views"]})]},e.page))})})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Traffic Sources"}),(0,r.jsx)(n.BT,{children:"Where your visitors are coming from"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)("div",{className:"space-y-3",children:e.topSources.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(c.E,{variant:"outline",className:"w-6 h-6 p-0 flex items-center justify-center text-xs",children:s+1}),(0,r.jsx)("span",{className:"font-medium",children:e.source})]}),(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.sessions.toLocaleString()," sessions"]})]},e.source))})})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Additional Metrics"}),(0,r.jsx)(n.BT,{children:"More insights about your portfolio performance"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[e.bounceRate,"%"]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Bounce Rate"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:(e.pageViews/e.sessions).toFixed(1)}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Pages per Session"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[(e.users/e.sessions*100).toFixed(1),"%"]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"New Visitors"})]})]})})]})]}):(0,r.jsx)("div",{className:"text-center py-8",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"No analytics data available"})})]})})}},61444:(e,s,t)=>{Promise.resolve().then(t.bind(t,48236))}},e=>{var s=s=>e(e.s=s);e.O(0,[4854,2108,8858,9368,9008,8441,1684,7358],()=>s(61444)),_N_E=e.O()}]);