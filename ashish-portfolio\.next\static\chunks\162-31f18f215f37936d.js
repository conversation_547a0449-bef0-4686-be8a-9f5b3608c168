"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[162],{3162:(e,a,s)=>{s.d(a,{ContactSection:()=>I});var t=s(5155),r=s(2115),i=s(6408),l=s(3096),n=s(2177),c=s(8778),o=s(1153),d=s(8883),m=s(9420),x=s(4516),u=s(4186),h=s(2894),p=s(9099),b=s(8175),g=s(4869),j=s(2486),v=s(285),y=s(2523),f=s(9434);function N(e){let{className:a,...s}=e;return(0,t.jsx)("textarea",{"data-slot":"textarea",className:(0,f.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...s})}var w=s(968);function k(e){let{className:a,...s}=e;return(0,t.jsx)(w.b,{"data-slot":"label",className:(0,f.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...s})}var P=s(6695),A=s(6671);let F=o.Ik({name:o.Yj().min(2,"Name must be at least 2 characters"),email:o.Yj().email("Please enter a valid email address"),subject:o.Yj().min(5,"Subject must be at least 5 characters"),message:o.Yj().min(10,"Message must be at least 10 characters")}),S=[{icon:d.A,label:"Email",value:"<EMAIL>",href:"mailto:<EMAIL>"},{icon:m.A,label:"Phone",value:"+9779810580378",href:"tel:+9779810580378"},{icon:x.A,label:"Location",value:"Kathmandu, Nepal",href:"https://maps.google.com/?q=Kathmandu,Nepal"},{icon:u.A,label:"Timezone",value:"NPT (UTC+5:45)",href:null}],T=[{icon:h.A,label:"LinkedIn",href:"https://linkedin.com/in/ashishkamat0",color:"text-blue-600 hover:text-blue-700"},{icon:p.A,label:"GitHub",href:"https://github.com/ash-333",color:"text-gray-800 dark:text-gray-200 hover:text-gray-600 dark:hover:text-gray-400"},{icon:b.A,label:"Twitter",href:"https://twitter.com/ashishkamat7",color:"text-blue-500 hover:text-blue-600"},{icon:g.A,label:"Website",href:"https://ashishkamat.com/np",color:"text-green-600 hover:text-green-700"}];function I(){let[e,a]=(0,r.useState)(!1),[s,o]=(0,l.Wx)({triggerOnce:!0,threshold:.1}),{register:d,handleSubmit:m,reset:x,formState:{errors:u}}=(0,n.mN)({resolver:(0,c.u)(F)}),h=async e=>{a(!0);try{await new Promise(e=>setTimeout(e,2e3)),console.log("Form submitted:",e),A.oR.success("Message sent successfully! I'll get back to you soon."),x()}catch(e){A.oR.error("Failed to send message. Please try again.")}finally{a(!1)}};return(0,t.jsx)("section",{ref:s,className:"py-20 bg-muted/30",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)(i.P.div,{className:"text-center mb-16",initial:{opacity:0,y:50},animate:o?{opacity:1,y:0}:{},transition:{duration:.8},children:[(0,t.jsx)("h2",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6",children:(0,t.jsx)("span",{className:"gradient-text",children:"Let's Work Together"})}),(0,t.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"Have a project in mind? I'd love to hear about it. Let's discuss how we can bring your ideas to life."})]}),(0,t.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto",children:[(0,t.jsxs)(i.P.div,{className:"space-y-8",initial:{opacity:0,x:-50},animate:o?{opacity:1,x:0}:{},transition:{duration:.8,delay:.2},children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-2xl font-bold mb-6",children:"Get in Touch"}),(0,t.jsx)("p",{className:"text-muted-foreground mb-8",children:"I'm always open to discussing new opportunities, creative projects, or potential collaborations. Feel free to reach out!"})]}),(0,t.jsx)("div",{className:"grid sm:grid-cols-2 gap-4",children:S.map((e,a)=>{let s=e.icon,r=(0,t.jsx)(P.Zp,{className:"hover-lift transition-all duration-300 border-border/50 hover:border-border",children:(0,t.jsx)(P.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center",children:(0,t.jsx)(s,{className:"h-5 w-5 text-primary"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:e.label}),(0,t.jsx)("div",{className:"font-medium",children:e.value})]})]})})});return(0,t.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:o?{opacity:1,y:0}:{},transition:{duration:.5,delay:.4+.1*a},children:e.href?(0,t.jsx)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",children:r}):r},e.label)})}),(0,t.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:o?{opacity:1,y:0}:{},transition:{duration:.8,delay:.6},children:[(0,t.jsx)("h4",{className:"font-semibold mb-4",children:"Connect with me"}),(0,t.jsx)("div",{className:"flex space-x-4",children:T.map((e,a)=>{let s=e.icon;return(0,t.jsx)(i.P.a,{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"p-3 rounded-full bg-background border border-border hover:border-primary/50 transition-all duration-300 ".concat(e.color),whileHover:{scale:1.1},whileTap:{scale:.9},initial:{opacity:0,scale:0},animate:o?{opacity:1,scale:1}:{},transition:{duration:.3,delay:.8+.1*a},children:(0,t.jsx)(s,{className:"h-5 w-5"})},e.label)})})]}),(0,t.jsxs)(i.P.div,{className:"bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg p-6 border border-border/50",initial:{opacity:0,y:20},animate:o?{opacity:1,y:0}:{},transition:{duration:.8,delay:.8},children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full animate-pulse"}),(0,t.jsx)("span",{className:"font-semibold text-green-700 dark:text-green-400",children:"Available for new projects"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"I'm currently accepting new client work and interesting project collaborations."})]})]}),(0,t.jsx)(i.P.div,{initial:{opacity:0,x:50},animate:o?{opacity:1,x:0}:{},transition:{duration:.8,delay:.4},children:(0,t.jsxs)(P.Zp,{className:"border-border/50",children:[(0,t.jsx)(P.aR,{children:(0,t.jsxs)(P.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(j.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Send me a message"})]})}),(0,t.jsx)(P.Wu,{children:(0,t.jsxs)("form",{onSubmit:m(h),className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid sm:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(k,{htmlFor:"name",children:"Name *"}),(0,t.jsx)(y.p,{id:"name",placeholder:"Your name",...d("name"),className:u.name?"border-destructive":""}),u.name&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:u.name.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(k,{htmlFor:"email",children:"Email *"}),(0,t.jsx)(y.p,{id:"email",type:"email",placeholder:"<EMAIL>",...d("email"),className:u.email?"border-destructive":""}),u.email&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:u.email.message})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(k,{htmlFor:"subject",children:"Subject *"}),(0,t.jsx)(y.p,{id:"subject",placeholder:"Project inquiry, collaboration, etc.",...d("subject"),className:u.subject?"border-destructive":""}),u.subject&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:u.subject.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(k,{htmlFor:"message",children:"Message *"}),(0,t.jsx)(N,{id:"message",placeholder:"Tell me about your project, timeline, budget, and any specific requirements...",rows:6,...d("message"),className:u.message?"border-destructive":""}),u.message&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:u.message.message})]}),(0,t.jsx)(v.$,{type:"submit",className:"w-full group",disabled:e,children:e?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Sending..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"mr-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300"}),"Send Message"]})})]})})]})})]})]})})}}}]);