(()=>{var e={};e.id=2905,e.ids=[2905],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26139:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var s=r(60687),i=r(43210),a=r(16189),o=r(62280),n=r(29523),d=r(44493),c=r(96834),l=r(96474),p=r(86561),u=r(40228),h=r(63143),x=r(88233),m=r(52581);function f(){let e=(0,a.useRouter)(),[t,r]=(0,i.useState)([]),[f,v]=(0,i.useState)(!0),b=async e=>{(0,m.oR)("Are you sure you want to delete this certification?",{action:{label:"Delete",onClick:async()=>{try{if(!(await fetch(`/api/certifications/${e}`,{method:"DELETE"})).ok)throw Error("Failed to delete certification");r(t.filter(t=>t.id!==e)),m.oR.success("Certification deleted successfully")}catch(e){console.error("Error deleting certification:",e),m.oR.error("Failed to delete certification")}}},cancel:{label:"Cancel",onClick:()=>{}}})},g=async(e,s)=>{try{if(!(await fetch(`/api/certifications/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({published:!s})})).ok)throw Error("Failed to update certification");r(t.map(t=>t.id===e?{...t,published:!s}:t)),m.oR.success(`Certification ${!s?"published":"unpublished"}`)}catch(e){console.error("Error updating certification:",e),m.oR.error("Failed to update certification")}};return f?(0,s.jsx)(o.DashboardLayout,{children:(0,s.jsx)("div",{className:"container mx-auto py-8",children:(0,s.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,s.jsx)("div",{className:"h-8 bg-gray-300 rounded w-64"}),(0,s.jsx)("div",{className:"grid gap-4",children:[1,2,3,4].map(e=>(0,s.jsx)("div",{className:"h-32 bg-gray-300 rounded"},e))})]})})}):(0,s.jsx)(o.DashboardLayout,{children:(0,s.jsxs)("div",{className:"container mx-auto py-8",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Certifications"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Manage your professional certifications"})]}),(0,s.jsxs)(n.$,{onClick:()=>e.push("/dashboard/certifications/add"),children:[(0,s.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Add Certification"]})]}),(0,s.jsx)("div",{className:"grid gap-6",children:t.map(t=>(0,s.jsxs)(d.Zp,{className:"overflow-hidden",children:[(0,s.jsx)(d.aR,{className:"pb-4",children:(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center",children:t.emoji?(0,s.jsx)("span",{className:"text-2xl",children:t.emoji}):(0,s.jsx)(p.A,{className:"h-6 w-6 text-orange-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d.ZB,{className:"text-xl",children:t.title}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-muted-foreground mt-1",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-1"}),t.issuer]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(u.A,{className:"h-4 w-4 mr-1"}),t.date]}),t.credentialId&&(0,s.jsxs)("div",{className:"text-xs bg-gray-100 px-2 py-1 rounded",children:["ID: ",t.credentialId]})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(c.E,{variant:t.published?"default":"secondary",children:t.published?"Published":"Draft"}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>g(t.id,t.published),children:t.published?"Unpublish":"Publish"}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>e.push(`/dashboard/certifications/${t.id}`),children:(0,s.jsx)(h.A,{className:"h-4 w-4"})}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>b(t.id),children:(0,s.jsx)(x.A,{className:"h-4 w-4"})})]})]})}),(0,s.jsx)(d.Wu,{children:(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:t.description})})]},t.id))}),0===t.length&&(0,s.jsx)(d.Zp,{children:(0,s.jsx)(d.Wu,{className:"py-8 text-center",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"No certifications found. Add your first certification to get started."})})})]})})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33387:(e,t,r)=>{Promise.resolve().then(r.bind(r,97401))},33873:e=>{"use strict";e.exports=require("path")},40228:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},76060:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>l,routeModule:()=>u,tree:()=>c});var s=r(65239),i=r(48088),a=r(88170),o=r.n(a),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let c={children:["",{children:["dashboard",{children:["certifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,97401)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\certifications\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\certifications\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/dashboard/certifications/page",pathname:"/dashboard/certifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},79551:e=>{"use strict";e.exports=require("url")},88233:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},93059:(e,t,r)=>{Promise.resolve().then(r.bind(r,26139))},96474:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>d});var s=r(60687);r(43210);var i=r(8730),a=r(24224),o=r(4780);let n=(0,a.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:r=!1,...a}){let d=r?i.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,o.cn)(n({variant:t}),e),...a})}},97401:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\portfolio\\\\portfolio-cms\\\\src\\\\app\\\\dashboard\\\\certifications\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\certifications\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,3310,1658,8580,4258,3868,2581,6929],()=>r(76060));module.exports=s})();