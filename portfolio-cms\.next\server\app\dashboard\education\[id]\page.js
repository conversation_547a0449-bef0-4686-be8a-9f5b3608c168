(()=>{var e={};e.id=9034,e.ids=[9034],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5e3:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var i=r(65239),s=r(48088),a=r(88170),n=r.n(a),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l={children:["",{children:["dashboard",{children:["education",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,44474)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\education\\[id]\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\education\\[id]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/education/[id]/page",pathname:"/dashboard/education/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27479:(e,t,r)=>{"use strict";r.d(t,{d:()=>w});var i=r(60687),s=r(43210),a=r(70569),n=r(98599),o=r(11273),d=r(65551),l=r(83721),c=r(18853),u=r(14163),p="Switch",[h,x]=(0,o.A)(p),[m,g]=h(p),f=s.forwardRef((e,t)=>{let{__scopeSwitch:r,name:o,checked:l,defaultChecked:c,required:h,disabled:x,value:g="on",onCheckedChange:f,form:v,...b}=e,[k,w]=s.useState(null),C=(0,n.s)(t,e=>w(e)),N=s.useRef(!1),P=!k||v||!!k.closest("form"),[A,E]=(0,d.i)({prop:l,defaultProp:c??!1,onChange:f,caller:p});return(0,i.jsxs)(m,{scope:r,checked:A,disabled:x,children:[(0,i.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":A,"aria-required":h,"data-state":y(A),"data-disabled":x?"":void 0,disabled:x,value:g,...b,ref:C,onClick:(0,a.m)(e.onClick,e=>{E(e=>!e),P&&(N.current=e.isPropagationStopped(),N.current||e.stopPropagation())})}),P&&(0,i.jsx)(j,{control:k,bubbles:!N.current,name:o,value:g,checked:A,required:h,disabled:x,form:v,style:{transform:"translateX(-100%)"}})]})});f.displayName=p;var v="SwitchThumb",b=s.forwardRef((e,t)=>{let{__scopeSwitch:r,...s}=e,a=g(v,r);return(0,i.jsx)(u.sG.span,{"data-state":y(a.checked),"data-disabled":a.disabled?"":void 0,...s,ref:t})});b.displayName=v;var j=s.forwardRef(({__scopeSwitch:e,control:t,checked:r,bubbles:a=!0,...o},d)=>{let u=s.useRef(null),p=(0,n.s)(u,d),h=(0,l.Z)(r),x=(0,c.X)(t);return s.useEffect(()=>{let e=u.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==r&&t){let i=new Event("click",{bubbles:a});t.call(e,r),e.dispatchEvent(i)}},[h,r,a]),(0,i.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...o,tabIndex:-1,ref:p,style:{...o.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function y(e){return e?"checked":"unchecked"}j.displayName="SwitchBubbleInput";var k=r(4780);let w=s.forwardRef(({className:e,...t},r)=>(0,i.jsx)(f,{className:(0,k.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:r,children:(0,i.jsx)(b,{className:(0,k.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));w.displayName=f.displayName},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,r)=>{"use strict";r.d(t,{T:()=>a});var i=r(60687);r(43210);var s=r(4780);function a({className:e,...t}){return(0,i.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},40471:(e,t,r)=>{Promise.resolve().then(r.bind(r,93444))},44474:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\portfolio\\\\portfolio-cms\\\\src\\\\app\\\\dashboard\\\\education\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\education\\[id]\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78148:(e,t,r)=>{"use strict";r.d(t,{b:()=>o});var i=r(43210),s=r(14163),a=r(60687),n=i.forwardRef((e,t)=>(0,a.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var o=n},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var i=r(60687);r(43210);var s=r(78148),a=r(4780);function n({className:e,...t}){return(0,i.jsx)(s.b,{"data-slot":"label",className:(0,a.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},80223:(e,t,r)=>{Promise.resolve().then(r.bind(r,44474))},83721:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var i=r(43210);function s(e){let t=i.useRef({value:e,previous:e});return i.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},88233:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var i=r(60687);r(43210);var s=r(4780);function a({className:e,type:t,...r}){return(0,i.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},93444:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var i=r(60687),s=r(43210),a=r(16189),n=r(62280),o=r(29523),d=r(44493),l=r(89667),c=r(80013),u=r(34729),p=r(27479),h=r(28559),x=r(88233),m=r(96474),g=r(52581);function f(){let e=(0,a.useRouter)(),t=(0,a.useParams)(),[r,f]=(0,s.useState)(!1),[v,b]=(0,s.useState)(!0),[j,y]=(0,s.useState)({degree:"",institution:"",location:"",period:"",grade:"",description:"",highlights:[""],order:0,published:!0}),k=(e,t)=>{y(r=>({...r,[e]:t}))},w=(e,t)=>{y(r=>({...r,highlights:r.highlights.map((r,i)=>i===e?t:r)}))},C=e=>{y(t=>({...t,highlights:t.highlights.filter((t,r)=>r!==e)}))},N=async r=>{r.preventDefault(),f(!0);try{let r={...j,highlights:j.highlights.filter(e=>""!==e.trim())};if(!(await fetch(`/api/education/${t.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)})).ok)throw Error("Failed to update education");g.oR.success("Education updated successfully"),e.push("/dashboard/education")}catch(e){console.error("Error updating education:",e),g.oR.error("Failed to update education")}finally{f(!1)}};return v?(0,i.jsx)(n.DashboardLayout,{children:(0,i.jsx)("div",{className:"container mx-auto py-8",children:(0,i.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,i.jsx)("div",{className:"h-8 bg-gray-300 rounded w-64"}),(0,i.jsx)("div",{className:"h-96 bg-gray-300 rounded"})]})})}):(0,i.jsx)(n.DashboardLayout,{children:(0,i.jsxs)("div",{className:"container mx-auto py-8",children:[(0,i.jsxs)("div",{className:"flex items-center mb-8",children:[(0,i.jsxs)(o.$,{variant:"outline",size:"sm",onClick:()=>e.back(),className:"mr-4",children:[(0,i.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold",children:"Edit Education"}),(0,i.jsx)("p",{className:"text-muted-foreground",children:"Update education entry"})]})]}),(0,i.jsxs)(d.Zp,{children:[(0,i.jsx)(d.aR,{children:(0,i.jsx)(d.ZB,{children:"Education Details"})}),(0,i.jsx)(d.Wu,{children:(0,i.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{htmlFor:"degree",children:"Degree *"}),(0,i.jsx)(l.p,{id:"degree",value:j.degree,onChange:e=>k("degree",e.target.value),required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{htmlFor:"institution",children:"Institution *"}),(0,i.jsx)(l.p,{id:"institution",value:j.institution,onChange:e=>k("institution",e.target.value),required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{htmlFor:"location",children:"Location *"}),(0,i.jsx)(l.p,{id:"location",value:j.location,onChange:e=>k("location",e.target.value),required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{htmlFor:"period",children:"Period *"}),(0,i.jsx)(l.p,{id:"period",value:j.period,onChange:e=>k("period",e.target.value),placeholder:"e.g., 2020 - 2024",required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{htmlFor:"grade",children:"Grade/GPA"}),(0,i.jsx)(l.p,{id:"grade",value:j.grade,onChange:e=>k("grade",e.target.value),placeholder:"e.g., First Class with Distinction (8.5/10 CGPA)"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{htmlFor:"order",children:"Display Order"}),(0,i.jsx)(l.p,{id:"order",type:"number",value:j.order,onChange:e=>k("order",parseInt(e.target.value)||0)})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{htmlFor:"description",children:"Description *"}),(0,i.jsx)(u.T,{id:"description",value:j.description,onChange:e=>k("description",e.target.value),rows:4,required:!0})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(c.J,{children:"Key Highlights"}),j.highlights.map((e,t)=>(0,i.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,i.jsx)(l.p,{value:e,onChange:e=>w(t,e.target.value),placeholder:"Enter a highlight"}),(0,i.jsx)(o.$,{type:"button",variant:"outline",size:"sm",onClick:()=>C(t),children:(0,i.jsx)(x.A,{className:"h-4 w-4"})})]},t)),(0,i.jsxs)(o.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{y(e=>({...e,highlights:[...e.highlights,""]}))},className:"mt-2",children:[(0,i.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Add Highlight"]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(p.d,{id:"published",checked:j.published,onCheckedChange:e=>k("published",e)}),(0,i.jsx)(c.J,{htmlFor:"published",children:"Published"})]}),(0,i.jsxs)("div",{className:"flex gap-4",children:[(0,i.jsx)(o.$,{type:"submit",disabled:r,children:r?"Updating...":"Update Education"}),(0,i.jsx)(o.$,{type:"button",variant:"outline",onClick:()=>e.back(),children:"Cancel"})]})]})})]})]})})}},96474:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4243,3310,1658,8580,4258,3868,2581,6929],()=>r(5e3));module.exports=i})();