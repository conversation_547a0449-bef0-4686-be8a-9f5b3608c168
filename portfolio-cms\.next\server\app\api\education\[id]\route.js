(()=>{var e={};e.id=4638,e.ids=[4638],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>a});var s=r(13581),o=r(16467),i=r(94747),n=r(85663);let a={adapter:(0,o.y)(i.z),providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let t=await i.z.user.findUnique({where:{email:e.email}});return t&&await n.Ay.compare(e.password,t.password)?{id:t.id,email:t.email,name:t.name,role:t.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role),e)},pages:{signIn:"/auth/signin"},secret:process.env.NEXTAUTH_SECRET}},27746:(e,t,r)=>{"use strict";r.d(t,{JB:()=>a,gx:()=>n});var s=r(32190);let o=["http://localhost:3000","http://localhost:3001","http://localhost:3002","http://localhost:3003","http://127.0.0.1:3000","http://127.0.0.1:3001","http://127.0.0.1:3002","http://127.0.0.1:3003"];function i(e){return{"Access-Control-Allow-Origin":e&&o.includes(e)?e:"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-Requested-With","Access-Control-Allow-Credentials":"true","Access-Control-Max-Age":"86400"}}function n(e,t){return Object.entries(i(t)).forEach(([t,r])=>{e.headers.set(t,r)}),e}function a(e){return new s.NextResponse(null,{status:200,headers:i(e)})}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},53139:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>w,serverHooks:()=>f,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{DELETE:()=>h,GET:()=>d,PATCH:()=>x});var o=r(96559),i=r(48088),n=r(37719),a=r(32190),u=r(19854),c=r(12909),l=r(94747),p=r(27746);async function d(e,{params:t}){try{let{id:e}=await t,r=await l.z.education.findUnique({where:{id:e}});if(!r)return(0,p.gx)(a.NextResponse.json({error:"Education record not found"},{status:404}));return(0,p.gx)(a.NextResponse.json(r))}catch(e){return console.error("Error fetching education:",e),(0,p.gx)(a.NextResponse.json({error:"Failed to fetch education"},{status:500}))}}async function x(e,{params:t}){try{let r=await (0,u.getServerSession)(c.N);if(!r?.user)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{id:s}=await t,o=await e.json(),i=await l.z.education.update({where:{id:s},data:o});return(0,p.gx)(a.NextResponse.json(i))}catch(e){return console.error("Error updating education:",e),(0,p.gx)(a.NextResponse.json({error:"Failed to update education"},{status:500}))}}async function h(e,{params:t}){try{let e=await (0,u.getServerSession)(c.N);if(!e?.user)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{id:r}=await t;return await l.z.education.delete({where:{id:r}}),(0,p.gx)(a.NextResponse.json({success:!0}))}catch(e){return console.error("Error deleting education:",e),(0,p.gx)(a.NextResponse.json({error:"Failed to delete education"},{status:500}))}}let w=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/education/[id]/route",pathname:"/api/education/[id]",filename:"route",bundlePath:"app/api/education/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\api\\education\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:m,serverHooks:f}=w;function y(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:m})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},94747:(e,t,r)=>{"use strict";r.d(t,{z:()=>o});let s=require("@prisma/client"),o=globalThis.prisma??new s.PrismaClient},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,3542,2190],()=>r(53139));module.exports=s})();