(()=>{var e={};e.id=8487,e.ids=[8487],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8623:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>b});var t=r(60687),i=r(43210),a=r(16189),n=r(62280),o=r(29523),d=r(44493),l=r(96834),c=r(96474),p=r(79410),h=r(97992),x=r(40228),u=r(63143),m=r(88233),v=r(25334),f=r(52581);function b(){let e=(0,a.useRouter)(),[s,r]=(0,i.useState)([]),[b,y]=(0,i.useState)(!0),g=async e=>{if(confirm("Are you sure you want to delete this experience?"))try{if(!(await fetch(`/api/experiences/${e}`,{method:"DELETE"})).ok)throw Error("Failed to delete experience");r(s.filter(s=>s.id!==e)),f.oR.success("Experience deleted successfully")}catch(e){console.error("Error deleting experience:",e),f.oR.error("Failed to delete experience")}},j=async(e,t)=>{try{if(!(await fetch(`/api/experiences/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({published:!t})})).ok)throw Error("Failed to update experience");r(s.map(s=>s.id===e?{...s,published:!t}:s)),f.oR.success(`Experience ${!t?"published":"unpublished"}`)}catch(e){console.error("Error updating experience:",e),f.oR.error("Failed to update experience")}};return b?(0,t.jsx)(n.DashboardLayout,{children:(0,t.jsx)("div",{className:"container mx-auto py-8",children:(0,t.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-300 rounded w-64"}),(0,t.jsx)("div",{className:"grid gap-4",children:[1,2,3].map(e=>(0,t.jsx)("div",{className:"h-48 bg-gray-300 rounded"},e))})]})})}):(0,t.jsx)(n.DashboardLayout,{children:(0,t.jsxs)("div",{className:"container mx-auto py-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"Work Experience"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Manage your professional experience"})]}),(0,t.jsxs)(o.$,{onClick:()=>e.push("/dashboard/experiences/add"),children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Add Experience"]})]}),(0,t.jsx)("div",{className:"grid gap-6",children:s.map(s=>(0,t.jsxs)(d.Zp,{className:"overflow-hidden",children:[(0,t.jsx)(d.aR,{className:"pb-4",children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[s.companyLogo&&(0,t.jsx)("img",{src:s.companyLogo,alt:`${s.company} logo`,className:"w-12 h-12 rounded-lg object-cover"}),(0,t.jsxs)("div",{children:[(0,t.jsx)(d.ZB,{className:"text-xl",children:s.title}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-muted-foreground mt-1",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-1"}),s.company]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-1"}),s.location]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-1"}),s.period]})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(l.E,{variant:s.published?"default":"secondary",children:s.published?"Published":"Draft"}),(0,t.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>j(s.id,s.published),children:s.published?"Unpublish":"Publish"}),(0,t.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>e.push(`/dashboard/experiences/${s.id}/edit`),children:(0,t.jsx)(u.A,{className:"h-4 w-4"})}),(0,t.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>g(s.id),children:(0,t.jsx)(m.A,{className:"h-4 w-4"})})]})]})}),(0,t.jsxs)(d.Wu,{children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:s.description}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"Key Achievements"}),(0,t.jsx)("ul",{className:"list-disc list-inside text-sm space-y-1",children:s.achievements.map((e,s)=>(0,t.jsx)("li",{children:e},s))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"Technologies"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:s.technologies.map((e,s)=>(0,t.jsx)(l.E,{variant:"outline",children:e},s))})]}),s.website&&(0,t.jsx)("div",{children:(0,t.jsxs)("a",{href:s.website,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-sm text-primary hover:underline",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 mr-1"}),"Company Website"]})})]})]})]},s.id))}),0===s.length&&(0,t.jsx)(d.Zp,{children:(0,t.jsx)(d.Wu,{className:"py-8 text-center",children:(0,t.jsx)("p",{className:"text-muted-foreground",children:"No experiences found. Add your first experience to get started."})})})]})})}},9295:(e,s,r)=>{Promise.resolve().then(r.bind(r,12173))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12173:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\portfolio\\\\portfolio-cms\\\\src\\\\app\\\\dashboard\\\\experiences\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\experiences\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25334:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40228:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63046:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>h,tree:()=>l});var t=r(65239),i=r(48088),a=r(88170),n=r.n(a),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(s,d);let l={children:["",{children:["dashboard",{children:["experiences",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,12173)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\experiences\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\experiences\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/dashboard/experiences/page",pathname:"/dashboard/experiences",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},63143:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},79551:e=>{"use strict";e.exports=require("url")},88233:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},92423:(e,s,r)=>{Promise.resolve().then(r.bind(r,8623))},96474:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96834:(e,s,r)=>{"use strict";r.d(s,{E:()=>d});var t=r(60687);r(43210);var i=r(8730),a=r(24224),n=r(4780);let o=(0,a.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:s,asChild:r=!1,...a}){let d=r?i.DX:"span";return(0,t.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(o({variant:s}),e),...a})}},97992:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4243,3310,1658,8580,4258,3868,2581,6929],()=>r(63046));module.exports=t})();