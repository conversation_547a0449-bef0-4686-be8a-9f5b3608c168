{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/:path*{(\\\\.json)}?", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GmR490q78K+bBIm3nmrLlRQO8rHHbnjcdhaBgWmTT64=", "__NEXT_PREVIEW_MODE_ID": "3640bd3b1c4ee26846094e6da652f693", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a91bdfbb8b869de94fe2613d1930ca424fcb90a14c23fe5808381c7c249f3b49", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4613e90530763ff81e8354e7e7d09bc8c0118e3a38b961095defaa018736a058"}}}, "instrumentation": null, "functions": {}}