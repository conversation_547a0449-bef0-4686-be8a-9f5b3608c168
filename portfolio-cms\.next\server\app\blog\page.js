(()=>{var e={};e.id=3831,e.ids=[3831],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,t,r)=>{"use strict";r.d(t,{KE:()=>m,_C:()=>u,cn:()=>l,z9:()=>c});var s=r(49384),i=r(82348),a=r(85126),n=r.n(a),o=r(26728),d=r.n(o);function l(...e){return(0,i.QP)((0,s.$)(e))}function c(e){return n()(e,{lower:!0,strict:!0,remove:/[*+~.()'"!:@]/g})}function u(e){return Math.ceil(d()(e).minutes)}function m(e){try{let t=JSON.parse(e);if("doc"===t.type&&t.content)return function e(t){let r="";for(let s of t)"text"===s.type?r+=s.text||"":s.content&&(r+=e(s.content)),["paragraph","heading","listItem"].includes(s.type)&&(r+=" ");return r.trim()}(t.content)}catch{}return e.replace(/<[^>]*>/g," ").replace(/\s+/g," ").trim()}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11639:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\portfolio\\\\portfolio-cms\\\\src\\\\app\\\\blog\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\blog\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(60687);r(43210);var i=r(8730),a=r(24224),n=r(4780);let o=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:r,asChild:a=!1,...d}){let l=a?i.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,n.cn)(o({variant:t,size:r,className:e})),...d})}},33873:e=>{"use strict";e.exports=require("path")},37789:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var s=r(60687),i=r(43210),a=r(85814),n=r.n(a),o=r(44493),d=r(96834),l=r(29523),c=r(28559),u=r(40228),m=r(48730),p=r(13861),h=r(30474);function v(){let[e,t]=(0,i.useState)([]),[r,a]=(0,i.useState)(!0),[v,x]=(0,i.useState)("All"),g=["All",...Array.from(new Set(e.map(e=>e.category)))],f="All"===v?e:e.filter(e=>e.category===v);return r?(0,s.jsx)("div",{className:"min-h-screen bg-background p-8",children:(0,s.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,s.jsx)("div",{className:"text-center",children:"Loading blog posts..."})})}):(0,s.jsx)("div",{className:"min-h-screen bg-background p-8",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[(0,s.jsx)(n(),{href:"/dashboard",children:(0,s.jsxs)(l.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Back to Dashboard"]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Blog Preview"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Preview your published blog posts"})]})]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2 mb-8",children:g.map(e=>(0,s.jsx)(l.$,{variant:v===e?"default":"outline",size:"sm",onClick:()=>x(e),children:e},e))}),0===f.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("p",{className:"text-muted-foreground",children:"No published blog posts found."}),(0,s.jsx)(n(),{href:"/dashboard/blog/new",children:(0,s.jsx)(l.$,{className:"mt-4",children:"Create your first blog post"})})]}):(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:f.map(e=>(0,s.jsx)(o.Zp,{className:"h-full hover:shadow-lg transition-shadow",children:(0,s.jsxs)(n(),{href:`/blog/${e.slug}`,children:[(0,s.jsxs)("div",{className:"relative h-48 overflow-hidden rounded-t-lg",children:[e.image?(0,s.jsx)(h.default,{src:e.image,alt:e.title,fill:!0,className:"object-cover hover:scale-105 transition-transform duration-300"}):(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 flex items-center justify-center",children:(0,s.jsx)("div",{className:"text-4xl opacity-20",children:"\uD83D\uDCDD"})}),(0,s.jsx)("div",{className:"absolute top-3 right-3",children:(0,s.jsx)(d.E,{variant:"secondary",className:"text-xs bg-background/80 backdrop-blur-sm",children:e.category})}),e.featured&&(0,s.jsx)("div",{className:"absolute top-3 left-3",children:(0,s.jsx)(d.E,{variant:"default",className:"text-xs",children:"Featured"})})]}),(0,s.jsx)(o.aR,{className:"pb-3",children:(0,s.jsx)(o.ZB,{className:"text-lg line-clamp-2 hover:text-primary transition-colors",children:e.title})}),(0,s.jsxs)(o.Wu,{className:"space-y-4",children:[(0,s.jsx)("p",{className:"text-muted-foreground text-sm line-clamp-3",children:e.excerpt}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.tags.slice(0,3).map((e,t)=>(0,s.jsx)(d.E,{variant:"outline",className:"text-xs",children:e},t)),e.tags.length>3&&(0,s.jsxs)(d.E,{variant:"outline",className:"text-xs",children:["+",e.tags.length-3]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-xs text-muted-foreground pt-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(u.A,{className:"mr-1 h-3 w-3"}),new Date(e.publishedAt||e.createdAt).toLocaleDateString()]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(m.A,{className:"mr-1 h-3 w-3"}),e.readTime||5," min"]})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(p.A,{className:"mr-1 h-3 w-3"}),e.views]})]}),(0,s.jsxs)("div",{className:"text-xs text-muted-foreground",children:["By ",e.author.name]})]})]})},e.id))})]})})}},40228:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>a,aR:()=>n});var s=r(60687);r(43210);var i=r(4780);function a({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",e),...t})}},45524:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var s=r(65239),i=r(48088),a=r(88170),n=r.n(a),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l={children:["",{children:["blog",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,11639)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\blog\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\blog\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/blog/page",pathname:"/blog",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},46627:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},47362:(e,t,r)=>{Promise.resolve().then(r.bind(r,11639))},48730:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},49633:(e,t,r)=>{Promise.resolve().then(r.bind(r,83305))},60699:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},80415:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>a});var s=r(60687),i=r(82136);function a({children:e}){return(0,s.jsx)(i.SessionProvider,{children:e})}},81866:(e,t,r)=>{Promise.resolve().then(r.bind(r,37789))},83305:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\components\\providers\\session-provider.tsx","AuthProvider")},86081:(e,t,r)=>{Promise.resolve().then(r.bind(r,80415))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>l});var s=r(37413),i=r(22376),a=r.n(i),n=r(68726),o=r.n(n);r(61135);var d=r(83305);let l={title:"Portfolio CMS",description:"Content Management System for Portfolio Website"};function c({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${a().variable} ${o().variable} antialiased`,children:(0,s.jsx)(d.AuthProvider,{children:e})})})}},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>d});var s=r(60687);r(43210);var i=r(8730),a=r(24224),n=r(4780);let o=(0,a.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:r=!1,...a}){let d=r?i.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(o({variant:t}),e),...a})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,3310,1658,8580,4258,474],()=>r(45524));module.exports=s})();