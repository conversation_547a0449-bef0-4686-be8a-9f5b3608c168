(()=>{var e={};e.id=492,e.ids=[492],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34711:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},38648:(e,r,t)=>{Promise.resolve().then(t.bind(t,83701)),Promise.resolve().then(t.bind(t,48482))},41115:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>h,pages:()=>p,routeModule:()=>m,tree:()=>d});var o=t(65239),s=t(48088),n=t(88170),i=t.n(n),a=t(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);t.d(r,l);let d={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,p=[],h={require:t,loadChunk:()=>Promise.resolve()},m=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},48376:(e,r,t)=>{Promise.resolve().then(t.bind(t,96871)),Promise.resolve().then(t.bind(t,64616))},48482:(e,r,t)=>{"use strict";t.d(r,{Toaster:()=>o});let o=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\ui\\sonner.tsx","Toaster")},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64616:(e,r,t)=>{"use strict";t.d(r,{Toaster:()=>i});var o=t(60687),s=t(10218),n=t(52581);let i=({...e})=>{let{theme:r="system"}=(0,s.D)();return(0,o.jsx)(n.l$,{theme:r,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},83701:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>o});let o=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\ashish-portfolio\\src\\components\\theme-provider.tsx","ThemeProvider")},87855:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h,metadata:()=>p});var o=t(37413),s=t(22376),n=t.n(s),i=t(68726),a=t.n(i);t(61135);var l=t(48482),d=t(83701);let p={title:"Ashish Kamat - Full Stack Developer & UI/UX Designer",description:"Passionate full-stack developer and UI/UX designer creating innovative digital experiences. Specializing in React, Next.js, TypeScript, and modern web technologies.",keywords:["Ashish Kamat","Full Stack Developer","UI/UX Designer","React","Next.js","TypeScript","Web Development","Backend Development","Portfolio"],authors:[{name:"Ashish Kamat"}],creator:"Ashish Kamat",openGraph:{type:"website",locale:"en_US",url:"https://ashishkamat.com.np",title:"Ashish Kamat - Full Stack Developer & UI/UX Designer",description:"Passionate full-stack developer and UI/UX designer creating innovative digital experiences.",siteName:"Ashish Kamat Portfolio"},twitter:{card:"summary_large_image",title:"Ashish Kamat - Full Stack Developer & UI/UX Designer",description:"Passionate full-stack developer and UI/UX designer creating innovative digital experiences.",creator:"@ashishkamat"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function h({children:e}){return(0,o.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,o.jsx)("body",{className:`${n().variable} ${a().variable} antialiased`,children:(0,o.jsxs)(d.ThemeProvider,{attribute:"class",defaultTheme:"dark",enableSystem:!0,disableTransitionOnChange:!0,children:[e,(0,o.jsx)(l.Toaster,{})]})})})}},96871:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>n});var o=t(60687);t(43210);var s=t(10218);function n({children:e,...r}){return(0,o.jsx)(s.N,{...r,children:e})}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[447,760],()=>t(41115));module.exports=o})();