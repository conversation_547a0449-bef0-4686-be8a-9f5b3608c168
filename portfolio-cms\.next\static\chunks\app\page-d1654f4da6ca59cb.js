(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{33792:(e,s,u)=>{"use strict";u.r(s),u.d(s,{default:()=>o});var n=u(95155),t=u(12115),r=u(35695),a=u(12108);function o(){let e=(0,r.useRouter)(),{data:s,status:u}=(0,a.useSession)();return(0,t.useEffect)(()=>{"loading"!==u&&(s?e.push("/dashboard"):e.push("/auth/signin"))},[s,u,e]),(0,n.jsx)("div",{className:"flex h-screen items-center justify-center",children:(0,n.jsx)("div",{className:"text-lg",children:"Redirecting..."})})}},35695:(e,s,u)=>{"use strict";var n=u(18999);u.o(n,"notFound")&&u.d(s,{notFound:function(){return n.notFound}}),u.o(n,"useParams")&&u.d(s,{useParams:function(){return n.useParams}}),u.o(n,"usePathname")&&u.d(s,{usePathname:function(){return n.usePathname}}),u.o(n,"useRouter")&&u.d(s,{useRouter:function(){return n.useRouter}})},46320:(e,s,u)=>{Promise.resolve().then(u.bind(u,33792))}},e=>{var s=s=>e(e.s=s);e.O(0,[2108,8441,1684,7358],()=>s(46320)),_N_E=e.O()}]);