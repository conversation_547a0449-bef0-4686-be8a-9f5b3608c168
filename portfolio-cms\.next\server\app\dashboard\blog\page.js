(()=>{var e={};e.id=7262,e.ids=[7262],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,t,s)=>{"use strict";s.d(t,{A0:()=>n,BF:()=>o,Hj:()=>l,XI:()=>i,nA:()=>c,nd:()=>d});var r=s(60687);s(43210);var a=s(4780);function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",e),...t})})}function n({className:e,...t}){return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",e),...t})}function o({className:e,...t}){return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function d({className:e,...t}){return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15250:(e,t,s)=>{Promise.resolve().then(s.bind(s,29528))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21256:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["dashboard",{children:["blog",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,80638)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\blog\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\blog\\page.tsx"],h={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/blog/page",pathname:"/dashboard/blog",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},25334:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29528:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var r=s(60687),a=s(43210),i=s(16189),n=s(62280),o=s(29523),l=s(44493),d=s(6211),c=s(96834),h=s(96474),u=s(13861),p=s(25334),x=s(63143),m=s(88233),b=s(52581);function g(){let e=(0,i.useRouter)(),[t,s]=(0,a.useState)([]),[g,v]=(0,a.useState)(!0),f=async e=>{(0,b.oR)("Are you sure you want to delete this blog post?",{action:{label:"Delete",onClick:async()=>{try{if((await fetch(`/api/blog/${e}`,{method:"DELETE"})).ok)s(t.filter(t=>t.id!==e)),b.oR.success("Blog post deleted successfully");else throw Error("Failed to delete blog post")}catch(e){console.error("Error deleting blog post:",e),b.oR.error("Failed to delete blog post")}}},cancel:{label:"Cancel",onClick:()=>{}}})};return(0,r.jsx)(n.DashboardLayout,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Blog Posts"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage your blog content"})]}),(0,r.jsxs)(o.$,{onClick:()=>e.push("/dashboard/blog/new"),children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"New Post"]})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsx)(l.ZB,{children:"All Blog Posts"}),(0,r.jsx)(l.BT,{children:"A list of all your blog posts and articles"})]}),(0,r.jsx)(l.Wu,{children:g?(0,r.jsx)("div",{className:"text-center py-4",children:"Loading..."}):0===t.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("p",{className:"text-muted-foreground",children:"No blog posts found."}),(0,r.jsxs)(o.$,{className:"mt-4",onClick:()=>e.push("/dashboard/blog/new"),children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Create your first post"]})]}):(0,r.jsxs)(d.XI,{children:[(0,r.jsx)(d.A0,{children:(0,r.jsxs)(d.Hj,{children:[(0,r.jsx)(d.nd,{children:"Title"}),(0,r.jsx)(d.nd,{children:"Category"}),(0,r.jsx)(d.nd,{children:"Tags"}),(0,r.jsx)(d.nd,{children:"Status"}),(0,r.jsx)(d.nd,{children:"Views"}),(0,r.jsx)(d.nd,{children:"Actions"})]})}),(0,r.jsx)(d.BF,{children:t.map(t=>(0,r.jsxs)(d.Hj,{children:[(0,r.jsx)(d.nA,{children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:t.title}),(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:[t.excerpt.substring(0,60),"..."]}),t.readTime&&(0,r.jsxs)("div",{className:"text-xs text-muted-foreground mt-1",children:[t.readTime," min read"]})]})}),(0,r.jsx)(d.nA,{children:(0,r.jsx)(c.E,{variant:"outline",children:t.category})}),(0,r.jsx)(d.nA,{children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-1",children:[t.tags.slice(0,2).map(e=>(0,r.jsx)(c.E,{variant:"secondary",className:"text-xs",children:e},e)),t.tags.length>2&&(0,r.jsxs)(c.E,{variant:"secondary",className:"text-xs",children:["+",t.tags.length-2]})]})}),(0,r.jsx)(d.nA,{children:(0,r.jsxs)("div",{className:"flex gap-1",children:[t.featured&&(0,r.jsx)(c.E,{variant:"default",children:"Featured"}),(0,r.jsx)(c.E,{variant:t.published?"default":"secondary",children:t.published?"Published":"Draft"})]})}),(0,r.jsx)(d.nA,{children:(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 text-muted-foreground"}),(0,r.jsx)("span",{className:"text-sm",children:t.views})]})}),(0,r.jsx)(d.nA,{children:(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(o.$,{variant:"ghost",size:"sm",onClick:()=>e.push(`/blog/${t.slug}`),title:"Preview",children:(0,r.jsx)(p.A,{className:"h-4 w-4"})}),(0,r.jsx)(o.$,{variant:"ghost",size:"sm",onClick:()=>e.push(`/dashboard/blog/${t.id}`),title:"Edit",children:(0,r.jsx)(x.A,{className:"h-4 w-4"})}),(0,r.jsx)(o.$,{variant:"ghost",size:"sm",onClick:()=>f(t.id),title:"Delete",children:(0,r.jsx)(m.A,{className:"h-4 w-4"})})]})})]},t.id))})]})})]})]})})}},33873:e=>{"use strict";e.exports=require("path")},52202:(e,t,s)=>{Promise.resolve().then(s.bind(s,80638))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},79551:e=>{"use strict";e.exports=require("url")},80638:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\portfolio\\\\portfolio-cms\\\\src\\\\app\\\\dashboard\\\\blog\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\portfolio\\portfolio-cms\\src\\app\\dashboard\\blog\\page.tsx","default")},88233:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},96474:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var r=s(60687);s(43210);var a=s(8730),i=s(24224),n=s(4780);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:s=!1,...i}){let l=s?a.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(o({variant:t}),e),...i})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,3310,1658,8580,4258,3868,2581,6929],()=>s(21256));module.exports=r})();